# CloudVPS 云虚拟化平台

## 项目概述

CloudVPS 是一个基于微服务架构的云虚拟化平台，提供完整的虚拟机生命周期管理、订单处理、支付系统和用户管理功能。平台采用现代化的技术栈，支持高并发、高可用的云服务场景。

## 技术架构

### 核心技术栈

- **Java 版本**: JDK 21 (LTS)
- **框架**: Spring Boot 3.2.1
- **微服务**: Spring Cloud 2023.0.0
- **数据库**: PostgreSQL 16+ (主数据库), H2 (开发测试)
- **缓存**: Redis 7.2+
- **构建工具**: Maven 3.9+
- **API 网关**: Spring Cloud Gateway
- **服务发现**: Consul (可选)
- **熔断器**: Resilience4j
- **文档**: Swagger/OpenAPI 3

### 系统架构图

```
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (Port 8080)   │
                    └─────────┬───────┘
                              │
            ┌─────────────────┼─────────────────┐
            │                 │                 │
    ┌───────▼──────┐ ┌───────▼──────┐ ┌───────▼──────┐
    │ System Service│ │Virtual Service│ │Order Service │
    │  (Port 8081)  │ │  (Port 8082)  │ │ (Port 8083) │
    └───────────────┘ └───────────────┘ └──────┬──────┘
                                               │
                                      ┌───────▼──────┐
                                      │Payment Service│
                                      │ (Port 8084)  │
                                      └──────────────┘
```

## 微服务架构

### 1. API 网关 (cloudvps-gateway)
- **端口**: 8080
- **功能**: 统一入口、路由转发、JWT 认证、限流、熔断
- **技术**: Spring Cloud Gateway, Resilience4j

### 2. 系统服务 (cloudvps-system)
- **端口**: 8081
- **功能**: 用户管理、认证授权、系统配置、字典管理
- **数据库**: PostgreSQL
- **API 数量**: 113个接口

### 3. 虚拟化服务 (cloudvps-virtualization)
- **端口**: 8082
- **功能**: PVE 集成、虚拟机生命周期管理、资源监控
- **数据库**: H2 (开发), PostgreSQL (生产)
- **集成**: pve-java-client 1.0.0

### 4. 订单服务 (cloudvps-order)
- **端口**: 8083
- **功能**: 订单管理、产品目录、订单处理流程
- **数据库**: PostgreSQL
- **集成**: 系统服务、虚拟化服务

### 5. 支付服务 (cloudvps-payment)
- **端口**: 8084
- **功能**: 多渠道支付、交易管理、退款处理
- **数据库**: PostgreSQL
- **支付渠道**: 支付宝、微信支付、银行转账

## 当前开发进度

### ✅ 已完成功能

#### 系统服务 (100% 完成)
- ✅ 用户注册、登录、注销
- ✅ JWT 令牌生成和验证
- ✅ 角色权限管理 (RBAC)
- ✅ 用户账户管理 (余额、充值、冻结)
- ✅ 系统配置动态管理
- ✅ 字典数据管理
- ✅ 登录日志审计

#### 虚拟化服务 (90% 完成)
- ✅ PVE 节点管理
- ✅ 虚拟机生命周期管理
- ✅ 模拟模式和真实 PVE 集成
- ✅ 定时任务 (心跳检测、自动启动)
- ✅ 资源监控
- 🔄 生产环境 PostgreSQL 集成

#### 订单服务 (80% 完成)
- ✅ 订单创建和管理
- ✅ 产品目录管理
- ✅ 订单状态流转
- ✅ 数据库架构设计
- 🔄 完整业务流程集成

#### 支付服务 (80% 完成)
- ✅ 支付渠道配置
- ✅ 交易记录管理
- ✅ 退款处理
- ✅ 数据库架构设计
- 🔄 第三方支付集成

#### API 网关 (100% 完成)
- ✅ 路由配置 (所有服务)
- ✅ JWT 认证过滤器
- ✅ 限流保护
- ✅ 熔断器配置
- ✅ CORS 跨域支持
- ✅ 健康检查

### 🔄 进行中功能
- 生产环境数据库配置优化
- 第三方支付接口集成
- 完整的端到端业务流程测试

### 📋 待开发功能
- 监控和日志聚合
- 容器化部署 (Docker/Kubernetes)
- 自动化 CI/CD 流水线
- 性能优化和负载测试

## 快速开始

### 环境要求
- JDK 21
- PostgreSQL 16+
- Redis 7.2+
- Maven 3.9+

### 数据库初始化
```bash
# 创建数据库
createdb cloudvps_system
createdb cloudvps_order
createdb cloudvps_payment

# 初始化数据库结构 (自动执行)
```

### 启动服务

#### 1. 启动基础服务
```bash
# 启动 PostgreSQL
brew services start postgresql

# 启动 Redis
brew services start redis
```

#### 2. 启动微服务 (按顺序)
```bash
# 1. 系统服务
cd cloudvps-system/cloudvps-system-service
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 2. 虚拟化服务
cd cloudvps-virtualization/cloudvps-virtualization-service
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 3. 订单服务
cd cloudvps-order/cloudvps-order-service
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 4. 支付服务
cd cloudvps-payment/cloudvps-payment-service
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 5. API 网关
cd cloudvps-gateway
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 健康检查
```bash
# 网关健康检查
curl http://localhost:8080/actuator/health

# 各服务健康检查
curl http://localhost:8081/actuator/health  # 系统服务
curl http://localhost:8082/actuator/health  # 虚拟化服务
curl http://localhost:8083/actuator/health  # 订单服务
curl http://localhost:8084/actuator/health  # 支付服务
```

## API 文档

### 访问地址
- **网关 Swagger**: http://localhost:8080/swagger-ui/index.html
- **系统服务**: http://localhost:8081/swagger-ui/index.html
- **虚拟化服务**: http://localhost:8082/swagger-ui/index.html
- **订单服务**: http://localhost:8083/swagger-ui/index.html
- **支付服务**: http://localhost:8084/swagger-ui/index.html

### 主要 API 路径
- `/system/**` - 系统管理相关接口
- `/virtualization/**` - 虚拟化管理接口
- `/order/**` - 订单管理接口
- `/payment/**` - 支付相关接口

## 部署指南

### 开发环境
- 使用 H2 内存数据库 (虚拟化服务)
- 本地 PostgreSQL (其他服务)
- 本地 Redis
- 模拟模式 (PVE 集成)

### 生产环境
- PostgreSQL 集群
- Redis 集群
- 真实 PVE 环境
- 容器化部署 (Docker/Kubernetes)

### 构建部署包
```bash
# 构建所有模块
mvn clean package -DskipTests

# 构建 Docker 镜像
docker build -t cloudvps-gateway:latest cloudvps-gateway/
docker build -t cloudvps-system:latest cloudvps-system/cloudvps-system-service/
# ... 其他服务
```

## 监控和运维

### 监控端点
- `/actuator/health` - 健康检查
- `/actuator/metrics` - 应用指标
- `/actuator/info` - 应用信息
- `/actuator/prometheus` - Prometheus 指标

### 日志配置
- 开发环境: DEBUG 级别
- 生产环境: INFO 级别
- 结构化日志输出

## 安全配置

### JWT 认证
- 令牌有效期: 24小时
- 刷新令牌机制
- 网关统一认证

### 权限控制
- 基于角色的访问控制 (RBAC)
- 细粒度权限管理
- API 级别权限控制

## 贡献指南

### 开发规范
- 代码风格: Google Java Style
- 提交规范: Conventional Commits
- 分支策略: Git Flow

### 测试要求
- 单元测试覆盖率 > 80%
- 集成测试覆盖主要业务流程
- API 测试覆盖所有接口

## 版本历史

- **v1.0.0**: 初始版本，基础微服务架构
- **当前版本**: JDK 21 + Spring Boot 3.2.1 现代化升级

## 联系方式

- 项目维护者: CloudVPS 开发团队
- 技术支持: 查看各服务模块文档
- 问题反馈: 通过健康检查端点监控服务状态
