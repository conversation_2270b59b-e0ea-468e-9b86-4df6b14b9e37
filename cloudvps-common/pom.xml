<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cloudvps</groupId>
        <artifactId>cloudvps-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../cloudvps-parent/pom.xml</relativePath>
    </parent>

    <artifactId>cloudvps-common</artifactId>
    <packaging>pom</packaging>

    <name>CloudVPS Common Modules</name>
    <description>CloudVPS 平台公共模块</description>

    <modules>
        <module>common-core</module>
        <module>common-security</module>
        <module>common-swagger</module>
        <module>common-feign</module>
        <module>common-mybatis</module>
    </modules>




</project>
