package com.cloudvps.common.mybatis.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础实体类 - MyBatis-Plus版本
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public abstract class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    /**
     * 创建者ID
     */
    @TableField("created_by")
    private Long createdBy;

    /**
     * 更新者ID
     */
    @TableField("updated_by")
    private Long updatedBy;

    /**
     * 是否删除（逻辑删除标记）
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted = false;

    /**
     * 版本号（乐观锁）
     */
    @Version
    @TableField("version")
    private Long version = 0L;
}
