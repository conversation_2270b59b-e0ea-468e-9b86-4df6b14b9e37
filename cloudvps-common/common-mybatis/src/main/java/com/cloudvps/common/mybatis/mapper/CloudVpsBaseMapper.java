package com.cloudvps.common.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * CloudVPS 通用 Mapper 接口
 * 扩展 MyBatis-Plus BaseMapper，提供常用的通用方法
 *
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CloudVpsBaseMapper<T> extends BaseMapper<T> {
    
    /**
     * 根据ID批量查询
     *
     * @param ids ID列表
     * @return 实体列表
     */
    List<T> selectBatchByIds(@Param("ids") List<? extends Serializable> ids);
    
    /**
     * 分页查询所有记录（包含逻辑删除）
     *
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<T> selectPageWithDeleted(Page<T> page);
    
    /**
     * 统计总记录数（不包含逻辑删除）
     *
     * @return 总记录数
     */
    Long countAll();
    
    /**
     * 统计总记录数（包含逻辑删除）
     *
     * @return 总记录数
     */
    Long countAllWithDeleted();
    
    /**
     * 物理删除（真正删除记录）
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deletePhysicalById(@Param("id") Serializable id);
    
    /**
     * 批量物理删除
     *
     * @param ids ID列表
     * @return 影响行数
     */
    int deletePhysicalByIds(@Param("ids") List<? extends Serializable> ids);
    
    /**
     * 恢复逻辑删除的记录
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int restoreById(@Param("id") Serializable id);
    
    /**
     * 批量恢复逻辑删除的记录
     *
     * @param ids ID列表
     * @return 影响行数
     */
    int restoreByIds(@Param("ids") List<? extends Serializable> ids);
}
