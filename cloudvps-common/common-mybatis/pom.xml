<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cloudvps</groupId>
        <artifactId>cloudvps-common</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    
    <artifactId>common-mybatis</artifactId>
    <packaging>jar</packaging>
    <name>CloudVPS Common MyBatis</name>
    <description>CloudVPS 通用 MyBatis 配置和组件</description>
    
    <dependencies>
        <!-- Common Core -->
        <dependency>
            <groupId>com.cloudvps</groupId>
            <artifactId>common-core</artifactId>
        </dependency>
        
        <!-- MyBatis-Plus Spring Boot 3 Starter -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        
        <!-- MyBatis-Plus Generator (可选) -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- MyBatis-Plus JSQLParser (分页插件需要) -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
        </dependency>
        
        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        
        <!-- Spring Security (用于获取当前用户) -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
            <optional>true</optional>
        </dependency>
        
        <!-- Jackson (用于JSON处理) -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
