package com.cloudvps.common.security.annotation;

import java.lang.annotation.*;

/**
 * 需要认证注解
 * 
 * 用于标记需要用户登录认证的接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireAuth {
    
    /**
     * 是否必须认证，默认为true
     */
    boolean required() default true;
    
    /**
     * 认证失败时的错误消息
     */
    String message() default "请先登录";
}
