package com.cloudvps.common.security.filter;

import com.cloudvps.common.security.util.JwtUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * JWT认证过滤器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    private final JwtUtils jwtUtils;

    public JwtAuthenticationFilter(JwtUtils jwtUtils) {
        this.jwtUtils = jwtUtils;
    }
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            String jwt = getJwtFromRequest(request);
            
            if (StringUtils.hasText(jwt) && jwtUtils.validateToken(jwt)) {
                String username = jwtUtils.getUsernameFromToken(jwt);
                Long userId = jwtUtils.getUserIdFromToken(jwt);
                
                if (username != null && userId != null) {
                    // 创建基本权限列表（这里可以根据需要扩展）
                    List<SimpleGrantedAuthority> authorities = getAuthoritiesFromToken(jwt);
                    
                    // 创建认证对象
                    UsernamePasswordAuthenticationToken authentication = 
                        new UsernamePasswordAuthenticationToken(username, null, authorities);
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    
                    // 设置用户信息到请求属性中
                    request.setAttribute("userId", userId);
                    request.setAttribute("username", username);
                    
                    // 设置到安全上下文
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                }
            }
        } catch (Exception e) {
            log.error("JWT认证失败", e);
        }
        
        filterChain.doFilter(request, response);
    }
    
    /**
     * 从请求中获取JWT令牌
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
    
    /**
     * 从JWT令牌中获取权限列表
     * 这里先返回基本权限，后续可以扩展为从system-service获取详细权限
     */
    private List<SimpleGrantedAuthority> getAuthoritiesFromToken(String jwt) {
        try {
            // 从JWT中获取用户ID，然后查询数据库获取角色信息
            Long userId = jwtUtils.getUserIdFromToken(jwt);
            String username = jwtUtils.getUsernameFromToken(jwt);

            // 检查是否为超级管理员用户
            if ("superadmin".equals(username) || Long.valueOf(1L).equals(userId)) {
                log.info("检测到超级管理员用户: {}, 分配所有权限", username);
                return getSuperAdminAuthorities();
            }

            // 普通用户返回基本权限
            return Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"));

        } catch (Exception e) {
            log.warn("解析JWT权限信息失败: {}", e.getMessage());
            // 如果解析失败，返回基本权限
            return Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"));
        }
    }

    /**
     * 获取超级管理员的所有权限
     */
    private List<SimpleGrantedAuthority> getSuperAdminAuthorities() {
        return List.of(
            // 基本角色权限
            new SimpleGrantedAuthority("ROLE_USER"),
            new SimpleGrantedAuthority("ROLE_ADMIN"),
            new SimpleGrantedAuthority("SUPER_ADMIN"),
            new SimpleGrantedAuthority("ROLE_SUPER_ADMIN"),

            // 用户管理权限
            new SimpleGrantedAuthority("USER_VIEW"),
            new SimpleGrantedAuthority("USER_CREATE"),
            new SimpleGrantedAuthority("USER_UPDATE"),
            new SimpleGrantedAuthority("USER_DELETE"),

            // 角色管理权限
            new SimpleGrantedAuthority("ROLE_VIEW"),
            new SimpleGrantedAuthority("ROLE_CREATE"),
            new SimpleGrantedAuthority("ROLE_UPDATE"),
            new SimpleGrantedAuthority("ROLE_DELETE"),

            // 菜单管理权限
            new SimpleGrantedAuthority("MENU_VIEW"),
            new SimpleGrantedAuthority("MENU_CREATE"),
            new SimpleGrantedAuthority("MENU_UPDATE"),
            new SimpleGrantedAuthority("MENU_DELETE"),

            // 权限管理权限
            new SimpleGrantedAuthority("PERMISSION_VIEW"),
            new SimpleGrantedAuthority("PERMISSION_CREATE"),
            new SimpleGrantedAuthority("PERMISSION_UPDATE"),
            new SimpleGrantedAuthority("PERMISSION_DELETE"),

            // 用户账户管理权限
            new SimpleGrantedAuthority("USER_ACCOUNT_VIEW"),
            new SimpleGrantedAuthority("USER_ACCOUNT_CREATE"),
            new SimpleGrantedAuthority("USER_ACCOUNT_UPDATE"),
            new SimpleGrantedAuthority("USER_ACCOUNT_DELETE"),

            // 登录日志管理权限
            new SimpleGrantedAuthority("LOGIN_LOG_VIEW"),
            new SimpleGrantedAuthority("LOGIN_LOG_DELETE"),

            // 字典类型管理权限
            new SimpleGrantedAuthority("DICT_TYPE_VIEW"),
            new SimpleGrantedAuthority("DICT_TYPE_CREATE"),
            new SimpleGrantedAuthority("DICT_TYPE_UPDATE"),
            new SimpleGrantedAuthority("DICT_TYPE_DELETE"),

            // 字典数据管理权限
            new SimpleGrantedAuthority("DICT_DATA_VIEW"),
            new SimpleGrantedAuthority("DICT_DATA_CREATE"),
            new SimpleGrantedAuthority("DICT_DATA_UPDATE"),
            new SimpleGrantedAuthority("DICT_DATA_DELETE"),

            // 系统配置管理权限
            new SimpleGrantedAuthority("SYSTEM_CONFIG_VIEW"),
            new SimpleGrantedAuthority("SYSTEM_CONFIG_CREATE"),
            new SimpleGrantedAuthority("SYSTEM_CONFIG_UPDATE"),
            new SimpleGrantedAuthority("SYSTEM_CONFIG_DELETE"),

            // 虚拟化管理权限
            new SimpleGrantedAuthority("VIRTUALIZATION_NODE_VIEW"),
            new SimpleGrantedAuthority("VIRTUALIZATION_NODE_CREATE"),
            new SimpleGrantedAuthority("VIRTUALIZATION_NODE_UPDATE"),
            new SimpleGrantedAuthority("VIRTUALIZATION_NODE_DELETE"),
            new SimpleGrantedAuthority("VIRTUALIZATION_VM_VIEW"),
            new SimpleGrantedAuthority("VIRTUALIZATION_VM_CREATE"),
            new SimpleGrantedAuthority("VIRTUALIZATION_VM_UPDATE"),
            new SimpleGrantedAuthority("VIRTUALIZATION_VM_DELETE"),

            // 订单管理权限
            new SimpleGrantedAuthority("ORDER_VIEW"),
            new SimpleGrantedAuthority("ORDER_CREATE"),
            new SimpleGrantedAuthority("ORDER_UPDATE"),
            new SimpleGrantedAuthority("ORDER_DELETE"),

            // 支付管理权限
            new SimpleGrantedAuthority("PAYMENT_VIEW"),
            new SimpleGrantedAuthority("PAYMENT_CREATE"),
            new SimpleGrantedAuthority("PAYMENT_UPDATE"),
            new SimpleGrantedAuthority("PAYMENT_DELETE")
        );
    }
}
