package com.cloudvps.common.security.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.security.SecureRandom;
import java.util.regex.Pattern;

/**
 * 密码工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class PasswordUtils {
    
    private static final PasswordEncoder PASSWORD_ENCODER = new BCryptPasswordEncoder();
    private static final SecureRandom RANDOM = new SecureRandom();
    
    // 密码强度正则表达式
    private static final Pattern WEAK_PASSWORD = Pattern.compile("^.{1,7}$");
    private static final Pattern MEDIUM_PASSWORD = Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$");
    private static final Pattern STRONG_PASSWORD = Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&].{8,}$");
    
    /**
     * 加密密码
     */
    public static String encode(String rawPassword) {
        return PASSWORD_ENCODER.encode(rawPassword);
    }
    
    /**
     * 验证密码
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        return PASSWORD_ENCODER.matches(rawPassword, encodedPassword);
    }
    
    /**
     * 生成随机密码
     */
    public static String generateRandomPassword(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@$!%*?&";
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            password.append(chars.charAt(RANDOM.nextInt(chars.length())));
        }
        
        return password.toString();
    }
    
    /**
     * 生成强密码
     */
    public static String generateStrongPassword() {
        return generateStrongPassword(12);
    }
    
    /**
     * 生成指定长度的强密码
     */
    public static String generateStrongPassword(int length) {
        if (length < 8) {
            throw new IllegalArgumentException("密码长度不能少于8位");
        }
        
        String uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String lowercase = "abcdefghijklmnopqrstuvwxyz";
        String digits = "0123456789";
        String special = "@$!%*?&";
        
        StringBuilder password = new StringBuilder();
        
        // 确保包含各种字符类型
        password.append(uppercase.charAt(RANDOM.nextInt(uppercase.length())));
        password.append(lowercase.charAt(RANDOM.nextInt(lowercase.length())));
        password.append(digits.charAt(RANDOM.nextInt(digits.length())));
        password.append(special.charAt(RANDOM.nextInt(special.length())));
        
        // 填充剩余长度
        String allChars = uppercase + lowercase + digits + special;
        for (int i = 4; i < length; i++) {
            password.append(allChars.charAt(RANDOM.nextInt(allChars.length())));
        }
        
        // 打乱字符顺序
        return shuffleString(password.toString());
    }
    
    /**
     * 检查密码强度
     */
    public static PasswordStrength checkPasswordStrength(String password) {
        if (password == null || password.isEmpty()) {
            return PasswordStrength.INVALID;
        }
        
        if (WEAK_PASSWORD.matcher(password).matches()) {
            return PasswordStrength.WEAK;
        }
        
        if (STRONG_PASSWORD.matcher(password).matches()) {
            return PasswordStrength.STRONG;
        }
        
        if (MEDIUM_PASSWORD.matcher(password).matches()) {
            return PasswordStrength.MEDIUM;
        }
        
        return PasswordStrength.WEAK;
    }
    
    /**
     * 验证密码格式
     */
    public static boolean isValidPassword(String password) {
        return password != null && password.length() >= 6 && password.length() <= 50;
    }
    
    /**
     * 打乱字符串
     */
    private static String shuffleString(String string) {
        char[] chars = string.toCharArray();
        for (int i = chars.length - 1; i > 0; i--) {
            int j = RANDOM.nextInt(i + 1);
            char temp = chars[i];
            chars[i] = chars[j];
            chars[j] = temp;
        }
        return new String(chars);
    }
    
    /**
     * 密码强度枚举
     */
    public enum PasswordStrength {
        INVALID("无效"),
        WEAK("弱"),
        MEDIUM("中等"),
        STRONG("强");
        
        private final String description;
        
        PasswordStrength(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
