package com.cloudvps.common.security.interceptor;

import com.cloudvps.common.core.constant.CommonConstants;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.common.security.annotation.RequireAdmin;
import com.cloudvps.common.security.annotation.RequireAuth;
import com.cloudvps.common.security.util.JwtUtils;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;

/**
 * 认证拦截器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class AuthInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(AuthInterceptor.class);

    private final JwtUtils jwtUtils;

    public AuthInterceptor(JwtUtils jwtUtils) {
        this.jwtUtils = jwtUtils;
    }
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        Class<?> clazz = handlerMethod.getBeanType();
        
        // 检查方法级别的注解
        RequireAuth methodAuth = method.getAnnotation(RequireAuth.class);
        RequireAdmin methodAdmin = method.getAnnotation(RequireAdmin.class);
        
        // 检查类级别的注解
        RequireAuth classAuth = clazz.getAnnotation(RequireAuth.class);
        RequireAdmin classAdmin = clazz.getAnnotation(RequireAdmin.class);
        
        // 确定最终的注解（方法级别优先）
        RequireAuth finalAuth = methodAuth != null ? methodAuth : classAuth;
        RequireAdmin finalAdmin = methodAdmin != null ? methodAdmin : classAdmin;
        
        // 如果没有任何认证注解，直接通过
        if (finalAuth == null && finalAdmin == null) {
            return true;
        }
        
        // 获取令牌
        String token = getTokenFromRequest(request);
        if (token == null) {
            String message = finalAuth != null ? finalAuth.message() : "请先登录";
            throw BusinessException.unauthorized(message);
        }
        
        // 验证令牌
        try {
            // 验证 JWT 令牌
            if (!jwtUtils.validateToken(token)) {
                throw BusinessException.unauthorized("令牌无效");
            }

            // 解析令牌获取用户信息
            Claims claims = jwtUtils.parseToken(token);
            Long userId = Long.valueOf(claims.getSubject());
            String username = claims.get("username", String.class);
            String role = claims.get("role", String.class);
            String status = claims.get("status", String.class);

            // 检查管理员权限
            if (finalAdmin != null) {
                if (!"ADMIN".equals(role)) {
                    throw BusinessException.forbidden(finalAdmin.message());
                }
            }

            // 将用户信息添加到请求属性中，供后续使用
            request.setAttribute("userId", userId);
            request.setAttribute("username", username);
            request.setAttribute("role", role);
            request.setAttribute("status", status);

            log.debug("用户认证成功: userId={}, username={}", userId, username);
            return true;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("认证过程中发生异常", e);
            throw BusinessException.unauthorized("认证失败");
        }
    }
    
    /**
     * 从请求中获取令牌
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(CommonConstants.Http.AUTHORIZATION_HEADER);
        if (authHeader != null && authHeader.startsWith(CommonConstants.Http.BEARER_PREFIX)) {
            return authHeader.substring(CommonConstants.Http.BEARER_PREFIX.length());
        }
        return null;
    }
}
