package com.cloudvps.common.security.annotation;

import com.cloudvps.common.security.config.SecurityConfig;
import com.cloudvps.common.security.interceptor.AuthInterceptor;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 启用CloudVPS安全模块注解
 * 
 * 使用此注解可以自动配置安全认证和权限控制
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import({SecurityConfig.class, AuthInterceptor.class})
public @interface EnableCloudVpsSecurity {
}
