package com.cloudvps.common.security.util;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;

/**
 * 安全工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SecurityUtils {
    
    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }

        Object userId = request.getAttribute("userId");
        return userId instanceof Long ? (Long) userId : null;
    }

    /**
     * 获取当前用户ID（必须存在）
     */
    public static Long getRequiredCurrentUserId() {
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new RuntimeException("用户未登录");
        }
        return userId;
    }
    
    /**
     * 获取当前用户信息
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getCurrentUserInfo() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        
        Object userInfo = request.getAttribute("userInfo");
        return userInfo instanceof Map ? (Map<String, Object>) userInfo : null;
    }
    
    /**
     * 检查当前用户是否为管理员
     */
    public static boolean isCurrentUserAdmin() {
        Map<String, Object> userInfo = getCurrentUserInfo();
        if (userInfo == null) {
            return false;
        }
        
        String status = (String) userInfo.get("status");
        return "ADMIN".equals(status);
    }
    
    /**
     * 获取当前用户名
     */
    public static String getCurrentUsername() {
        Map<String, Object> userInfo = getCurrentUserInfo();
        if (userInfo == null) {
            return null;
        }
        
        return (String) userInfo.get("username");
    }
    
    /**
     * 获取当前请求
     */
    private static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}
