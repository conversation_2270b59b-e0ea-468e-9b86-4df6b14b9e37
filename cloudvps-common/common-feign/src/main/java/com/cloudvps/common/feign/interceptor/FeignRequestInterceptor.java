package com.cloudvps.common.feign.interceptor;

import com.cloudvps.common.core.constant.CommonConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * Feign请求拦截器
 * 用于在服务间调用时传递认证信息和请求上下文
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class FeignRequestInterceptor implements RequestInterceptor {

    private static final Logger log = LoggerFactory.getLogger(FeignRequestInterceptor.class);
    
    @Override
    public void apply(RequestTemplate template) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            
            // 传递Authorization头
            String authorization = request.getHeader(CommonConstants.Http.AUTHORIZATION_HEADER);
            if (authorization != null) {
                template.header(CommonConstants.Http.AUTHORIZATION_HEADER, authorization);
                log.debug("传递Authorization头: {}", authorization.substring(0, Math.min(20, authorization.length())) + "...");
            }
            
            // 传递请求ID
            String requestId = request.getHeader(CommonConstants.Http.REQUEST_ID_HEADER);
            if (requestId == null) {
                requestId = UUID.randomUUID().toString();
            }
            template.header(CommonConstants.Http.REQUEST_ID_HEADER, requestId);
            
            // 传递用户ID
            String userId = request.getHeader(CommonConstants.Http.USER_ID_HEADER);
            if (userId != null) {
                template.header(CommonConstants.Http.USER_ID_HEADER, userId);
            }
            
            log.debug("Feign请求拦截器处理完成: requestId={}, userId={}", requestId, userId);
        } else {
            // 如果没有请求上下文，生成新的请求ID
            String requestId = UUID.randomUUID().toString();
            template.header(CommonConstants.Http.REQUEST_ID_HEADER, requestId);
            log.debug("生成新的请求ID: {}", requestId);
        }
    }
}
