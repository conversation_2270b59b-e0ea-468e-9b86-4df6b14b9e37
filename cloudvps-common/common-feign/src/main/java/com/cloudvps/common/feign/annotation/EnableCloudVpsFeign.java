package com.cloudvps.common.feign.annotation;

import com.cloudvps.common.feign.config.FeignConfig;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 启用CloudVPS Feign客户端注解
 * 
 * 使用此注解可以自动配置Feign客户端和相关配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@EnableFeignClients(basePackages = "com.cloudvps")
@Import(FeignConfig.class)
public @interface EnableCloudVpsFeign {
    
    /**
     * 扫描的基础包路径
     */
    String[] basePackages() default {"com.cloudvps"};
    
    /**
     * 扫描的基础类
     */
    Class<?>[] basePackageClasses() default {};
    
    /**
     * 指定的Feign客户端类
     */
    Class<?>[] clients() default {};
}
