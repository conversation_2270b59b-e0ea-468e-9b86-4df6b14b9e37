package com.cloudvps.common.feign.config;

import com.cloudvps.common.feign.interceptor.FeignRequestInterceptor;
import feign.Logger;
import feign.Request;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Feign配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class FeignConfig {
    
    /**
     * Feign日志级别
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }
    
    /**
     * Feign请求拦截器
     */
    @Bean
    public FeignRequestInterceptor feignRequestInterceptor() {
        return new FeignRequestInterceptor();
    }
    
    /**
     * Feign请求选项配置
     */
    @Bean
    public Request.Options feignRequestOptions() {
        return new Request.Options(
            5, TimeUnit.SECONDS,  // 连接超时时间
            10, TimeUnit.SECONDS, // 读取超时时间
            true                  // 跟随重定向
        );
    }
    
    /**
     * Feign重试配置
     */
    @Bean
    public Retryer feignRetryer() {
        // 最大重试次数3次，初始间隔100ms，最大间隔1s
        return new Retryer.Default(100, 1000, 3);
    }
}
