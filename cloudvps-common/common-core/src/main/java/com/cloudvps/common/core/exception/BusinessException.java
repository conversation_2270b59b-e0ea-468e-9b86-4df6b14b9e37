package com.cloudvps.common.core.exception;

import com.cloudvps.common.core.response.ResponseCode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务异常类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * HTTP状态码
     */
    private Integer httpStatus;
    
    public BusinessException(String message) {
        super(message);
        this.httpStatus = 400;
    }
    
    public BusinessException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
        this.httpStatus = 400;
    }
    
    public BusinessException(String message, String errorCode, Integer httpStatus) {
        super(message);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
    }
    
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.httpStatus = 400;
    }
    
    public BusinessException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.httpStatus = 400;
    }

    public BusinessException(ResponseCode responseCode, String message) {
        super(message);
        this.errorCode = String.valueOf(responseCode.getCode());
        this.httpStatus = responseCode.getCode();
    }
    
    /**
     * 创建参数错误异常
     */
    public static BusinessException badRequest(String message) {
        return new BusinessException(message, "BAD_REQUEST", 400);
    }
    
    /**
     * 创建未授权异常
     */
    public static BusinessException unauthorized(String message) {
        return new BusinessException(message, "UNAUTHORIZED", 401);
    }
    
    /**
     * 创建禁止访问异常
     */
    public static BusinessException forbidden(String message) {
        return new BusinessException(message, "FORBIDDEN", 403);
    }
    
    /**
     * 创建资源不存在异常
     */
    public static BusinessException notFound(String message) {
        return new BusinessException(message, "NOT_FOUND", 404);
    }
    
    /**
     * 创建服务器内部错误异常
     */
    public static BusinessException internalError(String message) {
        return new BusinessException(message, "INTERNAL_ERROR", 500);
    }

    /**
     * 获取错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 设置错误代码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 获取HTTP状态码
     */
    public Integer getHttpStatus() {
        return httpStatus;
    }

    /**
     * 设置HTTP状态码
     */
    public void setHttpStatus(Integer httpStatus) {
        this.httpStatus = httpStatus;
    }
}
