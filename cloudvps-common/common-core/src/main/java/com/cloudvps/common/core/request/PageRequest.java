package com.cloudvps.common.core.request;

import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class PageRequest {

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;

    /**
     * 排序字段
     */
    private String sortField = "createdAt";

    /**
     * 排序方向
     */
    private String sortOrder = "desc";

    /**
     * 创建时间开始
     */
    private String createdAtStart;

    /**
     * 创建时间结束
     */
    private String createdAtEnd;
}
