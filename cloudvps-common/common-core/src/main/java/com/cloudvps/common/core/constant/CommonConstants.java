package com.cloudvps.common.core.constant;

/**
 * 公共常量类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class CommonConstants {
    
    /**
     * 系统相关常量
     */
    public static class System {
        /** 系统名称 */
        public static final String SYSTEM_NAME = "CloudVPS";
        
        /** 系统版本 */
        public static final String SYSTEM_VERSION = "1.0.0";
        
        /** 默认字符编码 */
        public static final String DEFAULT_CHARSET = "UTF-8";
        
        /** 默认时区 */
        public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";
    }
    
    /**
     * HTTP相关常量
     */
    public static class Http {
        /** Authorization头名称 */
        public static final String AUTHORIZATION_HEADER = "Authorization";
        
        /** Bearer前缀 */
        public static final String BEARER_PREFIX = "Bearer ";
        
        /** 请求ID头名称 */
        public static final String REQUEST_ID_HEADER = "X-Request-ID";
        
        /** 用户ID头名称 */
        public static final String USER_ID_HEADER = "X-User-ID";
    }
    
    /**
     * 缓存相关常量
     */
    public static class Cache {
        /** 默认缓存时间（秒） */
        public static final int DEFAULT_TTL = 3600;
        
        /** 用户信息缓存前缀 */
        public static final String USER_CACHE_PREFIX = "user:";
        
        /** 配置缓存前缀 */
        public static final String CONFIG_CACHE_PREFIX = "config:";
        
        /** 字典缓存前缀 */
        public static final String DICT_CACHE_PREFIX = "dict:";
    }
    
    /**
     * 分页相关常量
     */
    public static class Page {
        /** 默认页码 */
        public static final int DEFAULT_PAGE = 0;
        
        /** 默认页大小 */
        public static final int DEFAULT_SIZE = 20;
        
        /** 最大页大小 */
        public static final int MAX_SIZE = 1000;
    }
    
    /**
     * 日期时间相关常量
     */
    public static class DateTime {
        /** 标准日期时间格式 */
        public static final String STANDARD_FORMAT = "yyyy-MM-dd HH:mm:ss";
        
        /** 日期格式 */
        public static final String DATE_FORMAT = "yyyy-MM-dd";
        
        /** 时间格式 */
        public static final String TIME_FORMAT = "HH:mm:ss";
        
        /** ISO日期时间格式 */
        public static final String ISO_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    }
    
    /**
     * 状态相关常量
     */
    public static class Status {
        /** 启用状态 */
        public static final String ENABLED = "ENABLED";
        
        /** 禁用状态 */
        public static final String DISABLED = "DISABLED";
        
        /** 删除状态 */
        public static final String DELETED = "DELETED";
    }
    
    /**
     * 用户相关常量
     */
    public static class User {
        /** 管理员角色 */
        public static final String ADMIN_ROLE = "ADMIN";
        
        /** 普通用户角色 */
        public static final String USER_ROLE = "USER";
        
        /** 默认密码 */
        public static final String DEFAULT_PASSWORD = "123456";
    }
    
    /**
     * 服务名称常量
     */
    public static class Service {
        /** 系统服务 */
        public static final String SYSTEM_SERVICE = "system-service";
        
        /** 虚拟化服务 */
        public static final String VIRTUALIZATION_SERVICE = "virtualization-service";
        
        /** 订单服务 */
        public static final String ORDER_SERVICE = "order-service";
        
        /** 支付服务 */
        public static final String PAYMENT_SERVICE = "payment-service";
    }
}
