package com.cloudvps.common.core.response;

/**
 * 响应状态码枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ResponseCode {
    
    // 成功响应
    SUCCESS(200, "操作成功"),
    
    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数验证失败"),
    
    // 服务器错误 5xx
    INTERNAL_ERROR(500, "系统内部错误"),
    INTERNAL_SERVER_ERROR(500, "系统内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误 1xxx
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    INVALID_PASSWORD(1003, "密码错误"),
    USER_DISABLED(1004, "用户已被禁用"),
    TOKEN_EXPIRED(1005, "令牌已过期"),
    TOKEN_INVALID(1006, "令牌无效"),
    
    // 账户相关错误 2xxx
    INSUFFICIENT_BALANCE(2001, "余额不足"),
    ACCOUNT_FROZEN(2002, "账户已冻结"),
    TRANSACTION_FAILED(2003, "交易失败"),
    
    // 虚拟机相关错误 3xxx
    VM_NOT_FOUND(3001, "虚拟机不存在"),
    VM_ALREADY_RUNNING(3002, "虚拟机已在运行"),
    VM_ALREADY_STOPPED(3003, "虚拟机已停止"),
    VM_OPERATION_FAILED(3004, "虚拟机操作失败"),
    NODE_NOT_AVAILABLE(3005, "节点不可用"),
    
    // 订单相关错误 4xxx
    ORDER_NOT_FOUND(4001, "订单不存在"),
    ORDER_ALREADY_PAID(4002, "订单已支付"),
    ORDER_EXPIRED(4003, "订单已过期"),
    
    // 支付相关错误 5xxx
    PAYMENT_FAILED(5001, "支付失败"),
    PAYMENT_TIMEOUT(5002, "支付超时"),
    REFUND_FAILED(5003, "退款失败");
    
    private final int code;
    private final String message;
    
    ResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据状态码获取枚举
     */
    public static ResponseCode valueOf(int code) {
        for (ResponseCode responseCode : values()) {
            if (responseCode.getCode() == code) {
                return responseCode;
            }
        }
        throw new IllegalArgumentException("Unknown response code: " + code);
    }
}
