package com.cloudvps.common.swagger.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class SwaggerConfig {
    
    @Value("${cloudvps.swagger.title:CloudVPS API}")
    private String title;
    
    @Value("${cloudvps.swagger.description:CloudVPS公有云平台API文档}")
    private String description;
    
    @Value("${cloudvps.swagger.version:1.0.0}")
    private String version;
    
    @Value("${cloudvps.swagger.contact.name:CloudVPS Team}")
    private String contactName;
    
    @Value("${cloudvps.swagger.contact.email:<EMAIL>}")
    private String contactEmail;
    
    @Value("${cloudvps.swagger.contact.url:https://www.cloudvps.com}")
    private String contactUrl;
    
    @Value("${cloudvps.swagger.license.name:Apache 2.0}")
    private String licenseName;
    
    @Value("${cloudvps.swagger.license.url:https://www.apache.org/licenses/LICENSE-2.0}")
    private String licenseUrl;
    
    @Value("${server.port:8080}")
    private String serverPort;
    
    @Value("${cloudvps.swagger.server.url:http://localhost}")
    private String serverUrl;
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(apiInfo())
            .servers(List.of(
                new Server()
                    .url(serverUrl + ":" + serverPort)
                    .description("开发环境")
            ))
            .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
            .components(new io.swagger.v3.oas.models.Components()
                .addSecuritySchemes("Bearer Authentication", createAPIKeyScheme()));
    }
    
    private Info apiInfo() {
        return new Info()
            .title(title)
            .description(description)
            .version(version)
            .contact(new Contact()
                .name(contactName)
                .email(contactEmail)
                .url(contactUrl))
            .license(new License()
                .name(licenseName)
                .url(licenseUrl));
    }
    
    private SecurityScheme createAPIKeyScheme() {
        return new SecurityScheme()
            .type(SecurityScheme.Type.HTTP)
            .bearerFormat("JWT")
            .scheme("bearer")
            .description("请输入JWT令牌，格式：Bearer {token}");
    }
}
