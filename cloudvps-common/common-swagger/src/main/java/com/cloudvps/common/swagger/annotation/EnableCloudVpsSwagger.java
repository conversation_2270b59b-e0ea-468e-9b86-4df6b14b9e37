package com.cloudvps.common.swagger.annotation;

import com.cloudvps.common.swagger.config.SwaggerConfig;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 启用CloudVPS Swagger文档注解
 * 
 * 使用此注解可以自动配置Swagger API文档
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(SwaggerConfig.class)
public @interface EnableCloudVpsSwagger {
    
    /**
     * API标题
     */
    String title() default "CloudVPS API";
    
    /**
     * API描述
     */
    String description() default "CloudVPS公有云平台API文档";
    
    /**
     * API版本
     */
    String version() default "1.0.0";
}
