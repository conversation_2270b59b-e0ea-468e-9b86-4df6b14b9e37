#!/bin/bash

# CloudVPS 平台快速开发环境设置脚本

echo "🚀 CloudVPS 平台开发环境设置"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Java版本
check_java() {
    echo -e "${BLUE}检查Java环境...${NC}"
    
    if ! command -v java &> /dev/null; then
        echo -e "${RED}❌ Java未安装，请先安装Java 21${NC}"
        exit 1
    fi
    
    # 设置Java 21环境
    if [ -f "set-java21.sh" ]; then
        source set-java21.sh
        echo -e "${GREEN}✅ Java 21环境已设置${NC}"
    else
        echo -e "${YELLOW}⚠️  set-java21.sh文件不存在，请手动设置JAVA_HOME${NC}"
    fi
}

# 检查Maven
check_maven() {
    echo -e "${BLUE}检查Maven环境...${NC}"
    
    if ! command -v mvn &> /dev/null; then
        echo -e "${RED}❌ Maven未安装，请先安装Maven${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Maven环境正常${NC}"
}

# 构建项目
build_project() {
    echo -e "${BLUE}构建项目...${NC}"
    
    # 确保使用Java 21
    if [ -f "set-java21.sh" ]; then
        source set-java21.sh
    fi
    
    # 清理并构建
    mvn clean install -DskipTests
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 项目构建成功${NC}"
    else
        echo -e "${RED}❌ 项目构建失败${NC}"
        exit 1
    fi
}

# 检查PostgreSQL
check_postgresql() {
    echo -e "${BLUE}检查PostgreSQL...${NC}"
    
    if ! command -v psql &> /dev/null; then
        echo -e "${YELLOW}⚠️  PostgreSQL客户端未安装${NC}"
        echo -e "${YELLOW}   请安装PostgreSQL或确保数据库服务可用${NC}"
    else
        echo -e "${GREEN}✅ PostgreSQL客户端已安装${NC}"
    fi
}

# 创建数据库（可选）
setup_databases() {
    echo -e "${BLUE}数据库设置...${NC}"
    echo -e "${YELLOW}数据库初始化脚本已创建在各模块的src/main/resources/db/目录下${NC}"
    echo -e "${YELLOW}请根据需要手动执行以下SQL脚本：${NC}"
    echo "  - cloudvps-service-system/src/main/resources/db/init-system.sql"
    echo "  - cloudvps-service-virtualization/src/main/resources/db/init-virtualization.sql"
    echo "  - cloudvps-service-order/src/main/resources/db/init-order.sql"
    echo "  - cloudvps-service-payment/src/main/resources/db/init-payment.sql"
}

# 显示启动命令
show_startup_commands() {
    echo -e "${BLUE}服务启动命令：${NC}"
    echo ""
    echo -e "${GREEN}1. 系统服务：${NC}"
    echo "   cd cloudvps-service-system"
    echo "   source ../set-java21.sh && mvn spring-boot:run"
    echo ""
    echo -e "${GREEN}2. 虚拟化服务：${NC}"
    echo "   cd cloudvps-service-virtualization"
    echo "   source ../set-java21.sh && mvn spring-boot:run"
    echo ""
    echo -e "${GREEN}3. 订单服务：${NC}"
    echo "   cd cloudvps-service-order"
    echo "   source ../set-java21.sh && mvn spring-boot:run"
    echo ""
    echo -e "${GREEN}4. 支付服务：${NC}"
    echo "   cd cloudvps-service-payment"
    echo "   source ../set-java21.sh && mvn spring-boot:run"
    echo ""
    echo -e "${GREEN}5. 网关服务：${NC}"
    echo "   cd cloudvps-gateway"
    echo "   source ../set-java21.sh && mvn spring-boot:run"
}

# 显示开发提示
show_dev_tips() {
    echo -e "${BLUE}开发提示：${NC}"
    echo ""
    echo -e "${GREEN}📁 项目结构：${NC}"
    echo "  ├── cloudvps-parent/          # 父POM配置"
    echo "  ├── cloudvps-common/          # 公共模块（JWT、工具类等）"
    echo "  ├── cloudvps-gateway/         # API网关"
    echo "  ├── cloudvps-service-system/  # 系统服务（用户、角色、权限）"
    echo "  ├── cloudvps-service-virtualization/ # 虚拟化服务（VM管理）"
    echo "  ├── cloudvps-service-order/   # 订单服务"
    echo "  └── cloudvps-service-payment/ # 支付服务"
    echo ""
    echo -e "${GREEN}🔧 开发工具：${NC}"
    echo "  - API文档: http://localhost:8080/swagger-ui.html"
    echo "  - 健康检查: http://localhost:8080/actuator/health"
    echo ""
    echo -e "${GREEN}📚 重要文档：${NC}"
    echo "  - DEVELOPMENT-NEXT-STEPS.md  # 详细开发计划"
    echo "  - System-Service-Module-Development-Guide.md"
    echo "  - Virtualization-Service-Module-Development-Guide.md"
    echo ""
    echo -e "${GREEN}🐛 调试技巧：${NC}"
    echo "  - 使用 'source set-java21.sh' 设置Java环境"
    echo "  - 使用 'mvn clean install -DskipTests' 快速构建"
    echo "  - 使用 'mvn spring-boot:run -Dspring-boot.run.profiles=dev' 开发模式运行"
}

# 主函数
main() {
    echo -e "${BLUE}开始环境检查和设置...${NC}"
    echo ""
    
    # 环境检查
    check_java
    check_maven
    check_postgresql
    
    echo ""
    echo -e "${BLUE}开始构建项目...${NC}"
    build_project
    
    echo ""
    setup_databases
    
    echo ""
    show_startup_commands
    
    echo ""
    show_dev_tips
    
    echo ""
    echo -e "${GREEN}🎉 开发环境设置完成！${NC}"
    echo -e "${GREEN}现在可以开始并行开发各个模块了${NC}"
    echo ""
    echo -e "${YELLOW}下一步：${NC}"
    echo "1. 根据需要初始化数据库"
    echo "2. 选择一个模块开始开发"
    echo "3. 参考 DEVELOPMENT-NEXT-STEPS.md 了解详细开发计划"
}

# 运行主函数
main
