# CloudVPS 支付服务模块

## 模块概述

支付服务是 CloudVPS 平台的核心财务模块，负责处理多渠道支付、交易管理、退款处理、账户充值等功能。作为平台的资金流转中心，支付服务确保交易安全、支付便捷，并提供完整的财务数据管理和风控机制。

## 技术架构

### 技术栈
- **框架**: Spring Boot 3.2.1
- **Java 版本**: JDK 21
- **数据库**: PostgreSQL 16+
- **缓存**: Redis 7.2+
- **消息队列**: 预留 RabbitMQ 集成
- **ORM**: Spring Data JPA + Hibernate
- **API 文档**: Swagger/OpenAPI 3
- **构建工具**: Maven 3.9+

### 架构设计模式
```
┌─────────────────────────────────────────┐
│              Controller 层               │
│PaymentController │ RefundController     │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│               Service 层                │
│ PaymentService │ ChannelService │ ...   │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│              Channel 层                 │
│AlipayChannel │ WechatChannel │ ...      │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            Third-party APIs             │
│  支付宝API │ 微信支付API │ 银行API      │
└─────────────────────────────────────────┘
```

## 核心功能模块

### 1. 多渠道支付模块
- **支付宝支付**: 集成支付宝开放平台API
- **微信支付**: 集成微信支付商户平台API
- **银行转账**: 支持银行转账支付方式
- **余额支付**: 用户账户余额支付
- **组合支付**: 多种支付方式组合使用

### 2. 交易管理模块
- **交易创建**: 创建支付交易订单
- **交易查询**: 实时查询交易状态
- **交易对账**: 与第三方平台对账
- **交易统计**: 交易数据统计分析
- **交易监控**: 异常交易监控告警

### 3. 退款处理模块
- **退款申请**: 用户和管理员退款申请
- **退款审核**: 退款申请审核流程
- **退款执行**: 自动化退款处理
- **退款查询**: 退款状态和进度查询
- **退款对账**: 退款数据对账验证

### 4. 账户充值模块
- **在线充值**: 多渠道在线充值
- **充值优惠**: 充值优惠活动管理
- **充值记录**: 充值历史记录查询
- **充值限额**: 充值金额限制管理
- **充值通知**: 充值成功通知机制

### 5. 风控安全模块
- **风险识别**: 异常交易识别和拦截
- **限额控制**: 交易金额和频次限制
- **黑名单管理**: 风险用户和IP管理
- **安全验证**: 支付密码和短信验证
- **审计日志**: 完整的操作审计记录

## 数据库设计

### 核心数据表

#### 支付相关表
```sql
-- 支付订单表
CREATE TABLE payments (
    id BIGSERIAL PRIMARY KEY,
    payment_no VARCHAR(32) NOT NULL UNIQUE,
    order_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'CNY',
    payment_method VARCHAR(20) NOT NULL,
    channel_code VARCHAR(20) NOT NULL,
    channel_order_no VARCHAR(64),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    subject VARCHAR(200),
    description TEXT,
    notify_url VARCHAR(500),
    return_url VARCHAR(500),
    expired_time TIMESTAMP,
    paid_time TIMESTAMP,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 支付记录表
CREATE TABLE payment_records (
    id BIGSERIAL PRIMARY KEY,
    payment_id BIGINT NOT NULL REFERENCES payments(id),
    channel_code VARCHAR(20) NOT NULL,
    channel_order_no VARCHAR(64),
    channel_trade_no VARCHAR(64),
    amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) NOT NULL,
    channel_response TEXT,
    processed_time TIMESTAMP,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 退款表
CREATE TABLE refunds (
    id BIGSERIAL PRIMARY KEY,
    refund_no VARCHAR(32) NOT NULL UNIQUE,
    payment_id BIGINT NOT NULL REFERENCES payments(id),
    order_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    refund_amount DECIMAL(15,2) NOT NULL,
    refund_reason VARCHAR(500),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    channel_refund_no VARCHAR(64),
    processed_time TIMESTAMP,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 渠道配置表
```sql
-- 支付渠道表
CREATE TABLE payment_channels (
    id BIGSERIAL PRIMARY KEY,
    channel_code VARCHAR(20) NOT NULL UNIQUE,
    channel_name VARCHAR(100) NOT NULL,
    channel_type VARCHAR(20) NOT NULL,
    config TEXT, -- JSON格式的渠道配置
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    sort_order INTEGER DEFAULT 0,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 渠道费率表
CREATE TABLE channel_rates (
    id BIGSERIAL PRIMARY KEY,
    channel_code VARCHAR(20) NOT NULL,
    payment_method VARCHAR(20) NOT NULL,
    rate_type VARCHAR(20) NOT NULL DEFAULT 'PERCENTAGE',
    rate_value DECIMAL(8,4) NOT NULL,
    min_fee DECIMAL(10,2) DEFAULT 0,
    max_fee DECIMAL(10,2),
    effective_date DATE NOT NULL,
    expiry_date DATE,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 账户相关表
```sql
-- 充值记录表
CREATE TABLE recharge_records (
    id BIGSERIAL PRIMARY KEY,
    recharge_no VARCHAR(32) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    bonus_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    payment_id BIGINT REFERENCES payments(id),
    completed_time TIMESTAMP,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 交易流水表
CREATE TABLE transactions (
    id BIGSERIAL PRIMARY KEY,
    transaction_no VARCHAR(32) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    type VARCHAR(20) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    related_id BIGINT,
    related_type VARCHAR(20),
    description VARCHAR(500),
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 数据库索引优化
```sql
-- 支付表索引
CREATE INDEX idx_payments_payment_no ON payments(payment_no);
CREATE INDEX idx_payments_order_id ON payments(order_id);
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_created_time ON payments(created_time);
CREATE INDEX idx_payments_channel_order_no ON payments(channel_order_no);

-- 退款表索引
CREATE INDEX idx_refunds_refund_no ON refunds(refund_no);
CREATE INDEX idx_refunds_payment_id ON refunds(payment_id);
CREATE INDEX idx_refunds_user_id ON refunds(user_id);
CREATE INDEX idx_refunds_status ON refunds(status);

-- 交易流水表索引
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_type ON transactions(type);
CREATE INDEX idx_transactions_created_time ON transactions(created_time);
CREATE INDEX idx_transactions_related ON transactions(related_id, related_type);
```

## 支付渠道设计

### 支付渠道抽象
```java
public interface PaymentChannel {

    /**
     * 创建支付订单
     */
    PaymentResult createPayment(PaymentRequest request);

    /**
     * 查询支付状态
     */
    PaymentStatusResult queryPaymentStatus(String channelOrderNo);

    /**
     * 处理支付回调
     */
    PaymentNotifyResult handlePaymentNotify(String notifyData);

    /**
     * 创建退款
     */
    RefundResult createRefund(RefundRequest request);

    /**
     * 查询退款状态
     */
    RefundStatusResult queryRefundStatus(String channelRefundNo);
}
```

### 支付宝渠道实现
```java
@Component("alipayChannel")
public class AlipayChannel implements PaymentChannel {

    private final AlipayClient alipayClient;
    private final AlipayConfig alipayConfig;

    @Override
    public PaymentResult createPayment(PaymentRequest request) {
        try {
            AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
            alipayRequest.setReturnUrl(request.getReturnUrl());
            alipayRequest.setNotifyUrl(request.getNotifyUrl());

            AlipayTradePagePayModel model = new AlipayTradePagePayModel();
            model.setOutTradeNo(request.getOrderNo());
            model.setTotalAmount(request.getAmount().toString());
            model.setSubject(request.getSubject());
            model.setProductCode("FAST_INSTANT_TRADE_PAY");

            alipayRequest.setBizModel(model);

            AlipayTradePagePayResponse response = alipayClient.pageExecute(alipayRequest);

            return PaymentResult.builder()
                    .success(response.isSuccess())
                    .paymentUrl(response.getBody())
                    .channelOrderNo(request.getOrderNo())
                    .build();

        } catch (Exception e) {
            log.error("支付宝支付创建失败", e);
            return PaymentResult.failure("支付宝支付创建失败: " + e.getMessage());
        }
    }

    @Override
    public PaymentNotifyResult handlePaymentNotify(String notifyData) {
        try {
            Map<String, String> params = parseNotifyParams(notifyData);

            // 验证签名
            boolean signVerified = AlipaySignature.rsaCheckV1(
                    params, alipayConfig.getAlipayPublicKey(),
                    alipayConfig.getCharset(), alipayConfig.getSignType());

            if (!signVerified) {
                return PaymentNotifyResult.failure("签名验证失败");
            }

            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            String totalAmount = params.get("total_amount");

            PaymentNotifyResult.Builder builder = PaymentNotifyResult.builder()
                    .channelOrderNo(outTradeNo)
                    .channelTradeNo(tradeNo)
                    .amount(new BigDecimal(totalAmount));

            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                return builder.success(true).status(PaymentStatus.SUCCESS).build();
            } else {
                return builder.success(false).status(PaymentStatus.FAILED).build();
            }

        } catch (Exception e) {
            log.error("支付宝回调处理失败", e);
            return PaymentNotifyResult.failure("回调处理失败: " + e.getMessage());
        }
    }
}
```

### 微信支付渠道实现
```java
@Component("wechatChannel")
public class WechatPayChannel implements PaymentChannel {

    private final WechatPayConfig wechatConfig;

    @Override
    public PaymentResult createPayment(PaymentRequest request) {
        try {
            WechatPayUnifiedOrderRequest wechatRequest = WechatPayUnifiedOrderRequest.builder()
                    .appid(wechatConfig.getAppId())
                    .mchId(wechatConfig.getMchId())
                    .nonceStr(generateNonceStr())
                    .body(request.getSubject())
                    .outTradeNo(request.getOrderNo())
                    .totalFee(request.getAmount().multiply(new BigDecimal(100)).intValue())
                    .spbillCreateIp(request.getClientIp())
                    .notifyUrl(request.getNotifyUrl())
                    .tradeType("NATIVE")
                    .build();

            // 生成签名
            String sign = generateWechatSign(wechatRequest);
            wechatRequest.setSign(sign);

            // 调用微信统一下单接口
            WechatPayUnifiedOrderResponse response = callWechatUnifiedOrder(wechatRequest);

            if ("SUCCESS".equals(response.getReturnCode()) && "SUCCESS".equals(response.getResultCode())) {
                return PaymentResult.builder()
                        .success(true)
                        .paymentUrl(response.getCodeUrl())
                        .channelOrderNo(request.getOrderNo())
                        .build();
            } else {
                return PaymentResult.failure("微信支付创建失败: " + response.getReturnMsg());
            }

        } catch (Exception e) {
            log.error("微信支付创建失败", e);
            return PaymentResult.failure("微信支付创建失败: " + e.getMessage());
        }
    }
}
```

## API 接口设计

### 支付管理接口
```java
@RestController
@RequestMapping("/payment/payments")
@PreAuthorize("hasAuthority('PAYMENT_VIEW')")
public class PaymentController {

    /**
     * 创建支付订单
     */
    @PostMapping
    public ApiResponse<PaymentResponse> createPayment(@RequestBody CreatePaymentRequest request) {
        PaymentResponse payment = paymentService.createPayment(request);
        return ApiResponse.success(payment);
    }

    /**
     * 查询支付状态
     */
    @GetMapping("/{paymentId}/status")
    public ApiResponse<PaymentStatusResponse> getPaymentStatus(@PathVariable Long paymentId) {
        PaymentStatusResponse status = paymentService.getPaymentStatus(paymentId);
        return ApiResponse.success(status);
    }

    /**
     * 支付回调处理
     */
    @PostMapping("/notify/{channelCode}")
    public String handlePaymentNotify(@PathVariable String channelCode,
                                    HttpServletRequest request) {
        String notifyData = getNotifyData(request);
        boolean success = paymentService.handlePaymentNotify(channelCode, notifyData);
        return success ? "SUCCESS" : "FAIL";
    }

    /**
     * 获取支付列表
     */
    @GetMapping
    public ApiResponse<PageResponse<PaymentResponse>> getPayments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long userId) {

        PageResponse<PaymentResponse> payments = paymentService
                .getPayments(page, size, status, userId);
        return ApiResponse.success(payments);
    }
}
```

### 退款管理接口
```java
@RestController
@RequestMapping("/payment/refunds")
@PreAuthorize("hasAuthority('REFUND_VIEW')")
public class RefundController {

    /**
     * 创建退款申请
     */
    @PostMapping
    @PreAuthorize("hasAuthority('REFUND_CREATE')")
    public ApiResponse<RefundResponse> createRefund(@RequestBody CreateRefundRequest request) {
        RefundResponse refund = refundService.createRefund(request);
        return ApiResponse.success(refund);
    }

    /**
     * 处理退款申请
     */
    @PostMapping("/{refundId}/process")
    @PreAuthorize("hasAuthority('REFUND_PROCESS')")
    public ApiResponse<Void> processRefund(@PathVariable Long refundId,
                                         @RequestBody ProcessRefundRequest request) {
        refundService.processRefund(refundId, request);
        return ApiResponse.success();
    }

    /**
     * 查询退款状态
     */
    @GetMapping("/{refundId}/status")
    public ApiResponse<RefundStatusResponse> getRefundStatus(@PathVariable Long refundId) {
        RefundStatusResponse status = refundService.getRefundStatus(refundId);
        return ApiResponse.success(status);
    }
}
```

### 账户充值接口
```java
@RestController
@RequestMapping("/payment/recharge")
public class RechargeController {

    /**
     * 创建充值订单
     */
    @PostMapping
    public ApiResponse<RechargeResponse> createRecharge(@RequestBody CreateRechargeRequest request) {
        RechargeResponse recharge = rechargeService.createRecharge(request);
        return ApiResponse.success(recharge);
    }

    /**
     * 获取充值记录
     */
    @GetMapping
    public ApiResponse<PageResponse<RechargeResponse>> getRechargeRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long userId) {

        PageResponse<RechargeResponse> records = rechargeService
                .getRechargeRecords(page, size, userId);
        return ApiResponse.success(records);
    }
}
```
```