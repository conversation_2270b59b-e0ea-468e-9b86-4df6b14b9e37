-- CloudVPS 支付服务数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS cloudvps_payment;
USE cloudvps_payment;

-- 支付渠道表
CREATE TABLE IF NOT EXISTS payment_channels (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    
    -- 渠道类型
    channel_type VARCHAR(50) NOT NULL CHECK (channel_type IN ('ALIPAY', 'WECHAT', 'BANK_CARD', 'PAYPAL', 'STRIPE', 'CRYPTO')),
    
    -- 配置信息
    config JSONB NOT NULL, -- 支付渠道配置（API密钥等）
    
    -- 费率信息
    fee_rate DECIMAL(5,4) DEFAULT 0, -- 手续费率
    fixed_fee DECIMAL(10,2) DEFAULT 0, -- 固定手续费
    
    -- 限额信息
    min_amount DECIMAL(10,2) DEFAULT 0.01, -- 最小支付金额
    max_amount DECIMAL(10,2) DEFAULT 999999.99, -- 最大支付金额
    daily_limit DECIMAL(10,2), -- 日限额
    
    -- 状态信息
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'MAINTENANCE')),
    sort_order INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 支付订单表
CREATE TABLE IF NOT EXISTS payment_orders (
    id BIGSERIAL PRIMARY KEY,
    payment_no VARCHAR(32) UNIQUE NOT NULL, -- 支付单号
    order_id BIGINT NOT NULL, -- 业务订单ID
    user_id BIGINT NOT NULL, -- 用户ID
    
    -- 支付信息
    channel_id BIGINT NOT NULL REFERENCES payment_channels(id),
    channel_code VARCHAR(50) NOT NULL, -- 支付渠道代码
    
    -- 金额信息
    amount DECIMAL(10,2) NOT NULL, -- 支付金额
    fee_amount DECIMAL(10,2) DEFAULT 0, -- 手续费
    actual_amount DECIMAL(10,2) NOT NULL, -- 实际支付金额
    
    -- 货币信息
    currency VARCHAR(10) DEFAULT 'CNY', -- 货币类型
    exchange_rate DECIMAL(10,6) DEFAULT 1, -- 汇率
    
    -- 状态信息
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED', 'REFUNDED')),
    
    -- 第三方信息
    third_party_order_no VARCHAR(100), -- 第三方订单号
    third_party_trade_no VARCHAR(100), -- 第三方交易号
    
    -- 支付方式详情
    payment_method VARCHAR(50), -- 具体支付方式（如：支付宝扫码、微信H5等）
    payment_url TEXT, -- 支付链接
    qr_code_url TEXT, -- 二维码链接
    
    -- 时间信息
    expired_at TIMESTAMP, -- 支付过期时间
    paid_at TIMESTAMP, -- 支付完成时间
    notified_at TIMESTAMP, -- 通知时间
    
    -- 回调信息
    callback_data JSONB, -- 回调数据
    callback_count INTEGER DEFAULT 0, -- 回调次数
    
    -- 备注信息
    subject VARCHAR(200), -- 支付主题
    description TEXT, -- 支付描述
    notes TEXT, -- 备注
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 支付记录表（支付流水）
CREATE TABLE IF NOT EXISTS payment_records (
    id BIGSERIAL PRIMARY KEY,
    payment_order_id BIGINT NOT NULL REFERENCES payment_orders(id),
    
    -- 记录类型
    record_type VARCHAR(20) NOT NULL CHECK (record_type IN ('PAYMENT', 'REFUND', 'CHARGEBACK')),
    
    -- 金额信息
    amount DECIMAL(10,2) NOT NULL,
    fee_amount DECIMAL(10,2) DEFAULT 0,
    
    -- 第三方信息
    third_party_trade_no VARCHAR(100),
    third_party_record_no VARCHAR(100),
    
    -- 状态信息
    status VARCHAR(20) NOT NULL CHECK (status IN ('SUCCESS', 'FAILED', 'PROCESSING')),
    
    -- 详细信息
    details JSONB, -- 详细信息
    error_code VARCHAR(50), -- 错误代码
    error_message TEXT, -- 错误信息
    
    -- 时间信息
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 退款订单表
CREATE TABLE IF NOT EXISTS refund_orders (
    id BIGSERIAL PRIMARY KEY,
    refund_no VARCHAR(32) UNIQUE NOT NULL, -- 退款单号
    payment_order_id BIGINT NOT NULL REFERENCES payment_orders(id),
    user_id BIGINT NOT NULL,
    
    -- 退款信息
    refund_amount DECIMAL(10,2) NOT NULL, -- 退款金额
    refund_fee DECIMAL(10,2) DEFAULT 0, -- 退款手续费
    actual_refund_amount DECIMAL(10,2) NOT NULL, -- 实际退款金额
    
    -- 退款原因
    reason VARCHAR(200), -- 退款原因
    description TEXT, -- 详细说明
    
    -- 状态信息
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED')),
    
    -- 第三方信息
    third_party_refund_no VARCHAR(100), -- 第三方退款单号
    
    -- 审核信息
    approved_by BIGINT, -- 审核人
    approved_at TIMESTAMP, -- 审核时间
    approval_notes TEXT, -- 审核备注
    
    -- 时间信息
    processed_at TIMESTAMP, -- 处理时间
    completed_at TIMESTAMP, -- 完成时间
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 支付通知记录表
CREATE TABLE IF NOT EXISTS payment_notifications (
    id BIGSERIAL PRIMARY KEY,
    payment_order_id BIGINT NOT NULL REFERENCES payment_orders(id),
    
    -- 通知类型
    notification_type VARCHAR(20) NOT NULL CHECK (notification_type IN ('PAYMENT', 'REFUND', 'CHARGEBACK')),
    
    -- 通知内容
    headers JSONB, -- 请求头
    body TEXT, -- 请求体
    query_params JSONB, -- 查询参数
    
    -- 处理信息
    processed BOOLEAN DEFAULT FALSE, -- 是否已处理
    process_result VARCHAR(20), -- 处理结果
    process_message TEXT, -- 处理消息
    process_count INTEGER DEFAULT 0, -- 处理次数
    
    -- 验签信息
    signature VARCHAR(500), -- 签名
    signature_verified BOOLEAN DEFAULT FALSE, -- 签名是否验证通过
    
    -- 时间信息
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- 账户余额表
CREATE TABLE IF NOT EXISTS account_balances (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE NOT NULL, -- 用户ID
    
    -- 余额信息
    available_balance DECIMAL(12,2) DEFAULT 0, -- 可用余额
    frozen_balance DECIMAL(12,2) DEFAULT 0, -- 冻结余额
    total_balance DECIMAL(12,2) DEFAULT 0, -- 总余额
    
    -- 统计信息
    total_recharge DECIMAL(12,2) DEFAULT 0, -- 累计充值
    total_consumption DECIMAL(12,2) DEFAULT 0, -- 累计消费
    
    -- 时间信息
    last_transaction_at TIMESTAMP, -- 最后交易时间
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 余额变动记录表
CREATE TABLE IF NOT EXISTS balance_transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    
    -- 交易信息
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('RECHARGE', 'CONSUMPTION', 'REFUND', 'FREEZE', 'UNFREEZE', 'TRANSFER')),
    amount DECIMAL(12,2) NOT NULL, -- 变动金额（正数为增加，负数为减少）
    
    -- 余额信息
    balance_before DECIMAL(12,2) NOT NULL, -- 变动前余额
    balance_after DECIMAL(12,2) NOT NULL, -- 变动后余额
    
    -- 关联信息
    related_order_id BIGINT, -- 关联订单ID
    related_payment_id BIGINT, -- 关联支付ID
    
    -- 描述信息
    description VARCHAR(200), -- 交易描述
    notes TEXT, -- 备注
    
    -- 操作信息
    operator_id BIGINT, -- 操作人ID
    operator_type VARCHAR(20) DEFAULT 'USER' CHECK (operator_type IN ('USER', 'ADMIN', 'SYSTEM')),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_payment_channels_type ON payment_channels(channel_type);
CREATE INDEX IF NOT EXISTS idx_payment_channels_status ON payment_channels(status);

CREATE INDEX IF NOT EXISTS idx_payment_orders_order_id ON payment_orders(order_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_user_id ON payment_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_channel_id ON payment_orders(channel_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_status ON payment_orders(status);
CREATE INDEX IF NOT EXISTS idx_payment_orders_created_at ON payment_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_orders_third_party ON payment_orders(third_party_order_no);

CREATE INDEX IF NOT EXISTS idx_payment_records_payment_order_id ON payment_records(payment_order_id);
CREATE INDEX IF NOT EXISTS idx_payment_records_type ON payment_records(record_type);
CREATE INDEX IF NOT EXISTS idx_payment_records_status ON payment_records(status);

CREATE INDEX IF NOT EXISTS idx_refund_orders_payment_order_id ON refund_orders(payment_order_id);
CREATE INDEX IF NOT EXISTS idx_refund_orders_user_id ON refund_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_refund_orders_status ON refund_orders(status);

CREATE INDEX IF NOT EXISTS idx_payment_notifications_payment_order_id ON payment_notifications(payment_order_id);
CREATE INDEX IF NOT EXISTS idx_payment_notifications_processed ON payment_notifications(processed);
CREATE INDEX IF NOT EXISTS idx_payment_notifications_received_at ON payment_notifications(received_at);

CREATE INDEX IF NOT EXISTS idx_account_balances_user_id ON account_balances(user_id);

CREATE INDEX IF NOT EXISTS idx_balance_transactions_user_id ON balance_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_balance_transactions_type ON balance_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_balance_transactions_created_at ON balance_transactions(created_at);

-- 插入初始数据

-- 插入支付渠道
INSERT INTO payment_channels (name, code, description, channel_type, config, fee_rate, min_amount, max_amount) VALUES 
('支付宝', 'ALIPAY', '支付宝在线支付', 'ALIPAY', '{"app_id": "", "private_key": "", "public_key": ""}', 0.006, 0.01, 50000.00),
('微信支付', 'WECHAT', '微信在线支付', 'WECHAT', '{"app_id": "", "mch_id": "", "api_key": ""}', 0.006, 0.01, 50000.00),
('银行卡支付', 'BANK_CARD', '银行卡在线支付', 'BANK_CARD', '{"merchant_id": "", "api_key": ""}', 0.008, 1.00, 100000.00),
('PayPal', 'PAYPAL', 'PayPal国际支付', 'PAYPAL', '{"client_id": "", "client_secret": ""}', 0.035, 1.00, 10000.00),
('余额支付', 'BALANCE', '账户余额支付', 'BALANCE', '{}', 0.000, 0.01, 999999.99)
ON CONFLICT (code) DO NOTHING;
