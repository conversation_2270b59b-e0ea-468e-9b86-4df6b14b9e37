-- =============================================
-- =============================================
-- CloudVPS 支付服务数据库初始化脚本
-- 数据库: cloudvps_payment
-- 版本: 1.0.0
-- 创建时间: 2024-01-01
-- =============================================

-- 创建数据库 (如果不存在)
-- CREATE DATABASE cloudvps_payment;
-- \c cloudvps_payment;

-- =============================================
-- 1. 支付渠道表
-- =============================================

-- 支付渠道表
CREATE TABLE IF NOT EXISTS payment_channels (
    id BIGSERIAL PRIMARY KEY,
    channel_code VARCHAR(50) UNIQUE NOT NULL,
    channel_name VARCHAR(100) NOT NULL,
    payment_method VARCHAR(20) NOT NULL CHECK (payment_method IN ('ALIPAY', 'WECHAT', 'BANK_CARD', 'BALANCE', 'PAYPAL', 'CREDIT_CARD')),
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    min_amount DECIMAL(10,2) DEFAULT 0.00,
    max_amount DECIMAL(10,2),
    fee_rate DECIMAL(5,4) DEFAULT 0.0000,
    fixed_fee DECIMAL(10,2) DEFAULT 0.00,
    sort_order INTEGER DEFAULT 0,
    config TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    is_deleted BOOLEAN DEFAULT FALSE,
    version BIGINT DEFAULT 0
);

-- =============================================
-- 2. 支付记录表
-- =============================================

-- 支付主表
CREATE TABLE IF NOT EXISTS payments (
    id BIGSERIAL PRIMARY KEY,
    payment_no VARCHAR(32) UNIQUE NOT NULL,
    order_no VARCHAR(50) NOT NULL,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    actual_amount DECIMAL(10,2) DEFAULT 0.00,
    payment_method VARCHAR(20) NOT NULL CHECK (payment_method IN ('ALIPAY', 'WECHAT', 'BANK_CARD', 'BALANCE', 'PAYPAL', 'CREDIT_CARD')),
    payment_type VARCHAR(20) NOT NULL CHECK (payment_type IN ('ORDER', 'RECHARGE', 'REFUND', 'WITHDRAWAL')),
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED', 'REFUNDED', 'PARTIAL_REFUNDED')),
    description VARCHAR(200),
    third_party_payment_no VARCHAR(100),
    channel_id BIGINT,
    payment_url VARCHAR(500),
    qr_code_url VARCHAR(500),
    callback_url VARCHAR(500),
    return_url VARCHAR(500),
    paid_at TIMESTAMP,
    expired_at TIMESTAMP,
    failure_reason VARCHAR(500),
    callback_data TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    is_deleted BOOLEAN DEFAULT FALSE,
    version BIGINT DEFAULT 0,
    FOREIGN KEY (channel_id) REFERENCES payment_channels(id)
);

-- =============================================
-- 3. 支付记录表（退款等操作）
-- =============================================

-- 支付操作记录表
CREATE TABLE IF NOT EXISTS payment_records (
    id BIGSERIAL PRIMARY KEY,
    record_no VARCHAR(32) UNIQUE NOT NULL,
    payment_id BIGINT NOT NULL,
    record_type VARCHAR(20) NOT NULL CHECK (record_type IN ('REFUND', 'CHARGEBACK', 'ADJUSTMENT')),
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED')),
    reason VARCHAR(500),
    third_party_record_no VARCHAR(100),
    processed_at TIMESTAMP,
    failure_reason VARCHAR(500),
    callback_data TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    is_deleted BOOLEAN DEFAULT FALSE,
    version BIGINT DEFAULT 0,
    FOREIGN KEY (payment_id) REFERENCES payments(id)
);

-- =============================================
-- CloudVPS 支付服务数据库初始化脚本
-- 数据库: cloudvps_payment
-- 版本: 1.0.0
-- 创建时间: 2024-01-01
-- =============================================

-- 创建数据库 (如果不存在)
-- CREATE DATABASE IF NOT EXISTS cloudvps_payment;
-- USE cloudvps_payment;

-- =============================================
-- 1. 支付渠道相关表
-- =============================================

-- 支付渠道表
CREATE TABLE IF NOT EXISTS payment_channels (
    id BIGSERIAL PRIMARY KEY,
    channel_code VARCHAR(50) UNIQUE NOT NULL,
    channel_name VARCHAR(100) NOT NULL,
    channel_type VARCHAR(20) NOT NULL CHECK (channel_type IN ('ALIPAY', 'WECHAT', 'UNIONPAY', 'BANK', 'BALANCE', 'CRYPTO')),
    description TEXT,
    config JSONB,
    fee_rate DECIMAL(5,4) DEFAULT 0,
    min_amount DECIMAL(10,2) DEFAULT 0.01,
    max_amount DECIMAL(10,2) DEFAULT 999999.99,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'MAINTENANCE')),
    sort_order INTEGER DEFAULT 0,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 2. 支付订单相关表
-- =============================================

-- 支付订单表
CREATE TABLE IF NOT EXISTS payment_orders (
    id BIGSERIAL PRIMARY KEY,
    payment_no VARCHAR(32) UNIQUE NOT NULL,
    business_order_no VARCHAR(32) NOT NULL,
    user_id BIGINT NOT NULL,
    channel_id BIGINT NOT NULL,
    channel_code VARCHAR(50) NOT NULL,
    payment_method VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    fee_amount DECIMAL(10,2) DEFAULT 0,
    actual_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED', 'REFUNDED', 'PARTIAL_REFUND')),
    third_party_order_no VARCHAR(100),
    third_party_transaction_no VARCHAR(100),
    subject VARCHAR(255),
    body TEXT,
    client_ip VARCHAR(45),
    return_url VARCHAR(500),
    notify_url VARCHAR(500),
    extra_data JSONB,
    paid_time TIMESTAMP,
    expire_time TIMESTAMP,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (channel_id) REFERENCES payment_channels(id)
);

-- 支付订单状态变更记录表
CREATE TABLE IF NOT EXISTS payment_status_logs (
    id BIGSERIAL PRIMARY KEY,
    payment_id BIGINT NOT NULL,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    change_reason VARCHAR(255),
    third_party_response TEXT,
    operator_id BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payment_id) REFERENCES payment_orders(id) ON DELETE CASCADE
);

-- =============================================
-- 3. 支付回调相关表
-- =============================================

-- 支付回调记录表
CREATE TABLE IF NOT EXISTS payment_callbacks (
    id BIGSERIAL PRIMARY KEY,
    payment_no VARCHAR(32) NOT NULL,
    channel_code VARCHAR(50) NOT NULL,
    callback_type VARCHAR(20) NOT NULL CHECK (callback_type IN ('NOTIFY', 'RETURN', 'QUERY')),
    request_headers TEXT,
    request_body TEXT,
    response_body TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    is_processed BOOLEAN DEFAULT FALSE,
    process_result TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 4. 退款相关表
-- =============================================

-- 退款订单表
CREATE TABLE IF NOT EXISTS refund_orders (
    id BIGSERIAL PRIMARY KEY,
    refund_no VARCHAR(32) UNIQUE NOT NULL,
    payment_id BIGINT NOT NULL,
    payment_no VARCHAR(32) NOT NULL,
    business_order_no VARCHAR(32) NOT NULL,
    user_id BIGINT NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    refund_reason VARCHAR(255),
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED')),
    third_party_refund_no VARCHAR(100),
    operator_id BIGINT,
    approved_by BIGINT,
    approved_time TIMESTAMP,
    processed_time TIMESTAMP,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payment_id) REFERENCES payment_orders(id)
);

-- =============================================
-- 5. 账户余额相关表
-- =============================================

-- 用户余额账户表
CREATE TABLE IF NOT EXISTS user_balance_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE NOT NULL,
    balance DECIMAL(10,2) DEFAULT 0.00,
    frozen_amount DECIMAL(10,2) DEFAULT 0.00,
    total_recharge DECIMAL(10,2) DEFAULT 0.00,
    total_consume DECIMAL(10,2) DEFAULT 0.00,
    total_withdraw DECIMAL(10,2) DEFAULT 0.00,
    version INTEGER DEFAULT 0,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 余额变动记录表
CREATE TABLE IF NOT EXISTS balance_transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    transaction_no VARCHAR(32) UNIQUE NOT NULL,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('RECHARGE', 'CONSUME', 'REFUND', 'WITHDRAW', 'FREEZE', 'UNFREEZE', 'TRANSFER')),
    amount DECIMAL(10,2) NOT NULL,
    balance_before DECIMAL(10,2) NOT NULL,
    balance_after DECIMAL(10,2) NOT NULL,
    business_type VARCHAR(20),
    business_no VARCHAR(32),
    description VARCHAR(255),
    operator_id BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user_balance_accounts(user_id)
);

-- =============================================
-- 6. 充值相关表
-- =============================================

-- 充值订单表
CREATE TABLE IF NOT EXISTS recharge_orders (
    id BIGSERIAL PRIMARY KEY,
    recharge_no VARCHAR(32) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    bonus_amount DECIMAL(10,2) DEFAULT 0.00,
    actual_amount DECIMAL(10,2) NOT NULL,
    payment_id BIGINT,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PAID', 'COMPLETED', 'FAILED', 'CANCELLED')),
    completed_time TIMESTAMP,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payment_id) REFERENCES payment_orders(id)
);

-- 充值优惠规则表
CREATE TABLE IF NOT EXISTS recharge_bonus_rules (
    id BIGSERIAL PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL,
    min_amount DECIMAL(10,2) NOT NULL,
    bonus_type VARCHAR(20) NOT NULL CHECK (bonus_type IN ('FIXED', 'PERCENT')),
    bonus_value DECIMAL(10,2) NOT NULL,
    max_bonus DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE')),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 7. 创建索引
-- =============================================

-- 支付渠道索引
CREATE INDEX IF NOT EXISTS idx_payment_channels_code ON payment_channels(channel_code);
CREATE INDEX IF NOT EXISTS idx_payment_channels_type ON payment_channels(channel_type);
CREATE INDEX IF NOT EXISTS idx_payment_channels_status ON payment_channels(status);

-- 支付订单索引
CREATE INDEX IF NOT EXISTS idx_payment_orders_payment_no ON payment_orders(payment_no);
CREATE INDEX IF NOT EXISTS idx_payment_orders_business_order_no ON payment_orders(business_order_no);
CREATE INDEX IF NOT EXISTS idx_payment_orders_user_id ON payment_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_channel_id ON payment_orders(channel_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_status ON payment_orders(status);
CREATE INDEX IF NOT EXISTS idx_payment_orders_created_time ON payment_orders(created_time);
CREATE INDEX IF NOT EXISTS idx_payment_orders_third_party_order_no ON payment_orders(third_party_order_no);

-- 支付状态日志索引
CREATE INDEX IF NOT EXISTS idx_payment_status_logs_payment_id ON payment_status_logs(payment_id);
CREATE INDEX IF NOT EXISTS idx_payment_status_logs_created_time ON payment_status_logs(created_time);

-- 支付回调索引
CREATE INDEX IF NOT EXISTS idx_payment_callbacks_payment_no ON payment_callbacks(payment_no);
CREATE INDEX IF NOT EXISTS idx_payment_callbacks_channel_code ON payment_callbacks(channel_code);
CREATE INDEX IF NOT EXISTS idx_payment_callbacks_created_time ON payment_callbacks(created_time);

-- 退款订单索引
CREATE INDEX IF NOT EXISTS idx_refund_orders_refund_no ON refund_orders(refund_no);
CREATE INDEX IF NOT EXISTS idx_refund_orders_payment_id ON refund_orders(payment_id);
CREATE INDEX IF NOT EXISTS idx_refund_orders_user_id ON refund_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_refund_orders_status ON refund_orders(status);

-- 余额账户索引
CREATE INDEX IF NOT EXISTS idx_user_balance_accounts_user_id ON user_balance_accounts(user_id);

-- 余额交易索引
CREATE INDEX IF NOT EXISTS idx_balance_transactions_user_id ON balance_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_balance_transactions_transaction_no ON balance_transactions(transaction_no);
CREATE INDEX IF NOT EXISTS idx_balance_transactions_type ON balance_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_balance_transactions_created_time ON balance_transactions(created_time);

-- 充值订单索引
CREATE INDEX IF NOT EXISTS idx_recharge_orders_recharge_no ON recharge_orders(recharge_no);
CREATE INDEX IF NOT EXISTS idx_recharge_orders_user_id ON recharge_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_recharge_orders_status ON recharge_orders(status);

-- =============================================
-- 8. 插入初始数据
-- =============================================

-- 插入支付渠道
INSERT INTO payment_channels (channel_code, channel_name, channel_type, description, fee_rate, min_amount, max_amount, sort_order) VALUES
('alipay_pc', '支付宝PC支付', 'ALIPAY', '支付宝电脑网站支付', 0.0060, 0.01, 50000.00, 1),
('alipay_mobile', '支付宝手机支付', 'ALIPAY', '支付宝手机网站支付', 0.0060, 0.01, 50000.00, 2),
('alipay_app', '支付宝APP支付', 'ALIPAY', '支付宝APP支付', 0.0060, 0.01, 50000.00, 3),
('wechat_native', '微信扫码支付', 'WECHAT', '微信Native支付', 0.0060, 0.01, 50000.00, 4),
('wechat_jsapi', '微信公众号支付', 'WECHAT', '微信JSAPI支付', 0.0060, 0.01, 50000.00, 5),
('wechat_app', '微信APP支付', 'WECHAT', '微信APP支付', 0.0060, 0.01, 50000.00, 6),
('unionpay', '银联支付', 'UNIONPAY', '中国银联在线支付', 0.0055, 0.01, 50000.00, 7),
('balance', '余额支付', 'BALANCE', '账户余额支付', 0.0000, 0.01, 999999.99, 8)
ON CONFLICT (channel_code) DO NOTHING;

-- 插入测试用户余额账户
INSERT INTO user_balance_accounts (user_id, balance, total_recharge) VALUES
(1, 1000.00, 1000.00),
(2, 500.00, 500.00)
ON CONFLICT (user_id) DO NOTHING;

-- 插入充值优惠规则
INSERT INTO recharge_bonus_rules (rule_name, min_amount, bonus_type, bonus_value, max_bonus, start_time, end_time) VALUES
('充值满100送10', 100.00, 'FIXED', 10.00, 10.00, '2024-01-01 00:00:00', '2024-12-31 23:59:59'),
('充值满500送8%', 500.00, 'PERCENT', 8.00, 100.00, '2024-01-01 00:00:00', '2024-12-31 23:59:59'),
('充值满1000送15%', 1000.00, 'PERCENT', 15.00, 300.00, '2024-01-01 00:00:00', '2024-12-31 23:59:59')
ON CONFLICT DO NOTHING;

-- 插入测试支付订单
INSERT INTO payment_orders (payment_no, business_order_no, user_id, channel_id, channel_code, payment_method, amount, actual_amount, status, subject, paid_time) 
SELECT 
    'PAY202401010001',
    'ORD202401010001',
    1,
    pc.id,
    'alipay_pc',
    'ALIPAY',
    99.00,
    99.00,
    'SUCCESS',
    '云服务器购买',
    '2024-01-01 10:30:00'
FROM payment_channels pc WHERE pc.channel_code = 'alipay_pc'
ON CONFLICT (payment_no) DO NOTHING;

INSERT INTO payment_orders (payment_no, business_order_no, user_id, channel_id, channel_code, payment_method, amount, actual_amount, status, subject, paid_time) 
SELECT 
    'PAY202401010003',
    'ORD202401010003',
    2,
    pc.id,
    'balance',
    'BALANCE',
    299.00,
    299.00,
    'SUCCESS',
    '云服务器购买',
    '2024-01-01 14:20:00'
FROM payment_channels pc WHERE pc.channel_code = 'balance'
ON CONFLICT (payment_no) DO NOTHING;

-- 插入余额变动记录
INSERT INTO balance_transactions (user_id, transaction_no, transaction_type, amount, balance_before, balance_after, business_type, business_no, description) VALUES
(1, 'TXN202401010001', 'RECHARGE', 1000.00, 0.00, 1000.00, 'RECHARGE', 'RCH202401010001', '账户充值'),
(2, 'TXN202401010002', 'RECHARGE', 500.00, 0.00, 500.00, 'RECHARGE', 'RCH202401010002', '账户充值'),
(2, 'TXN202401010003', 'CONSUME', -299.00, 500.00, 201.00, 'ORDER', 'ORD202401010003', '购买云服务器')
ON CONFLICT (transaction_no) DO NOTHING;

-- 更新用户余额
UPDATE user_balance_accounts SET 
    balance = 201.00,
    total_consume = 299.00
WHERE user_id = 2;

-- 插入充值订单
INSERT INTO recharge_orders (recharge_no, user_id, amount, bonus_amount, actual_amount, status, completed_time) VALUES
('RCH202401010001', 1, 1000.00, 150.00, 1150.00, 'COMPLETED', '2024-01-01 09:00:00'),
('RCH202401010002', 2, 500.00, 40.00, 540.00, 'COMPLETED', '2024-01-01 11:00:00')
ON CONFLICT (recharge_no) DO NOTHING;

-- =============================================
-- 9. 创建触发器
-- =============================================

-- 创建更新时间触发器函数 (如果不存在)
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建触发器
CREATE TRIGGER update_payment_channels_updated_time BEFORE UPDATE ON payment_channels FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_payment_orders_updated_time BEFORE UPDATE ON payment_orders FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_refund_orders_updated_time BEFORE UPDATE ON refund_orders FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_user_balance_accounts_updated_time BEFORE UPDATE ON user_balance_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_recharge_orders_updated_time BEFORE UPDATE ON recharge_orders FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_recharge_bonus_rules_updated_time BEFORE UPDATE ON recharge_bonus_rules FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- =============================================
-- 10. 创建视图
-- =============================================

-- 支付订单详情视图
CREATE OR REPLACE VIEW payment_order_details AS
SELECT 
    po.payment_no,
    po.business_order_no,
    po.user_id,
    po.amount,
    po.actual_amount,
    po.status,
    po.paid_time,
    po.created_time,
    pc.channel_name,
    pc.channel_type,
    po.subject,
    po.third_party_order_no
FROM payment_orders po
LEFT JOIN payment_channels pc ON po.channel_id = pc.id;

-- 用户余额统计视图
CREATE OR REPLACE VIEW user_balance_summary AS
SELECT 
    uba.user_id,
    uba.balance,
    uba.frozen_amount,
    uba.total_recharge,
    uba.total_consume,
    uba.total_withdraw,
    (uba.balance + uba.frozen_amount) as total_balance,
    uba.updated_time as last_update_time
FROM user_balance_accounts uba;

-- 支付统计视图
CREATE OR REPLACE VIEW payment_statistics AS
SELECT 
    DATE(po.created_time) as payment_date,
    pc.channel_type,
    COUNT(*) as order_count,
    SUM(po.amount) as total_amount,
    SUM(CASE WHEN po.status = 'SUCCESS' THEN po.amount ELSE 0 END) as success_amount,
    COUNT(CASE WHEN po.status = 'SUCCESS' THEN 1 END) as success_count
FROM payment_orders po
LEFT JOIN payment_channels pc ON po.channel_id = pc.id
GROUP BY DATE(po.created_time), pc.channel_type;

-- =============================================
-- 11. 创建存储过程
-- =============================================

-- 余额变动存储过程
CREATE OR REPLACE FUNCTION update_user_balance(
    p_user_id BIGINT,
    p_transaction_type VARCHAR(20),
    p_amount DECIMAL(10,2),
    p_business_type VARCHAR(20) DEFAULT NULL,
    p_business_no VARCHAR(32) DEFAULT NULL,
    p_description VARCHAR(255) DEFAULT NULL,
    p_operator_id BIGINT DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_balance_before DECIMAL(10,2);
    v_balance_after DECIMAL(10,2);
    v_transaction_no VARCHAR(32);
BEGIN
    -- 生成交易号
    v_transaction_no := 'TXN' || TO_CHAR(NOW(), 'YYYYMMDDHH24MISS') || LPAD(NEXTVAL('balance_transactions_id_seq')::TEXT, 6, '0');
    
    -- 获取当前余额并锁定记录
    SELECT balance INTO v_balance_before 
    FROM user_balance_accounts 
    WHERE user_id = p_user_id 
    FOR UPDATE;
    
    -- 如果账户不存在，创建账户
    IF v_balance_before IS NULL THEN
        INSERT INTO user_balance_accounts (user_id, balance) VALUES (p_user_id, 0.00);
        v_balance_before := 0.00;
    END IF;
    
    -- 计算变动后余额
    v_balance_after := v_balance_before + p_amount;
    
    -- 检查余额是否足够
    IF v_balance_after < 0 THEN
        RAISE EXCEPTION '余额不足';
    END IF;
    
    -- 更新账户余额
    UPDATE user_balance_accounts 
    SET balance = v_balance_after,
        total_recharge = CASE WHEN p_transaction_type = 'RECHARGE' THEN total_recharge + p_amount ELSE total_recharge END,
        total_consume = CASE WHEN p_transaction_type = 'CONSUME' THEN total_consume + ABS(p_amount) ELSE total_consume END,
        total_withdraw = CASE WHEN p_transaction_type = 'WITHDRAW' THEN total_withdraw + ABS(p_amount) ELSE total_withdraw END,
        version = version + 1
    WHERE user_id = p_user_id;
    
    -- 插入交易记录
    INSERT INTO balance_transactions (
        user_id, transaction_no, transaction_type, amount, 
        balance_before, balance_after, business_type, business_no, 
        description, operator_id
    ) VALUES (
        p_user_id, v_transaction_no, p_transaction_type, p_amount,
        v_balance_before, v_balance_after, p_business_type, p_business_no,
        p_description, p_operator_id
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- 初始化完成
-- =============================================
SELECT 'CloudVPS 支付服务数据库初始化完成!' as message;
