package com.cloudvps.payment.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 支付服务配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableJpaAuditing
@EnableAsync
@EnableScheduling
public class PaymentConfig {
}
