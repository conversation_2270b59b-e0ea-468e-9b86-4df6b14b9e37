package com.cloudvps.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.payment.api.enums.PaymentMethod;
import com.cloudvps.payment.api.enums.PaymentStatus;
import com.cloudvps.payment.api.enums.PaymentType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("payments")
public class Payment extends BaseEntity {
    
    /**
     * 支付单号
     */
    @TableField("payment_no")
    private String paymentNo;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 支付金额
     */
    private BigDecimal amount = BigDecimal.ZERO;

    /**
     * 实际支付金额
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount = BigDecimal.ZERO;

    /**
     * 支付方式
     */
    @TableField("payment_method")
    private PaymentMethod paymentMethod;

    /**
     * 支付类型
     */
    @TableField("payment_type")
    private PaymentType paymentType = PaymentType.ORDER;

    /**
     * 支付状态
     */
    private PaymentStatus status = PaymentStatus.PENDING;
    
    /**
     * 支付描述
     */
    private String description;

    /**
     * 第三方支付单号
     */
    @TableField("third_party_payment_no")
    private String thirdPartyPaymentNo;

    /**
     * 支付渠道ID
     */
    @TableField("channel_id")
    private Long channelId;
    
    /**
     * 支付URL
     */
    @TableField("payment_url")
    private String paymentUrl;

    /**
     * 二维码URL
     */
    @TableField("qr_code_url")
    private String qrCodeUrl;

    /**
     * 回调地址
     */
    @TableField("callback_url")
    private String callbackUrl;

    /**
     * 返回地址
     */
    @TableField("return_url")
    private String returnUrl;
    
    /**
     * 支付时间
     */
    @TableField("paid_at")
    private LocalDateTime paidAt;

    /**
     * 过期时间
     */
    @TableField("expired_at")
    private LocalDateTime expiredAt;

    /**
     * 失败原因
     */
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 支付完成回调数据
     */
    @TableField("callback_data")
    private String callbackData;
    
    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return expiredAt != null && LocalDateTime.now().isAfter(expiredAt);
    }
    
    /**
     * 检查是否可以取消
     */
    public boolean canCancel() {
        return status == PaymentStatus.PENDING || status == PaymentStatus.PROCESSING;
    }
    
    /**
     * 检查是否可以退款
     */
    public boolean canRefund() {
        return status == PaymentStatus.SUCCESS;
    }
}
