package com.cloudvps.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.payment.api.enums.PaymentMethod;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 支付渠道实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("payment_channels")
public class PaymentChannel extends BaseEntity {
    
    /**
     * 渠道代码
     */
    @TableField("channel_code")
    private String channelCode;

    /**
     * 渠道名称
     */
    @TableField("channel_name")
    private String channelName;

    /**
     * 支付方式
     */
    @TableField("payment_method")
    private PaymentMethod paymentMethod;

    /**
     * 渠道描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;

    /**
     * 最小支付金额
     */
    @TableField("min_amount")
    private BigDecimal minAmount = BigDecimal.ZERO;

    /**
     * 最大支付金额
     */
    @TableField("max_amount")
    private BigDecimal maxAmount;

    /**
     * 手续费率（百分比）
     */
    @TableField("fee_rate")
    private BigDecimal feeRate = BigDecimal.ZERO;

    /**
     * 固定手续费
     */
    @TableField("fixed_fee")
    private BigDecimal fixedFee = BigDecimal.ZERO;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder = 0;

    /**
     * 渠道配置（JSON格式）
     */
    private String config;
    
    /**
     * 计算手续费
     */
    public BigDecimal calculateFee(BigDecimal amount) {
        BigDecimal fee = amount.multiply(feeRate.divide(BigDecimal.valueOf(100)));
        return fee.add(fixedFee);
    }
    
    /**
     * 检查金额是否在允许范围内
     */
    public boolean isAmountValid(BigDecimal amount) {
        if (amount.compareTo(minAmount) < 0) {
            return false;
        }
        return maxAmount == null || amount.compareTo(maxAmount) <= 0;
    }
}
