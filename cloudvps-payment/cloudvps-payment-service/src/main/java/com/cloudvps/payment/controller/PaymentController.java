package com.cloudvps.payment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.common.security.util.SecurityUtils;
import com.cloudvps.payment.api.dto.request.PaymentCreateRequest;
import com.cloudvps.payment.api.dto.request.PaymentQueryRequest;
import com.cloudvps.payment.api.dto.request.PaymentUpdateRequest;
import com.cloudvps.payment.api.dto.response.PaymentResponse;
import com.cloudvps.payment.api.enums.PaymentStatus;
import com.cloudvps.payment.api.enums.PaymentType;
import com.cloudvps.payment.service.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 支付管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/payments")
@RequiredArgsConstructor
@Validated
@Tag(name = "支付管理", description = "支付管理相关接口")
public class PaymentController {

    private final PaymentService paymentService;

    /**
     * 分页查询支付记录
     */
    @GetMapping
    @Operation(summary = "分页查询支付记录", description = "根据条件分页查询支付记录")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ApiResponse<IPage<PaymentResponse>> getPaymentPage(@Valid PaymentQueryRequest request) {
        log.info("分页查询支付记录，请求参数：{}", request);
        IPage<PaymentResponse> result = paymentService.queryPayments(request);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID查询支付详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询支付详情", description = "根据支付ID查询支付详情")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ApiResponse<PaymentResponse> getPaymentById(
            @Parameter(description = "支付ID", required = true) @PathVariable Long id) {
        log.info("查询支付详情，支付ID：{}", id);
        PaymentResponse result = paymentService.findById(id);
        return ApiResponse.success(result);
    }

    /**
     * 根据支付单号查询支付详情
     */
    @GetMapping("/payment-no/{paymentNo}")
    @Operation(summary = "根据支付单号查询", description = "根据支付单号查询支付详情")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ApiResponse<PaymentResponse> getPaymentByPaymentNo(
            @Parameter(description = "支付单号", required = true) @PathVariable String paymentNo) {
        log.info("根据支付单号查询支付详情，支付单号：{}", paymentNo);
        PaymentResponse result = paymentService.findByPaymentNo(paymentNo);
        return ApiResponse.success(result);
    }

    /**
     * 根据订单号查询支付详情
     */
    @GetMapping("/order-no/{orderNo}")
    @Operation(summary = "根据订单号查询", description = "根据订单号查询支付详情")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ApiResponse<PaymentResponse> getPaymentByOrderNo(
            @Parameter(description = "订单号", required = true) @PathVariable String orderNo) {
        log.info("根据订单号查询支付详情，订单号：{}", orderNo);
        PaymentResponse result = paymentService.findByOrderNo(orderNo);
        return ApiResponse.success(result);
    }

    /**
     * 创建支付
     */
    @PostMapping
    @Operation(summary = "创建支付", description = "创建新的支付记录")
    @PreAuthorize("hasAuthority('PAYMENT_CREATE')")
    public ApiResponse<PaymentResponse> createPayment(@Valid @RequestBody PaymentCreateRequest request) {
        log.info("创建支付，请求参数：{}", request);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        PaymentResponse result = paymentService.createPayment(request);
        return ApiResponse.success(result);
    }

    /**
     * 更新支付
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新支付", description = "更新支付信息")
    @PreAuthorize("hasAuthority('PAYMENT_UPDATE')")
    public ApiResponse<PaymentResponse> updatePayment(
            @Parameter(description = "支付ID", required = true) @PathVariable Long id,
            @Valid @RequestBody PaymentUpdateRequest request) {
        log.info("更新支付，支付ID：{}，请求参数：{}", id, request);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        PaymentResponse result = paymentService.updatePayment(id, request);
        return ApiResponse.success(result);
    }

    /**
     * 删除支付记录
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除支付记录", description = "根据支付ID删除支付记录")
    @PreAuthorize("hasAuthority('PAYMENT_DELETE')")
    public ApiResponse<Void> deletePayment(
            @Parameter(description = "支付ID", required = true) @PathVariable Long id) {
        log.info("删除支付记录，支付ID：{}", id);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        paymentService.deletePayment(id);
        return ApiResponse.success();
    }

    /**
     * 批量删除支付记录
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除支付记录", description = "批量删除支付记录")
    @PreAuthorize("hasAuthority('PAYMENT_DELETE')")
    public ApiResponse<Void> batchDeletePayments(
            @Parameter(description = "支付ID列表", required = true) @RequestBody List<Long> paymentIds) {
        log.info("批量删除支付记录，支付ID列表：{}", paymentIds);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        paymentService.batchDeletePayments(paymentIds);
        return ApiResponse.success();
    }

    /**
     * 获取当前用户的支付记录
     */
    @GetMapping("/my")
    @Operation(summary = "获取我的支付记录", description = "获取当前用户的支付记录")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ApiResponse<IPage<PaymentResponse>> getMyPayments(@Valid PaymentQueryRequest request) {
        log.info("获取我的支付记录，请求参数：{}", request);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        // 强制设置为当前用户ID
        request.setUserId(currentUserId);
        IPage<PaymentResponse> result = paymentService.queryPayments(request);
        return ApiResponse.success(result);
    }

    /**
     * 根据状态查询支付记录
     */
    @GetMapping("/status/{status}")
    @Operation(summary = "根据状态查询支付", description = "根据支付状态查询支付记录")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ApiResponse<List<PaymentResponse>> getPaymentsByStatus(
            @Parameter(description = "支付状态", required = true) @PathVariable PaymentStatus status) {
        log.info("根据状态查询支付记录，支付状态：{}", status);
        List<PaymentResponse> result = paymentService.findByStatus(status);
        return ApiResponse.success(result);
    }

    /**
     * 根据支付类型查询支付记录
     */
    @GetMapping("/type/{paymentType}")
    @Operation(summary = "根据类型查询支付", description = "根据支付类型查询支付记录")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ApiResponse<List<PaymentResponse>> getPaymentsByType(
            @Parameter(description = "支付类型", required = true) @PathVariable PaymentType paymentType) {
        log.info("根据类型查询支付记录，支付类型：{}", paymentType);
        List<PaymentResponse> result = paymentService.findByPaymentType(paymentType);
        return ApiResponse.success(result);
    }

    /**
     * 更新支付状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新支付状态", description = "更新指定支付的状态")
    @PreAuthorize("hasAuthority('PAYMENT_UPDATE')")
    public ApiResponse<PaymentResponse> updatePaymentStatus(
            @Parameter(description = "支付ID", required = true) @PathVariable Long id,
            @Parameter(description = "支付状态", required = true) @RequestParam PaymentStatus status) {
        log.info("更新支付状态，支付ID：{}，状态：{}", id, status);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        PaymentResponse result = paymentService.updatePaymentStatus(id, status);
        return ApiResponse.success(result);
    }

    /**
     * 支付成功处理
     */
    @PostMapping("/{id}/success")
    @Operation(summary = "支付成功处理", description = "处理支付成功回调")
    @PreAuthorize("hasAuthority('PAYMENT_UPDATE')")
    public ApiResponse<PaymentResponse> paymentSuccess(
            @Parameter(description = "支付ID", required = true) @PathVariable Long id,
            @Parameter(description = "第三方支付单号") @RequestParam(required = false) String thirdPartyPaymentNo,
            @Parameter(description = "回调数据") @RequestParam(required = false) String callbackData) {
        log.info("支付成功处理，支付ID：{}，第三方支付单号：{}", id, thirdPartyPaymentNo);
        PaymentResponse result = paymentService.paymentSuccess(id, thirdPartyPaymentNo, callbackData);
        return ApiResponse.success(result);
    }

    /**
     * 支付失败处理
     */
    @PostMapping("/{id}/failed")
    @Operation(summary = "支付失败处理", description = "处理支付失败")
    @PreAuthorize("hasAuthority('PAYMENT_UPDATE')")
    public ApiResponse<PaymentResponse> paymentFailed(
            @Parameter(description = "支付ID", required = true) @PathVariable Long id,
            @Parameter(description = "失败原因") @RequestParam(required = false) String failureReason) {
        log.info("支付失败处理，支付ID：{}，失败原因：{}", id, failureReason);
        PaymentResponse result = paymentService.paymentFailed(id, failureReason);
        return ApiResponse.success(result);
    }

    /**
     * 取消支付
     */
    @PostMapping("/{id}/cancel")
    @Operation(summary = "取消支付", description = "取消指定的支付")
    @PreAuthorize("hasAuthority('PAYMENT_UPDATE')")
    public ApiResponse<PaymentResponse> cancelPayment(
            @Parameter(description = "支付ID", required = true) @PathVariable Long id) {
        log.info("取消支付，支付ID：{}", id);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        PaymentResponse result = paymentService.cancelPayment(id);
        return ApiResponse.success(result);
    }

    /**
     * 生成支付单号
     */
    @PostMapping("/generate-payment-no")
    @Operation(summary = "生成支付单号", description = "生成新的支付单号")
    @PreAuthorize("hasAuthority('PAYMENT_CREATE')")
    public ApiResponse<String> generatePaymentNo() {
        log.info("生成支付单号");
        String paymentNo = paymentService.generatePaymentNo();
        return ApiResponse.success(paymentNo);
    }

    /**
     * 检查支付单号是否存在
     */
    @GetMapping("/exists/{paymentNo}")
    @Operation(summary = "检查支付单号", description = "检查支付单号是否已存在")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ApiResponse<Boolean> existsByPaymentNo(
            @Parameter(description = "支付单号", required = true) @PathVariable String paymentNo) {
        log.info("检查支付单号是否存在，支付单号：{}", paymentNo);
        boolean exists = paymentService.existsByPaymentNo(paymentNo);
        return ApiResponse.success(exists);
    }
}
