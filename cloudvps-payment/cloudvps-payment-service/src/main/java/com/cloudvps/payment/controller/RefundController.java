package com.cloudvps.payment.controller;

import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.common.security.util.SecurityUtils;
import com.cloudvps.payment.api.dto.request.RefundRequest;
import com.cloudvps.payment.api.dto.response.RefundResponse;
import com.cloudvps.payment.service.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 退款控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/refunds")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "退款管理", description = "退款相关API")
public class RefundController {
    
    private final PaymentService paymentService;
    
    @PostMapping
    @Operation(summary = "申请退款")
    @PreAuthorize("hasAuthority('PAYMENT_REFUND')")
    public ResponseEntity<ApiResponse<RefundResponse>> createRefund(
            @Valid @RequestBody RefundRequest request) {
        
        try {
            RefundResponse response = paymentService.createRefund(request);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("申请退款失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "CREATE_REFUND_FAILED"));
        }
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取退款信息")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ResponseEntity<ApiResponse<RefundResponse>> getRefundById(
            @Parameter(description = "退款ID") @PathVariable Long id) {
        
        try {
            RefundResponse response = paymentService.getRefundById(id);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("获取退款信息失败: id={}", id, e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "GET_REFUND_FAILED"));
        }
    }
    
    @GetMapping("/my")
    @Operation(summary = "分页查询我的退款记录")
    @PreAuthorize("hasAuthority('PAYMENT_VIEW')")
    public ResponseEntity<ApiResponse<Page<RefundResponse>>> getMyRefunds(
            @PageableDefault(size = 20) Pageable pageable) {
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            Page<RefundResponse> response = paymentService.getUserRefunds(userId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("查询我的退款记录失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "GET_MY_REFUNDS_FAILED"));
        }
    }
}
