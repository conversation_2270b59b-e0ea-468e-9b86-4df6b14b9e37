package com.cloudvps.payment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.payment.api.dto.request.PaymentCreateRequest;
import com.cloudvps.payment.api.dto.request.PaymentQueryRequest;
import com.cloudvps.payment.api.dto.request.PaymentUpdateRequest;
import com.cloudvps.payment.api.dto.response.PaymentResponse;
import com.cloudvps.payment.api.enums.PaymentStatus;
import com.cloudvps.payment.api.enums.PaymentType;

import java.util.List;

/**
 * 支付服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PaymentService {
    
    /**
     * 分页查询支付记录
     */
    IPage<PaymentResponse> queryPayments(PaymentQueryRequest request);
    
    /**
     * 根据ID获取支付记录
     */
    PaymentResponse findById(Long id);
    
    /**
     * 根据支付单号获取支付记录
     */
    PaymentResponse findByPaymentNo(String paymentNo);
    
    /**
     * 根据订单号获取支付记录
     */
    PaymentResponse findByOrderNo(String orderNo);
    
    /**
     * 创建支付
     */
    PaymentResponse createPayment(PaymentCreateRequest request);
    
    /**
     * 更新支付
     */
    PaymentResponse updatePayment(Long id, PaymentUpdateRequest request);
    
    /**
     * 删除支付记录
     */
    void deletePayment(Long id);
    
    /**
     * 批量删除支付记录
     */
    void batchDeletePayments(List<Long> paymentIds);
    
    /**
     * 根据用户ID查询支付记录
     */
    List<PaymentResponse> findByUserId(Long userId);
    
    /**
     * 根据状态查询支付记录
     */
    List<PaymentResponse> findByStatus(PaymentStatus status);
    
    /**
     * 根据支付类型查询支付记录
     */
    List<PaymentResponse> findByPaymentType(PaymentType paymentType);
    
    /**
     * 更新支付状态
     */
    PaymentResponse updatePaymentStatus(Long id, PaymentStatus status);
    
    /**
     * 支付成功处理
     */
    PaymentResponse paymentSuccess(Long id, String thirdPartyPaymentNo, String callbackData);
    
    /**
     * 支付失败处理
     */
    PaymentResponse paymentFailed(Long id, String failureReason);
    
    /**
     * 取消支付
     */
    PaymentResponse cancelPayment(Long id);
    
    /**
     * 统计用户支付数量
     */
    Long countByUserId(Long userId);
    
    /**
     * 统计指定状态的支付数量
     */
    Long countByStatus(PaymentStatus status);
    
    /**
     * 检查支付单号是否存在
     */
    boolean existsByPaymentNo(String paymentNo);
    
    /**
     * 生成支付单号
     */
    String generatePaymentNo();
}
