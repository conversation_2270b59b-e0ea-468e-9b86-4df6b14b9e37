package com.cloudvps.payment;

import com.cloudvps.common.swagger.annotation.EnableCloudVpsSwagger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 支付服务启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.cloudvps.payment", "com.cloudvps.common"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.cloudvps.system.api.client", "com.cloudvps.order.api.client"})
@EnableCloudVpsSwagger
public class PaymentServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(PaymentServiceApplication.class, args);
    }
}
