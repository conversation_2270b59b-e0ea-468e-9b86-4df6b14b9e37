package com.cloudvps.payment.convert;

import com.cloudvps.payment.api.dto.request.PaymentCreateRequest;
import com.cloudvps.payment.api.dto.request.PaymentUpdateRequest;
import com.cloudvps.payment.api.dto.response.PaymentResponse;
import com.cloudvps.payment.entity.Payment;
import org.mapstruct.*;

/**
 * 支付对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface PaymentConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "paymentNo", ignore = true) // 需要在Service层生成
    @Mapping(target = "actualAmount", constant = "0.00")
    @Mapping(target = "status", constant = "PENDING")
    @Mapping(target = "paymentUrl", ignore = true)
    @Mapping(target = "qrCodeUrl", ignore = true)
    @Mapping(target = "thirdPartyPaymentNo", ignore = true)
    @Mapping(target = "paidAt", ignore = true)
    @Mapping(target = "failureReason", ignore = true)
    @Mapping(target = "callbackData", ignore = true)
    @Mapping(target = "userId", ignore = true) // 需要在Service层设置
    @Mapping(target = "channelId", ignore = true) // 需要在Service层设置
    @Mapping(target = "expiredAt", ignore = true) // 需要在Service层设置
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    Payment toEntity(PaymentCreateRequest request);
    
    /**
     * 实体转响应
     */
    @Mapping(target = "userId", source = "userId")
    @Mapping(target = "channelId", source = "channelId")
    @Mapping(target = "expiredAt", source = "expiredAt")
    PaymentResponse toResponse(Payment payment);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "paymentNo", ignore = true) // 支付单号不允许修改
    @Mapping(target = "orderNo", ignore = true) // 订单号不允许修改
    @Mapping(target = "userId", ignore = true) // 用户ID不允许修改
    @Mapping(target = "amount", ignore = true) // 支付金额不允许修改
    @Mapping(target = "paymentMethod", ignore = true) // 支付方式不允许修改
    @Mapping(target = "paymentType", ignore = true) // 支付类型不允许修改
    @Mapping(target = "channelId", ignore = true) // 支付渠道不允许修改
    @Mapping(target = "callbackUrl", ignore = true) // 回调地址不允许修改
    @Mapping(target = "returnUrl", ignore = true) // 返回地址不允许修改
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget Payment payment, PaymentUpdateRequest request);
}
