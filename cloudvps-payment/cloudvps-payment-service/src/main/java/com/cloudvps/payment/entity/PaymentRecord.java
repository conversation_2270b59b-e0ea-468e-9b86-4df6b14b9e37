package com.cloudvps.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.payment.api.enums.PaymentStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付记录实体类（用于退款等操作记录）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("payment_records")
public class PaymentRecord extends BaseEntity {
    
    /**
     * 记录单号
     */
    @TableField("record_no")
    private String recordNo;

    /**
     * 关联支付ID
     */
    @TableField("payment_id")
    private Long paymentId;

    /**
     * 记录类型（REFUND-退款, CHARGEBACK-拒付等）
     */
    @TableField("record_type")
    private String recordType;

    /**
     * 金额
     */
    private BigDecimal amount = BigDecimal.ZERO;

    /**
     * 状态
     */
    private PaymentStatus status = PaymentStatus.PENDING;
    
    /**
     * 原因/描述
     */
    private String reason;

    /**
     * 第三方记录号
     */
    @TableField("third_party_record_no")
    private String thirdPartyRecordNo;

    /**
     * 处理时间
     */
    @TableField("processed_at")
    private LocalDateTime processedAt;

    /**
     * 失败原因
     */
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 回调数据
     */
    @TableField("callback_data")
    private String callbackData;
}
