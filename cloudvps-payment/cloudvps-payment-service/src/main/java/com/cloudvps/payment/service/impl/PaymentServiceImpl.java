package com.cloudvps.payment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.payment.api.dto.request.PaymentCreateRequest;
import com.cloudvps.payment.api.dto.request.PaymentQueryRequest;
import com.cloudvps.payment.api.dto.request.PaymentUpdateRequest;
import com.cloudvps.payment.api.dto.response.PaymentResponse;
import com.cloudvps.payment.api.enums.PaymentStatus;
import com.cloudvps.payment.api.enums.PaymentType;
import com.cloudvps.payment.convert.PaymentConvert;
import com.cloudvps.payment.entity.Payment;
import com.cloudvps.payment.mapper.PaymentDataMapper;
import com.cloudvps.payment.service.PaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 支付服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentServiceImpl implements PaymentService {
    
    private final PaymentDataMapper paymentDataMapper;
    private final PaymentConvert paymentConvert;
    
    @Override
    public IPage<PaymentResponse> queryPayments(PaymentQueryRequest request) {
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(request.getPaymentNo())) {
            queryWrapper.like(Payment::getPaymentNo, request.getPaymentNo());
        }
        if (StringUtils.hasText(request.getOrderNo())) {
            queryWrapper.like(Payment::getOrderNo, request.getOrderNo());
        }
        if (request.getUserId() != null) {
            queryWrapper.eq(Payment::getUserId, request.getUserId());
        }
        if (request.getPaymentMethod() != null) {
            queryWrapper.eq(Payment::getPaymentMethod, request.getPaymentMethod());
        }
        if (request.getPaymentType() != null) {
            queryWrapper.eq(Payment::getPaymentType, request.getPaymentType());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq(Payment::getStatus, request.getStatus());
        }
        if (request.getMinAmount() != null) {
            queryWrapper.ge(Payment::getAmount, request.getMinAmount());
        }
        if (request.getMaxAmount() != null) {
            queryWrapper.le(Payment::getAmount, request.getMaxAmount());
        }
        if (request.getChannelId() != null) {
            queryWrapper.eq(Payment::getChannelId, request.getChannelId());
        }
        if (StringUtils.hasText(request.getThirdPartyPaymentNo())) {
            queryWrapper.like(Payment::getThirdPartyPaymentNo, request.getThirdPartyPaymentNo());
        }
        if (request.getCreatedTimeStart() != null) {
            queryWrapper.ge(Payment::getCreatedTime, request.getCreatedTimeStart());
        }
        if (request.getCreatedTimeEnd() != null) {
            queryWrapper.le(Payment::getCreatedTime, request.getCreatedTimeEnd());
        }
        if (request.getPaidAtStart() != null) {
            queryWrapper.ge(Payment::getPaidAt, request.getPaidAtStart());
        }
        if (request.getPaidAtEnd() != null) {
            queryWrapper.le(Payment::getPaidAt, request.getPaidAtEnd());
        }
        if (request.getExpiredAtStart() != null) {
            queryWrapper.ge(Payment::getExpiredAt, request.getExpiredAtStart());
        }
        if (request.getExpiredAtEnd() != null) {
            queryWrapper.le(Payment::getExpiredAt, request.getExpiredAtEnd());
        }
        
        // 排序
        queryWrapper.orderByDesc(Payment::getCreatedTime);
        
        // 分页查询
        Page<Payment> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<Payment> paymentPage = paymentDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return paymentPage.convert(paymentConvert::toResponse);
    }
    
    @Override
    public PaymentResponse findById(Long id) {
        Payment payment = paymentDataMapper.selectById(id);
        if (payment == null) {
            throw new BusinessException("支付记录不存在");
        }
        return paymentConvert.toResponse(payment);
    }
    
    @Override
    public PaymentResponse findByPaymentNo(String paymentNo) {
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Payment::getPaymentNo, paymentNo);
        
        Payment payment = paymentDataMapper.selectOne(queryWrapper);
        if (payment == null) {
            throw new BusinessException("支付记录不存在");
        }
        return paymentConvert.toResponse(payment);
    }
    
    @Override
    public PaymentResponse findByOrderNo(String orderNo) {
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Payment::getOrderNo, orderNo);
        
        Payment payment = paymentDataMapper.selectOne(queryWrapper);
        if (payment == null) {
            throw new BusinessException("支付记录不存在");
        }
        return paymentConvert.toResponse(payment);
    }
    
    @Override
    @Transactional
    public PaymentResponse createPayment(PaymentCreateRequest request) {
        // 检查订单是否已有支付记录
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Payment::getOrderNo, request.getOrderNo());
        if (paymentDataMapper.selectCount(queryWrapper) > 0) {
            throw new BusinessException("该订单已存在支付记录");
        }
        
        // 生成支付单号
        String paymentNo = generatePaymentNo();
        
        Payment payment = paymentConvert.toEntity(request);
        payment.setPaymentNo(paymentNo);
        
        paymentDataMapper.insert(payment);
        
        log.info("创建支付记录成功: paymentNo={}, orderNo={}, amount={}", 
                paymentNo, payment.getOrderNo(), payment.getAmount());
        
        return paymentConvert.toResponse(payment);
    }
    
    @Override
    @Transactional
    public PaymentResponse updatePayment(Long id, PaymentUpdateRequest request) {
        Payment existingPayment = paymentDataMapper.selectById(id);
        if (existingPayment == null) {
            throw new BusinessException("支付记录不存在");
        }
        
        // 检查支付状态是否允许修改
        if (existingPayment.getStatus() == PaymentStatus.SUCCESS || 
            existingPayment.getStatus() == PaymentStatus.CANCELLED) {
            throw new BusinessException("支付已完成或已取消，无法修改");
        }
        
        // 更新字段
        paymentConvert.updateFromRequest(existingPayment, request);
        paymentDataMapper.updateById(existingPayment);
        
        log.info("更新支付记录成功: paymentId={}, paymentNo={}", 
                id, existingPayment.getPaymentNo());
        
        return paymentConvert.toResponse(existingPayment);
    }
    
    @Override
    @Transactional
    public void deletePayment(Long id) {
        Payment payment = paymentDataMapper.selectById(id);
        if (payment == null) {
            throw new BusinessException("支付记录不存在");
        }
        
        // 检查支付状态
        if (payment.getStatus() == PaymentStatus.SUCCESS) {
            throw new BusinessException("已成功的支付记录无法删除");
        }
        
        paymentDataMapper.deleteById(id);
        
        log.info("删除支付记录成功: paymentId={}, paymentNo={}", 
                id, payment.getPaymentNo());
    }
    
    @Override
    @Transactional
    public void batchDeletePayments(List<Long> paymentIds) {
        if (paymentIds == null || paymentIds.isEmpty()) {
            return;
        }
        
        // 检查支付状态
        for (Long paymentId : paymentIds) {
            Payment payment = paymentDataMapper.selectById(paymentId);
            if (payment != null && payment.getStatus() == PaymentStatus.SUCCESS) {
                throw new BusinessException("存在已成功的支付记录，无法删除");
            }
        }
        
        // 批量删除
        paymentDataMapper.deleteBatchIds(paymentIds);
        
        log.info("批量删除支付记录成功: paymentIds={}", paymentIds);
    }
    
    @Override
    public List<PaymentResponse> findByUserId(Long userId) {
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Payment::getUserId, userId)
                   .orderByDesc(Payment::getCreatedTime);
        
        List<Payment> payments = paymentDataMapper.selectList(queryWrapper);
        return payments.stream()
                      .map(paymentConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    public List<PaymentResponse> findByStatus(PaymentStatus status) {
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Payment::getStatus, status)
                   .orderByDesc(Payment::getCreatedTime);
        
        List<Payment> payments = paymentDataMapper.selectList(queryWrapper);
        return payments.stream()
                      .map(paymentConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    public List<PaymentResponse> findByPaymentType(PaymentType paymentType) {
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Payment::getPaymentType, paymentType)
                   .orderByDesc(Payment::getCreatedTime);
        
        List<Payment> payments = paymentDataMapper.selectList(queryWrapper);
        return payments.stream()
                      .map(paymentConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public PaymentResponse updatePaymentStatus(Long id, PaymentStatus status) {
        Payment payment = paymentDataMapper.selectById(id);
        if (payment == null) {
            throw new BusinessException("支付记录不存在");
        }
        
        payment.setStatus(status);
        if (status == PaymentStatus.SUCCESS) {
            payment.setPaidAt(LocalDateTime.now());
        }
        
        paymentDataMapper.updateById(payment);
        
        log.info("更新支付状态成功: paymentId={}, paymentNo={}, status={}", 
                id, payment.getPaymentNo(), status);
        
        return paymentConvert.toResponse(payment);
    }
    
    @Override
    @Transactional
    public PaymentResponse paymentSuccess(Long id, String thirdPartyPaymentNo, String callbackData) {
        Payment payment = paymentDataMapper.selectById(id);
        if (payment == null) {
            throw new BusinessException("支付记录不存在");
        }
        
        if (payment.getStatus() == PaymentStatus.SUCCESS) {
            throw new BusinessException("支付已成功，请勿重复操作");
        }
        
        payment.setStatus(PaymentStatus.SUCCESS);
        payment.setThirdPartyPaymentNo(thirdPartyPaymentNo);
        payment.setCallbackData(callbackData);
        payment.setPaidAt(LocalDateTime.now());
        payment.setActualAmount(payment.getAmount());
        
        paymentDataMapper.updateById(payment);
        
        log.info("支付成功处理完成: paymentId={}, paymentNo={}, thirdPartyPaymentNo={}", 
                id, payment.getPaymentNo(), thirdPartyPaymentNo);
        
        return paymentConvert.toResponse(payment);
    }
    
    @Override
    @Transactional
    public PaymentResponse paymentFailed(Long id, String failureReason) {
        Payment payment = paymentDataMapper.selectById(id);
        if (payment == null) {
            throw new BusinessException("支付记录不存在");
        }
        
        payment.setStatus(PaymentStatus.FAILED);
        payment.setFailureReason(failureReason);
        
        paymentDataMapper.updateById(payment);
        
        log.info("支付失败处理完成: paymentId={}, paymentNo={}, reason={}", 
                id, payment.getPaymentNo(), failureReason);
        
        return paymentConvert.toResponse(payment);
    }
    
    @Override
    @Transactional
    public PaymentResponse cancelPayment(Long id) {
        Payment payment = paymentDataMapper.selectById(id);
        if (payment == null) {
            throw new BusinessException("支付记录不存在");
        }
        
        if (payment.getStatus() == PaymentStatus.SUCCESS) {
            throw new BusinessException("支付已成功，无法取消");
        }
        
        payment.setStatus(PaymentStatus.CANCELLED);
        paymentDataMapper.updateById(payment);
        
        log.info("取消支付成功: paymentId={}, paymentNo={}", 
                id, payment.getPaymentNo());
        
        return paymentConvert.toResponse(payment);
    }
    
    @Override
    public Long countByUserId(Long userId) {
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Payment::getUserId, userId);
        return paymentDataMapper.selectCount(queryWrapper);
    }
    
    @Override
    public Long countByStatus(PaymentStatus status) {
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Payment::getStatus, status);
        return paymentDataMapper.selectCount(queryWrapper);
    }
    
    @Override
    public boolean existsByPaymentNo(String paymentNo) {
        LambdaQueryWrapper<Payment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Payment::getPaymentNo, paymentNo);
        return paymentDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public String generatePaymentNo() {
        String prefix = "PAY";
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.format("%04d", ThreadLocalRandom.current().nextInt(10000));
        return prefix + timestamp + random;
    }
}
