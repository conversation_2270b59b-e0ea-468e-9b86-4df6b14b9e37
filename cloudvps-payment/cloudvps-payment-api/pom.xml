<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cloudvps</groupId>
        <artifactId>cloudvps-payment</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>cloudvps-payment-api</artifactId>
    <packaging>jar</packaging>
    <name>CloudVPS Payment API</name>
    <description>CloudVPS支付服务API模块</description>

    <dependencies>
        <!-- CloudVPS Common Core -->
        <dependency>
            <groupId>com.cloudvps</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <!-- Spring Cloud OpenFeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <!-- Common Swagger -->
        <dependency>
            <groupId>com.cloudvps</groupId>
            <artifactId>common-swagger</artifactId>
        </dependency>
    </dependencies>
</project>
