package com.cloudvps.payment.api.dto.request;

import com.cloudvps.payment.api.enums.PaymentStatus;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PaymentUpdateRequest {
    
    /**
     * 实际支付金额
     */
    @Min(value = 0, message = "实际支付金额不能为负数")
    private BigDecimal actualAmount;
    
    /**
     * 支付状态
     */
    private PaymentStatus status;
    
    /**
     * 支付描述
     */
    @Size(max = 200, message = "支付描述长度不能超过200个字符")
    private String description;
    
    /**
     * 第三方支付单号
     */
    @Size(max = 100, message = "第三方支付单号长度不能超过100个字符")
    private String thirdPartyPaymentNo;
    
    /**
     * 支付URL
     */
    @Size(max = 500, message = "支付URL长度不能超过500个字符")
    private String paymentUrl;
    
    /**
     * 二维码URL
     */
    @Size(max = 500, message = "二维码URL长度不能超过500个字符")
    private String qrCodeUrl;
    
    /**
     * 支付时间
     */
    private LocalDateTime paidAt;
    
    /**
     * 过期时间
     */
    private LocalDateTime expiredAt;
    
    /**
     * 失败原因
     */
    @Size(max = 500, message = "失败原因长度不能超过500个字符")
    private String failureReason;
    
    /**
     * 支付完成回调数据
     */
    @Size(max = 2000, message = "回调数据长度不能超过2000个字符")
    private String callbackData;
}
