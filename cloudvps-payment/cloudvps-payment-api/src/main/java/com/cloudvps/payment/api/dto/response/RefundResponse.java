package com.cloudvps.payment.api.dto.response;

import com.cloudvps.payment.api.enums.PaymentStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退款响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "退款响应")
public class RefundResponse {
    
    @Schema(description = "退款ID")
    private Long id;
    
    @Schema(description = "退款单号")
    private String refundNo;
    
    @Schema(description = "支付ID")
    private Long paymentId;
    
    @Schema(description = "支付单号")
    private String paymentNo;
    
    @Schema(description = "退款金额")
    private BigDecimal refundAmount;
    
    @Schema(description = "退款原因")
    private String refundReason;
    
    @Schema(description = "退款状态")
    private PaymentStatus status;
    
    @Schema(description = "第三方退款单号")
    private String thirdPartyRefundNo;
    
    @Schema(description = "退款时间")
    private LocalDateTime refundedAt;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
