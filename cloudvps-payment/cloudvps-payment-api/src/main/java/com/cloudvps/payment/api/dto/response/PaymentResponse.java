package com.cloudvps.payment.api.dto.response;

import com.cloudvps.payment.api.enums.PaymentMethod;
import com.cloudvps.payment.api.enums.PaymentStatus;
import com.cloudvps.payment.api.enums.PaymentType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "支付响应")
public class PaymentResponse {
    
    @Schema(description = "支付ID")
    private Long id;
    
    @Schema(description = "支付单号")
    private String paymentNo;
    
    @Schema(description = "订单号")
    private String orderNo;
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "支付金额")
    private BigDecimal amount;
    
    @Schema(description = "实际支付金额")
    private BigDecimal actualAmount;
    
    @Schema(description = "支付方式")
    private PaymentMethod paymentMethod;
    
    @Schema(description = "支付类型")
    private PaymentType paymentType;
    
    @Schema(description = "支付状态")
    private PaymentStatus status;
    
    @Schema(description = "支付描述")
    private String description;
    
    @Schema(description = "第三方支付单号")
    private String thirdPartyPaymentNo;
    
    @Schema(description = "支付渠道ID")
    private Long channelId;
    
    @Schema(description = "支付URL")
    private String paymentUrl;
    
    @Schema(description = "二维码URL")
    private String qrCodeUrl;
    
    @Schema(description = "回调地址")
    private String callbackUrl;
    
    @Schema(description = "返回地址")
    private String returnUrl;
    
    @Schema(description = "支付时间")
    private LocalDateTime paidAt;
    
    @Schema(description = "过期时间")
    private LocalDateTime expiredAt;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;
}
