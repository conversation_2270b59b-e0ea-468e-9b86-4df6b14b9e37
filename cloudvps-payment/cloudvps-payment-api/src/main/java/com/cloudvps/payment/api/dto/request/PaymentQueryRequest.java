package com.cloudvps.payment.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.payment.api.enums.PaymentMethod;
import com.cloudvps.payment.api.enums.PaymentStatus;
import com.cloudvps.payment.api.enums.PaymentType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "支付查询请求")
public class PaymentQueryRequest extends PageRequest {
    
    @Schema(description = "支付单号")
    private String paymentNo;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "支付方式")
    private PaymentMethod paymentMethod;

    @Schema(description = "支付类型")
    private PaymentType paymentType;

    @Schema(description = "支付状态")
    private PaymentStatus status;
    
    @Schema(description = "最小金额")
    private BigDecimal minAmount;

    @Schema(description = "最大金额")
    private BigDecimal maxAmount;

    @Schema(description = "支付渠道ID")
    private Long channelId;

    @Schema(description = "第三方支付单号")
    private String thirdPartyPaymentNo;
    
    @Schema(description = "创建时间开始")
    private LocalDateTime createdTimeStart;

    @Schema(description = "创建时间结束")
    private LocalDateTime createdTimeEnd;

    @Schema(description = "支付时间开始")
    private LocalDateTime paidAtStart;

    @Schema(description = "支付时间结束")
    private LocalDateTime paidAtEnd;

    @Schema(description = "过期时间开始")
    private LocalDateTime expiredAtStart;

    @Schema(description = "过期时间结束")
    private LocalDateTime expiredAtEnd;
}
