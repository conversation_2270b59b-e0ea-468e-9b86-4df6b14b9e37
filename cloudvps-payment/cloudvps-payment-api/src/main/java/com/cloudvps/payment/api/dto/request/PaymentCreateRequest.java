package com.cloudvps.payment.api.dto.request;

import com.cloudvps.payment.api.enums.PaymentMethod;
import com.cloudvps.payment.api.enums.PaymentType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 创建支付请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "创建支付请求")
public class PaymentCreateRequest {
    
    @Schema(description = "订单号", example = "ORD20240101120000001")
    @NotBlank(message = "订单号不能为空")
    @Size(max = 50, message = "订单号长度不能超过50个字符")
    private String orderNo;
    
    @Schema(description = "支付金额", example = "99.00")
    @NotNull(message = "支付金额不能为空")
    @DecimalMin(value = "0.01", message = "支付金额必须大于0")
    private BigDecimal amount;
    
    @Schema(description = "支付方式", example = "ALIPAY")
    @NotNull(message = "支付方式不能为空")
    private PaymentMethod paymentMethod;
    
    @Schema(description = "支付类型", example = "ORDER")
    @NotNull(message = "支付类型不能为空")
    private PaymentType paymentType = PaymentType.ORDER;
    
    @Schema(description = "支付描述", example = "订单支付")
    @Size(max = 200, message = "支付描述长度不能超过200个字符")
    private String description;
    
    @Schema(description = "回调地址", example = "https://api.cloudvps.com/callback")
    @Size(max = 500, message = "回调地址长度不能超过500个字符")
    private String callbackUrl;
    
    @Schema(description = "返回地址", example = "https://www.cloudvps.com/order/success")
    @Size(max = 500, message = "返回地址长度不能超过500个字符")
    private String returnUrl;
}
