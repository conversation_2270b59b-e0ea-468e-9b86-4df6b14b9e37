package com.cloudvps.payment.api.enums;

/**
 * 支付类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PaymentType {
    
    /**
     * 订单支付
     */
    ORDER("订单支付"),
    
    /**
     * 充值
     */
    RECHARGE("充值"),
    
    /**
     * 退款
     */
    REFUND("退款"),
    
    /**
     * 提现
     */
    WITHDRAWAL("提现");
    
    private final String description;
    
    PaymentType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
