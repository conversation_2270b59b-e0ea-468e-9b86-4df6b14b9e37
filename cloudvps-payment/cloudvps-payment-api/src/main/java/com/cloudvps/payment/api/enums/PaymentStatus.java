package com.cloudvps.payment.api.enums;

/**
 * 支付状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PaymentStatus {
    
    /**
     * 待支付
     */
    PENDING("待支付"),
    
    /**
     * 支付中
     */
    PROCESSING("支付中"),
    
    /**
     * 支付成功
     */
    SUCCESS("支付成功"),
    
    /**
     * 支付失败
     */
    FAILED("支付失败"),
    
    /**
     * 已取消
     */
    CANCELLED("已取消"),
    
    /**
     * 已退款
     */
    REFUNDED("已退款"),
    
    /**
     * 部分退款
     */
    PARTIAL_REFUNDED("部分退款");
    
    private final String description;
    
    PaymentStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
