package com.cloudvps.payment.api.client;

import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.payment.api.dto.request.PaymentCreateRequest;
import com.cloudvps.payment.api.dto.request.RefundRequest;
import com.cloudvps.payment.api.dto.response.PaymentResponse;
import com.cloudvps.payment.api.dto.response.RefundResponse;
import org.springframework.cloud.openfeign.FeignClient;
import com.cloudvps.common.core.response.PageResponse;
import org.springframework.web.bind.annotation.*;

/**
 * 支付服务Feign客户端
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@FeignClient(
    name = "payment-service",
    url = "${cloudvps.services.payment.url:http://localhost:8084}",
    path = "/api/v1"
)
public interface PaymentServiceClient {
    
    /**
     * 创建支付
     */
    @PostMapping("/payments")
    ApiResponse<PaymentResponse> createPayment(@RequestBody PaymentCreateRequest request);
    
    /**
     * 根据ID获取支付信息
     */
    @GetMapping("/payments/{id}")
    ApiResponse<PaymentResponse> getPaymentById(@PathVariable("id") Long id);
    
    /**
     * 根据支付单号获取支付信息
     */
    @GetMapping("/payments/by-no/{paymentNo}")
    ApiResponse<PaymentResponse> getPaymentByNo(@PathVariable("paymentNo") String paymentNo);
    
    /**
     * 根据订单号获取支付信息
     */
    @GetMapping("/payments/by-order/{orderNo}")
    ApiResponse<PaymentResponse> getPaymentByOrderNo(@PathVariable("orderNo") String orderNo);
    
    /**
     * 分页查询用户支付记录
     */
    @GetMapping("/payments/my")
    ApiResponse<PageResponse<PaymentResponse>> getMyPayments(@RequestParam int page, @RequestParam int size);
    
    /**
     * 取消支付
     */
    @PostMapping("/payments/{id}/cancel")
    ApiResponse<PaymentResponse> cancelPayment(@PathVariable("id") Long id);
    
    /**
     * 申请退款
     */
    @PostMapping("/refunds")
    ApiResponse<RefundResponse> createRefund(@RequestBody RefundRequest request);
    
    /**
     * 查询退款信息
     */
    @GetMapping("/refunds/{id}")
    ApiResponse<RefundResponse> getRefundById(@PathVariable("id") Long id);
    
    /**
     * 分页查询用户退款记录
     */
    @GetMapping("/refunds/my")
    ApiResponse<PageResponse<RefundResponse>> getMyRefunds(@RequestParam int page, @RequestParam int size);
}
