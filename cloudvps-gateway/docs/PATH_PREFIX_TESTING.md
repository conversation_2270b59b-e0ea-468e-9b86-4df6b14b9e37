# CloudVPS 网关路径前缀功能测试指南

## 功能概述

CloudVPS 网关现在支持统一路径前缀配置，可以为所有微服务路由添加统一的前缀（如 `/api/v1`），便于前端开发的反向代理配置。

## 配置说明

### 基本配置

```yaml
cloudvps:
  gateway:
    path-prefix:
      enabled: false          # 是否启用路径前缀
      prefix: /api/v1         # 统一路径前缀
      keep-prefix: false      # 转发到后端服务时是否保留前缀
      exclude-patterns:       # 排除前缀的路径模式
        - /actuator/**
        - /health/**
        - /info/**
```

### 环境特定配置

- **开发环境 (dev)**: 默认关闭路径前缀，便于调试
- **生产环境 (prod)**: 默认启用路径前缀，提供统一API入口

## 路由规则说明

### 不启用路径前缀时
- 系统服务: `http://gateway:8080/system/**`
- 虚拟化服务: `http://gateway:8080/virtualization/**`
- 订单服务: `http://gateway:8080/order/**`
- 支付服务: `http://gateway:8080/payment/**`

### 启用路径前缀 `/api/v1` 时
- 系统服务: `http://gateway:8080/api/v1/system/**`
- 虚拟化服务: `http://gateway:8080/api/v1/virtualization/**`
- 订单服务: `http://gateway:8080/api/v1/order/**`
- 支付服务: `http://gateway:8080/api/v1/payment/**`

## 管理 API

### 1. 获取当前路径前缀配置

```bash
curl -X GET http://localhost:8080/gateway/management/path-prefix
```

响应示例：
```json
{
  "enabled": false,
  "prefix": "/api/v1",
  "keepPrefix": false,
  "excludePatterns": ["/actuator/**", "/health/**", "/info/**"],
  "stripPrefixCount": 2
}
```

### 2. 启用路径前缀

```bash
curl -X POST "http://localhost:8080/gateway/management/path-prefix/enable?prefix=/api/v1"
```

### 3. 禁用路径前缀

```bash
curl -X POST http://localhost:8080/gateway/management/path-prefix/disable
```

### 4. 更新路径前缀配置

```bash
curl -X POST http://localhost:8080/gateway/management/path-prefix \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "prefix": "/api/v2",
    "keepPrefix": false
  }'
```

### 5. 刷新路由配置

```bash
curl -X POST http://localhost:8080/gateway/management/refresh-routes
```

### 6. 获取当前路由信息

```bash
curl -X GET http://localhost:8080/gateway/management/routes
```

### 7. 健康检查

```bash
curl -X GET http://localhost:8080/gateway/management/health
```

## 测试场景

### 场景1：开发环境测试（不启用前缀）

1. 启动网关服务（dev profile）
2. 验证路由：
   ```bash
   # 系统服务健康检查
   curl http://localhost:8080/system/actuator/health
   
   # 虚拟化服务健康检查
   curl http://localhost:8080/virtualization/actuator/health
   ```

### 场景2：生产环境测试（启用前缀）

1. 启动网关服务（prod profile）
2. 验证路由：
   ```bash
   # 系统服务健康检查
   curl http://localhost:8080/api/v1/system/actuator/health
   
   # 虚拟化服务健康检查
   curl http://localhost:8080/api/v1/virtualization/actuator/health
   ```

### 场景3：动态切换测试

1. 启动网关服务
2. 检查当前配置：
   ```bash
   curl http://localhost:8080/gateway/management/path-prefix
   ```
3. 启用路径前缀：
   ```bash
   curl -X POST "http://localhost:8080/gateway/management/path-prefix/enable?prefix=/api/v1"
   ```
4. 验证路由变更：
   ```bash
   curl http://localhost:8080/gateway/management/routes
   ```
5. 测试新路由：
   ```bash
   curl http://localhost:8080/api/v1/system/actuator/health
   ```

## 前端集成说明

### Nginx 反向代理配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 代理到网关服务
    location /api/ {
        proxy_pass http://cloudvps-gateway:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
}
```

### Vue.js 配置示例

```javascript
// vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api/v1'  // 如果网关启用了 /api/v1 前缀
        }
      }
    }
  }
}
```

## 注意事项

1. **路径前缀变更会立即生效**，无需重启服务
2. **排除路径**（如 `/actuator/**`）不会添加前缀
3. **StripPrefix 过滤器**会自动计算需要剥离的路径段数
4. **生产环境**建议启用路径前缀，提供统一的API版本管理
5. **开发环境**建议关闭路径前缀，便于直接调试后端服务

## 故障排除

### 问题1：路由不生效
- 检查配置是否正确
- 调用刷新路由API
- 查看网关日志

### 问题2：404错误
- 确认路径前缀配置
- 检查后端服务是否启动
- 验证路由规则

### 问题3：前缀配置不生效
- 确认配置文件格式正确
- 重启网关服务
- 检查环境变量设置
