# CloudVPS API 网关服务模块

## 模块概述

API 网关是 CloudVPS 平台的统一入口，负责路由转发、身份认证、权限控制、限流熔断、监控日志等功能。作为微服务架构的门户，网关服务确保系统的安全性、稳定性和可观测性，为客户端提供统一、简洁的API访问体验。

## 技术架构

### 技术栈
- **框架**: Spring Cloud Gateway 4.1.0
- **Java 版本**: JDK 21
- **响应式编程**: Spring WebFlux
- **缓存**: Redis 7.2+
- **熔断器**: Resilience4j
- **监控**: Spring Boot Actuator + Micrometer
- **构建工具**: Maven 3.9+

### 架构设计模式
```
┌─────────────────────────────────────────┐
│              Client 层                  │
│   Web前端 │ 移动端 │ 第三方系统         │
└─────────────────┬───────────────────────┘
                  │ HTTPS/HTTP
┌─────────────────▼───────────────────────┐
│              Gateway 层                 │
│  路由 │ 认证 │ 限流 │ 熔断 │ 监控      │
└─────────────────┬───────────────────────┘
                  │ HTTP/内部通信
┌─────────────────▼───────────────────────┐
│            Microservices 层             │
│ 系统服务 │ 虚拟化 │ 订单 │ 支付        │
└─────────────────────────────────────────┘
```

## 核心功能模块

### 1. 路由管理模块
- **动态路由**: 基于配置的动态路由规则
- **路径匹配**: 支持精确匹配和模式匹配
- **负载均衡**: 多实例服务的负载均衡
- **健康检查**: 后端服务健康状态检查
- **故障转移**: 服务不可用时的故障转移

### 2. 身份认证模块
- **JWT 认证**: 基于 JWT 令牌的身份验证
- **令牌解析**: 提取用户身份和权限信息
- **会话管理**: 用户会话状态管理
- **免认证路径**: 公开接口的免认证配置
- **认证缓存**: 认证结果缓存优化

### 3. 权限控制模块
- **RBAC 权限**: 基于角色的访问控制
- **接口权限**: 细粒度的接口级权限控制
- **权限缓存**: 用户权限信息缓存
- **权限传递**: 向下游服务传递用户上下文
- **权限审计**: 权限检查结果审计

### 4. 限流熔断模块
- **请求限流**: 基于用户、IP的请求频率限制
- **熔断保护**: 下游服务异常时的熔断机制
- **降级处理**: 服务不可用时的降级响应
- **限流算法**: 滑动窗口、令牌桶等限流算法
- **熔断恢复**: 服务恢复后的自动熔断恢复

### 5. 监控日志模块
- **请求日志**: 完整的请求响应日志记录
- **性能监控**: 接口响应时间和吞吐量监控
- **错误统计**: 错误率和异常统计
- **链路追踪**: 分布式链路追踪支持
- **指标收集**: Prometheus 指标收集

## 路由配置设计

### 路由规则配置
```yaml
spring:
  cloud:
    gateway:
      routes:
        # 系统服务路由
        - id: system-service
          uri: http://localhost:8081
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=0
            - name: CircuitBreaker
              args:
                name: system-service-cb
                fallbackUri: forward:/fallback/system

        # 虚拟化服务路由
        - id: virtualization-service
          uri: http://localhost:8082
          predicates:
            - Path=/virtualization/**
          filters:
            - StripPrefix=0
            - name: CircuitBreaker
              args:
                name: virtualization-service-cb
                fallbackUri: forward:/fallback/virtualization

        # 订单服务路由
        - id: order-service
          uri: http://localhost:8083
          predicates:
            - Path=/order/**
          filters:
            - StripPrefix=0
            - name: CircuitBreaker
              args:
                name: order-service-cb
                fallbackUri: forward:/fallback/order

        # 支付服务路由
        - id: payment-service
          uri: http://localhost:8084
          predicates:
            - Path=/payment/**
          filters:
            - StripPrefix=0
            - name: CircuitBreaker
              args:
                name: payment-service-cb
                fallbackUri: forward:/fallback/payment
```

### 动态路由管理
```java
@Component
public class DynamicRouteService {
    
    private final RouteDefinitionWriter routeDefinitionWriter;
    private final ApplicationEventPublisher publisher;
    
    /**
     * 添加路由
     */
    public void addRoute(RouteDefinition definition) {
        routeDefinitionWriter.save(Mono.just(definition)).subscribe();
        publisher.publishEvent(new RefreshRoutesEvent(this));
    }
    
    /**
     * 更新路由
     */
    public void updateRoute(RouteDefinition definition) {
        routeDefinitionWriter.delete(Mono.just(definition.getId()));
        routeDefinitionWriter.save(Mono.just(definition)).subscribe();
        publisher.publishEvent(new RefreshRoutesEvent(this));
    }
    
    /**
     * 删除路由
     */
    public void deleteRoute(String routeId) {
        routeDefinitionWriter.delete(Mono.just(routeId)).subscribe();
        publisher.publishEvent(new RefreshRoutesEvent(this));
    }
}
```

## JWT 认证设计

### JWT 认证过滤器
```java
@Component
public class JwtAuthenticationFilter implements GlobalFilter, Ordered {
    
    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
    
    @Value("${cloudvps.jwt.secret}")
    private String jwtSecret;
    
    @Value("#{'${cloudvps.gateway.permit-all-paths:/system/auth/register,/system/auth/login,/order/products/**,/payment/webhooks/**,/payment/callbacks/**,/actuator/**}'.split(',')}")
    private List<String> permitAllPaths;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        
        log.debug("Gateway处理请求: {}", path);
        
        // 检查是否为免认证路径
        if (isPermitAllPath(path)) {
            log.debug("路径 {} 无需认证，直接放行", path);
            return chain.filter(exchange);
        }
        
        // 获取Authorization头
        String authHeader = request.getHeaders().getFirst("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            log.warn("请求路径 {} 缺少有效的Authorization头", path);
            return unauthorized(exchange.getResponse());
        }
        
        // 提取JWT token
        String token = authHeader.substring(7);
        try {
            // 验证并解析JWT
            Claims claims = parseJwtToken(token);
            
            // 提取用户信息
            String userId = claims.getSubject();
            String username = claims.get("username", String.class);
            
            if (userId == null || userId.isEmpty()) {
                log.warn("JWT token中缺少用户ID");
                return unauthorized(exchange.getResponse());
            }
            
            // 将用户信息添加到请求头中，传递给下游服务
            ServerHttpRequest modifiedRequest = request.mutate()
                    .header("X-User-Id", userId)
                    .header("X-Username", username != null ? username : "")
                    .build();
            
            log.debug("JWT认证成功，用户ID: {}, 用户名: {}", userId, username);
            
            return chain.filter(exchange.mutate().request(modifiedRequest).build());
            
        } catch (Exception e) {
            log.error("JWT token验证失败: {}", e.getMessage());
            return unauthorized(exchange.getResponse());
        }
    }
    
    /**
     * 检查路径是否在免认证列表中
     */
    private boolean isPermitAllPath(String path) {
        return permitAllPaths.stream()
                .anyMatch(permitPath -> {
                    String cleanPath = permitPath.trim();
                    if (cleanPath.endsWith("/**")) {
                        String prefix = cleanPath.substring(0, cleanPath.length() - 3);
                        return path.startsWith(prefix);
                    } else {
                        return path.equals(cleanPath);
                    }
                });
    }
    
    /**
     * 解析JWT token
     */
    private Claims parseJwtToken(String token) {
        SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
        return Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }
    
    /**
     * 返回401未授权响应
     */
    private Mono<Void> unauthorized(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        
        String body = """
                {
                    "code": 401,
                    "success": false,
                    "message": "未授权访问，请先登录",
                    "timestamp": %d
                }
                """.formatted(System.currentTimeMillis());
        
        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8))));
    }
    
    @Override
    public int getOrder() {
        return -100; // 高优先级，确保在其他过滤器之前执行
    }
}
```

## 限流设计

### 限流过滤器
```java
@Component
public class RateLimitFilter implements GlobalFilter, Ordered {
    
    private static final Logger log = LoggerFactory.getLogger(RateLimitFilter.class);
    
    private final ReactiveStringRedisTemplate redisTemplate;
    
    @Value("${cloudvps.gateway.rate-limit.enabled:true}")
    private boolean rateLimitEnabled;
    
    @Value("${cloudvps.gateway.rate-limit.default-requests-per-second:100}")
    private int defaultRequestsPerSecond;
    
    private static final String RATE_LIMIT_KEY_PREFIX = "rate_limit:";
    private static final String USER_ID_HEADER = "X-User-Id";
    
    public RateLimitFilter(ReactiveStringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (!rateLimitEnabled) {
            return chain.filter(exchange);
        }
        
        ServerHttpRequest request = exchange.getRequest();
        String clientId = getClientId(request);
        String rateLimitKey = RATE_LIMIT_KEY_PREFIX + clientId;
        
        return checkRateLimit(rateLimitKey)
                .flatMap(allowed -> {
                    if (allowed) {
                        log.debug("客户端 {} 通过限流检查", clientId);
                        return chain.filter(exchange);
                    } else {
                        log.warn("客户端 {} 触发限流", clientId);
                        return rateLimitExceeded(exchange.getResponse());
                    }
                });
    }
    
    /**
     * 获取客户端标识
     */
    private String getClientId(ServerHttpRequest request) {
        // 优先使用用户ID（已认证用户）
        String userId = request.getHeaders().getFirst(USER_ID_HEADER);
        if (userId != null) {
            return "user:" + userId;
        }
        
        // 使用IP地址作为标识（未认证用户）
        String clientIp = getClientIp(request);
        return "ip:" + clientIp;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddress() != null ? 
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }
    
    /**
     * 检查限流 - 使用滑动窗口算法
     */
    private Mono<Boolean> checkRateLimit(String key) {
        long currentTime = System.currentTimeMillis();
        long windowStart = currentTime - 1000; // 1秒窗口
        
        return redisTemplate.opsForZSet()
                .removeRangeByScore(key, Range.closed(0.0, (double) windowStart))
                .then(redisTemplate.opsForZSet().count(key, Range.closed((double) windowStart, (double) currentTime)))
                .flatMap(currentCount -> {
                    if (currentCount < defaultRequestsPerSecond) {
                        return redisTemplate.opsForZSet()
                                .add(key, String.valueOf(currentTime), currentTime)
                                .then(redisTemplate.expire(key, Duration.ofSeconds(2)))
                                .thenReturn(true);
                    } else {
                        return Mono.just(false);
                    }
                })
                .onErrorReturn(true); // Redis错误时允许通过
    }
    
    /**
     * 返回限流响应
     */
    private Mono<Void> rateLimitExceeded(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        response.getHeaders().add("X-RateLimit-Limit", String.valueOf(defaultRequestsPerSecond));
        response.getHeaders().add("X-RateLimit-Remaining", "0");
        response.getHeaders().add("Retry-After", "1");
        
        String body = """
                {
                    "code": 429,
                    "success": false,
                    "message": "请求过于频繁，请稍后再试",
                    "timestamp": %d
                }
                """.formatted(System.currentTimeMillis());
        
        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8))));
    }
    
    @Override
    public int getOrder() {
        return -90; // 在JWT认证过滤器之后执行
    }
}
