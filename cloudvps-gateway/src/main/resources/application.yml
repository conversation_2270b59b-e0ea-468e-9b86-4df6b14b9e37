spring:
  application:
    name: cloudvps-gateway
  profiles:
    active: dev

  cloud:
    gateway:
      # 静态路由配置已移至动态路由配置类
      # 路由规则现在由 DynamicRouteConfig 根据路径前缀配置动态生成

      # 全局过滤器配置
      default-filters:
        - AddRequestHeader=X-Gateway, CloudVPS-Gateway
        - AddResponseHeader=X-Response-Time, ${timestamp}

      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: false

  redis:
    host: localhost
    port: 6379
    database: 4
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms



server:
  port: 8080

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.cloudvps: DEBUG
    org.springframework.cloud.gateway: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# CloudVPS 自定义配置
cloudvps:
  jwt:
    secret: cloudvps-unified-jwt-secret-key-for-all-services-2024
    expiration: 86400  # 24小时（秒）
    issuer: cloudvps

  gateway:
    # 统一路径前缀配置
    path-prefix:
      enabled: false  # 默认关闭，可通过配置文件或环境变量启用
      prefix: /api/v1  # 统一路径前缀
      keep-prefix: false  # 转发到后端服务时是否保留前缀
      exclude-patterns:  # 排除前缀的路径模式
        - /actuator/**
        - /health/**
        - /info/**

    # 限流配置
    rate-limit:
      enabled: true
      default-requests-per-second: 100
      burst-capacity: 200

    # 不需要认证的路径（注意：如果启用了路径前缀，这些路径也会受影响）
    permit-all-paths:
      - /system/auth/register
      - /system/auth/login
      - /order/products/**
      - /payment/webhooks/**
      - /payment/callbacks/**
      - /actuator/**

---
spring:
  config:
    activate:
      on-profile: dev

# 开发环境：不启用路径前缀，便于调试
cloudvps:
  gateway:
    path-prefix:
      enabled: false
      prefix: /api/v1

logging:
  level:
    root: INFO
    com.cloudvps: DEBUG

---
spring:
  config:
    activate:
      on-profile: prod

# 生产环境：启用路径前缀，提供统一的API入口
cloudvps:
  gateway:
    path-prefix:
      enabled: ${GATEWAY_PATH_PREFIX_ENABLED:true}
      prefix: ${GATEWAY_PATH_PREFIX:/api/v1}
      keep-prefix: ${GATEWAY_KEEP_PREFIX:false}

  jwt:
    secret: ${JWT_SECRET:cloudvps-unified-jwt-secret-key-for-all-services-2024}
    expiration: ${JWT_EXPIRATION:86400}
    issuer: cloudvps

logging:
  level:
    root: WARN
    com.cloudvps: INFO

# Resilience4j Circuit Breaker 配置
resilience4j:
  circuitbreaker:
    instances:
      system-service-cb:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
        minimum-number-of-calls: 5
      virtualization-service-cb:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
        minimum-number-of-calls: 5
      order-service-cb:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
        minimum-number-of-calls: 5
      payment-service-cb:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
        minimum-number-of-calls: 5
