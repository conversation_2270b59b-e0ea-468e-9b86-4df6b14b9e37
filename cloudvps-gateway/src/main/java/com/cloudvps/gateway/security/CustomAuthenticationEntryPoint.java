package com.cloudvps.gateway.security;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.server.ServerAuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义认证入口点
 * 
 * 处理认证失败的情况，避免弹出HTTP Basic Authentication对话框
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class CustomAuthenticationEntryPoint implements ServerAuthenticationEntryPoint {

    private static final Logger log = LoggerFactory.getLogger(CustomAuthenticationEntryPoint.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Mono<Void> commence(ServerWebExchange exchange, AuthenticationException ex) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        
        log.warn("Authentication failed for path: {}, error: {}", 
                request.getPath().value(), ex.getMessage());
        
        // 设置401状态码
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        
        // 根据请求类型返回不同格式的响应
        if (isApiRequest(request)) {
            return handleApiAuthenticationFailure(exchange, ex);
        } else {
            return handleBrowserAuthenticationFailure(exchange, ex);
        }
    }
    
    /**
     * 判断是否为API请求
     */
    private boolean isApiRequest(ServerHttpRequest request) {
        List<String> acceptHeaders = request.getHeaders().get("Accept");
        List<String> contentTypeHeaders = request.getHeaders().get("Content-Type");
        
        // 检查Accept头
        if (acceptHeaders != null) {
            for (String accept : acceptHeaders) {
                if (accept.contains("application/json")) {
                    return true;
                }
            }
        }
        
        // 检查Content-Type头
        if (contentTypeHeaders != null) {
            for (String contentType : contentTypeHeaders) {
                if (contentType.contains("application/json")) {
                    return true;
                }
            }
        }
        
        // 检查路径是否以API相关路径开头
        String path = request.getPath().value();
        if (path.startsWith("/api/") || path.startsWith("/system/") || 
            path.startsWith("/virtualization/") || path.startsWith("/order/") || 
            path.startsWith("/payment/")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 处理API认证失败，返回JSON响应
     */
    private Mono<Void> handleApiAuthenticationFailure(ServerWebExchange exchange, AuthenticationException ex) {
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("status", 401);
        errorResponse.put("error", "Unauthorized");
        errorResponse.put("message", "认证失败，请提供有效的访问令牌");
        errorResponse.put("path", exchange.getRequest().getPath().value());
        errorResponse.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        try {
            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            DataBuffer buffer = response.bufferFactory().wrap(jsonResponse.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException e) {
            log.error("Error serializing authentication error response", e);
            return response.setComplete();
        }
    }
    
    /**
     * 处理浏览器认证失败，返回HTML响应
     */
    private Mono<Void> handleBrowserAuthenticationFailure(ServerWebExchange exchange, AuthenticationException ex) {
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().setContentType(MediaType.TEXT_HTML);
        
        String htmlResponse = createHtml401Page(exchange.getRequest().getPath().value());
        DataBuffer buffer = response.bufferFactory().wrap(htmlResponse.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }
    
    /**
     * 创建HTML 401页面
     */
    private String createHtml401Page(String path) {
        return String.format("""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>401 - 认证失败</title>
                <style>
                    body { 
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                        margin: 0; 
                        padding: 40px; 
                        background: linear-gradient(135deg, #ff6b6b 0%%, #ee5a24 100%%);
                        min-height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .container { 
                        max-width: 600px; 
                        background: white; 
                        padding: 60px 40px; 
                        border-radius: 16px; 
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                        text-align: center;
                    }
                    .error-code { 
                        font-size: 120px; 
                        font-weight: bold; 
                        color: #e74c3c; 
                        margin: 0;
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                    }
                    .error-title { 
                        font-size: 32px; 
                        color: #2c3e50; 
                        margin: 20px 0;
                        font-weight: 300;
                    }
                    .error-message { 
                        font-size: 18px; 
                        color: #7f8c8d; 
                        margin: 30px 0;
                        line-height: 1.6;
                    }
                    .error-path { 
                        font-size: 14px; 
                        color: #95a5a6; 
                        background: #f8f9fa; 
                        padding: 15px; 
                        border-radius: 8px; 
                        font-family: 'Courier New', monospace;
                        border-left: 4px solid #e74c3c;
                        margin: 20px 0;
                    }
                    .login-link { 
                        display: inline-block; 
                        margin-top: 30px; 
                        padding: 15px 30px; 
                        background: linear-gradient(45deg, #e74c3c, #c0392b);
                        color: white; 
                        text-decoration: none; 
                        border-radius: 50px;
                        font-weight: 500;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
                    }
                    .login-link:hover { 
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
                    }
                    .icon {
                        font-size: 48px;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="icon">🔒</div>
                    <div class="error-code">401</div>
                    <div class="error-title">认证失败</div>
                    <div class="error-message">
                        您需要登录才能访问此资源。<br>
                        请提供有效的访问令牌或重新登录。
                    </div>
                    <div class="error-path">请求路径: %s</div>
                    <a href="/system/auth/login" class="login-link">🔑 前往登录</a>
                </div>
            </body>
            </html>
            """, path);
    }
}
