package com.cloudvps.gateway.security;

import com.cloudvps.gateway.util.ReactiveJwtUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;

/**
 * WebFlux响应式JWT认证过滤器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ReactiveJwtAuthenticationFilter implements WebFilter {

    private static final Logger log = LoggerFactory.getLogger(ReactiveJwtAuthenticationFilter.class);

    private final ReactiveJwtUtils jwtUtils;

    public ReactiveJwtAuthenticationFilter(ReactiveJwtUtils jwtUtils) {
        this.jwtUtils = jwtUtils;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 获取JWT token
        String jwt = getJwtFromRequest(request);
        
        if (StringUtils.hasText(jwt)) {
            return jwtUtils.validateToken(jwt)
                .flatMap(isValid -> {
                    if (isValid) {
                        return createAuthentication(jwt)
                            .flatMap(authentication -> {
                                // 设置用户信息到请求属性
                                return setUserInfoToRequest(exchange, jwt)
                                    .then(chain.filter(exchange)
                                        .contextWrite(ReactiveSecurityContextHolder.withAuthentication(authentication)));
                            });
                    } else {
                        log.warn("JWT token validation failed");
                        return chain.filter(exchange);
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("JWT authentication error", throwable);
                    return chain.filter(exchange);
                });
        }
        
        return chain.filter(exchange);
    }

    /**
     * 从请求中获取JWT令牌
     */
    private String getJwtFromRequest(ServerHttpRequest request) {
        String bearerToken = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 创建认证对象
     */
    private Mono<Authentication> createAuthentication(String jwt) {
        return jwtUtils.getUsernameFromToken(jwt)
            .zipWith(jwtUtils.getUserIdFromToken(jwt))
            .map(tuple -> {
                String username = tuple.getT1();
                Long userId = tuple.getT2();
                
                // 创建基本权限列表
                List<SimpleGrantedAuthority> authorities = getAuthoritiesFromToken(jwt);
                
                // 创建认证对象
                UsernamePasswordAuthenticationToken authentication = 
                    new UsernamePasswordAuthenticationToken(username, null, authorities);
                
                return authentication;
            });
    }

    /**
     * 设置用户信息到请求属性
     */
    private Mono<Void> setUserInfoToRequest(ServerWebExchange exchange, String jwt) {
        return jwtUtils.getUserIdFromToken(jwt)
            .zipWith(jwtUtils.getUsernameFromToken(jwt))
            .doOnNext(tuple -> {
                Long userId = tuple.getT1();
                String username = tuple.getT2();
                
                // 添加用户信息到请求头，传递给下游服务
                ServerHttpRequest mutatedRequest = exchange.getRequest().mutate()
                    .header("X-User-Id", userId.toString())
                    .header("X-Username", username)
                    .build();
                
                exchange.mutate().request(mutatedRequest).build();
            })
            .then();
    }

    /**
     * 从JWT令牌中获取权限列表
     */
    private List<SimpleGrantedAuthority> getAuthoritiesFromToken(String jwt) {
        // TODO: 这里可以扩展为从JWT中解析权限或从system-service获取权限
        // 目前先返回基本的认证权限
        return Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"));
    }
}
