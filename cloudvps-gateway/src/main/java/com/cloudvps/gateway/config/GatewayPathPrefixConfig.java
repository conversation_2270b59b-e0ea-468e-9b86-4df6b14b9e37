package com.cloudvps.gateway.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 网关路径前缀配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "cloudvps.gateway.path-prefix")
public class GatewayPathPrefixConfig {

    /**
     * 是否启用统一路径前缀
     */
    private boolean enabled = false;

    /**
     * 统一路径前缀，如 /api/v1
     */
    private String prefix = "/api/v1";

    /**
     * 是否在转发到后端服务时保留前缀
     */
    private boolean keepPrefix = false;

    /**
     * 需要排除前缀的路径模式
     */
    private String[] excludePatterns = {
        "/actuator/**",
        "/health/**",
        "/info/**"
    };

    // Getter 和 Setter 方法
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public boolean isKeepPrefix() {
        return keepPrefix;
    }

    public void setKeepPrefix(boolean keepPrefix) {
        this.keepPrefix = keepPrefix;
    }

    public String[] getExcludePatterns() {
        return excludePatterns;
    }

    public void setExcludePatterns(String[] excludePatterns) {
        this.excludePatterns = excludePatterns;
    }

    /**
     * 获取完整的路径模式（包含前缀）
     */
    public String getFullPathPattern(String servicePath) {
        if (!enabled || prefix == null || prefix.trim().isEmpty()) {
            return servicePath;
        }
        
        String cleanPrefix = prefix.trim();
        if (!cleanPrefix.startsWith("/")) {
            cleanPrefix = "/" + cleanPrefix;
        }
        if (cleanPrefix.endsWith("/")) {
            cleanPrefix = cleanPrefix.substring(0, cleanPrefix.length() - 1);
        }
        
        return cleanPrefix + servicePath;
    }

    /**
     * 获取需要剥离的前缀层级数
     */
    public int getStripPrefixCount() {
        if (!enabled || keepPrefix || prefix == null || prefix.trim().isEmpty()) {
            return 0;
        }
        
        String cleanPrefix = prefix.trim();
        if (cleanPrefix.equals("/")) {
            return 0;
        }
        
        // 计算前缀中的路径段数量
        String[] segments = cleanPrefix.split("/");
        int count = 0;
        for (String segment : segments) {
            if (!segment.isEmpty()) {
                count++;
            }
        }
        return count;
    }

    /**
     * 检查路径是否需要排除前缀
     */
    public boolean isExcluded(String path) {
        if (excludePatterns == null || excludePatterns.length == 0) {
            return false;
        }
        
        for (String pattern : excludePatterns) {
            if (pathMatches(path, pattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 简单的路径匹配（支持 ** 通配符）
     */
    private boolean pathMatches(String path, String pattern) {
        if (pattern.endsWith("/**")) {
            String prefix = pattern.substring(0, pattern.length() - 3);
            return path.startsWith(prefix);
        }
        return path.equals(pattern);
    }
}
