package com.cloudvps.gateway.config;

import com.cloudvps.gateway.security.CustomAuthenticationEntryPoint;
import com.cloudvps.gateway.security.ReactiveJwtAuthenticationFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.SecurityWebFiltersOrder;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.context.NoOpServerSecurityContextRepository;

/**
 * Gateway WebFlux安全配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {

    private final ReactiveJwtAuthenticationFilter jwtAuthenticationFilter;
    private final CustomAuthenticationEntryPoint customAuthenticationEntryPoint;

    public SecurityConfig(ReactiveJwtAuthenticationFilter jwtAuthenticationFilter,
                         CustomAuthenticationEntryPoint customAuthenticationEntryPoint) {
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
        this.customAuthenticationEntryPoint = customAuthenticationEntryPoint;
    }

    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        return http
            // 禁用CSRF，因为我们使用JWT
            .csrf(ServerHttpSecurity.CsrfSpec::disable)
            
            // 禁用表单登录
            .formLogin(ServerHttpSecurity.FormLoginSpec::disable)
            
            // 禁用HTTP Basic认证
            .httpBasic(ServerHttpSecurity.HttpBasicSpec::disable)
            
            // 设置无状态会话管理
            .securityContextRepository(NoOpServerSecurityContextRepository.getInstance())
            
            // 配置路径访问权限
            .authorizeExchange(exchanges -> exchanges
                // 允许健康检查和监控端点
                .pathMatchers("/actuator/**").permitAll()
                .pathMatchers("/health/**").permitAll()
                .pathMatchers("/info/**").permitAll()

                // 允许错误处理和降级端点
                .pathMatchers("/fallback/**").permitAll()
                .pathMatchers("/error/**").permitAll()

                // 允许认证相关端点
                .pathMatchers(HttpMethod.POST, "/system/auth/register").permitAll()
                .pathMatchers(HttpMethod.POST, "/system/auth/login").permitAll()
                .pathMatchers(HttpMethod.POST, "/system/auth/refresh").permitAll()

                // 允许公开的产品信息
                .pathMatchers(HttpMethod.GET, "/order/products/**").permitAll()

                // 允许支付回调和webhook
                .pathMatchers("/payment/webhooks/**").permitAll()
                .pathMatchers("/payment/callbacks/**").permitAll()

                // 允许OPTIONS请求（CORS预检）
                .pathMatchers(HttpMethod.OPTIONS, "/**").permitAll()

                // 已配置的微服务路由需要认证
                .pathMatchers("/system/**").authenticated()
                .pathMatchers("/virtualization/**").authenticated()
                .pathMatchers("/order/**").authenticated()
                .pathMatchers("/payment/**").authenticated()

                // 其他所有未匹配的路径允许通过，让错误处理器处理
                .anyExchange().permitAll()
            )

            // 配置异常处理
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(customAuthenticationEntryPoint)
            )

            // 添加JWT认证过滤器
            .addFilterBefore(jwtAuthenticationFilter, SecurityWebFiltersOrder.AUTHENTICATION)

            .build();
    }
}
