package com.cloudvps.gateway.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

/**
 * 网关配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class GatewayConfig {

    private static final Logger log = LoggerFactory.getLogger(GatewayConfig.class);

    /**
     * 配置路由
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
                // 系统服务路由
                .route("system-service", r -> r
                        .path("/system/**")
                        .uri("http://localhost:8081"))

                // 虚拟化服务路由
                .route("virtualization-service", r -> r
                        .path("/virtualization/**")
                        .uri("http://localhost:8082"))

                // 订单服务路由
                .route("order-service", r -> r
                        .path("/order/**")
                        .uri("http://localhost:8083"))

                // 支付服务路由
                .route("payment-service", r -> r
                        .path("/payment/**")
                        .uri("http://localhost:8084"))

                // Actuator健康检查路由
                .route("actuator-health", r -> r
                        .path("/actuator/**")
                        .uri("http://localhost:8080"))
                
                .build();
    }

    /**
     * 配置CORS
     */
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowCredentials(false);
        corsConfig.addAllowedOriginPattern("*");
        corsConfig.addAllowedMethod("*");
        corsConfig.addAllowedHeader("*");
        corsConfig.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        return new CorsWebFilter(source);
    }
}
