package com.cloudvps.gateway.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.FilterDefinition;
import org.springframework.cloud.gateway.handler.predicate.PredicateDefinition;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.*;

/**
 * 动态路由配置
 * 根据路径前缀配置动态生成路由规则
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class DynamicRouteConfig {

    private static final Logger log = LoggerFactory.getLogger(DynamicRouteConfig.class);

    private final GatewayPathPrefixConfig pathPrefixConfig;

    public DynamicRouteConfig(GatewayPathPrefixConfig pathPrefixConfig) {
        this.pathPrefixConfig = pathPrefixConfig;
    }

    /**
     * 服务路由配置
     */
    private static final Map<String, ServiceRouteInfo> SERVICE_ROUTES = Map.of(
        "system-service", new ServiceRouteInfo("system-service", "http://localhost:8081", "/system/**"),
        "virtualization-service", new ServiceRouteInfo("virtualization-service", "http://localhost:8082", "/virtualization/**"),
        "order-service", new ServiceRouteInfo("order-service", "http://localhost:8083", "/order/**"),
        "payment-service", new ServiceRouteInfo("payment-service", "http://localhost:8084", "/payment/**")
    );

    @Bean
    public RouteDefinitionRepository customRouteDefinitionRepository() {
        return new RouteDefinitionRepository() {
            @Override
            public Flux<RouteDefinition> getRouteDefinitions() {
                List<RouteDefinition> routes = new ArrayList<>();
                
                // 为每个服务生成路由定义
                SERVICE_ROUTES.forEach((serviceId, serviceInfo) -> {
                    RouteDefinition route = createRouteDefinition(serviceId, serviceInfo);
                    routes.add(route);
                    log.info("Created dynamic route: {} -> {} with path: {}", 
                        serviceId, serviceInfo.getUri(), route.getPredicates().get(0).getArgs().get("pattern"));
                });
                
                return Flux.fromIterable(routes);
            }

            @Override
            public Mono<Void> save(Mono<RouteDefinition> route) {
                return Mono.empty();
            }

            @Override
            public Mono<Void> delete(Mono<String> routeId) {
                return Mono.empty();
            }
        };
    }

    /**
     * 创建路由定义
     */
    private RouteDefinition createRouteDefinition(String serviceId, ServiceRouteInfo serviceInfo) {
        RouteDefinition route = new RouteDefinition();
        route.setId(serviceId);
        route.setUri(URI.create(serviceInfo.getUri()));

        // 设置路径谓词
        PredicateDefinition pathPredicate = new PredicateDefinition();
        pathPredicate.setName("Path");
        
        String pathPattern = serviceInfo.getPath();
        if (pathPrefixConfig.isEnabled() && !pathPrefixConfig.isExcluded(pathPattern)) {
            pathPattern = pathPrefixConfig.getFullPathPattern(pathPattern);
        }
        
        pathPredicate.addArg("pattern", pathPattern);
        route.setPredicates(List.of(pathPredicate));

        // 设置过滤器
        List<FilterDefinition> filters = new ArrayList<>();
        
        // StripPrefix 过滤器
        if (pathPrefixConfig.isEnabled() && !pathPrefixConfig.isKeepPrefix()) {
            int stripCount = pathPrefixConfig.getStripPrefixCount();
            if (stripCount > 0) {
                FilterDefinition stripPrefixFilter = new FilterDefinition();
                stripPrefixFilter.setName("StripPrefix");
                stripPrefixFilter.addArg("parts", String.valueOf(stripCount));
                filters.add(stripPrefixFilter);
            }
        }
        
        // 熔断器过滤器
        FilterDefinition circuitBreakerFilter = new FilterDefinition();
        circuitBreakerFilter.setName("CircuitBreaker");
        circuitBreakerFilter.addArg("name", serviceId + "-cb");
        circuitBreakerFilter.addArg("fallbackUri", "forward:/fallback/" + serviceInfo.getServiceName());
        filters.add(circuitBreakerFilter);

        route.setFilters(filters);
        return route;
    }

    /**
     * 服务路由信息
     */
    private static class ServiceRouteInfo {
        private final String serviceName;
        private final String uri;
        private final String path;

        public ServiceRouteInfo(String serviceName, String uri, String path) {
            this.serviceName = serviceName;
            this.uri = uri;
            this.path = path;
        }

        public String getServiceName() {
            return serviceName.replace("-service", "");
        }

        public String getUri() {
            return uri;
        }

        public String getPath() {
            return path;
        }
    }
}
