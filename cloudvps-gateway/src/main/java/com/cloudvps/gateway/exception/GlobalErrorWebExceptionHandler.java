package com.cloudvps.gateway.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 全局错误处理器
 * 
 * 处理网关层面的各种错误，包括404、500等
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Order(-1) // 确保优先级高于默认的错误处理器
public class GlobalErrorWebExceptionHandler implements ErrorWebExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalErrorWebExceptionHandler.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        
        // 记录错误日志
        log.error("Gateway error occurred for path: {}, error: {}", 
                request.getPath().value(), ex.getMessage(), ex);
        
        // 确定HTTP状态码
        HttpStatus status = determineHttpStatus(ex);
        response.setStatusCode(status);
        
        // 根据请求类型返回不同格式的响应
        if (isApiRequest(request)) {
            return handleApiError(exchange, status, ex);
        } else {
            return handleBrowserError(exchange, status, ex);
        }
    }
    
    /**
     * 判断是否为API请求
     */
    private boolean isApiRequest(ServerHttpRequest request) {
        List<String> acceptHeaders = request.getHeaders().get("Accept");
        List<String> contentTypeHeaders = request.getHeaders().get("Content-Type");
        
        // 检查Accept头
        if (acceptHeaders != null) {
            for (String accept : acceptHeaders) {
                if (accept.contains("application/json")) {
                    return true;
                }
            }
        }
        
        // 检查Content-Type头
        if (contentTypeHeaders != null) {
            for (String contentType : contentTypeHeaders) {
                if (contentType.contains("application/json")) {
                    return true;
                }
            }
        }
        
        // 检查路径是否以/api开头
        String path = request.getPath().value();
        if (path.startsWith("/api/") || path.startsWith("/system/") || 
            path.startsWith("/virtualization/") || path.startsWith("/order/") || 
            path.startsWith("/payment/")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 处理API错误，返回JSON响应
     */
    private Mono<Void> handleApiError(ServerWebExchange exchange, HttpStatus status, Throwable ex) {
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("status", status.value());
        errorResponse.put("error", status.getReasonPhrase());
        errorResponse.put("message", getErrorMessage(status, ex));
        errorResponse.put("path", exchange.getRequest().getPath().value());
        errorResponse.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        try {
            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            DataBuffer buffer = response.bufferFactory().wrap(jsonResponse.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException e) {
            log.error("Error serializing error response", e);
            return response.setComplete();
        }
    }
    
    /**
     * 处理浏览器错误，返回HTML响应
     */
    private Mono<Void> handleBrowserError(ServerWebExchange exchange, HttpStatus status, Throwable ex) {
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().setContentType(MediaType.TEXT_HTML);
        
        String htmlResponse = createHtmlErrorPage(status, ex, exchange.getRequest().getPath().value());
        DataBuffer buffer = response.bufferFactory().wrap(htmlResponse.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }
    
    /**
     * 确定HTTP状态码
     */
    private HttpStatus determineHttpStatus(Throwable ex) {
        if (ex instanceof ResponseStatusException) {
            return HttpStatus.valueOf(((ResponseStatusException) ex).getStatusCode().value());
        }

        // 检查异常消息中是否包含404相关信息
        String message = ex.getMessage();
        if (message != null && (message.contains("404") || message.contains("Not Found") ||
                               message.contains("No matching handler") ||
                               message.contains("No static resource") ||
                               ex.getClass().getSimpleName().contains("NotFound"))) {
            return HttpStatus.NOT_FOUND;
        }

        return HttpStatus.INTERNAL_SERVER_ERROR;
    }
    
    /**
     * 获取错误消息
     */
    private String getErrorMessage(HttpStatus status, Throwable ex) {
        switch (status) {
            case NOT_FOUND:
                return "请求的路由不存在";
            case INTERNAL_SERVER_ERROR:
                return "服务器内部错误";
            case SERVICE_UNAVAILABLE:
                return "服务暂时不可用";
            case BAD_GATEWAY:
                return "网关错误";
            case GATEWAY_TIMEOUT:
                return "网关超时";
            default:
                return ex.getMessage() != null ? ex.getMessage() : "未知错误";
        }
    }
    
    /**
     * 创建HTML错误页面
     */
    private String createHtmlErrorPage(HttpStatus status, Throwable ex, String path) {
        return String.format("""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>%d - %s</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 40px; background-color: #f5f5f5; }
                    .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    .error-code { font-size: 72px; font-weight: bold; color: #e74c3c; margin: 0; }
                    .error-title { font-size: 24px; color: #2c3e50; margin: 10px 0; }
                    .error-message { font-size: 16px; color: #7f8c8d; margin: 20px 0; }
                    .error-path { font-size: 14px; color: #95a5a6; background: #ecf0f1; padding: 10px; border-radius: 4px; font-family: monospace; }
                    .back-link { display: inline-block; margin-top: 20px; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 4px; }
                    .back-link:hover { background: #2980b9; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="error-code">%d</div>
                    <div class="error-title">%s</div>
                    <div class="error-message">%s</div>
                    <div class="error-path">请求路径: %s</div>
                    <a href="/" class="back-link">返回首页</a>
                </div>
            </body>
            </html>
            """, 
            status.value(), status.getReasonPhrase(),
            status.value(), status.getReasonPhrase(),
            getErrorMessage(status, ex), path);
    }
}
