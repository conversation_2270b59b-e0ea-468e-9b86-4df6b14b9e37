package com.cloudvps.gateway.filter;

import cn.hutool.core.util.StrUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * JWT认证过滤器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JwtAuthenticationFilter implements GlobalFilter, Ordered {

    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    @Value("${cloudvps.jwt.secret}")
    private String jwtSecret;

    @Value("#{'${cloudvps.gateway.permit-all-paths:/system/auth/register,/system/auth/login,/order/products/**,/payment/webhooks/**,/payment/callbacks/**,/actuator/**}'.split(',')}")
    private List<String> permitAllPaths;

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String USER_ID_HEADER = "X-User-Id";
    private static final String USERNAME_HEADER = "X-Username";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        log.debug("Gateway处理请求: {}", path);

        // 检查是否为不需要认证的路径
        if (isPermitAllPath(path)) {
            log.debug("路径 {} 无需认证，直接放行", path);
            return chain.filter(exchange);
        }

        // 获取Authorization头
        String authHeader = request.getHeaders().getFirst(AUTHORIZATION_HEADER);
        if (StrUtil.isBlank(authHeader) || !authHeader.startsWith(BEARER_PREFIX)) {
            log.warn("请求路径 {} 缺少有效的Authorization头", path);
            return unauthorized(exchange.getResponse());
        }

        // 提取JWT token
        String token = authHeader.substring(BEARER_PREFIX.length());

        try {
            // 验证并解析JWT
            Claims claims = parseJwtToken(token);
            
            // 提取用户信息
            String userId = claims.getSubject();
            String username = claims.get("username", String.class);
            
            if (StrUtil.isBlank(userId)) {
                log.warn("JWT token中缺少用户ID");
                return unauthorized(exchange.getResponse());
            }

            // 将用户信息添加到请求头中，传递给下游服务
            ServerHttpRequest modifiedRequest = request.mutate()
                    .header(USER_ID_HEADER, userId)
                    .header(USERNAME_HEADER, username != null ? username : "")
                    .build();

            log.debug("JWT认证成功，用户ID: {}, 用户名: {}", userId, username);
            
            return chain.filter(exchange.mutate().request(modifiedRequest).build());
            
        } catch (Exception e) {
            log.error("JWT token验证失败: {}", e.getMessage());
            return unauthorized(exchange.getResponse());
        }
    }

    /**
     * 检查路径是否在免认证列表中
     */
    private boolean isPermitAllPath(String path) {
        return permitAllPaths.stream()
                .anyMatch(permitPath -> {
                    // 移除可能的空格
                    String cleanPath = permitPath.trim();
                    if (cleanPath.endsWith("/**")) {
                        // 通配符匹配
                        String prefix = cleanPath.substring(0, cleanPath.length() - 3);
                        return path.startsWith(prefix);
                    } else {
                        // 精确匹配
                        return path.equals(cleanPath);
                    }
                });
    }

    /**
     * 解析JWT token
     */
    private Claims parseJwtToken(String token) {
        SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
        return Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    /**
     * 返回401未授权响应
     */
    private Mono<Void> unauthorized(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        
        String body = """
                {
                    "code": 401,
                    "success": false,
                    "message": "未授权访问，请先登录",
                    "timestamp": %d
                }
                """.formatted(System.currentTimeMillis());
        
        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8))));
    }

    @Override
    public int getOrder() {
        // 设置较高优先级，确保在其他过滤器之前执行
        return -100;
    }
}
