package com.cloudvps.gateway.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.data.domain.Range;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

/**
 * 限流过滤器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class RateLimitFilter implements GlobalFilter, Ordered {

    private static final Logger log = LoggerFactory.getLogger(RateLimitFilter.class);

    private final ReactiveStringRedisTemplate redisTemplate;

    @Value("${cloudvps.gateway.rate-limit.enabled:true}")
    private boolean rateLimitEnabled;

    @Value("${cloudvps.gateway.rate-limit.default-requests-per-second:100}")
    private int defaultRequestsPerSecond;

    @Value("${cloudvps.gateway.rate-limit.burst-capacity:200}")
    private int burstCapacity;

    private static final String RATE_LIMIT_KEY_PREFIX = "rate_limit:";
    private static final String USER_ID_HEADER = "X-User-Id";

    public RateLimitFilter(ReactiveStringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (!rateLimitEnabled) {
            return chain.filter(exchange);
        }

        ServerHttpRequest request = exchange.getRequest();
        String clientId = getClientId(request);
        String rateLimitKey = RATE_LIMIT_KEY_PREFIX + clientId;

        return checkRateLimit(rateLimitKey)
                .flatMap(allowed -> {
                    if (allowed) {
                        log.debug("客户端 {} 通过限流检查", clientId);
                        return chain.filter(exchange);
                    } else {
                        log.warn("客户端 {} 触发限流", clientId);
                        return rateLimitExceeded(exchange.getResponse());
                    }
                });
    }

    /**
     * 获取客户端标识
     */
    private String getClientId(ServerHttpRequest request) {
        // 优先使用用户ID（已认证用户）
        String userId = request.getHeaders().getFirst(USER_ID_HEADER);
        if (userId != null) {
            return "user:" + userId;
        }

        // 使用IP地址作为标识（未认证用户）
        String clientIp = getClientIp(request);
        return "ip:" + clientIp;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddress() != null ? 
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    /**
     * 检查限流
     * 使用滑动窗口算法
     */
    private Mono<Boolean> checkRateLimit(String key) {
        long currentTime = System.currentTimeMillis();
        long windowStart = currentTime - 1000; // 1秒窗口

        return redisTemplate.opsForZSet()
                // 移除窗口外的记录
                .removeRangeByScore(key, Range.closed(0.0, (double) windowStart))
                .then(redisTemplate.opsForZSet().count(key, Range.closed((double) windowStart, (double) currentTime)))
                .flatMap(currentCount -> {
                    if (currentCount < defaultRequestsPerSecond) {
                        // 添加当前请求记录
                        return redisTemplate.opsForZSet()
                                .add(key, String.valueOf(currentTime), currentTime)
                                .then(redisTemplate.expire(key, Duration.ofSeconds(2)))
                                .thenReturn(true);
                    } else {
                        return Mono.just(false);
                    }
                })
                .onErrorReturn(true); // Redis错误时允许通过
    }

    /**
     * 返回限流响应
     */
    private Mono<Void> rateLimitExceeded(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        response.getHeaders().add("X-RateLimit-Limit", String.valueOf(defaultRequestsPerSecond));
        response.getHeaders().add("X-RateLimit-Remaining", "0");
        response.getHeaders().add("Retry-After", "1");

        String body = """
                {
                    "code": 429,
                    "success": false,
                    "message": "请求过于频繁，请稍后再试",
                    "timestamp": %d
                }
                """.formatted(System.currentTimeMillis());

        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8))));
    }

    @Override
    public int getOrder() {
        // 在JWT认证过滤器之后执行
        return -90;
    }
}
