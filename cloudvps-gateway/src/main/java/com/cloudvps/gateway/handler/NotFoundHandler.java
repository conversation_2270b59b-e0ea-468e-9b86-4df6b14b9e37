package com.cloudvps.gateway.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 404错误处理器
 *
 * 注意：这个类现在主要用于GlobalErrorWebExceptionHandler
 * 不再作为RouterFunction使用，避免拦截Spring Cloud Gateway的路由
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class NotFoundHandler {

    private static final Logger log = LoggerFactory.getLogger(NotFoundHandler.class);

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理404错误
     */
    public Mono<Void> handle(ServerWebExchange exchange) {
        String path = exchange.getRequest().getPath().value();
        log.warn("404 Not Found: {}", path);

        // 根据请求类型返回不同格式的响应
        if (isApiRequest(exchange.getRequest())) {
            return handleApiNotFound(exchange);
        } else {
            return handleBrowserNotFound(exchange);
        }
    }
    
    /**
     * 判断是否为API请求
     */
    private boolean isApiRequest(ServerHttpRequest request) {
        List<String> acceptHeaders = request.getHeaders().get("Accept");
        List<String> contentTypeHeaders = request.getHeaders().get("Content-Type");

        // 检查Accept头
        if (acceptHeaders != null) {
            for (String accept : acceptHeaders) {
                if (accept.contains("application/json")) {
                    return true;
                }
            }
        }

        // 检查Content-Type头
        if (contentTypeHeaders != null) {
            for (String contentType : contentTypeHeaders) {
                if (contentType.contains("application/json")) {
                    return true;
                }
            }
        }

        // 检查路径是否以API相关路径开头
        String path = request.getPath().value();
        if (path.startsWith("/api/") || path.startsWith("/system/") ||
            path.startsWith("/virtualization/") || path.startsWith("/order/") ||
            path.startsWith("/payment/")) {
            return true;
        }

        return false;
    }
    
    /**
     * 处理API 404错误，返回JSON响应
     */
    private Mono<Void> handleApiNotFound(ServerWebExchange exchange) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.NOT_FOUND);
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("status", 404);
        errorResponse.put("error", "Not Found");
        errorResponse.put("message", "请求的路由不存在");
        errorResponse.put("path", exchange.getRequest().getPath().value());
        errorResponse.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        try {
            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            DataBuffer buffer = response.bufferFactory().wrap(jsonResponse.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException e) {
            log.error("Error serializing 404 error response", e);
            return response.setComplete();
        }
    }

    /**
     * 处理浏览器404错误，返回HTML响应
     */
    private Mono<Void> handleBrowserNotFound(ServerWebExchange exchange) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.NOT_FOUND);
        response.getHeaders().setContentType(MediaType.TEXT_HTML);

        String htmlResponse = createHtml404Page(exchange.getRequest().getPath().value());
        DataBuffer buffer = response.bufferFactory().wrap(htmlResponse.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }
    
    /**
     * 创建HTML 404页面
     */
    private String createHtml404Page(String path) {
        return String.format("""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>404 - 页面不存在</title>
                <style>
                    body { 
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                        margin: 0; 
                        padding: 40px; 
                        background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%);
                        min-height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .container { 
                        max-width: 600px; 
                        background: white; 
                        padding: 60px 40px; 
                        border-radius: 16px; 
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                        text-align: center;
                    }
                    .error-code { 
                        font-size: 120px; 
                        font-weight: bold; 
                        color: #e74c3c; 
                        margin: 0;
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                    }
                    .error-title { 
                        font-size: 32px; 
                        color: #2c3e50; 
                        margin: 20px 0;
                        font-weight: 300;
                    }
                    .error-message { 
                        font-size: 18px; 
                        color: #7f8c8d; 
                        margin: 30px 0;
                        line-height: 1.6;
                    }
                    .error-path { 
                        font-size: 14px; 
                        color: #95a5a6; 
                        background: #f8f9fa; 
                        padding: 15px; 
                        border-radius: 8px; 
                        font-family: 'Courier New', monospace;
                        border-left: 4px solid #e74c3c;
                        margin: 20px 0;
                    }
                    .back-link { 
                        display: inline-block; 
                        margin-top: 30px; 
                        padding: 15px 30px; 
                        background: linear-gradient(45deg, #3498db, #2980b9);
                        color: white; 
                        text-decoration: none; 
                        border-radius: 50px;
                        font-weight: 500;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
                    }
                    .back-link:hover { 
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
                    }
                    .icon {
                        font-size: 48px;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="icon">🔍</div>
                    <div class="error-code">404</div>
                    <div class="error-title">页面不存在</div>
                    <div class="error-message">
                        抱歉，您访问的页面不存在或已被移除。<br>
                        请检查URL是否正确，或返回首页继续浏览。
                    </div>
                    <div class="error-path">请求路径: %s</div>
                    <a href="/" class="back-link">🏠 返回首页</a>
                </div>
            </body>
            </html>
            """, path);
    }
}
