package com.cloudvps.gateway.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

/**
 * WebFlux响应式JWT工具类
 * 与common-security的JwtUtils保持逻辑一致
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ReactiveJwtUtils {

    private static final Logger log = LoggerFactory.getLogger(ReactiveJwtUtils.class);
    
    @Value("${cloudvps.jwt.secret:cloudvps-unified-jwt-secret-key-for-all-services-2024}")
    private String secret;
    
    @Value("${cloudvps.jwt.expiration:86400}")
    private Long expiration;
    
    @Value("${cloudvps.jwt.issuer:cloudvps}")
    private String issuer;
    
    /**
     * 生成JWT令牌
     */
    public Mono<String> generateToken(Long userId, String username, Map<String, Object> claims) {
        return Mono.fromCallable(() -> {
            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + expiration * 1000);

            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));

            JwtBuilder builder = Jwts.builder()
                .issuer(issuer)
                .subject(userId.toString())
                .claim("username", username)
                .issuedAt(now)
                .expiration(expiryDate);

            // 添加自定义声明
            if (claims != null) {
                builder.claims(claims);
            }

            return builder.signWith(key).compact();
        });
    }
    
    /**
     * 生成简单的JWT令牌
     */
    public Mono<String> generateToken(Long userId, String username) {
        return generateToken(userId, username, null);
    }
    
    /**
     * 验证JWT令牌
     */
    public Mono<Boolean> validateToken(String token) {
        return Mono.fromCallable(() -> {
            try {
                SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
                Jwts.parser()
                    .verifyWith(key)
                    .requireIssuer(issuer)
                    .build()
                    .parseSignedClaims(token);
                return true;
            } catch (JwtException e) {
                log.warn("JWT令牌验证失败: {}", e.getMessage());
                return false;
            }
        });
    }

    /**
     * 解析JWT令牌
     */
    public Mono<Claims> parseToken(String token) {
        return Mono.fromCallable(() -> {
            try {
                SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
                return Jwts.parser()
                    .verifyWith(key)
                    .requireIssuer(issuer)
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
            } catch (JwtException e) {
                log.warn("JWT令牌解析失败: {}", e.getMessage());
                throw new IllegalArgumentException("无效的JWT令牌", e);
            }
        });
    }
    
    /**
     * 从令牌中获取用户ID
     */
    public Mono<Long> getUserIdFromToken(String token) {
        return parseToken(token)
            .map(claims -> Long.valueOf(claims.getSubject()));
    }

    /**
     * 从令牌中获取用户名
     */
    public Mono<String> getUsernameFromToken(String token) {
        return parseToken(token)
            .map(claims -> claims.get("username", String.class));
    }

    /**
     * 从令牌中获取声明值
     */
    public Mono<String> getClaimFromToken(String token, String claimName) {
        return parseToken(token)
            .map(claims -> claims.get(claimName, String.class));
    }

    /**
     * 检查令牌是否过期
     */
    public Mono<Boolean> isTokenExpired(String token) {
        return parseToken(token)
            .map(claims -> claims.getExpiration().before(new Date()))
            .onErrorReturn(true);
    }

    /**
     * 获取令牌过期时间
     */
    public Mono<Date> getExpirationDateFromToken(String token) {
        return parseToken(token)
            .map(Claims::getExpiration);
    }

    /**
     * 刷新令牌
     */
    public Mono<String> refreshToken(String token) {
        return parseToken(token)
            .flatMap(claims -> {
                Long userId = Long.valueOf(claims.getSubject());
                String username = claims.get("username", String.class);

                // 提取其他声明
                Map<String, Object> customClaims = Map.of(
                    "role", claims.get("role", String.class),
                    "status", claims.get("status", String.class)
                );

                return generateToken(userId, username, customClaims);
            });
    }

    /**
     * 同步版本的验证方法（用于兼容性）
     */
    public boolean validateTokenSync(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
            Jwts.parser()
                .verifyWith(key)
                .requireIssuer(issuer)
                .build()
                .parseSignedClaims(token);
            return true;
        } catch (JwtException e) {
            log.warn("JWT令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 同步版本的用户ID获取方法
     */
    public Long getUserIdFromTokenSync(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
            Claims claims = Jwts.parser()
                .verifyWith(key)
                .requireIssuer(issuer)
                .build()
                .parseSignedClaims(token)
                .getPayload();
            return Long.valueOf(claims.getSubject());
        } catch (JwtException e) {
            log.warn("JWT令牌解析失败: {}", e.getMessage());
            throw new IllegalArgumentException("无效的JWT令牌", e);
        }
    }

    /**
     * 同步版本的用户名获取方法
     */
    public String getUsernameFromTokenSync(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
            Claims claims = Jwts.parser()
                .verifyWith(key)
                .requireIssuer(issuer)
                .build()
                .parseSignedClaims(token)
                .getPayload();
            return claims.get("username", String.class);
        } catch (JwtException e) {
            log.warn("JWT令牌解析失败: {}", e.getMessage());
            throw new IllegalArgumentException("无效的JWT令牌", e);
        }
    }
}
