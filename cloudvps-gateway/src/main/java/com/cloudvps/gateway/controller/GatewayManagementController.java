package com.cloudvps.gateway.controller;

import com.cloudvps.gateway.config.GatewayPathPrefixConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 网关管理控制器
 * 提供动态配置路径前缀和路由刷新功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/gateway/management")
public class GatewayManagementController {

    private static final Logger log = LoggerFactory.getLogger(GatewayManagementController.class);

    private final GatewayPathPrefixConfig pathPrefixConfig;
    private final ApplicationEventPublisher eventPublisher;

    public GatewayManagementController(GatewayPathPrefixConfig pathPrefixConfig,
                                     ApplicationEventPublisher eventPublisher) {
        this.pathPrefixConfig = pathPrefixConfig;
        this.eventPublisher = eventPublisher;
    }

    /**
     * 获取当前路径前缀配置
     */
    @GetMapping("/path-prefix")
    public Map<String, Object> getPathPrefixConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", pathPrefixConfig.isEnabled());
        config.put("prefix", pathPrefixConfig.getPrefix());
        config.put("keepPrefix", pathPrefixConfig.isKeepPrefix());
        config.put("excludePatterns", pathPrefixConfig.getExcludePatterns());
        config.put("stripPrefixCount", pathPrefixConfig.getStripPrefixCount());
        
        log.info("Retrieved path prefix configuration: {}", config);
        return config;
    }

    /**
     * 更新路径前缀配置
     */
    @PostMapping("/path-prefix")
    public Map<String, Object> updatePathPrefixConfig(@RequestBody Map<String, Object> configUpdate) {
        log.info("Updating path prefix configuration: {}", configUpdate);
        
        // 更新配置
        if (configUpdate.containsKey("enabled")) {
            pathPrefixConfig.setEnabled((Boolean) configUpdate.get("enabled"));
        }
        if (configUpdate.containsKey("prefix")) {
            pathPrefixConfig.setPrefix((String) configUpdate.get("prefix"));
        }
        if (configUpdate.containsKey("keepPrefix")) {
            pathPrefixConfig.setKeepPrefix((Boolean) configUpdate.get("keepPrefix"));
        }
        
        // 刷新路由
        refreshRoutes();
        
        return getPathPrefixConfig();
    }

    /**
     * 启用路径前缀
     */
    @PostMapping("/path-prefix/enable")
    public Map<String, Object> enablePathPrefix(@RequestParam(defaultValue = "/api/v1") String prefix) {
        log.info("Enabling path prefix with: {}", prefix);
        
        pathPrefixConfig.setEnabled(true);
        pathPrefixConfig.setPrefix(prefix);
        
        refreshRoutes();
        
        return getPathPrefixConfig();
    }

    /**
     * 禁用路径前缀
     */
    @PostMapping("/path-prefix/disable")
    public Map<String, Object> disablePathPrefix() {
        log.info("Disabling path prefix");
        
        pathPrefixConfig.setEnabled(false);
        
        refreshRoutes();
        
        return getPathPrefixConfig();
    }

    /**
     * 刷新路由配置
     */
    @PostMapping("/refresh-routes")
    public Map<String, String> refreshRoutes() {
        log.info("Refreshing gateway routes");
        
        // 发布路由刷新事件
        eventPublisher.publishEvent(new RefreshRoutesEvent(this));
        
        Map<String, String> result = new HashMap<>();
        result.put("status", "success");
        result.put("message", "Routes refreshed successfully");
        
        return result;
    }

    /**
     * 获取当前路由信息
     */
    @GetMapping("/routes")
    public Map<String, Object> getCurrentRoutes() {
        Map<String, Object> routeInfo = new HashMap<>();
        
        // 模拟路由信息（实际应该从 RouteDefinitionRepository 获取）
        Map<String, String> routes = new HashMap<>();
        String prefix = pathPrefixConfig.isEnabled() ? pathPrefixConfig.getPrefix() : "";
        
        routes.put("system-service", prefix + "/system/**");
        routes.put("virtualization-service", prefix + "/virtualization/**");
        routes.put("order-service", prefix + "/order/**");
        routes.put("payment-service", prefix + "/payment/**");
        
        routeInfo.put("routes", routes);
        routeInfo.put("pathPrefixEnabled", pathPrefixConfig.isEnabled());
        routeInfo.put("pathPrefix", pathPrefixConfig.getPrefix());
        
        return routeInfo;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, String> health() {
        Map<String, String> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "cloudvps-gateway");
        health.put("pathPrefixEnabled", String.valueOf(pathPrefixConfig.isEnabled()));
        
        return health;
    }
}
