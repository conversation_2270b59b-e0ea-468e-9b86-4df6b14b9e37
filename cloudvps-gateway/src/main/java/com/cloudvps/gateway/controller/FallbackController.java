package com.cloudvps.gateway.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 熔断降级控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/fallback")
public class FallbackController {

    private static final Logger log = LoggerFactory.getLogger(FallbackController.class);

    /**
     * 系统服务降级
     */
    @GetMapping("/system")
    @PostMapping("/system")
    public ResponseEntity<Map<String, Object>> systemFallback() {
        log.warn("系统服务不可用，触发熔断降级");
        return createFallbackResponse("系统服务暂时不可用，请稍后重试");
    }

    /**
     * 虚拟化服务降级
     */
    @GetMapping("/virtualization")
    @PostMapping("/virtualization")
    public ResponseEntity<Map<String, Object>> virtualizationFallback() {
        log.warn("虚拟化服务不可用，触发熔断降级");
        return createFallbackResponse("虚拟化服务暂时不可用，请稍后重试");
    }

    /**
     * 订单服务降级
     */
    @GetMapping("/order")
    @PostMapping("/order")
    public ResponseEntity<Map<String, Object>> orderFallback() {
        log.warn("订单服务不可用，触发熔断降级");
        return createFallbackResponse("订单服务暂时不可用，请稍后重试");
    }

    /**
     * 支付服务降级
     */
    @GetMapping("/payment")
    @PostMapping("/payment")
    public ResponseEntity<Map<String, Object>> paymentFallback() {
        log.warn("支付服务不可用，触发熔断降级");
        return createFallbackResponse("支付服务暂时不可用，请稍后重试");
    }

    /**
     * 创建降级响应
     */
    private ResponseEntity<Map<String, Object>> createFallbackResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 503);
        response.put("success", false);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }
}
