<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cloudvps</groupId>
    <artifactId>cloudvps-platform</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>CloudVPS Platform</name>
    <description>CloudVPS 云虚拟化平台 - 聚合器项目</description>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <!-- Spring Boot 版本 -->
        <spring-boot.version>3.5.3</spring-boot.version>
        <spring-cloud.version>2025.0.0</spring-cloud.version>

        <lombok.version>1.18.38</lombok.version>
        <mapstruct.version>1.6.2</mapstruct.version>

        <!-- Maven 插件版本 -->
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.2.5</maven-surefire-plugin.version>
    </properties>

    <modules>
        <!-- 公共模块 -->
        <module>cloudvps-common</module>
        
        <!-- 父级依赖管理 -->
        <module>cloudvps-parent</module>
        
        <!-- 网关服务 -->
        <module>cloudvps-gateway</module>
        
        <!-- 系统服务域 -->
        <module>cloudvps-system</module>
        
        <!-- 虚拟化服务域 -->
        <module>cloudvps-virtualization</module>
        
        <!-- 订单服务域 -->
        <module>cloudvps-order</module>
        
        <!-- 支付服务域 -->
        <module>cloudvps-payment</module>
    </modules>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>cloudvps-parent</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Maven 编译插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <annotationProcessorPaths>
<!--                            <path>-->
<!--                                <groupId>org.springframework.boot</groupId>-->
<!--                                <artifactId>spring-boot-configuration-processor</artifactId>-->
<!--                                <version>${spring-boot.version}</version>-->
<!--                            </path>-->
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                        <!-- 编译参数写在 arg 内，解决 Spring Boot 3.2 的 Parameter Name Discovery 问题 -->
                        <debug>false</debug>
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>

                <!-- Maven Surefire 插件 (单元测试) -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <includes>
                            <include>**/*Test.java</include>
                            <include>**/*Tests.java</include>
                        </includes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>oss</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
