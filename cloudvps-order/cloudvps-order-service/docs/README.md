# CloudVPS 订单服务模块

## 模块概述

订单服务是 CloudVPS 平台的核心业务模块，负责处理用户的虚拟机订购、产品目录管理、订单生命周期管理等功能。作为业务流程的中枢，订单服务连接用户需求、产品配置、支付处理和资源分配，实现完整的云服务订购流程。

## 技术架构

### 技术栈
- **框架**: Spring Boot 3.2.1
- **Java 版本**: JDK 21
- **数据库**: PostgreSQL 16+
- **缓存**: Redis 7.2+
- **消息队列**: 预留 RabbitMQ 集成
- **ORM**: Spring Data JPA + Hibernate
- **API 文档**: Swagger/OpenAPI 3
- **构建工具**: Maven 3.9+

### 架构设计模式
```
┌─────────────────────────────────────────┐
│              Controller 层               │
│ OrderController │ ProductController     │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│               Service 层                │
│  OrderService │ ProductService │ ...    │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│             Repository 层               │
│OrderRepository │ ProductRepository │... │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│              Database 层                │
│           PostgreSQL 数据库             │
└─────────────────────────────────────────┘
```

## 核心功能模块

### 1. 订单管理模块
- **订单创建**: 用户选择产品配置创建订单
- **订单验证**: 库存检查、用户权限验证、价格计算
- **订单支付**: 与支付服务集成处理订单支付
- **订单履行**: 与虚拟化服务集成分配资源
- **订单查询**: 订单列表、详情查询、状态跟踪

### 2. 产品目录模块
- **产品管理**: 虚拟机产品规格定义和管理
- **价格策略**: 灵活的定价模型和折扣策略
- **库存管理**: 产品库存跟踪和预留机制
- **产品分类**: 按用途、配置等维度分类管理
- **产品推荐**: 基于用户需求的产品推荐

### 3. 订单状态管理
- **状态流转**: 完整的订单状态生命周期管理
- **状态通知**: 订单状态变更通知机制
- **异常处理**: 订单异常情况的处理和恢复
- **状态查询**: 实时订单状态查询接口

### 4. 业务流程编排
- **订单工作流**: 订单处理的完整业务流程
- **服务集成**: 与系统服务、支付服务、虚拟化服务集成
- **事务管理**: 分布式事务处理和补偿机制
- **流程监控**: 业务流程执行监控和告警

### 5. 订单统计分析
- **销售统计**: 订单量、销售额等业务指标统计
- **产品分析**: 热门产品、用户偏好分析
- **趋势分析**: 订单趋势和预测分析
- **报表生成**: 各类业务报表生成

## 数据库设计

### 核心数据表

#### 订单相关表
```sql
-- 订单主表
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(32) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    final_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    payment_status VARCHAR(20) NOT NULL DEFAULT 'UNPAID',
    fulfillment_status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    remark TEXT,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    paid_time TIMESTAMP,
    completed_time TIMESTAMP
);

-- 订单项表
CREATE TABLE order_items (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT NOT NULL REFERENCES orders(id),
    product_id BIGINT NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_spec TEXT,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    vm_config TEXT, -- JSON格式的虚拟机配置
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 订单状态历史表
CREATE TABLE order_status_history (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT NOT NULL REFERENCES orders(id),
    from_status VARCHAR(20),
    to_status VARCHAR(20) NOT NULL,
    operator_id BIGINT,
    operator_name VARCHAR(100),
    remark TEXT,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 产品相关表
```sql
-- 产品表
CREATE TABLE products (
    id BIGSERIAL PRIMARY KEY,
    product_code VARCHAR(50) NOT NULL UNIQUE,
    product_name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id BIGINT,
    cpu_cores INTEGER NOT NULL,
    memory_gb INTEGER NOT NULL,
    disk_gb INTEGER NOT NULL,
    bandwidth_mbps INTEGER,
    os_type VARCHAR(50),
    base_price DECIMAL(10,2) NOT NULL,
    billing_cycle VARCHAR(20) NOT NULL DEFAULT 'MONTHLY',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    sort_order INTEGER DEFAULT 0,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 产品分类表
CREATE TABLE product_categories (
    id BIGSERIAL PRIMARY KEY,
    category_code VARCHAR(50) NOT NULL UNIQUE,
    category_name VARCHAR(100) NOT NULL,
    parent_id BIGINT REFERENCES product_categories(id),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 产品价格表
CREATE TABLE product_pricing (
    id BIGSERIAL PRIMARY KEY,
    product_id BIGINT NOT NULL REFERENCES products(id),
    billing_cycle VARCHAR(20) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    discount_price DECIMAL(10,2),
    effective_date DATE NOT NULL,
    expiry_date DATE,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 产品库存表
CREATE TABLE product_inventory (
    id BIGSERIAL PRIMARY KEY,
    product_id BIGINT NOT NULL REFERENCES products(id),
    node_name VARCHAR(100),
    total_stock INTEGER NOT NULL DEFAULT 0,
    available_stock INTEGER NOT NULL DEFAULT 0,
    reserved_stock INTEGER NOT NULL DEFAULT 0,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 数据库索引优化
```sql
-- 订单表索引
CREATE INDEX idx_orders_order_no ON orders(order_no);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_time ON orders(created_time);
CREATE INDEX idx_orders_user_status ON orders(user_id, status);

-- 订单项表索引
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- 产品表索引
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_code ON products(product_code);

-- 库存表索引
CREATE UNIQUE INDEX idx_product_inventory_product_node ON product_inventory(product_id, node_name);
```

## 订单状态机设计

### 订单状态流转
```
DRAFT (草稿) → PENDING (待支付) → PAID (已支付) → PROCESSING (处理中) → COMPLETED (已完成)
    ↓              ↓                ↓              ↓
CANCELLED      CANCELLED        CANCELLED      FAILED (失败)
```

### 状态说明
- **DRAFT**: 订单草稿，用户正在配置
- **PENDING**: 订单提交，等待支付
- **PAID**: 订单已支付，等待处理
- **PROCESSING**: 正在分配资源和创建虚拟机
- **COMPLETED**: 订单完成，资源已分配
- **CANCELLED**: 订单取消
- **FAILED**: 订单处理失败

## API 接口设计

### 订单管理接口
```java
@RestController
@RequestMapping("/order/orders")
@PreAuthorize("hasAuthority('ORDER_VIEW')")
public class OrderController {
    
    /**
     * 创建订单
     */
    @PostMapping
    @PreAuthorize("hasAuthority('ORDER_CREATE')")
    public ApiResponse<OrderResponse> createOrder(@RequestBody CreateOrderRequest request) {
        OrderResponse order = orderService.createOrder(request);
        return ApiResponse.success(order);
    }
    
    /**
     * 获取订单列表
     */
    @GetMapping
    public ApiResponse<PageResponse<OrderResponse>> getOrders(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long userId) {
        
        PageResponse<OrderResponse> orders = orderService.getOrders(page, size, status, userId);
        return ApiResponse.success(orders);
    }
    
    /**
     * 获取订单详情
     */
    @GetMapping("/{orderId}")
    public ApiResponse<OrderDetailResponse> getOrderDetail(@PathVariable Long orderId) {
        OrderDetailResponse order = orderService.getOrderDetail(orderId);
        return ApiResponse.success(order);
    }
    
    /**
     * 取消订单
     */
    @PostMapping("/{orderId}/cancel")
    @PreAuthorize("hasAuthority('ORDER_CANCEL')")
    public ApiResponse<Void> cancelOrder(@PathVariable Long orderId, 
                                       @RequestBody CancelOrderRequest request) {
        orderService.cancelOrder(orderId, request.getReason());
        return ApiResponse.success();
    }
    
    /**
     * 订单支付
     */
    @PostMapping("/{orderId}/pay")
    public ApiResponse<PaymentResponse> payOrder(@PathVariable Long orderId,
                                               @RequestBody PayOrderRequest request) {
        PaymentResponse payment = orderService.payOrder(orderId, request);
        return ApiResponse.success(payment);
    }
}
```

### 产品目录接口
```java
@RestController
@RequestMapping("/order/products")
public class ProductController {
    
    /**
     * 获取产品列表
     */
    @GetMapping
    public ApiResponse<PageResponse<ProductResponse>> getProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String keyword) {
        
        PageResponse<ProductResponse> products = productService
                .getProducts(page, size, categoryId, keyword);
        return ApiResponse.success(products);
    }
    
    /**
     * 获取产品详情
     */
    @GetMapping("/{productId}")
    public ApiResponse<ProductDetailResponse> getProductDetail(@PathVariable Long productId) {
        ProductDetailResponse product = productService.getProductDetail(productId);
        return ApiResponse.success(product);
    }
    
    /**
     * 获取产品分类
     */
    @GetMapping("/categories")
    public ApiResponse<List<ProductCategoryResponse>> getProductCategories() {
        List<ProductCategoryResponse> categories = productService.getProductCategories();
        return ApiResponse.success(categories);
    }
    
    /**
     * 获取产品价格
     */
    @GetMapping("/{productId}/pricing")
    public ApiResponse<List<ProductPricingResponse>> getProductPricing(@PathVariable Long productId) {
        List<ProductPricingResponse> pricing = productService.getProductPricing(productId);
        return ApiResponse.success(pricing);
    }
    
    /**
     * 检查产品库存
     */
    @GetMapping("/{productId}/inventory")
    public ApiResponse<ProductInventoryResponse> checkProductInventory(
            @PathVariable Long productId,
            @RequestParam(required = false) String nodeName) {
        
        ProductInventoryResponse inventory = productService
                .checkProductInventory(productId, nodeName);
        return ApiResponse.success(inventory);
    }
}
```

## 业务流程设计

### 订单创建流程
```java
@Service
@Transactional
public class OrderService {
    
    /**
     * 创建订单的完整流程
     */
    public OrderResponse createOrder(CreateOrderRequest request) {
        // 1. 验证用户权限
        validateUserPermission(request.getUserId());
        
        // 2. 验证产品信息
        Product product = validateProduct(request.getProductId());
        
        // 3. 检查库存
        checkInventory(request.getProductId(), request.getQuantity());
        
        // 4. 计算价格
        OrderPricing pricing = calculateOrderPricing(request);
        
        // 5. 创建订单
        Order order = createOrderEntity(request, product, pricing);
        
        // 6. 预留库存
        reserveInventory(request.getProductId(), request.getQuantity());
        
        // 7. 记录状态变更
        recordStatusChange(order.getId(), null, OrderStatus.DRAFT);
        
        // 8. 发送订单创建事件
        publishOrderCreatedEvent(order);
        
        return convertToResponse(order);
    }
    
    /**
     * 订单支付流程
     */
    public PaymentResponse payOrder(Long orderId, PayOrderRequest request) {
        Order order = getOrderById(orderId);
        
        // 1. 验证订单状态
        validateOrderForPayment(order);
        
        // 2. 调用支付服务
        PaymentResponse payment = paymentServiceClient.createPayment(
                PaymentRequest.builder()
                        .orderId(orderId)
                        .amount(order.getFinalAmount())
                        .paymentMethod(request.getPaymentMethod())
                        .build()
        );
        
        // 3. 更新订单状态
        updateOrderStatus(orderId, OrderStatus.PENDING);
        
        return payment;
    }
    
    /**
     * 订单履行流程
     */
    @Async
    public void fulfillOrder(Long orderId) {
        Order order = getOrderById(orderId);
        
        try {
            // 1. 更新订单状态为处理中
            updateOrderStatus(orderId, OrderStatus.PROCESSING);
            
            // 2. 调用虚拟化服务创建虚拟机
            VmCreateResponse vmResponse = virtualizationServiceClient.createVm(
                    VmCreateRequest.builder()
                            .orderId(orderId)
                            .productSpec(order.getProductSpec())
                            .build()
            );
            
            // 3. 更新订单完成状态
            updateOrderStatus(orderId, OrderStatus.COMPLETED);
            order.setCompletedTime(LocalDateTime.now());
            orderRepository.save(order);
            
            // 4. 发送订单完成通知
            publishOrderCompletedEvent(order, vmResponse);
            
        } catch (Exception e) {
            // 处理失败，更新订单状态
            updateOrderStatus(orderId, OrderStatus.FAILED);
            
            // 释放预留库存
            releaseReservedInventory(order.getProductId(), order.getQuantity());
            
            // 发送失败通知
            publishOrderFailedEvent(order, e.getMessage());
        }
    }
}
```

### 服务集成设计
```java
@Component
public class OrderIntegrationService {
    
    private final SystemServiceClient systemServiceClient;
    private final PaymentServiceClient paymentServiceClient;
    private final VirtualizationServiceClient virtualizationServiceClient;
    
    /**
     * 验证用户信息
     */
    public UserResponse validateUser(Long userId) {
        return systemServiceClient.getUser(userId);
    }
    
    /**
     * 检查用户余额
     */
    public AccountResponse checkUserBalance(Long userId) {
        return systemServiceClient.getUserAccount(userId);
    }
    
    /**
     * 创建支付订单
     */
    public PaymentResponse createPayment(PaymentRequest request) {
        return paymentServiceClient.createPayment(request);
    }
    
    /**
     * 创建虚拟机
     */
    public VmCreateResponse createVirtualMachine(VmCreateRequest request) {
        return virtualizationServiceClient.createVm(request);
    }
}
```

## 缓存策略

### Redis 缓存配置
```java
@Configuration
public class OrderCacheConfig {
    
    @Bean
    public CacheManager orderCacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(15))
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 产品信息缓存 - 1小时
        cacheConfigurations.put("products", config.entryTtl(Duration.ofHours(1)));
        
        // 产品价格缓存 - 30分钟
        cacheConfigurations.put("product_pricing", config.entryTtl(Duration.ofMinutes(30)));
        
        // 库存信息缓存 - 5分钟
        cacheConfigurations.put("product_inventory", config.entryTtl(Duration.ofMinutes(5)));
        
        // 订单信息缓存 - 15分钟
        cacheConfigurations.put("orders", config.entryTtl(Duration.ofMinutes(15)));
        
        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
```

### 缓存使用示例
```java
@Service
public class ProductService {
    
    /**
     * 缓存产品信息
     */
    @Cacheable(value = "products", key = "#productId")
    public ProductResponse getProductById(Long productId) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new ProductNotFoundException("产品不存在"));
        return convertToResponse(product);
    }
    
    /**
     * 缓存产品价格
     */
    @Cacheable(value = "product_pricing", key = "#productId + '_' + #billingCycle")
    public ProductPricingResponse getProductPricing(Long productId, String billingCycle) {
        return productPricingRepository.findByProductIdAndBillingCycle(productId, billingCycle)
                .map(this::convertToPricingResponse)
                .orElse(null);
    }
    
    /**
     * 更新产品时清除缓存
     */
    @CacheEvict(value = {"products", "product_pricing"}, key = "#productId")
    public ProductResponse updateProduct(Long productId, UpdateProductRequest request) {
        // 更新产品逻辑
        return getProductById(productId);
    }
}
```

## 配置管理

### 应用配置
```yaml
# application.yml
spring:
  application:
    name: cloudvps-order-service
  
  datasource:
    url: ***********************************************
    username: ${DB_USERNAME:cloudvps}
    password: ${DB_PASSWORD:cloudvps123}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: ${DDL_AUTO:update}
    show-sql: ${SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DB:2}

# 订单配置
cloudvps:
  order:
    # 订单超时配置
    timeout:
      payment: ${ORDER_PAYMENT_TIMEOUT:1800}  # 30分钟
      processing: ${ORDER_PROCESSING_TIMEOUT:3600}  # 1小时
    
    # 库存配置
    inventory:
      reserve-timeout: ${INVENTORY_RESERVE_TIMEOUT:1800}  # 30分钟
      auto-release: ${INVENTORY_AUTO_RELEASE:true}
    
    # 通知配置
    notification:
      enabled: ${ORDER_NOTIFICATION_ENABLED:true}
      channels: ${ORDER_NOTIFICATION_CHANNELS:email,sms}

# 服务集成配置
integration:
  system-service:
    url: ${SYSTEM_SERVICE_URL:http://localhost:8081}
    timeout: 30000
  payment-service:
    url: ${PAYMENT_SERVICE_URL:http://localhost:8084}
    timeout: 30000
  virtualization-service:
    url: ${VIRTUALIZATION_SERVICE_URL:http://localhost:8082}
    timeout: 60000

# 服务配置
server:
  port: 8083

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

## 监控和运维

### 业务监控指标
```java
@Component
public class OrderMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter orderCreatedCounter;
    private final Counter orderPaidCounter;
    private final Counter orderCompletedCounter;
    private final Timer orderProcessingTimer;
    
    public OrderMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // 订单创建计数器
        this.orderCreatedCounter = Counter.builder("cloudvps.order.created")
                .description("Total number of orders created")
                .register(meterRegistry);
        
        // 订单支付计数器
        this.orderPaidCounter = Counter.builder("cloudvps.order.paid")
                .description("Total number of orders paid")
                .register(meterRegistry);
        
        // 订单完成计数器
        this.orderCompletedCounter = Counter.builder("cloudvps.order.completed")
                .description("Total number of orders completed")
                .register(meterRegistry);
        
        // 订单处理时间
        this.orderProcessingTimer = Timer.builder("cloudvps.order.processing.time")
                .description("Order processing time")
                .register(meterRegistry);
    }
    
    public void incrementOrderCreated() {
        orderCreatedCounter.increment();
    }
    
    public void incrementOrderPaid() {
        orderPaidCounter.increment();
    }
    
    public void incrementOrderCompleted() {
        orderCompletedCounter.increment();
    }
    
    public Timer.Sample startOrderProcessing() {
        return Timer.start(meterRegistry);
    }
}
```

### 健康检查
```java
@Component
public class OrderHealthIndicator implements HealthIndicator {
    
    private final OrderService orderService;
    private final ProductService productService;
    
    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        try {
            // 检查数据库连接
            long totalOrders = orderService.getTotalOrderCount();
            long totalProducts = productService.getTotalProductCount();
            
            // 检查待处理订单数量
            long pendingOrders = orderService.getPendingOrderCount();
            
            builder.up()
                    .withDetail("total_orders", totalOrders)
                    .withDetail("total_products", totalProducts)
                    .withDetail("pending_orders", pendingOrders)
                    .withDetail("database_status", "connected");
                    
        } catch (Exception e) {
            builder.down().withException(e);
        }
        
        return builder.build();
    }
}
```

## 未来扩展计划

### 功能扩展
- **订单模板**: 常用配置模板化
- **批量订单**: 批量虚拟机订购
- **订单调度**: 智能订单调度和资源分配
- **价格策略**: 动态定价和促销活动
- **订单分析**: 深度业务分析和预测

### 技术优化
- **事件驱动**: 基于事件的异步处理
- **工作流引擎**: 复杂业务流程编排
- **分布式事务**: Saga 模式事务管理
- **性能优化**: 查询优化和缓存策略

### 集成扩展
- **消息队列**: RabbitMQ 异步消息处理
- **搜索引擎**: Elasticsearch 订单搜索
- **数据分析**: 大数据分析平台集成
- **第三方集成**: 外部系统集成接口
