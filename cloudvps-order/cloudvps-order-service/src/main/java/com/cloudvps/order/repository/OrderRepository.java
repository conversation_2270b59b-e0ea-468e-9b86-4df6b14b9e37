package com.cloudvps.order.repository;

import com.cloudvps.order.entity.Order;
import com.cloudvps.order.api.enums.OrderStatus;
import com.cloudvps.order.api.enums.PaymentStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 订单Repository接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    
    /**
     * 根据订单号查找订单
     */
    Optional<Order> findByOrderNo(String orderNo);
    
    /**
     * 根据用户ID分页查询订单
     */
    Page<Order> findByUserIdOrderByCreatedTimeDesc(Long userId, Pageable pageable);
    
    /**
     * 根据用户ID和订单状态分页查询订单
     */
    Page<Order> findByUserIdAndStatusOrderByCreatedTimeDesc(Long userId, OrderStatus status, Pageable pageable);
    
    /**
     * 根据订单状态查询订单
     */
    List<Order> findByStatus(OrderStatus status);
    
    /**
     * 根据支付状态查询订单
     */
    List<Order> findByPaymentStatus(PaymentStatus paymentStatus);
    
    /**
     * 查询过期未支付的订单
     */
    @Query("SELECT o FROM Order o WHERE o.status = :status AND o.expiredAt < :expiredTime")
    List<Order> findExpiredOrders(@Param("status") OrderStatus status, @Param("expiredTime") LocalDateTime expiredTime);
    
    /**
     * 统计用户订单数量
     */
    long countByUserId(Long userId);
    
    /**
     * 统计用户指定状态的订单数量
     */
    long countByUserIdAndStatus(Long userId, OrderStatus status);
    
    /**
     * 查询用户最近的订单
     */
    @Query("SELECT o FROM Order o WHERE o.userId = :userId ORDER BY o.createdTime DESC")
    List<Order> findRecentOrdersByUserId(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 根据时间范围查询订单
     */
    @Query("SELECT o FROM Order o WHERE o.createdTime BETWEEN :startTime AND :endTime")
    List<Order> findOrdersByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                     @Param("endTime") LocalDateTime endTime);
    
    /**
     * 检查订单号是否存在
     */
    boolean existsByOrderNo(String orderNo);
}
