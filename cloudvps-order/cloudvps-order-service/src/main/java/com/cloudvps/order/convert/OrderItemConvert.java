package com.cloudvps.order.convert;

import com.cloudvps.order.api.dto.request.OrderItemCreateRequest;
import com.cloudvps.order.api.dto.response.OrderItemResponse;
import com.cloudvps.order.entity.OrderItem;
import org.mapstruct.*;

/**
 * 订单项对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface OrderItemConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "orderId", ignore = true) // 需要在Service层设置
    @Mapping(target = "totalPrice", expression = "java(request.getUnitPrice().multiply(java.math.BigDecimal.valueOf(request.getQuantity())))")
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    OrderItem toEntity(OrderItemCreateRequest request);
    
    /**
     * 实体转响应
     */
    OrderItemResponse toResponse(OrderItem orderItem);
}
