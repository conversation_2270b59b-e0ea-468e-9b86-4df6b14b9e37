package com.cloudvps.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.order.api.dto.request.ProductCreateRequest;
import com.cloudvps.order.api.dto.request.ProductQueryRequest;
import com.cloudvps.order.api.dto.request.ProductUpdateRequest;
import com.cloudvps.order.api.dto.response.ProductResponse;
import com.cloudvps.order.api.enums.ProductType;
import com.cloudvps.order.convert.ProductConvert;
import com.cloudvps.order.entity.Product;
import com.cloudvps.order.mapper.ProductDataMapper;
import com.cloudvps.order.service.ProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductServiceImpl implements ProductService {
    
    private final ProductDataMapper productDataMapper;
    private final ProductConvert productConvert;
    
    @Override
    public IPage<ProductResponse> queryProducts(ProductQueryRequest request) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (request.getCategoryId() != null) {
            queryWrapper.eq(Product::getCategoryId, request.getCategoryId());
        }
        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like(Product::getName, request.getName());
        }
        if (StringUtils.hasText(request.getCode())) {
            queryWrapper.like(Product::getCode, request.getCode());
        }
        if (request.getProductType() != null) {
            queryWrapper.eq(Product::getProductType, request.getProductType());
        }
        if (request.getMinPrice() != null) {
            queryWrapper.ge(Product::getPriceMonthly, request.getMinPrice());
        }
        if (request.getMaxPrice() != null) {
            queryWrapper.le(Product::getPriceMonthly, request.getMaxPrice());
        }
        if (request.getEnabled() != null) {
            queryWrapper.eq(Product::getEnabled, request.getEnabled());
        }
        if (request.getCpuCores() != null) {
            queryWrapper.eq(Product::getCpuCores, request.getCpuCores());
        }
        if (request.getMemoryMb() != null) {
            queryWrapper.eq(Product::getMemoryMb, request.getMemoryMb());
        }
        if (request.getDiskGb() != null) {
            queryWrapper.eq(Product::getDiskGb, request.getDiskGb());
        }
        if (StringUtils.hasText(request.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like(Product::getName, request.getKeyword())
                .or()
                .like(Product::getCode, request.getKeyword())
                .or()
                .like(Product::getDescription, request.getKeyword())
            );
        }
        
        // 排序
        queryWrapper.orderByAsc(Product::getSortOrder)
                   .orderByDesc(Product::getCreatedTime);
        
        // 分页查询
        Page<Product> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<Product> productPage = productDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return productPage.convert(productConvert::toResponse);
    }
    
    @Override
    public ProductResponse findById(Long id) {
        Product product = productDataMapper.selectById(id);
        if (product == null) {
            throw new BusinessException("产品不存在");
        }
        return productConvert.toResponse(product);
    }
    
    @Override
    public ProductResponse findByCode(String code) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getCode, code);
        
        Product product = productDataMapper.selectOne(queryWrapper);
        if (product == null) {
            throw new BusinessException("产品不存在");
        }
        return productConvert.toResponse(product);
    }
    
    @Override
    @Transactional
    public ProductResponse createProduct(ProductCreateRequest request) {
        // 检查产品代码是否已存在
        if (existsByCode(request.getCode())) {
            throw new BusinessException("产品代码已存在");
        }
        
        Product product = productConvert.toEntity(request);
        productDataMapper.insert(product);
        
        log.info("创建产品成功: code={}, name={}", 
                product.getCode(), product.getName());
        return productConvert.toResponse(product);
    }
    
    @Override
    @Transactional
    public ProductResponse updateProduct(Long id, ProductUpdateRequest request) {
        Product existingProduct = productDataMapper.selectById(id);
        if (existingProduct == null) {
            throw new BusinessException("产品不存在");
        }
        
        // 更新字段
        productConvert.updateFromRequest(existingProduct, request);
        productDataMapper.updateById(existingProduct);
        
        log.info("更新产品成功: productId={}, code={}", 
                id, existingProduct.getCode());
        return productConvert.toResponse(existingProduct);
    }
    
    @Override
    @Transactional
    public void deleteProduct(Long id) {
        Product product = productDataMapper.selectById(id);
        if (product == null) {
            throw new BusinessException("产品不存在");
        }
        
        // 删除产品
        productDataMapper.deleteById(id);
        
        log.info("删除产品成功: productId={}, code={}", 
                id, product.getCode());
    }
    
    @Override
    @Transactional
    public void batchDeleteProducts(List<Long> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            return;
        }
        
        // 批量删除产品
        productDataMapper.deleteBatchIds(productIds);
        
        log.info("批量删除产品成功: productIds={}", productIds);
    }
    
    @Override
    public List<ProductResponse> findByCategoryId(Long categoryId) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getCategoryId, categoryId)
                   .orderByAsc(Product::getSortOrder)
                   .orderByDesc(Product::getCreatedTime);
        
        List<Product> products = productDataMapper.selectList(queryWrapper);
        return products.stream()
                      .map(productConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    public List<ProductResponse> findByProductType(ProductType productType) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getProductType, productType)
                   .orderByAsc(Product::getSortOrder)
                   .orderByDesc(Product::getCreatedTime);
        
        List<Product> products = productDataMapper.selectList(queryWrapper);
        return products.stream()
                      .map(productConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    public List<ProductResponse> findEnabledProducts() {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getEnabled, true)
                   .orderByAsc(Product::getSortOrder)
                   .orderByDesc(Product::getCreatedTime);
        
        List<Product> products = productDataMapper.selectList(queryWrapper);
        return products.stream()
                      .map(productConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    public List<ProductResponse> findAvailableProducts() {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getEnabled, true)
                   .and(wrapper -> wrapper
                       .eq(Product::getStockQuantity, -1)
                       .or()
                       .gt(Product::getStockQuantity, 0)
                   )
                   .orderByAsc(Product::getSortOrder)
                   .orderByDesc(Product::getCreatedTime);
        
        List<Product> products = productDataMapper.selectList(queryWrapper);
        return products.stream()
                      .map(productConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public ProductResponse updateProductEnabled(Long id, Boolean enabled) {
        Product product = productDataMapper.selectById(id);
        if (product == null) {
            throw new BusinessException("产品不存在");
        }
        
        product.setEnabled(enabled);
        productDataMapper.updateById(product);
        
        log.info("更新产品启用状态成功: productId={}, enabled={}", id, enabled);
        return productConvert.toResponse(product);
    }
    
    @Override
    @Transactional
    public void reduceStock(Long id, Integer quantity) {
        Product product = productDataMapper.selectById(id);
        if (product == null) {
            throw new BusinessException("产品不存在");
        }
        
        if (product.getStockQuantity() != -1) {
            if (product.getStockQuantity() < quantity) {
                throw new BusinessException("库存不足");
            }
            product.setStockQuantity(product.getStockQuantity() - quantity);
        }
        
        product.setSoldQuantity(product.getSoldQuantity() + quantity);
        productDataMapper.updateById(product);
        
        log.debug("减少产品库存成功: productId={}, quantity={}", id, quantity);
    }
    
    @Override
    @Transactional
    public void increaseStock(Long id, Integer quantity) {
        Product product = productDataMapper.selectById(id);
        if (product == null) {
            throw new BusinessException("产品不存在");
        }
        
        if (product.getStockQuantity() != -1) {
            product.setStockQuantity(product.getStockQuantity() + quantity);
        }
        
        product.setSoldQuantity(Math.max(0, product.getSoldQuantity() - quantity));
        productDataMapper.updateById(product);
        
        log.debug("增加产品库存成功: productId={}, quantity={}", id, quantity);
    }
    
    @Override
    public boolean isStockSufficient(Long id, Integer quantity) {
        Product product = productDataMapper.selectById(id);
        if (product == null) {
            return false;
        }
        
        return product.getStockQuantity() == -1 || product.getStockQuantity() >= quantity;
    }
    
    @Override
    public boolean existsByCode(String code) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getCode, code);
        return productDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public Long countByCategoryId(Long categoryId) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getCategoryId, categoryId);
        return productDataMapper.selectCount(queryWrapper);
    }
    
    @Override
    public Long countEnabledProducts() {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getEnabled, true);
        return productDataMapper.selectCount(queryWrapper);
    }
}
