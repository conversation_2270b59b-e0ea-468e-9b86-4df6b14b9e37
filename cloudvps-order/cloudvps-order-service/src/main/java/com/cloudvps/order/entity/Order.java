package com.cloudvps.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.order.api.enums.OrderStatus;
import com.cloudvps.order.api.enums.OrderType;
import com.cloudvps.order.api.enums.PaymentStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("orders")
public class Order extends BaseEntity {
    
    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 用户ID（来自系统服务）
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 订单类型
     */
    @TableField("order_type")
    private OrderType orderType = OrderType.NEW;

    /**
     * 订单状态
     */
    private OrderStatus status = OrderStatus.PENDING;

    /**
     * 支付状态
     */
    @TableField("payment_status")
    private PaymentStatus paymentStatus = PaymentStatus.UNPAID;
    
    /**
     * 小计金额
     */
    private BigDecimal subtotal = BigDecimal.ZERO;

    /**
     * 折扣金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount = BigDecimal.ZERO;

    /**
     * 税费金额
     */
    @TableField("tax_amount")
    private BigDecimal taxAmount = BigDecimal.ZERO;

    /**
     * 总金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount = BigDecimal.ZERO;

    /**
     * 已付金额
     */
    @TableField("paid_amount")
    private BigDecimal paidAmount = BigDecimal.ZERO;

    /**
     * 优惠券代码
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 优惠券折扣
     */
    @TableField("coupon_discount")
    private BigDecimal couponDiscount = BigDecimal.ZERO;
    
    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系邮箱
     */
    @TableField("contact_email")
    private String contactEmail;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 订单备注
     */
    private String notes;
    
    /**
     * 管理员备注
     */
    @TableField("admin_notes")
    private String adminNotes;

    /**
     * 订单过期时间
     */
    @TableField("expired_at")
    private LocalDateTime expiredAt;

    /**
     * 支付时间
     */
    @TableField("paid_at")
    private LocalDateTime paidAt;

    /**
     * 完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;

    /**
     * 取消时间
     */
    @TableField("cancelled_at")
    private LocalDateTime cancelledAt;

    /**
     * 计算总金额
     */
    public void calculateTotalAmount() {
        this.totalAmount = this.subtotal
            .subtract(this.discountAmount)
            .subtract(this.couponDiscount)
            .add(this.taxAmount);
    }
}
