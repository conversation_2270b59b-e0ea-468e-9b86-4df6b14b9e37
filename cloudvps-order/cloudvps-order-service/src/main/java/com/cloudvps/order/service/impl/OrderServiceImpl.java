package com.cloudvps.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.order.api.dto.request.OrderCreateRequest;
import com.cloudvps.order.api.dto.request.OrderQueryRequest;
import com.cloudvps.order.api.dto.request.OrderUpdateRequest;
import com.cloudvps.order.api.dto.response.OrderResponse;
import com.cloudvps.order.api.enums.OrderStatus;
import com.cloudvps.order.api.enums.PaymentStatus;
import com.cloudvps.order.convert.OrderConvert;
import com.cloudvps.order.entity.Order;
import com.cloudvps.order.mapper.OrderDataMapper;
import com.cloudvps.order.service.OrderItemService;
import com.cloudvps.order.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 订单服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderServiceImpl implements OrderService {
    
    private final OrderDataMapper orderDataMapper;
    private final OrderItemService orderItemService;
    private final OrderConvert orderConvert;
    
    @Override
    public IPage<OrderResponse> queryOrders(OrderQueryRequest request) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(request.getOrderNo())) {
            queryWrapper.like(Order::getOrderNo, request.getOrderNo());
        }
        if (request.getUserId() != null) {
            queryWrapper.eq(Order::getUserId, request.getUserId());
        }
        if (request.getOrderType() != null) {
            queryWrapper.eq(Order::getOrderType, request.getOrderType());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq(Order::getStatus, request.getStatus());
        }
        if (request.getPaymentStatus() != null) {
            queryWrapper.eq(Order::getPaymentStatus, request.getPaymentStatus());
        }
        if (request.getMinTotalAmount() != null) {
            queryWrapper.ge(Order::getTotalAmount, request.getMinTotalAmount());
        }
        if (request.getMaxTotalAmount() != null) {
            queryWrapper.le(Order::getTotalAmount, request.getMaxTotalAmount());
        }
        if (StringUtils.hasText(request.getContactEmail())) {
            queryWrapper.like(Order::getContactEmail, request.getContactEmail());
        }
        if (StringUtils.hasText(request.getContactPhone())) {
            queryWrapper.like(Order::getContactPhone, request.getContactPhone());
        }
        if (request.getCreatedTimeStart() != null) {
            queryWrapper.ge(Order::getCreatedTime, request.getCreatedTimeStart());
        }
        if (request.getCreatedTimeEnd() != null) {
            queryWrapper.le(Order::getCreatedTime, request.getCreatedTimeEnd());
        }
        if (request.getPaidAtStart() != null) {
            queryWrapper.ge(Order::getPaidAt, request.getPaidAtStart());
        }
        if (request.getPaidAtEnd() != null) {
            queryWrapper.le(Order::getPaidAt, request.getPaidAtEnd());
        }
        
        // 排序
        queryWrapper.orderByDesc(Order::getCreatedTime);
        
        // 分页查询
        Page<Order> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<Order> orderPage = orderDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象并设置订单项
        return orderPage.convert(order -> {
            OrderResponse response = orderConvert.toResponse(order);
            response.setItems(orderItemService.findByOrderId(order.getId()));
            return response;
        });
    }
    
    @Override
    public OrderResponse findById(Long id) {
        Order order = orderDataMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        OrderResponse response = orderConvert.toResponse(order);
        response.setItems(orderItemService.findByOrderId(id));
        return response;
    }
    
    @Override
    public OrderResponse findByOrderNo(String orderNo) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderNo, orderNo);
        
        Order order = orderDataMapper.selectOne(queryWrapper);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        OrderResponse response = orderConvert.toResponse(order);
        response.setItems(orderItemService.findByOrderId(order.getId()));
        return response;
    }
    
    @Override
    @Transactional
    public OrderResponse createOrder(OrderCreateRequest request) {
        // 生成订单号
        String orderNo = generateOrderNo();
        
        Order order = orderConvert.toEntity(request);
        order.setOrderNo(orderNo);
        
        // 创建订单项并计算总金额
        orderItemService.batchCreateOrderItems(order.getId(), request.getItems());
        order.calculateTotalAmount();
        
        orderDataMapper.insert(order);
        
        log.info("创建订单成功: orderNo={}, userId={}, totalAmount={}", 
                orderNo, order.getUserId(), order.getTotalAmount());
        
        OrderResponse response = orderConvert.toResponse(order);
        response.setItems(orderItemService.findByOrderId(order.getId()));
        return response;
    }
    
    @Override
    @Transactional
    public OrderResponse updateOrder(Long id, OrderUpdateRequest request) {
        Order existingOrder = orderDataMapper.selectById(id);
        if (existingOrder == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单状态是否允许修改
        if (existingOrder.getStatus() == OrderStatus.COMPLETED || 
            existingOrder.getStatus() == OrderStatus.CANCELLED) {
            throw new BusinessException("订单已完成或已取消，无法修改");
        }
        
        // 更新字段
        orderConvert.updateFromRequest(existingOrder, request);
        orderDataMapper.updateById(existingOrder);
        
        log.info("更新订单成功: orderId={}, orderNo={}", 
                id, existingOrder.getOrderNo());
        
        OrderResponse response = orderConvert.toResponse(existingOrder);
        response.setItems(orderItemService.findByOrderId(id));
        return response;
    }
    
    @Override
    @Transactional
    public void deleteOrder(Long id) {
        Order order = orderDataMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单状态
        if (order.getStatus() == OrderStatus.PAID || 
            order.getStatus() == OrderStatus.COMPLETED) {
            throw new BusinessException("已支付或已完成的订单无法删除");
        }
        
        // 删除订单项
        orderItemService.deleteByOrderId(id);
        
        // 删除订单
        orderDataMapper.deleteById(id);
        
        log.info("删除订单成功: orderId={}, orderNo={}", 
                id, order.getOrderNo());
    }
    
    @Override
    @Transactional
    public void batchDeleteOrders(List<Long> orderIds) {
        if (orderIds == null || orderIds.isEmpty()) {
            return;
        }
        
        // 检查订单状态
        for (Long orderId : orderIds) {
            Order order = orderDataMapper.selectById(orderId);
            if (order != null && (order.getStatus() == OrderStatus.PAID || 
                                 order.getStatus() == OrderStatus.COMPLETED)) {
                throw new BusinessException("存在已支付或已完成的订单，无法删除");
            }
        }
        
        // 批量删除订单项
        for (Long orderId : orderIds) {
            orderItemService.deleteByOrderId(orderId);
        }
        
        // 批量删除订单
        orderDataMapper.deleteBatchIds(orderIds);
        
        log.info("批量删除订单成功: orderIds={}", orderIds);
    }
    
    @Override
    public List<OrderResponse> findByUserId(Long userId) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getUserId, userId)
                   .orderByDesc(Order::getCreatedTime);
        
        List<Order> orders = orderDataMapper.selectList(queryWrapper);
        return orders.stream()
                    .map(order -> {
                        OrderResponse response = orderConvert.toResponse(order);
                        response.setItems(orderItemService.findByOrderId(order.getId()));
                        return response;
                    })
                    .collect(Collectors.toList());
    }
    
    @Override
    public List<OrderResponse> findByStatus(OrderStatus status) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getStatus, status)
                   .orderByDesc(Order::getCreatedTime);
        
        List<Order> orders = orderDataMapper.selectList(queryWrapper);
        return orders.stream()
                    .map(orderConvert::toResponse)
                    .collect(Collectors.toList());
    }
    
    @Override
    public List<OrderResponse> findByPaymentStatus(PaymentStatus paymentStatus) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getPaymentStatus, paymentStatus)
                   .orderByDesc(Order::getCreatedTime);
        
        List<Order> orders = orderDataMapper.selectList(queryWrapper);
        return orders.stream()
                    .map(orderConvert::toResponse)
                    .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public OrderResponse updateOrderStatus(Long id, OrderStatus status) {
        Order order = orderDataMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        order.setStatus(status);
        if (status == OrderStatus.COMPLETED) {
            order.setCompletedAt(LocalDateTime.now());
        } else if (status == OrderStatus.CANCELLED) {
            order.setCancelledAt(LocalDateTime.now());
        }
        
        orderDataMapper.updateById(order);
        
        log.info("更新订单状态成功: orderId={}, orderNo={}, status={}", 
                id, order.getOrderNo(), status);
        
        return orderConvert.toResponse(order);
    }
    
    @Override
    @Transactional
    public OrderResponse updatePaymentStatus(Long id, PaymentStatus paymentStatus) {
        Order order = orderDataMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        order.setPaymentStatus(paymentStatus);
        if (paymentStatus == PaymentStatus.PAID) {
            order.setPaidAt(LocalDateTime.now());
            order.setStatus(OrderStatus.PAID);
        }
        
        orderDataMapper.updateById(order);
        
        log.info("更新支付状态成功: orderId={}, orderNo={}, paymentStatus={}", 
                id, order.getOrderNo(), paymentStatus);
        
        return orderConvert.toResponse(order);
    }
    
    @Override
    @Transactional
    public OrderResponse cancelOrder(Long id, String reason) {
        Order order = orderDataMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        if (order.getStatus() == OrderStatus.COMPLETED) {
            throw new BusinessException("订单已完成，无法取消");
        }
        
        order.setStatus(OrderStatus.CANCELLED);
        order.setCancelledAt(LocalDateTime.now());
        if (StringUtils.hasText(reason)) {
            order.setAdminNotes(reason);
        }
        
        orderDataMapper.updateById(order);
        
        log.info("取消订单成功: orderId={}, orderNo={}, reason={}", 
                id, order.getOrderNo(), reason);
        
        return orderConvert.toResponse(order);
    }
    
    @Override
    @Transactional
    public OrderResponse completeOrder(Long id) {
        return updateOrderStatus(id, OrderStatus.COMPLETED);
    }
    
    @Override
    public Long countByUserId(Long userId) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getUserId, userId);
        return orderDataMapper.selectCount(queryWrapper);
    }
    
    @Override
    public Long countByStatus(OrderStatus status) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getStatus, status);
        return orderDataMapper.selectCount(queryWrapper);
    }
    
    @Override
    public boolean existsByOrderNo(String orderNo) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderNo, orderNo);
        return orderDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public String generateOrderNo() {
        String prefix = "ORD";
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.format("%04d", ThreadLocalRandom.current().nextInt(10000));
        return prefix + timestamp + random;
    }
}
