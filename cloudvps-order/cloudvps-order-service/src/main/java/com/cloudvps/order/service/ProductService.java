package com.cloudvps.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.order.api.dto.request.ProductCreateRequest;
import com.cloudvps.order.api.dto.request.ProductQueryRequest;
import com.cloudvps.order.api.dto.request.ProductUpdateRequest;
import com.cloudvps.order.api.dto.response.ProductResponse;
import com.cloudvps.order.api.enums.ProductType;

import java.util.List;

/**
 * 产品服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ProductService {
    
    /**
     * 分页查询产品
     */
    IPage<ProductResponse> queryProducts(ProductQueryRequest request);
    
    /**
     * 根据ID获取产品
     */
    ProductResponse findById(Long id);
    
    /**
     * 根据产品代码获取产品
     */
    ProductResponse findByCode(String code);
    
    /**
     * 创建产品
     */
    ProductResponse createProduct(ProductCreateRequest request);
    
    /**
     * 更新产品
     */
    ProductResponse updateProduct(Long id, ProductUpdateRequest request);
    
    /**
     * 删除产品
     */
    void deleteProduct(Long id);
    
    /**
     * 批量删除产品
     */
    void batchDeleteProducts(List<Long> productIds);
    
    /**
     * 根据分类ID查询产品
     */
    List<ProductResponse> findByCategoryId(Long categoryId);
    
    /**
     * 根据产品类型查询产品
     */
    List<ProductResponse> findByProductType(ProductType productType);
    
    /**
     * 获取启用的产品
     */
    List<ProductResponse> findEnabledProducts();
    
    /**
     * 获取可用的产品（启用且有库存）
     */
    List<ProductResponse> findAvailableProducts();
    
    /**
     * 更新产品启用状态
     */
    ProductResponse updateProductEnabled(Long id, Boolean enabled);
    
    /**
     * 减少库存
     */
    void reduceStock(Long id, Integer quantity);
    
    /**
     * 增加库存
     */
    void increaseStock(Long id, Integer quantity);
    
    /**
     * 检查库存是否充足
     */
    boolean isStockSufficient(Long id, Integer quantity);
    
    /**
     * 检查产品代码是否存在
     */
    boolean existsByCode(String code);
    
    /**
     * 统计分类下的产品数量
     */
    Long countByCategoryId(Long categoryId);
    
    /**
     * 统计启用的产品数量
     */
    Long countEnabledProducts();
}
