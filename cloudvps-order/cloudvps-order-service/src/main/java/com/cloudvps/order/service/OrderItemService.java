package com.cloudvps.order.service;

import com.cloudvps.order.api.dto.request.OrderItemCreateRequest;
import com.cloudvps.order.api.dto.response.OrderItemResponse;

import java.util.List;

/**
 * 订单项服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OrderItemService {
    
    /**
     * 根据ID获取订单项
     */
    OrderItemResponse findById(Long id);
    
    /**
     * 根据订单ID查询订单项
     */
    List<OrderItemResponse> findByOrderId(Long orderId);
    
    /**
     * 创建订单项
     */
    OrderItemResponse createOrderItem(Long orderId, OrderItemCreateRequest request);
    
    /**
     * 批量创建订单项
     */
    List<OrderItemResponse> batchCreateOrderItems(Long orderId, List<OrderItemCreateRequest> requests);
    
    /**
     * 删除订单项
     */
    void deleteOrderItem(Long id);
    
    /**
     * 批量删除订单项
     */
    void batchDeleteOrderItems(List<Long> itemIds);
    
    /**
     * 根据订单ID删除所有订单项
     */
    void deleteByOrderId(Long orderId);
    
    /**
     * 统计订单的订单项数量
     */
    Long countByOrderId(Long orderId);
}
