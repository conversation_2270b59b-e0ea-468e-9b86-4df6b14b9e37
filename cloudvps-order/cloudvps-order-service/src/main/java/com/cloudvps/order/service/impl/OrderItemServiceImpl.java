package com.cloudvps.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.order.api.dto.request.OrderItemCreateRequest;
import com.cloudvps.order.api.dto.response.OrderItemResponse;
import com.cloudvps.order.convert.OrderItemConvert;
import com.cloudvps.order.entity.OrderItem;
import com.cloudvps.order.mapper.OrderItemDataMapper;
import com.cloudvps.order.service.OrderItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单项服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderItemServiceImpl implements OrderItemService {
    
    private final OrderItemDataMapper orderItemDataMapper;
    private final OrderItemConvert orderItemConvert;
    
    @Override
    public OrderItemResponse findById(Long id) {
        OrderItem orderItem = orderItemDataMapper.selectById(id);
        if (orderItem == null) {
            throw new BusinessException("订单项不存在");
        }
        return orderItemConvert.toResponse(orderItem);
    }
    
    @Override
    public List<OrderItemResponse> findByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItem::getOrderId, orderId)
                   .orderByAsc(OrderItem::getId);
        
        List<OrderItem> orderItems = orderItemDataMapper.selectList(queryWrapper);
        return orderItems.stream()
                        .map(orderItemConvert::toResponse)
                        .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public OrderItemResponse createOrderItem(Long orderId, OrderItemCreateRequest request) {
        OrderItem orderItem = orderItemConvert.toEntity(request);
        orderItem.setOrderId(orderId);
        
        orderItemDataMapper.insert(orderItem);
        
        log.debug("创建订单项成功: orderId={}, productId={}", 
                orderId, orderItem.getProductId());
        return orderItemConvert.toResponse(orderItem);
    }
    
    @Override
    @Transactional
    public List<OrderItemResponse> batchCreateOrderItems(Long orderId, List<OrderItemCreateRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            return List.of();
        }
        
        List<OrderItem> orderItems = requests.stream()
                .map(request -> {
                    OrderItem orderItem = orderItemConvert.toEntity(request);
                    orderItem.setOrderId(orderId);
                    return orderItem;
                })
                .collect(Collectors.toList());
        
        // 批量插入
        for (OrderItem orderItem : orderItems) {
            orderItemDataMapper.insert(orderItem);
        }
        
        log.debug("批量创建订单项成功: orderId={}, count={}", 
                orderId, orderItems.size());
        
        return orderItems.stream()
                        .map(orderItemConvert::toResponse)
                        .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public void deleteOrderItem(Long id) {
        OrderItem orderItem = orderItemDataMapper.selectById(id);
        if (orderItem == null) {
            throw new BusinessException("订单项不存在");
        }
        
        orderItemDataMapper.deleteById(id);
        
        log.debug("删除订单项成功: orderItemId={}", id);
    }
    
    @Override
    @Transactional
    public void batchDeleteOrderItems(List<Long> itemIds) {
        if (itemIds == null || itemIds.isEmpty()) {
            return;
        }
        
        orderItemDataMapper.deleteBatchIds(itemIds);
        
        log.debug("批量删除订单项成功: itemIds={}", itemIds);
    }
    
    @Override
    @Transactional
    public void deleteByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItem::getOrderId, orderId);
        
        orderItemDataMapper.delete(queryWrapper);
        
        log.debug("删除订单的所有订单项成功: orderId={}", orderId);
    }
    
    @Override
    public Long countByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItem::getOrderId, orderId);
        return orderItemDataMapper.selectCount(queryWrapper);
    }
}
