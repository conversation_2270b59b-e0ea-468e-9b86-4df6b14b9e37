package com.cloudvps.order.util;

import com.cloudvps.order.api.dto.response.ProductResponse;
import com.cloudvps.order.entity.Product;
import org.springframework.stereotype.Component;

/**
 * 产品映射工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ProductMapper {
    
    /**
     * 将Product实体转换为ProductResponse
     */
    public ProductResponse toResponse(Product product) {
        if (product == null) {
            return null;
        }
        
        ProductResponse response = new ProductResponse();
        response.setId(product.getId());
        response.setCategoryId(product.getCategoryId());
        response.setName(product.getName());
        response.setCode(product.getCode());
        response.setDescription(product.getDescription());
        response.setProductType(product.getProductType());
        response.setCpuCores(product.getCpuCores());
        response.setMemoryMb(product.getMemoryMb());
        response.setDiskGb(product.getDiskGb());
        response.setBandwidthMbps(product.getBandwidthMbps());
        response.setTrafficGb(product.getTrafficGb());
        response.setPriceMonthly(product.getPriceMonthly());
        response.setPriceQuarterly(product.getPriceQuarterly());
        response.setPriceYearly(product.getPriceYearly());
        response.setSetupFee(product.getSetupFee());
        response.setStockQuantity(product.getStockQuantity());
        response.setSoldQuantity(product.getSoldQuantity());
        response.setEnabled(product.getEnabled());
        response.setSortOrder(product.getSortOrder());
        response.setCreatedAt(product.getCreatedTime());
        response.setUpdatedAt(product.getUpdatedTime());
        
        return response;
    }
}
