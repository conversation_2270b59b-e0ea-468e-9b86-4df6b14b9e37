package com.cloudvps.order.task;

import com.cloudvps.order.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 订单定时任务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "cloudvps.order.auto-cancel-enabled", havingValue = "true", matchIfMissing = true)
public class OrderScheduledTask {
    
    private final OrderService orderService;
    
    /**
     * 每5分钟检查一次过期订单
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void cancelExpiredOrders() {
        try {
            log.debug("开始检查过期订单");
            orderService.cancelExpiredOrders();
        } catch (Exception e) {
            log.error("取消过期订单任务执行失败", e);
        }
    }
}
