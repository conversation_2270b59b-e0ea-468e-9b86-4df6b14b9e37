package com.cloudvps.order;

// import com.cloudvps.common.swagger.annotation.EnableCloudVpsSwagger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 订单服务启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.cloudvps.order", "com.cloudvps.common"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.cloudvps.system.api.client", "com.cloudvps.virtualization.api.client"})
// @EnableCloudVpsSwagger
public class OrderServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderServiceApplication.class, args);
    }
}
