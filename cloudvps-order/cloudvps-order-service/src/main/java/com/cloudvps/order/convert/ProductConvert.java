package com.cloudvps.order.convert;

import com.cloudvps.order.api.dto.request.ProductCreateRequest;
import com.cloudvps.order.api.dto.request.ProductUpdateRequest;
import com.cloudvps.order.api.dto.response.ProductResponse;
import com.cloudvps.order.entity.Product;
import org.mapstruct.*;

/**
 * 产品对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface ProductConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "soldQuantity", constant = "0")
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    Product toEntity(ProductCreateRequest request);
    
    /**
     * 实体转响应
     */
    @Mapping(target = "categoryName", ignore = true) // 需要在Service层设置
    @Mapping(target = "available", expression = "java(product.getEnabled() && (product.getStockQuantity() == -1 || product.getStockQuantity() > 0))")
    ProductResponse toResponse(Product product);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true) // 产品代码不允许修改
    @Mapping(target = "soldQuantity", ignore = true) // 已售数量通过业务逻辑更新
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget Product product, ProductUpdateRequest request);
}
