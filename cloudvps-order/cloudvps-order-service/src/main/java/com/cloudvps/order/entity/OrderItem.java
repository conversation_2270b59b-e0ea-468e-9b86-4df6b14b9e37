package com.cloudvps.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.order.api.enums.BillingCycle;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 订单项实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_items")
public class OrderItem extends BaseEntity {
    
    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 产品名称快照
     */
    @TableField("product_name")
    private String productName;

    /**
     * 产品代码快照
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 产品配置快照
     */
    @TableField("product_config")
    private String productConfig;
    
    /**
     * 数量
     */
    private Integer quantity = 1;

    /**
     * 单价
     */
    @TableField("unit_price")
    private BigDecimal unitPrice = BigDecimal.ZERO;

    /**
     * 总价
     */
    @TableField("total_price")
    private BigDecimal totalPrice = BigDecimal.ZERO;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private BillingCycle billingCycle;

    /**
     * 服务期限（月）
     */
    @TableField("service_period")
    private Integer servicePeriod;

    /**
     * 状态
     */
    private String status = "PENDING";
    
    /**
     * 计算总价
     */
    public void calculateTotalPrice() {
        if (this.unitPrice != null && this.quantity != null) {
            this.totalPrice = this.unitPrice.multiply(BigDecimal.valueOf(this.quantity));
        }
    }
    
    /**
     * 设置单价并计算总价
     */
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        calculateTotalPrice();
    }
    
    /**
     * 设置数量并计算总价
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
        calculateTotalPrice();
    }
}
