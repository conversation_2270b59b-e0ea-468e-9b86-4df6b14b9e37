package com.cloudvps.order.repository;

import com.cloudvps.order.entity.Product;
import com.cloudvps.order.api.enums.ProductType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 产品Repository接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    /**
     * 根据产品代码查找产品
     */
    Optional<Product> findByCode(String code);
    
    /**
     * 根据产品类型查询产品
     */
    List<Product> findByProductTypeAndEnabledTrueOrderBySortOrderAsc(ProductType productType);
    
    /**
     * 根据分类ID查询产品
     */
    Page<Product> findByCategoryIdAndEnabledTrueOrderBySortOrderAsc(Long categoryId, Pageable pageable);
    
    /**
     * 查询启用的产品
     */
    Page<Product> findByEnabledTrueOrderBySortOrderAsc(Pageable pageable);
    
    /**
     * 根据关键词搜索产品
     */
    @Query("SELECT p FROM Product p WHERE p.enabled = true AND " +
           "(p.name LIKE %:keyword% OR p.description LIKE %:keyword% OR p.code LIKE %:keyword%) " +
           "ORDER BY p.sortOrder ASC")
    Page<Product> searchProducts(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据分类和关键词搜索产品
     */
    @Query("SELECT p FROM Product p WHERE p.enabled = true AND p.categoryId = :categoryId AND " +
           "(p.name LIKE %:keyword% OR p.description LIKE %:keyword% OR p.code LIKE %:keyword%) " +
           "ORDER BY p.sortOrder ASC")
    Page<Product> searchProductsByCategory(@Param("categoryId") Long categoryId, 
                                          @Param("keyword") String keyword, 
                                          Pageable pageable);
    
    /**
     * 查询库存不足的产品
     */
    @Query("SELECT p FROM Product p WHERE p.enabled = true AND p.stockQuantity > 0 AND p.stockQuantity < :threshold")
    List<Product> findLowStockProducts(@Param("threshold") Integer threshold);
    
    /**
     * 查询热销产品
     */
    @Query("SELECT p FROM Product p WHERE p.enabled = true ORDER BY p.soldQuantity DESC")
    List<Product> findPopularProducts(Pageable pageable);
    
    /**
     * 检查产品代码是否存在
     */
    boolean existsByCode(String code);
    
    /**
     * 统计启用的产品数量
     */
    long countByEnabledTrue();
    
    /**
     * 根据产品类型统计数量
     */
    long countByProductTypeAndEnabledTrue(ProductType productType);
}
