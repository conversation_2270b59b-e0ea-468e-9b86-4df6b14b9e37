package com.cloudvps.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.order.api.dto.request.OrderCreateRequest;
import com.cloudvps.order.api.dto.request.OrderQueryRequest;
import com.cloudvps.order.api.dto.request.OrderUpdateRequest;
import com.cloudvps.order.api.dto.response.OrderResponse;
import com.cloudvps.order.api.enums.OrderStatus;
import com.cloudvps.order.api.enums.PaymentStatus;

import java.util.List;

/**
 * 订单服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OrderService {
    
    /**
     * 分页查询订单
     */
    IPage<OrderResponse> queryOrders(OrderQueryRequest request);
    
    /**
     * 根据ID获取订单
     */
    OrderResponse findById(Long id);
    
    /**
     * 根据订单号获取订单
     */
    OrderResponse findByOrderNo(String orderNo);
    
    /**
     * 创建订单
     */
    OrderResponse createOrder(OrderCreateRequest request);
    
    /**
     * 更新订单
     */
    OrderResponse updateOrder(Long id, OrderUpdateRequest request);
    
    /**
     * 删除订单
     */
    void deleteOrder(Long id);
    
    /**
     * 批量删除订单
     */
    void batchDeleteOrders(List<Long> orderIds);
    
    /**
     * 根据用户ID查询订单
     */
    List<OrderResponse> findByUserId(Long userId);
    
    /**
     * 根据状态查询订单
     */
    List<OrderResponse> findByStatus(OrderStatus status);
    
    /**
     * 根据支付状态查询订单
     */
    List<OrderResponse> findByPaymentStatus(PaymentStatus paymentStatus);
    
    /**
     * 更新订单状态
     */
    OrderResponse updateOrderStatus(Long id, OrderStatus status);
    
    /**
     * 更新支付状态
     */
    OrderResponse updatePaymentStatus(Long id, PaymentStatus paymentStatus);
    
    /**
     * 取消订单
     */
    OrderResponse cancelOrder(Long id, String reason);
    
    /**
     * 完成订单
     */
    OrderResponse completeOrder(Long id);
    
    /**
     * 统计用户订单数量
     */
    Long countByUserId(Long userId);
    
    /**
     * 统计指定状态的订单数量
     */
    Long countByStatus(OrderStatus status);
    
    /**
     * 检查订单号是否存在
     */
    boolean existsByOrderNo(String orderNo);
    
    /**
     * 生成订单号
     */
    String generateOrderNo();
}
