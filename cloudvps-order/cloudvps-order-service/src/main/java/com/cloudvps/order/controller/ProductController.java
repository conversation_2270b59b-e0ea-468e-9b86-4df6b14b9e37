package com.cloudvps.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.common.security.util.SecurityUtils;
import com.cloudvps.order.api.dto.request.ProductCreateRequest;
import com.cloudvps.order.api.dto.request.ProductQueryRequest;
import com.cloudvps.order.api.dto.request.ProductUpdateRequest;
import com.cloudvps.order.api.dto.response.ProductResponse;
import com.cloudvps.order.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 产品管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/products")
@RequiredArgsConstructor
@Validated
@Tag(name = "产品管理", description = "产品管理相关接口")
public class ProductController {

    private final ProductService productService;

    /**
     * 分页查询产品列表
     */
    @GetMapping
    @Operation(summary = "分页查询产品列表", description = "根据条件分页查询产品列表")
    @PreAuthorize("hasAuthority('PRODUCT_VIEW')")
    public ApiResponse<IPage<ProductResponse>> getProductPage(@Valid ProductQueryRequest request) {
        log.info("分页查询产品列表，请求参数：{}", request);
        IPage<ProductResponse> result = productService.queryProducts(request);
        return ApiResponse.success(result);
    }

    /**
     * 查询所有启用的产品列表
     */
    @GetMapping("/enabled")
    @Operation(summary = "查询启用的产品列表", description = "查询所有启用状态的产品列表")
    @PreAuthorize("hasAuthority('PRODUCT_VIEW')")
    public ApiResponse<List<ProductResponse>> getEnabledProducts() {
        log.info("查询启用的产品列表");
        List<ProductResponse> result = productService.findEnabledProducts();
        return ApiResponse.success(result);
    }

    /**
     * 根据ID查询产品详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询产品详情", description = "根据产品ID查询产品详情")
    @PreAuthorize("hasAuthority('PRODUCT_VIEW')")
    public ApiResponse<ProductResponse> getProductById(
            @Parameter(description = "产品ID", required = true) @PathVariable Long id) {
        log.info("查询产品详情，产品ID：{}", id);
        ProductResponse result = productService.findById(id);
        return ApiResponse.success(result);
    }

    /**
     * 创建产品
     */
    @PostMapping
    @Operation(summary = "创建产品", description = "创建新的产品")
    @PreAuthorize("hasAuthority('PRODUCT_CREATE')")
    public ApiResponse<ProductResponse> createProduct(@Valid @RequestBody ProductCreateRequest request) {
        log.info("创建产品，请求参数：{}", request);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        ProductResponse result = productService.createProduct(request);
        return ApiResponse.success(result);
    }

    /**
     * 更新产品
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新产品", description = "更新产品信息")
    @PreAuthorize("hasAuthority('PRODUCT_UPDATE')")
    public ApiResponse<ProductResponse> updateProduct(
            @Parameter(description = "产品ID", required = true) @PathVariable Long id,
            @Valid @RequestBody ProductUpdateRequest request) {
        log.info("更新产品，产品ID：{}，请求参数：{}", id, request);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        ProductResponse result = productService.updateProduct(id, request);
        return ApiResponse.success(result);
    }

    /**
     * 删除产品
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除产品", description = "根据产品ID删除产品")
    @PreAuthorize("hasAuthority('PRODUCT_DELETE')")
    public ApiResponse<Void> deleteProduct(
            @Parameter(description = "产品ID", required = true) @PathVariable Long id) {
        log.info("删除产品，产品ID：{}", id);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        productService.deleteProduct(id);
        return ApiResponse.success();
    }

    /**
     * 启用产品
     */
    @PostMapping("/{id}/enable")
    @Operation(summary = "启用产品", description = "启用指定的产品")
    @PreAuthorize("hasAuthority('PRODUCT_UPDATE')")
    public ApiResponse<ProductResponse> enableProduct(
            @Parameter(description = "产品ID", required = true) @PathVariable Long id) {
        log.info("启用产品，产品ID：{}", id);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        ProductResponse result = productService.updateProductEnabled(id, true);
        return ApiResponse.success(result);
    }

    /**
     * 禁用产品
     */
    @PostMapping("/{id}/disable")
    @Operation(summary = "禁用产品", description = "禁用指定的产品")
    @PreAuthorize("hasAuthority('PRODUCT_UPDATE')")
    public ApiResponse<ProductResponse> disableProduct(
            @Parameter(description = "产品ID", required = true) @PathVariable Long id) {
        log.info("禁用产品，产品ID：{}", id);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        ProductResponse result = productService.updateProductEnabled(id, false);
        return ApiResponse.success(result);
    }
}
