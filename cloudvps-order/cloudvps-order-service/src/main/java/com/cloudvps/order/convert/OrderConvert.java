package com.cloudvps.order.convert;

import com.cloudvps.order.api.dto.request.OrderCreateRequest;
import com.cloudvps.order.api.dto.request.OrderUpdateRequest;
import com.cloudvps.order.api.dto.response.OrderResponse;
import com.cloudvps.order.entity.Order;
import org.mapstruct.*;

/**
 * 订单对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface OrderConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "orderNo", ignore = true) // 需要在Service层生成
    @Mapping(target = "subtotal", constant = "0.00")
    @Mapping(target = "totalAmount", constant = "0.00")
    @Mapping(target = "paidAmount", constant = "0.00")
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    Order toEntity(OrderCreateRequest request);
    
    /**
     * 实体转响应
     */
    @Mapping(target = "items", ignore = true) // 需要在Service层设置
    OrderResponse toResponse(Order order);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "orderNo", ignore = true) // 订单号不允许修改
    @Mapping(target = "userId", ignore = true) // 用户ID不允许修改
    @Mapping(target = "orderType", ignore = true) // 订单类型不允许修改
    @Mapping(target = "subtotal", ignore = true) // 小计通过计算得出
    @Mapping(target = "totalAmount", ignore = true) // 总金额通过计算得出
    @Mapping(target = "paidAmount", ignore = true) // 已付金额通过支付更新
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget Order order, OrderUpdateRequest request);
}
