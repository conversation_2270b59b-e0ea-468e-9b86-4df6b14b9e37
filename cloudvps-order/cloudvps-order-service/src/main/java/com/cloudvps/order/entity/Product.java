package com.cloudvps.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.order.api.enums.ProductType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 产品实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("products")
public class Product extends BaseEntity {
    
    /**
     * 产品分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品代码
     */
    private String code;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 产品类型
     */
    @TableField("product_type")
    private ProductType productType;
    
    /**
     * CPU核心数
     */
    @TableField("cpu_cores")
    private Integer cpuCores;

    /**
     * 内存大小(MB)
     */
    @TableField("memory_mb")
    private Integer memoryMb;

    /**
     * 磁盘大小(GB)
     */
    @TableField("disk_gb")
    private Integer diskGb;

    /**
     * 带宽(Mbps)
     */
    @TableField("bandwidth_mbps")
    private Integer bandwidthMbps;

    /**
     * 月流量(GB)，0表示不限制
     */
    @TableField("traffic_gb")
    private Integer trafficGb;

    /**
     * 月付价格
     */
    @TableField("price_monthly")
    private BigDecimal priceMonthly = BigDecimal.ZERO;

    /**
     * 季付价格
     */
    @TableField("price_quarterly")
    private BigDecimal priceQuarterly;

    /**
     * 年付价格
     */
    @TableField("price_yearly")
    private BigDecimal priceYearly;

    /**
     * 安装费
     */
    @TableField("setup_fee")
    private BigDecimal setupFee = BigDecimal.ZERO;
    
    /**
     * 库存数量，-1表示无限库存
     */
    @TableField("stock_quantity")
    private Integer stockQuantity = -1;

    /**
     * 已售数量
     */
    @TableField("sold_quantity")
    private Integer soldQuantity = 0;

    /**
     * 是否启用
     */
    private Boolean enabled = true;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder = 0;
    
    /**
     * 检查库存是否充足
     */
    public boolean isStockSufficient(int quantity) {
        if (stockQuantity == -1) {
            return true; // 无限库存
        }
        return stockQuantity >= quantity;
    }
    
    /**
     * 减少库存
     */
    public void reduceStock(int quantity) {
        if (stockQuantity != -1) {
            this.stockQuantity -= quantity;
        }
        this.soldQuantity += quantity;
    }
    
    /**
     * 增加库存（退货时使用）
     */
    public void increaseStock(int quantity) {
        if (stockQuantity != -1) {
            this.stockQuantity += quantity;
        }
        this.soldQuantity = Math.max(0, this.soldQuantity - quantity);
    }
}
