package com.cloudvps.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.common.security.util.SecurityUtils;
import com.cloudvps.order.api.dto.request.OrderCreateRequest;
import com.cloudvps.order.api.dto.request.OrderQueryRequest;
import com.cloudvps.order.api.dto.request.OrderUpdateRequest;
import com.cloudvps.order.api.dto.response.OrderResponse;
import com.cloudvps.order.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 订单管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
@Validated
@Tag(name = "订单管理", description = "订单管理相关接口")
public class OrderController {

    private final OrderService orderService;

    /**
     * 分页查询订单列表
     */
    @GetMapping
    @Operation(summary = "分页查询订单列表", description = "根据条件分页查询订单列表")
    @PreAuthorize("hasAuthority('ORDER_VIEW')")
    public ApiResponse<IPage<OrderResponse>> getOrderPage(@Valid OrderQueryRequest request) {
        log.info("分页查询订单列表，请求参数：{}", request);
        IPage<OrderResponse> result = orderService.queryOrders(request);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID查询订单详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询订单详情", description = "根据订单ID查询订单详情")
    @PreAuthorize("hasAuthority('ORDER_VIEW')")
    public ApiResponse<OrderResponse> getOrderById(
            @Parameter(description = "订单ID", required = true) @PathVariable Long id) {
        log.info("查询订单详情，订单ID：{}", id);
        OrderResponse result = orderService.findById(id);
        return ApiResponse.success(result);
    }

    /**
     * 创建订单
     */
    @PostMapping
    @Operation(summary = "创建订单", description = "创建新的订单")
    @PreAuthorize("hasAuthority('ORDER_CREATE')")
    public ApiResponse<OrderResponse> createOrder(@Valid @RequestBody OrderCreateRequest request) {
        log.info("创建订单，请求参数：{}", request);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        // 当前用户ID将在Service层处理
        OrderResponse result = orderService.createOrder(request);
        return ApiResponse.success(result);
    }

    /**
     * 更新订单
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新订单", description = "更新订单信息")
    @PreAuthorize("hasAuthority('ORDER_UPDATE')")
    public ApiResponse<OrderResponse> updateOrder(
            @Parameter(description = "订单ID", required = true) @PathVariable Long id,
            @Valid @RequestBody OrderUpdateRequest request) {
        log.info("更新订单，订单ID：{}，请求参数：{}", id, request);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        OrderResponse result = orderService.updateOrder(id, request);
        return ApiResponse.success(result);
    }

    /**
     * 删除订单
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除订单", description = "根据订单ID删除订单")
    @PreAuthorize("hasAuthority('ORDER_DELETE')")
    public ApiResponse<Void> deleteOrder(
            @Parameter(description = "订单ID", required = true) @PathVariable Long id) {
        log.info("删除订单，订单ID：{}", id);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        orderService.deleteOrder(id);
        return ApiResponse.success();
    }

    /**
     * 取消订单
     */
    @PostMapping("/{id}/cancel")
    @Operation(summary = "取消订单", description = "取消指定的订单")
    @PreAuthorize("hasAuthority('ORDER_UPDATE')")
    public ApiResponse<OrderResponse> cancelOrder(
            @Parameter(description = "订单ID", required = true) @PathVariable Long id,
            @RequestParam(value = "reason", defaultValue = "用户取消") String reason) {
        log.info("取消订单，订单ID：{}，原因：{}", id, reason);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        OrderResponse result = orderService.cancelOrder(id, reason);
        return ApiResponse.success(result);
    }

    /**
     * 完成订单
     */
    @PostMapping("/{id}/complete")
    @Operation(summary = "完成订单", description = "完成指定的订单")
    @PreAuthorize("hasAuthority('ORDER_UPDATE')")
    public ApiResponse<OrderResponse> completeOrder(
            @Parameter(description = "订单ID", required = true) @PathVariable Long id) {
        log.info("完成订单，订单ID：{}", id);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        OrderResponse result = orderService.completeOrder(id);
        return ApiResponse.success(result);
    }

    /**
     * 获取当前用户的订单列表
     */
    @GetMapping("/my")
    @Operation(summary = "获取我的订单", description = "获取当前用户的订单列表")
    @PreAuthorize("hasAuthority('ORDER_VIEW')")
    public ApiResponse<IPage<OrderResponse>> getMyOrders(@Valid OrderQueryRequest request) {
        log.info("获取我的订单列表，请求参数：{}", request);
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        // 强制设置为当前用户ID
        request.setUserId(currentUserId);
        IPage<OrderResponse> result = orderService.queryOrders(request);
        return ApiResponse.success(result);
    }
}
