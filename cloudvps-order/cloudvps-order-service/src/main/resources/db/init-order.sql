-- CloudVPS 订单服务数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS cloudvps_order;
USE cloudvps_order;

-- 产品分类表
CREATE TABLE IF NOT EXISTS product_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    parent_id BIGINT REFERENCES product_categories(id),
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 产品表
CREATE TABLE IF NOT EXISTS products (
    id BIGSERIAL PRIMARY KEY,
    category_id BIGINT NOT NULL REFERENCES product_categories(id),
    name VARCHAR(200) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- 产品类型
    product_type VARCHAR(50) NOT NULL CHECK (product_type IN ('VPS', 'DEDICATED', 'CLOUD_STORAGE', 'BANDWIDTH', 'IP_ADDRESS')),
    
    -- VPS配置（当product_type为VPS时使用）
    cpu_cores INTEGER,
    memory_mb INTEGER,
    disk_gb INTEGER,
    bandwidth_mbps INTEGER,
    traffic_gb INTEGER, -- 月流量限制，0表示不限制
    
    -- 价格信息
    price_monthly DECIMAL(10,2) NOT NULL, -- 月付价格
    price_quarterly DECIMAL(10,2), -- 季付价格
    price_yearly DECIMAL(10,2), -- 年付价格
    setup_fee DECIMAL(10,2) DEFAULT 0, -- 安装费
    
    -- 库存信息
    stock_quantity INTEGER DEFAULT -1, -- -1表示无限库存
    sold_quantity INTEGER DEFAULT 0, -- 已售数量
    
    -- 状态信息
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'SOLD_OUT')),
    is_featured BOOLEAN DEFAULT FALSE, -- 是否推荐产品
    sort_order INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(32) UNIQUE NOT NULL, -- 订单号
    user_id BIGINT NOT NULL, -- 用户ID（来自系统服务）
    
    -- 订单基本信息
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PAID', 'PROCESSING', 'COMPLETED', 'CANCELLED', 'REFUNDED')),
    payment_status VARCHAR(20) DEFAULT 'UNPAID' CHECK (payment_status IN ('UNPAID', 'PAID', 'PARTIAL_PAID', 'REFUNDED')),
    
    -- 金额信息
    subtotal DECIMAL(10,2) NOT NULL, -- 小计
    discount_amount DECIMAL(10,2) DEFAULT 0, -- 折扣金额
    tax_amount DECIMAL(10,2) DEFAULT 0, -- 税费
    total_amount DECIMAL(10,2) NOT NULL, -- 总金额
    paid_amount DECIMAL(10,2) DEFAULT 0, -- 已付金额
    
    -- 优惠信息
    coupon_code VARCHAR(50), -- 优惠券代码
    coupon_discount DECIMAL(10,2) DEFAULT 0, -- 优惠券折扣
    
    -- 联系信息
    contact_name VARCHAR(100),
    contact_email VARCHAR(100),
    contact_phone VARCHAR(20),
    
    -- 备注信息
    notes TEXT, -- 订单备注
    admin_notes TEXT, -- 管理员备注
    
    -- 时间信息
    expired_at TIMESTAMP, -- 订单过期时间
    paid_at TIMESTAMP, -- 支付时间
    completed_at TIMESTAMP, -- 完成时间
    cancelled_at TIMESTAMP, -- 取消时间
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单项表
CREATE TABLE IF NOT EXISTS order_items (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id BIGINT NOT NULL REFERENCES products(id),
    
    -- 产品信息快照
    product_name VARCHAR(200) NOT NULL,
    product_code VARCHAR(100) NOT NULL,
    product_config JSONB, -- 产品配置快照
    
    -- 数量和价格
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL, -- 单价
    total_price DECIMAL(10,2) NOT NULL, -- 总价
    
    -- 服务周期
    billing_cycle VARCHAR(20) NOT NULL CHECK (billing_cycle IN ('MONTHLY', 'QUARTERLY', 'YEARLY')),
    service_period INTEGER NOT NULL, -- 服务期限（月）
    
    -- 状态
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'ACTIVE', 'SUSPENDED', 'CANCELLED')),
    
    -- 服务时间
    service_start_date DATE, -- 服务开始日期
    service_end_date DATE, -- 服务结束日期
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 服务实例表（记录已开通的服务）
CREATE TABLE IF NOT EXISTS service_instances (
    id BIGSERIAL PRIMARY KEY,
    order_item_id BIGINT NOT NULL REFERENCES order_items(id),
    user_id BIGINT NOT NULL,
    
    -- 服务信息
    service_type VARCHAR(50) NOT NULL, -- 服务类型：VPS, DEDICATED等
    service_id VARCHAR(100), -- 外部服务ID（如VM ID）
    service_name VARCHAR(200),
    
    -- 配置信息
    config JSONB, -- 服务配置信息
    
    -- 状态信息
    status VARCHAR(20) DEFAULT 'CREATING' CHECK (status IN ('CREATING', 'ACTIVE', 'SUSPENDED', 'TERMINATED', 'ERROR')),
    
    -- 时间信息
    activated_at TIMESTAMP, -- 激活时间
    suspended_at TIMESTAMP, -- 暂停时间
    terminated_at TIMESTAMP, -- 终止时间
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 优惠券表
CREATE TABLE IF NOT EXISTS coupons (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- 折扣信息
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('PERCENTAGE', 'FIXED_AMOUNT')),
    discount_value DECIMAL(10,2) NOT NULL, -- 折扣值（百分比或固定金额）
    min_order_amount DECIMAL(10,2) DEFAULT 0, -- 最小订单金额
    max_discount_amount DECIMAL(10,2), -- 最大折扣金额
    
    -- 使用限制
    usage_limit INTEGER DEFAULT -1, -- 使用次数限制，-1表示无限制
    used_count INTEGER DEFAULT 0, -- 已使用次数
    user_limit INTEGER DEFAULT 1, -- 每用户使用次数限制
    
    -- 适用范围
    applicable_products BIGINT[], -- 适用产品ID列表，空表示全部产品
    
    -- 有效期
    valid_from TIMESTAMP NOT NULL,
    valid_until TIMESTAMP NOT NULL,
    
    -- 状态
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'EXPIRED')),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 优惠券使用记录表
CREATE TABLE IF NOT EXISTS coupon_usages (
    id BIGSERIAL PRIMARY KEY,
    coupon_id BIGINT NOT NULL REFERENCES coupons(id),
    order_id BIGINT NOT NULL REFERENCES orders(id),
    user_id BIGINT NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_product_categories_parent_id ON product_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_product_categories_status ON product_categories(status);

CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_type ON products(product_type);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_products_featured ON products(is_featured);

CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_expired_at ON orders(expired_at);

CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_status ON order_items(status);

CREATE INDEX IF NOT EXISTS idx_service_instances_order_item_id ON service_instances(order_item_id);
CREATE INDEX IF NOT EXISTS idx_service_instances_user_id ON service_instances(user_id);
CREATE INDEX IF NOT EXISTS idx_service_instances_type ON service_instances(service_type);
CREATE INDEX IF NOT EXISTS idx_service_instances_status ON service_instances(status);

CREATE INDEX IF NOT EXISTS idx_coupons_code ON coupons(code);
CREATE INDEX IF NOT EXISTS idx_coupons_status ON coupons(status);
CREATE INDEX IF NOT EXISTS idx_coupons_valid_period ON coupons(valid_from, valid_until);

CREATE INDEX IF NOT EXISTS idx_coupon_usages_coupon_id ON coupon_usages(coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_usages_user_id ON coupon_usages(user_id);

-- 插入初始数据

-- 插入产品分类
INSERT INTO product_categories (name, code, description) VALUES 
('云服务器', 'VPS', '虚拟专用服务器'),
('独立服务器', 'DEDICATED', '物理独立服务器'),
('云存储', 'STORAGE', '云端存储服务'),
('网络服务', 'NETWORK', '网络相关服务')
ON CONFLICT (code) DO NOTHING;

-- 插入VPS产品
INSERT INTO products (category_id, name, code, description, product_type, cpu_cores, memory_mb, disk_gb, bandwidth_mbps, traffic_gb, price_monthly, price_quarterly, price_yearly) VALUES 
((SELECT id FROM product_categories WHERE code = 'VPS'), '入门型VPS', 'VPS_STARTER', '适合个人网站和小型应用', 'VPS', 1, 1024, 20, 100, 1000, 29.00, 79.00, 299.00),
((SELECT id FROM product_categories WHERE code = 'VPS'), '标准型VPS', 'VPS_STANDARD', '适合中小型企业应用', 'VPS', 2, 2048, 40, 100, 2000, 59.00, 159.00, 599.00),
((SELECT id FROM product_categories WHERE code = 'VPS'), '高性能VPS', 'VPS_PREMIUM', '适合高性能应用和数据库', 'VPS', 4, 4096, 80, 100, 5000, 119.00, 319.00, 1199.00),
((SELECT id FROM product_categories WHERE code = 'VPS'), '企业级VPS', 'VPS_ENTERPRISE', '适合大型企业级应用', 'VPS', 8, 8192, 160, 100, 10000, 239.00, 639.00, 2399.00)
ON CONFLICT (code) DO NOTHING;

-- 插入示例优惠券
INSERT INTO coupons (code, name, description, discount_type, discount_value, min_order_amount, usage_limit, valid_from, valid_until) VALUES 
('WELCOME10', '新用户10%折扣', '新用户首次购买享受10%折扣', 'PERCENTAGE', 10.00, 50.00, 100, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days'),
('SAVE50', '满200减50', '订单满200元减50元', 'FIXED_AMOUNT', 50.00, 200.00, 50, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '15 days')
ON CONFLICT (code) DO NOTHING;
