spring:
  application:
    name: cloudvps-service-order
  profiles:
    active: dev

  datasource:
    url: ********************************************************************
    username: youthidc
    password:
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: create
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          time_zone: Asia/Shanghai

  redis:
    host: localhost
    port: 6379
    database: 2
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  cloud:
    consul:
      enabled: false  # 完全禁用Consul
      host: localhost
      port: 8500
      discovery:
        enabled: false  # 本地开发暂时禁用
        service-name: ${spring.application.name}
        health-check-interval: 10s

server:
  port: 8083
  servlet:
    context-path: /

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.cloudvps: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# CloudVPS 自定义配置
cloudvps:
  jwt:
    secret: cloudvps-unified-jwt-secret-key-for-all-services-2024
    expiration: 86400  # 24小时（秒）
    issuer: cloudvps

  order:
    # 订单过期时间
    expire-minutes: 30
    # 自动取消未支付订单
    auto-cancel-enabled: true
    auto-cancel-delay: PT30M

  security:
    # 不需要认证的路径
    permit-all-paths:
      - /api/v1/products/**
      - /actuator/**
      - /swagger-ui/**
      - /v3/api-docs/**
    # 需要管理员权限的路径
    admin-paths:
      - /api/v1/orders/admin/**

  # 外部服务配置
  services:
    system:
      url: http://localhost:8081
    virtualization:
      url: http://localhost:8082

---
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    root: INFO
    com.cloudvps: DEBUG

---
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    root: WARN
    com.cloudvps: INFO

cloudvps:
  jwt:
    secret: ${JWT_SECRET:cloudvps-unified-jwt-secret-key-for-all-services-2024}
    expiration: ${JWT_EXPIRATION:86400}
    issuer: cloudvps
