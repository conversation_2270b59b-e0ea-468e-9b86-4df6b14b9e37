-- =============================================
-- CloudVPS 订单服务数据库初始化脚本
-- 数据库: cloudvps_order
-- 版本: 1.0.0
-- 创建时间: 2024-01-01
-- =============================================

-- 创建数据库 (如果不存在)
-- CREATE DATABASE IF NOT EXISTS cloudvps_order;
-- USE cloudvps_order;

-- =============================================
-- 1. 产品相关表
-- =============================================

-- 产品分类表
CREATE TABLE IF NOT EXISTS product_categories (
    id BIGSERIAL PRIMARY KEY,
    category_code VARCHAR(50) UNIQUE NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_url VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE')),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 产品表
CREATE TABLE IF NOT EXISTS products (
    id BIGSERIAL PRIMARY KEY,
    product_code VARCHAR(50) UNIQUE NOT NULL,
    product_name VARCHAR(100) NOT NULL,
    category_id BIGINT NOT NULL,
    product_type VARCHAR(20) DEFAULT 'VM' CHECK (product_type IN ('VM', 'STORAGE', 'NETWORK', 'SERVICE')),
    description TEXT,
    specifications JSONB,
    base_price DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) DEFAULT 'MONTH' CHECK (unit IN ('HOUR', 'DAY', 'MONTH', 'YEAR')),
    min_duration INTEGER DEFAULT 1,
    max_duration INTEGER DEFAULT 36,
    is_renewable BOOLEAN DEFAULT TRUE,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'DISCONTINUED')),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id)
);

-- 产品规格表
CREATE TABLE IF NOT EXISTS product_specs (
    id BIGSERIAL PRIMARY KEY,
    product_id BIGINT NOT NULL,
    spec_name VARCHAR(50) NOT NULL,
    spec_value VARCHAR(100) NOT NULL,
    spec_unit VARCHAR(20),
    display_order INTEGER DEFAULT 0,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- 产品价格表 (支持阶梯定价)
CREATE TABLE IF NOT EXISTS product_prices (
    id BIGSERIAL PRIMARY KEY,
    product_id BIGINT NOT NULL,
    duration_months INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    effective_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expire_time TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- =============================================
-- 2. 订单相关表
-- =============================================

-- 订单主表
CREATE TABLE IF NOT EXISTS orders (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(32) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    order_type VARCHAR(20) NOT NULL CHECK (order_type IN ('NEW', 'RENEW', 'UPGRADE', 'DOWNGRADE')),
    order_status VARCHAR(20) DEFAULT 'PENDING' CHECK (order_status IN ('PENDING', 'PAID', 'PROCESSING', 'COMPLETED', 'CANCELLED', 'REFUNDED')),
    total_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    coupon_amount DECIMAL(10,2) DEFAULT 0.00,
    actual_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    payment_method VARCHAR(20),
    payment_status VARCHAR(20) DEFAULT 'UNPAID' CHECK (payment_status IN ('UNPAID', 'PAID', 'REFUNDED', 'PARTIAL_REFUND')),
    payment_time TIMESTAMP,
    expire_time TIMESTAMP,
    remark TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单详情表
CREATE TABLE IF NOT EXISTS order_items (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    product_name VARCHAR(100) NOT NULL,
    product_code VARCHAR(50) NOT NULL,
    specifications JSONB,
    unit_price DECIMAL(10,2) NOT NULL,
    quantity INTEGER DEFAULT 1,
    duration_months INTEGER NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    actual_amount DECIMAL(10,2) NOT NULL,
    vm_id VARCHAR(50),
    service_start_time TIMESTAMP,
    service_end_time TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- 订单状态变更记录表
CREATE TABLE IF NOT EXISTS order_status_logs (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT NOT NULL,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    change_reason VARCHAR(255),
    operator_id BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);

-- =============================================
-- 3. 购物车相关表
-- =============================================

-- 购物车表
CREATE TABLE IF NOT EXISTS shopping_cart (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    quantity INTEGER DEFAULT 1,
    duration_months INTEGER NOT NULL,
    specifications JSONB,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE(user_id, product_id)
);

-- =============================================
-- 4. 优惠券相关表
-- =============================================

-- 优惠券模板表
CREATE TABLE IF NOT EXISTS coupon_templates (
    id BIGSERIAL PRIMARY KEY,
    template_code VARCHAR(50) UNIQUE NOT NULL,
    template_name VARCHAR(100) NOT NULL,
    coupon_type VARCHAR(20) NOT NULL CHECK (coupon_type IN ('FIXED', 'PERCENT', 'FULL_REDUCTION')),
    discount_value DECIMAL(10,2) NOT NULL,
    min_amount DECIMAL(10,2) DEFAULT 0,
    max_discount DECIMAL(10,2),
    valid_days INTEGER DEFAULT 30,
    total_quantity INTEGER DEFAULT 0,
    used_quantity INTEGER DEFAULT 0,
    per_user_limit INTEGER DEFAULT 1,
    applicable_products JSONB,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'EXPIRED')),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户优惠券表
CREATE TABLE IF NOT EXISTS user_coupons (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    template_id BIGINT NOT NULL,
    coupon_code VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'UNUSED' CHECK (status IN ('UNUSED', 'USED', 'EXPIRED')),
    used_order_id BIGINT,
    used_time TIMESTAMP,
    expire_time TIMESTAMP NOT NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES coupon_templates(id),
    FOREIGN KEY (used_order_id) REFERENCES orders(id)
);

-- =============================================
-- 5. 促销活动相关表
-- =============================================

-- 促销活动表
CREATE TABLE IF NOT EXISTS promotions (
    id BIGSERIAL PRIMARY KEY,
    promotion_code VARCHAR(50) UNIQUE NOT NULL,
    promotion_name VARCHAR(100) NOT NULL,
    promotion_type VARCHAR(20) NOT NULL CHECK (promotion_type IN ('DISCOUNT', 'GIFT', 'BUNDLE', 'FLASH_SALE')),
    description TEXT,
    rules JSONB,
    applicable_products JSONB,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'EXPIRED')),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 6. 创建索引
-- =============================================

-- 产品相关索引
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_product_code ON products(product_code);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_product_specs_product_id ON product_specs(product_id);
CREATE INDEX IF NOT EXISTS idx_product_prices_product_id ON product_prices(product_id);

-- 订单相关索引
CREATE INDEX IF NOT EXISTS idx_orders_order_no ON orders(order_no);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_created_time ON orders(created_time);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_order_status_logs_order_id ON order_status_logs(order_id);

-- 购物车索引
CREATE INDEX IF NOT EXISTS idx_shopping_cart_user_id ON shopping_cart(user_id);
CREATE INDEX IF NOT EXISTS idx_shopping_cart_product_id ON shopping_cart(product_id);

-- 优惠券索引
CREATE INDEX IF NOT EXISTS idx_coupon_templates_code ON coupon_templates(template_code);
CREATE INDEX IF NOT EXISTS idx_user_coupons_user_id ON user_coupons(user_id);
CREATE INDEX IF NOT EXISTS idx_user_coupons_code ON user_coupons(coupon_code);
CREATE INDEX IF NOT EXISTS idx_user_coupons_status ON user_coupons(status);

-- 促销活动索引
CREATE INDEX IF NOT EXISTS idx_promotions_code ON promotions(promotion_code);
CREATE INDEX IF NOT EXISTS idx_promotions_time ON promotions(start_time, end_time);

-- =============================================
-- 7. 插入初始数据
-- =============================================

-- 插入产品分类
INSERT INTO product_categories (category_code, category_name, description, icon_url, sort_order) VALUES
('vm-basic', '基础云服务器', '适合个人开发者和小型应用', '/static/icons/vm-basic.png', 1),
('vm-standard', '标准云服务器', '适合中小企业和生产环境', '/static/icons/vm-standard.png', 2),
('vm-high-performance', '高性能云服务器', '适合大型应用和高并发场景', '/static/icons/vm-high.png', 3),
('storage', '云存储', '弹性块存储和对象存储服务', '/static/icons/storage.png', 4),
('network', '网络服务', 'CDN、负载均衡等网络服务', '/static/icons/network.png', 5)
ON CONFLICT (category_code) DO NOTHING;

-- 插入基础云服务器产品
INSERT INTO products (product_code, product_name, category_id, specifications, base_price, description) VALUES
('vm-basic-1c1g', '基础型 1核1G', 1, '{"cpu": 1, "memory": 1, "disk": 20, "bandwidth": 1}', 29.00, '1核CPU，1GB内存，20GB SSD硬盘，1Mbps带宽'),
('vm-basic-1c2g', '基础型 1核2G', 1, '{"cpu": 1, "memory": 2, "disk": 40, "bandwidth": 1}', 49.00, '1核CPU，2GB内存，40GB SSD硬盘，1Mbps带宽'),
('vm-basic-2c4g', '基础型 2核4G', 1, '{"cpu": 2, "memory": 4, "disk": 60, "bandwidth": 2}', 99.00, '2核CPU，4GB内存，60GB SSD硬盘，2Mbps带宽'),
('vm-standard-2c4g', '标准型 2核4G', 2, '{"cpu": 2, "memory": 4, "disk": 80, "bandwidth": 5}', 159.00, '2核CPU，4GB内存，80GB SSD硬盘，5Mbps带宽'),
('vm-standard-4c8g', '标准型 4核8G', 2, '{"cpu": 4, "memory": 8, "disk": 120, "bandwidth": 5}', 299.00, '4核CPU，8GB内存，120GB SSD硬盘，5Mbps带宽'),
('vm-high-8c16g', '高性能 8核16G', 3, '{"cpu": 8, "memory": 16, "disk": 200, "bandwidth": 10}', 599.00, '8核CPU，16GB内存，200GB SSD硬盘，10Mbps带宽')
ON CONFLICT (product_code) DO NOTHING;

-- 插入产品规格详情
INSERT INTO product_specs (product_id, spec_name, spec_value, spec_unit, display_order) 
SELECT p.id, spec.key, spec.value, 
    CASE 
        WHEN spec.key = 'cpu' THEN '核'
        WHEN spec.key = 'memory' THEN 'GB'
        WHEN spec.key = 'disk' THEN 'GB'
        WHEN spec.key = 'bandwidth' THEN 'Mbps'
    END,
    CASE 
        WHEN spec.key = 'cpu' THEN 1
        WHEN spec.key = 'memory' THEN 2
        WHEN spec.key = 'disk' THEN 3
        WHEN spec.key = 'bandwidth' THEN 4
    END
FROM products p, jsonb_each_text(p.specifications) spec
WHERE p.product_code LIKE 'vm-%'
ON CONFLICT DO NOTHING;

-- 插入产品价格 (阶梯定价)
INSERT INTO product_prices (product_id, duration_months, price, discount_percent) 
SELECT p.id, duration.months, 
    ROUND(p.base_price * duration.months * (1 - duration.discount), 2),
    duration.discount * 100
FROM products p,
(VALUES 
    (1, 0.0),
    (3, 0.05),
    (6, 0.10),
    (12, 0.15),
    (24, 0.20),
    (36, 0.25)
) AS duration(months, discount)
WHERE p.product_code LIKE 'vm-%'
ON CONFLICT DO NOTHING;

-- 插入优惠券模板
INSERT INTO coupon_templates (template_code, template_name, coupon_type, discount_value, min_amount, valid_days, total_quantity, per_user_limit) VALUES
('NEW_USER_50', '新用户50元代金券', 'FIXED', 50.00, 100.00, 30, 1000, 1),
('DISCOUNT_10', '全场9折优惠券', 'PERCENT', 10.00, 200.00, 30, 500, 2),
('FULL_200_30', '满200减30', 'FULL_REDUCTION', 30.00, 200.00, 30, 300, 1)
ON CONFLICT (template_code) DO NOTHING;

-- 插入测试订单数据
INSERT INTO orders (order_no, user_id, order_type, order_status, total_amount, actual_amount, payment_status) VALUES
('ORD202401010001', 1, 'NEW', 'COMPLETED', 99.00, 99.00, 'PAID'),
('ORD202401010002', 1, 'NEW', 'PENDING', 159.00, 159.00, 'UNPAID'),
('ORD202401010003', 2, 'NEW', 'COMPLETED', 299.00, 249.00, 'PAID')
ON CONFLICT (order_no) DO NOTHING;

-- 插入订单详情
INSERT INTO order_items (order_id, product_id, product_name, product_code, specifications, unit_price, duration_months, subtotal, actual_amount, vm_id) 
SELECT o.id, p.id, p.product_name, p.product_code, p.specifications, p.base_price, 1, p.base_price, p.base_price, 
    CASE 
        WHEN o.order_no = 'ORD202401010001' THEN 'vm-test-001'
        WHEN o.order_no = 'ORD202401010003' THEN 'vm-demo-001'
        ELSE NULL
    END
FROM orders o, products p 
WHERE (o.order_no = 'ORD202401010001' AND p.product_code = 'vm-basic-2c4g')
   OR (o.order_no = 'ORD202401010002' AND p.product_code = 'vm-standard-2c4g')
   OR (o.order_no = 'ORD202401010003' AND p.product_code = 'vm-standard-4c8g')
ON CONFLICT DO NOTHING;

-- 插入购物车测试数据
INSERT INTO shopping_cart (user_id, product_id, quantity, duration_months, specifications) 
SELECT 1, p.id, 1, 3, p.specifications 
FROM products p 
WHERE p.product_code = 'vm-high-8c16g'
ON CONFLICT DO NOTHING;

-- =============================================
-- 8. 创建触发器
-- =============================================

-- 创建更新时间触发器函数 (如果不存在)
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建触发器
CREATE TRIGGER update_product_categories_updated_time BEFORE UPDATE ON product_categories FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_products_updated_time BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_orders_updated_time BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_shopping_cart_updated_time BEFORE UPDATE ON shopping_cart FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_coupon_templates_updated_time BEFORE UPDATE ON coupon_templates FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_promotions_updated_time BEFORE UPDATE ON promotions FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- =============================================
-- 9. 创建视图
-- =============================================

-- 订单详情视图
CREATE OR REPLACE VIEW order_details AS
SELECT 
    o.order_no,
    o.user_id,
    o.order_type,
    o.order_status,
    o.total_amount,
    o.discount_amount,
    o.actual_amount,
    o.payment_status,
    o.payment_time,
    o.created_time,
    oi.product_name,
    oi.product_code,
    oi.specifications,
    oi.unit_price,
    oi.quantity,
    oi.duration_months,
    oi.vm_id,
    oi.service_start_time,
    oi.service_end_time
FROM orders o
LEFT JOIN order_items oi ON o.id = oi.order_id;

-- 产品详情视图
CREATE OR REPLACE VIEW product_details AS
SELECT 
    p.product_code,
    p.product_name,
    pc.category_name,
    p.product_type,
    p.description,
    p.specifications,
    p.base_price,
    p.unit,
    p.status,
    p.created_time
FROM products p
LEFT JOIN product_categories pc ON p.category_id = pc.id;

-- =============================================
-- 初始化完成
-- =============================================
SELECT 'CloudVPS 订单服务数据库初始化完成!' as message;
