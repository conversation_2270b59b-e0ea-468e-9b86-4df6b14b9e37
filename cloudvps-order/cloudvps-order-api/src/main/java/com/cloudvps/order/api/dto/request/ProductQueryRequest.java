package com.cloudvps.order.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.order.api.enums.ProductType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 产品查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品查询请求")
public class ProductQueryRequest extends PageRequest {
    
    @Schema(description = "产品分类ID")
    private Long categoryId;

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "产品代码")
    private String code;

    @Schema(description = "产品类型")
    private ProductType productType;
    
    @Schema(description = "最小价格")
    private BigDecimal minPrice;

    @Schema(description = "最大价格")
    private BigDecimal maxPrice;

    @Schema(description = "是否启用")
    private Boolean enabled;
    
    @Schema(description = "CPU核心数")
    private Integer cpuCores;

    @Schema(description = "内存大小(MB)")
    private Integer memoryMb;

    @Schema(description = "磁盘大小(GB)")
    private Integer diskGb;

    @Schema(description = "关键词搜索（名称、代码、描述）")
    private String keyword;
}
