package com.cloudvps.order.api.enums;

/**
 * 支付状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PaymentStatus {
    
    /**
     * 未支付
     */
    UNPAID("未支付"),
    
    /**
     * 已支付
     */
    PAID("已支付"),
    
    /**
     * 部分支付
     */
    PARTIAL_PAID("部分支付"),
    
    /**
     * 已退款
     */
    REFUNDED("已退款");
    
    private final String description;
    
    PaymentStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
