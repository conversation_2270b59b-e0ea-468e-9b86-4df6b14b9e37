package com.cloudvps.order.api.dto.request;

import com.cloudvps.order.api.enums.BillingCycle;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单项请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "订单项请求")
public class OrderItemRequest {
    
    @Schema(description = "产品ID", example = "1")
    @NotNull(message = "产品ID不能为空")
    private Long productId;
    
    @Schema(description = "数量", example = "1")
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于0")
    private Integer quantity = 1;
    
    @Schema(description = "计费周期", example = "MONTHLY")
    @NotNull(message = "计费周期不能为空")
    private BillingCycle billingCycle;
    
    @Schema(description = "服务期限（月）", example = "12")
    @NotNull(message = "服务期限不能为空")
    @Min(value = 1, message = "服务期限必须大于0")
    private Integer servicePeriod;
}
