package com.cloudvps.order.api.enums;

/**
 * 计费周期枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum BillingCycle {
    
    /**
     * 月付
     */
    MONTHLY("月付", 1),
    
    /**
     * 季付
     */
    QUARTERLY("季付", 3),
    
    /**
     * 年付
     */
    YEARLY("年付", 12);
    
    private final String description;
    private final int months;
    
    BillingCycle(String description, int months) {
        this.description = description;
        this.months = months;
    }
    
    public String getDescription() {
        return description;
    }
    
    public int getMonths() {
        return months;
    }
}
