package com.cloudvps.order.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.order.api.enums.OrderStatus;
import com.cloudvps.order.api.enums.OrderType;
import com.cloudvps.order.api.enums.PaymentStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单查询请求")
public class OrderQueryRequest extends PageRequest {
    
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "订单类型")
    private OrderType orderType;

    @Schema(description = "订单状态")
    private OrderStatus status;

    @Schema(description = "支付状态")
    private PaymentStatus paymentStatus;
    
    @Schema(description = "最小金额")
    private BigDecimal minTotalAmount;

    @Schema(description = "最大金额")
    private BigDecimal maxTotalAmount;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "联系电话")
    private String contactPhone;
    
    @Schema(description = "创建时间开始")
    private LocalDateTime createdTimeStart;

    @Schema(description = "创建时间结束")
    private LocalDateTime createdTimeEnd;

    @Schema(description = "支付时间开始")
    private LocalDateTime paidAtStart;

    @Schema(description = "支付时间结束")
    private LocalDateTime paidAtEnd;
}
