package com.cloudvps.order.api.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 联系信息请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "联系信息请求")
public class ContactInfoRequest {
    
    @Schema(description = "联系人姓名", example = "张三")
    @NotBlank(message = "联系人姓名不能为空")
    @Size(max = 100, message = "联系人姓名长度不能超过100个字符")
    private String name;
    
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    @NotBlank(message = "联系邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    @Schema(description = "联系电话", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
}
