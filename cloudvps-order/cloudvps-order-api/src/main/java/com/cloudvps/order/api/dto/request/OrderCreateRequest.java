package com.cloudvps.order.api.dto.request;

import com.cloudvps.order.api.enums.OrderType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 创建订单请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "创建订单请求")
public class OrderCreateRequest {
    
    @Schema(description = "订单类型", example = "NEW")
    @NotNull(message = "订单类型不能为空")
    private OrderType orderType = OrderType.NEW;
    
    @Schema(description = "订单项列表")
    @NotEmpty(message = "订单项不能为空")
    @Valid
    private List<OrderItemCreateRequest> items;
    
    @Schema(description = "优惠券代码", example = "WELCOME10")
    @Size(max = 50, message = "优惠券代码长度不能超过50个字符")
    private String couponCode;
    
    @Schema(description = "联系信息")
    @Valid
    private ContactInfoRequest contactInfo;
    
    @Schema(description = "订单备注", example = "请尽快开通服务")
    @Size(max = 500, message = "订单备注长度不能超过500个字符")
    private String notes;
}
