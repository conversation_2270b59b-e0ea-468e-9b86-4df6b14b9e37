package com.cloudvps.order.api.dto.request;

import com.cloudvps.order.api.enums.ProductType;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 产品更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ProductUpdateRequest {
    
    /**
     * 产品分类ID
     */
    private Long categoryId;
    
    /**
     * 产品名称
     */
    @Size(max = 200, message = "产品名称长度不能超过200个字符")
    private String name;
    
    /**
     * 产品描述
     */
    @Size(max = 2000, message = "产品描述长度不能超过2000个字符")
    private String description;
    
    /**
     * 产品类型
     */
    private ProductType productType;
    
    /**
     * CPU核心数
     */
    @Min(value = 1, message = "CPU核心数必须大于0")
    private Integer cpuCores;
    
    /**
     * 内存大小(MB)
     */
    @Min(value = 1, message = "内存大小必须大于0")
    private Integer memoryMb;
    
    /**
     * 磁盘大小(GB)
     */
    @Min(value = 1, message = "磁盘大小必须大于0")
    private Integer diskGb;
    
    /**
     * 带宽(Mbps)
     */
    @Min(value = 1, message = "带宽必须大于0")
    private Integer bandwidthMbps;
    
    /**
     * 月流量(GB)，0表示不限制
     */
    @Min(value = 0, message = "月流量不能为负数")
    private Integer trafficGb;
    
    /**
     * 月付价格
     */
    @Min(value = 0, message = "月付价格不能为负数")
    private BigDecimal priceMonthly;
    
    /**
     * 季付价格
     */
    @Min(value = 0, message = "季付价格不能为负数")
    private BigDecimal priceQuarterly;
    
    /**
     * 年付价格
     */
    @Min(value = 0, message = "年付价格不能为负数")
    private BigDecimal priceYearly;
    
    /**
     * 安装费
     */
    @Min(value = 0, message = "安装费不能为负数")
    private BigDecimal setupFee;
    
    /**
     * 库存数量，-1表示无限库存
     */
    @Min(value = -1, message = "库存数量不能小于-1")
    private Integer stockQuantity;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 排序
     */
    @Min(value = 0, message = "排序不能为负数")
    private Integer sortOrder;
}
