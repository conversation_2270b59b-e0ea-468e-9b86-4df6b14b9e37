package com.cloudvps.order.api.enums;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrderStatus {
    
    /**
     * 待支付
     */
    PENDING("待支付"),
    
    /**
     * 已支付
     */
    PAID("已支付"),
    
    /**
     * 处理中
     */
    PROCESSING("处理中"),
    
    /**
     * 已完成
     */
    COMPLETED("已完成"),
    
    /**
     * 已取消
     */
    CANCELLED("已取消"),
    
    /**
     * 已退款
     */
    REFUNDED("已退款");
    
    private final String description;
    
    OrderStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
