package com.cloudvps.order.api.dto.response;

import com.cloudvps.order.api.enums.OrderStatus;
import com.cloudvps.order.api.enums.OrderType;
import com.cloudvps.order.api.enums.PaymentStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "订单响应")
public class OrderResponse {
    
    @Schema(description = "订单ID")
    private Long id;
    
    @Schema(description = "订单号")
    private String orderNo;
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "订单类型")
    private OrderType orderType;
    
    @Schema(description = "订单状态")
    private OrderStatus status;
    
    @Schema(description = "支付状态")
    private PaymentStatus paymentStatus;
    
    @Schema(description = "小计金额")
    private BigDecimal subtotal;
    
    @Schema(description = "折扣金额")
    private BigDecimal discountAmount;
    
    @Schema(description = "税费金额")
    private BigDecimal taxAmount;
    
    @Schema(description = "总金额")
    private BigDecimal totalAmount;
    
    @Schema(description = "已付金额")
    private BigDecimal paidAmount;
    
    @Schema(description = "优惠券代码")
    private String couponCode;
    
    @Schema(description = "优惠券折扣")
    private BigDecimal couponDiscount;
    
    @Schema(description = "联系人姓名")
    private String contactName;
    
    @Schema(description = "联系邮箱")
    private String contactEmail;
    
    @Schema(description = "联系电话")
    private String contactPhone;
    
    @Schema(description = "订单备注")
    private String notes;
    
    @Schema(description = "管理员备注")
    private String adminNotes;
    
    @Schema(description = "订单过期时间")
    private LocalDateTime expiredAt;
    
    @Schema(description = "支付时间")
    private LocalDateTime paidAt;
    
    @Schema(description = "完成时间")
    private LocalDateTime completedAt;
    
    @Schema(description = "取消时间")
    private LocalDateTime cancelledAt;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    @Schema(description = "订单项列表")
    private List<OrderItemResponse> items;
}
