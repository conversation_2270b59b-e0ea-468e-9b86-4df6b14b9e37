package com.cloudvps.order.api.enums;

/**
 * 订单类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrderType {
    
    /**
     * 新购
     */
    NEW("新购"),
    
    /**
     * 续费
     */
    RENEW("续费"),
    
    /**
     * 升级
     */
    UPGRADE("升级"),
    
    /**
     * 降级
     */
    DOWNGRADE("降级");
    
    private final String description;
    
    OrderType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
