package com.cloudvps.order.api.client;

import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.order.api.dto.request.OrderCreateRequest;
import com.cloudvps.order.api.dto.response.OrderResponse;
import com.cloudvps.order.api.dto.response.ProductResponse;
import org.springframework.cloud.openfeign.FeignClient;
import com.cloudvps.common.core.response.PageResponse;
import org.springframework.web.bind.annotation.*;

/**
 * 订单服务Feign客户端
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@FeignClient(
    name = "order-service",
    url = "${cloudvps.services.order.url:http://localhost:8083}",
    path = "/api/v1"
)
public interface OrderServiceClient {
    
    /**
     * 创建订单
     */
    @PostMapping("/orders")
    ApiResponse<OrderResponse> createOrder(@RequestBody OrderCreateRequest request);
    
    /**
     * 根据ID获取订单
     */
    @GetMapping("/orders/{id}")
    ApiResponse<OrderResponse> getOrderById(@PathVariable("id") Long id);
    
    /**
     * 根据订单号获取订单
     */
    @GetMapping("/orders/by-no/{orderNo}")
    ApiResponse<OrderResponse> getOrderByNo(@PathVariable("orderNo") String orderNo);
    
    /**
     * 分页查询用户订单
     */
    @GetMapping("/orders/my")
    ApiResponse<PageResponse<OrderResponse>> getMyOrders(@RequestParam int page, @RequestParam int size);
    
    /**
     * 取消订单
     */
    @PostMapping("/orders/{id}/cancel")
    ApiResponse<OrderResponse> cancelOrder(@PathVariable("id") Long id);
    
    /**
     * 分页查询产品
     */
    @GetMapping("/products")
    ApiResponse<PageResponse<ProductResponse>> getProducts(
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) Long categoryId,
        @RequestParam int page,
        @RequestParam int size
    );
    
    /**
     * 根据ID获取产品
     */
    @GetMapping("/products/{id}")
    ApiResponse<ProductResponse> getProductById(@PathVariable("id") Long id);
}
