package com.cloudvps.order.api.dto.request;

import com.cloudvps.order.api.enums.OrderStatus;
import com.cloudvps.order.api.enums.PaymentStatus;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class OrderUpdateRequest {
    
    /**
     * 订单状态
     */
    private OrderStatus status;
    
    /**
     * 支付状态
     */
    private PaymentStatus paymentStatus;
    
    /**
     * 税费金额
     */
    private BigDecimal taxAmount;
    
    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;
    
    /**
     * 优惠券代码
     */
    @Size(max = 50, message = "优惠券代码长度不能超过50个字符")
    private String couponCode;
    
    /**
     * 优惠券折扣
     */
    private BigDecimal couponDiscount;
    
    /**
     * 联系人姓名
     */
    @Size(max = 100, message = "联系人姓名长度不能超过100个字符")
    private String contactName;
    
    /**
     * 联系邮箱
     */
    @Email(message = "联系邮箱格式不正确")
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    private String contactEmail;
    
    /**
     * 联系电话
     */
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String contactPhone;
    
    /**
     * 订单备注
     */
    @Size(max = 1000, message = "订单备注长度不能超过1000个字符")
    private String notes;
    
    /**
     * 管理员备注
     */
    @Size(max = 1000, message = "管理员备注长度不能超过1000个字符")
    private String adminNotes;
    
    /**
     * 订单过期时间
     */
    private LocalDateTime expiredAt;
    
    /**
     * 支付时间
     */
    private LocalDateTime paidAt;
    
    /**
     * 完成时间
     */
    private LocalDateTime completedAt;
    
    /**
     * 取消时间
     */
    private LocalDateTime cancelledAt;
}
