package com.cloudvps.order.api.dto.response;

import com.cloudvps.order.api.enums.ProductType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "产品响应")
public class ProductResponse {
    
    @Schema(description = "产品ID")
    private Long id;
    
    @Schema(description = "产品分类ID")
    private Long categoryId;

    @Schema(description = "产品分类名称")
    private String categoryName;

    @Schema(description = "产品名称")
    private String name;
    
    @Schema(description = "产品代码")
    private String code;
    
    @Schema(description = "产品描述")
    private String description;
    
    @Schema(description = "产品类型")
    private ProductType productType;
    
    @Schema(description = "CPU核心数")
    private Integer cpuCores;
    
    @Schema(description = "内存大小(MB)")
    private Integer memoryMb;
    
    @Schema(description = "磁盘大小(GB)")
    private Integer diskGb;
    
    @Schema(description = "带宽(Mbps)")
    private Integer bandwidthMbps;
    
    @Schema(description = "月流量(GB)")
    private Integer trafficGb;
    
    @Schema(description = "月付价格")
    private BigDecimal priceMonthly;
    
    @Schema(description = "季付价格")
    private BigDecimal priceQuarterly;
    
    @Schema(description = "年付价格")
    private BigDecimal priceYearly;
    
    @Schema(description = "安装费")
    private BigDecimal setupFee;
    
    @Schema(description = "库存数量")
    private Integer stockQuantity;
    
    @Schema(description = "已售数量")
    private Integer soldQuantity;
    
    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "是否可用（启用且有库存）")
    private Boolean available;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;
}
