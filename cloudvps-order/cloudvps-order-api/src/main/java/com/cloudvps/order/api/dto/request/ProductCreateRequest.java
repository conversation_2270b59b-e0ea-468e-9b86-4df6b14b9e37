package com.cloudvps.order.api.dto.request;

import com.cloudvps.order.api.enums.ProductType;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 产品创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ProductCreateRequest {
    
    /**
     * 产品分类ID
     */
    @NotNull(message = "产品分类ID不能为空")
    private Long categoryId;
    
    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    @Size(max = 200, message = "产品名称长度不能超过200个字符")
    private String name;
    
    /**
     * 产品代码
     */
    @NotBlank(message = "产品代码不能为空")
    @Size(max = 100, message = "产品代码长度不能超过100个字符")
    private String code;
    
    /**
     * 产品描述
     */
    @Size(max = 2000, message = "产品描述长度不能超过2000个字符")
    private String description;
    
    /**
     * 产品类型
     */
    @NotNull(message = "产品类型不能为空")
    private ProductType productType;
    
    /**
     * CPU核心数
     */
    @Min(value = 1, message = "CPU核心数必须大于0")
    private Integer cpuCores;
    
    /**
     * 内存大小(MB)
     */
    @Min(value = 1, message = "内存大小必须大于0")
    private Integer memoryMb;
    
    /**
     * 磁盘大小(GB)
     */
    @Min(value = 1, message = "磁盘大小必须大于0")
    private Integer diskGb;
    
    /**
     * 带宽(Mbps)
     */
    @Min(value = 1, message = "带宽必须大于0")
    private Integer bandwidthMbps;
    
    /**
     * 月流量(GB)，0表示不限制
     */
    @Min(value = 0, message = "月流量不能为负数")
    private Integer trafficGb;
    
    /**
     * 月付价格
     */
    @NotNull(message = "月付价格不能为空")
    @Min(value = 0, message = "月付价格不能为负数")
    private BigDecimal priceMonthly;
    
    /**
     * 季付价格
     */
    @Min(value = 0, message = "季付价格不能为负数")
    private BigDecimal priceQuarterly;
    
    /**
     * 年付价格
     */
    @Min(value = 0, message = "年付价格不能为负数")
    private BigDecimal priceYearly;
    
    /**
     * 安装费
     */
    @Min(value = 0, message = "安装费不能为负数")
    private BigDecimal setupFee = BigDecimal.ZERO;
    
    /**
     * 库存数量，-1表示无限库存
     */
    @Min(value = -1, message = "库存数量不能小于-1")
    private Integer stockQuantity = -1;
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;
    
    /**
     * 排序
     */
    @Min(value = 0, message = "排序不能为负数")
    private Integer sortOrder = 0;
}
