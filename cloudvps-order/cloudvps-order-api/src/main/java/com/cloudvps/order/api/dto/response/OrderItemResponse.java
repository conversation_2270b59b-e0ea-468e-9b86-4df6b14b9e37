package com.cloudvps.order.api.dto.response;

import com.cloudvps.order.api.enums.BillingCycle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单项响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "订单项响应")
public class OrderItemResponse {
    
    @Schema(description = "订单项ID")
    private Long id;
    
    @Schema(description = "订单ID")
    private Long orderId;
    
    @Schema(description = "产品ID")
    private Long productId;
    
    @Schema(description = "产品名称")
    private String productName;
    
    @Schema(description = "产品代码")
    private String productCode;
    
    @Schema(description = "产品配置快照")
    private String productConfig;
    
    @Schema(description = "数量")
    private Integer quantity;
    
    @Schema(description = "单价")
    private BigDecimal unitPrice;
    
    @Schema(description = "总价")
    private BigDecimal totalPrice;
    
    @Schema(description = "计费周期")
    private BillingCycle billingCycle;
    
    @Schema(description = "服务期限（月）")
    private Integer servicePeriod;
    
    @Schema(description = "状态")
    private String status;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
