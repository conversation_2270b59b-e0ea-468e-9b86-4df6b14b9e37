package com.cloudvps.order.api.dto.request;

import com.cloudvps.order.api.enums.BillingCycle;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单项创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class OrderItemCreateRequest {
    
    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空")
    private Long productId;
    
    /**
     * 产品名称快照
     */
    @NotBlank(message = "产品名称不能为空")
    @Size(max = 200, message = "产品名称长度不能超过200个字符")
    private String productName;
    
    /**
     * 产品代码快照
     */
    @NotBlank(message = "产品代码不能为空")
    @Size(max = 100, message = "产品代码长度不能超过100个字符")
    private String productCode;
    
    /**
     * 产品配置快照
     */
    @Size(max = 2000, message = "产品配置长度不能超过2000个字符")
    private String productConfig;
    
    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于0")
    private Integer quantity = 1;
    
    /**
     * 单价
     */
    @NotNull(message = "单价不能为空")
    @Min(value = 0, message = "单价不能为负数")
    private BigDecimal unitPrice;
    
    /**
     * 计费周期
     */
    @NotNull(message = "计费周期不能为空")
    private BillingCycle billingCycle;
    
    /**
     * 服务期限（月）
     */
    @NotNull(message = "服务期限不能为空")
    @Min(value = 1, message = "服务期限必须大于0")
    private Integer servicePeriod;
    
    /**
     * 状态
     */
    @Size(max = 20, message = "状态长度不能超过20个字符")
    private String status = "PENDING";
}
