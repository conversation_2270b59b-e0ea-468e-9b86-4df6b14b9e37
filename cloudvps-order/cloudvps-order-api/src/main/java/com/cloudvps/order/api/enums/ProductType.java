package com.cloudvps.order.api.enums;

/**
 * 产品类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ProductType {
    
    /**
     * VPS虚拟专用服务器
     */
    VPS("VPS虚拟专用服务器"),
    
    /**
     * 独立服务器
     */
    DEDICATED("独立服务器"),
    
    /**
     * 云存储
     */
    CLOUD_STORAGE("云存储"),
    
    /**
     * 带宽
     */
    BANDWIDTH("带宽"),
    
    /**
     * IP地址
     */
    IP_ADDRESS("IP地址");
    
    private final String description;
    
    ProductType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
