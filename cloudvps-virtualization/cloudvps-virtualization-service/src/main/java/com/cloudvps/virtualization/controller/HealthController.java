package com.cloudvps.virtualization.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@RestController
@RequestMapping("/health")
public class HealthController {
    
    @GetMapping
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "cloudvps-virtualization-service");
        result.put("timestamp", LocalDateTime.now());
        result.put("port", 8082);
        return result;
    }
    
    @GetMapping("/info")
    public Map<String, Object> info() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "CloudVPS Virtualization Service");
        result.put("version", "1.0.0-SNAPSHOT");
        result.put("description", "虚拟化服务 - PVE节点和虚拟机管理");
        result.put("features", new String[]{
            "PVE节点管理",
            "虚拟机生命周期管理", 
            "资源监控",
            "批量操作",
            "自动化任务"
        });
        return result;
    }
}
