package com.cloudvps.virtualization.client;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 节点状态信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class NodeStatusInfo {
    
    /**
     * 是否在线
     */
    private boolean online;
    
    /**
     * CPU使用率
     */
    private BigDecimal cpuUsage;
    
    /**
     * 内存使用率
     */
    private BigDecimal memoryUsage;
    
    /**
     * 磁盘使用率
     */
    private BigDecimal diskUsage;
    
    /**
     * 运行时间（秒）
     */
    private Long uptime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建离线状态信息
     */
    public static NodeStatusInfo offline() {
        NodeStatusInfo info = new NodeStatusInfo();
        info.setOnline(false);
        info.setCpuUsage(BigDecimal.ZERO);
        info.setMemoryUsage(BigDecimal.ZERO);
        info.setDiskUsage(BigDecimal.ZERO);
        info.setUptime(0L);
        return info;
    }
    
    /**
     * 创建错误状态信息
     */
    public static NodeStatusInfo error(String errorMessage) {
        NodeStatusInfo info = offline();
        info.setErrorMessage(errorMessage);
        return info;
    }
}
