package com.cloudvps.virtualization.convert;

import com.cloudvps.virtualization.api.dto.request.PveNodeCreateRequest;
import com.cloudvps.virtualization.api.dto.request.PveNodeUpdateRequest;
import com.cloudvps.virtualization.api.dto.response.PveNodeResponse;
import com.cloudvps.virtualization.entity.PveNode;
import org.mapstruct.*;

/**
 * PVE节点对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface PveNodeConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "cpuUsage", constant = "0.00")
    @Mapping(target = "memoryUsage", constant = "0.00")
    @Mapping(target = "diskUsage", constant = "0.00")
    @Mapping(target = "uptime", constant = "0L")
    @Mapping(target = "lastHeartbeat", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    PveNode toEntity(PveNodeCreateRequest request);
    
    /**
     * 实体转响应
     */
    @Mapping(target = "vmCount", ignore = true) // 需要在Service层设置
    @Mapping(target = "runningVmCount", ignore = true) // 需要在Service层设置
    PveNodeResponse toResponse(PveNode pveNode);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "nodeName", ignore = true) // 节点名称不允许修改
    @Mapping(target = "cpuUsage", ignore = true) // 使用率通过监控更新
    @Mapping(target = "memoryUsage", ignore = true)
    @Mapping(target = "diskUsage", ignore = true)
    @Mapping(target = "uptime", ignore = true)
    @Mapping(target = "lastHeartbeat", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget PveNode pveNode, PveNodeUpdateRequest request);
}
