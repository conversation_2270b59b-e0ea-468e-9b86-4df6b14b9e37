package com.cloudvps.virtualization.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * PVE配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pve")
public class PveConfig {
    
    /**
     * 默认PVE配置
     */
    private DefaultConfig defaultConfig = new DefaultConfig();
    
    /**
     * 连接池配置
     */
    private PoolConfig pool = new PoolConfig();
    
    @Data
    public static class DefaultConfig {
        /**
         * 默认主机地址
         */
        private String host = "*************";
        
        /**
         * 默认端口
         */
        private Integer port = 8006;
        
        /**
         * 默认用户名
         */
        private String username = "root@pam";
        
        /**
         * 默认密码
         */
        private String password = "your-password";
        
        /**
         * 是否验证SSL
         */
        private Boolean verifySsl = false;
        
        /**
         * 连接超时时间（毫秒）
         */
        private Integer timeout = 30000;
    }
    
    @Data
    public static class PoolConfig {
        /**
         * 最大连接数
         */
        private Integer maxConnections = 10;
        
        /**
         * 每个路由的最大连接数
         */
        private Integer maxConnectionsPerRoute = 5;
        
        /**
         * 连接超时时间（毫秒）
         */
        private Integer connectionTimeout = 5000;
        
        /**
         * Socket超时时间（毫秒）
         */
        private Integer socketTimeout = 30000;
        
        /**
         * 连接请求超时时间（毫秒）
         */
        private Integer connectionRequestTimeout = 5000;
    }
    
    /**
     * 获取默认连接URL
     */
    public String getDefaultConnectionUrl() {
        return String.format("https://%s:%d", defaultConfig.getHost(), defaultConfig.getPort());
    }
}
