package com.cloudvps.virtualization.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineCreateRequest;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineQueryRequest;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineUpdateRequest;
import com.cloudvps.virtualization.api.dto.response.VirtualMachineResponse;
import com.cloudvps.virtualization.api.enums.VmStatus;

import java.util.List;

/**
 * 虚拟机服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface VirtualMachineService {
    
    /**
     * 分页查询虚拟机
     */
    IPage<VirtualMachineResponse> queryVirtualMachines(VirtualMachineQueryRequest request);
    
    /**
     * 根据ID获取虚拟机
     */
    VirtualMachineResponse findById(Long id);
    
    /**
     * 根据VMID获取虚拟机
     */
    VirtualMachineResponse findByVmid(Integer vmid);
    
    /**
     * 创建虚拟机
     */
    VirtualMachineResponse createVirtualMachine(VirtualMachineCreateRequest request);
    
    /**
     * 更新虚拟机
     */
    VirtualMachineResponse updateVirtualMachine(Long id, VirtualMachineUpdateRequest request);
    
    /**
     * 删除虚拟机
     */
    void deleteVirtualMachine(Long id);
    
    /**
     * 批量删除虚拟机
     */
    void batchDeleteVirtualMachines(List<Long> vmIds);
    
    /**
     * 检查VMID是否存在
     */
    boolean existsByVmid(Integer vmid);
    
    /**
     * 根据用户ID查询虚拟机
     */
    List<VirtualMachineResponse> findByUserId(Long userId);
    
    /**
     * 根据节点ID查询虚拟机
     */
    List<VirtualMachineResponse> findByNodeId(Long nodeId);
    
    /**
     * 根据状态查询虚拟机
     */
    List<VirtualMachineResponse> findByStatus(VmStatus status);
    
    /**
     * 获取运行中的虚拟机
     */
    List<VirtualMachineResponse> findRunningVms();
    
    /**
     * 获取已停止的虚拟机
     */
    List<VirtualMachineResponse> findStoppedVms();
    
    /**
     * 更新虚拟机状态
     */
    VirtualMachineResponse updateVmStatus(Long id, VmStatus status);
    
    /**
     * 统计用户的虚拟机数量
     */
    Long countByUserId(Long userId);
    
    /**
     * 统计节点的虚拟机数量
     */
    Long countByNodeId(Long nodeId);
    
    /**
     * 统计节点运行中的虚拟机数量
     */
    Long countRunningByNodeId(Long nodeId);
}
