package com.cloudvps.virtualization.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;

import com.cloudvps.virtualization.api.dto.request.VirtualMachineCreateRequest;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineQueryRequest;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineUpdateRequest;
import com.cloudvps.virtualization.api.dto.response.VirtualMachineResponse;
import com.cloudvps.virtualization.api.enums.VmStatus;
import com.cloudvps.virtualization.service.VirtualMachineService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 虚拟机控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/virtualization/vms")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "虚拟机管理", description = "虚拟机的增删改查和生命周期管理")
public class VirtualMachineController {
    
    private final VirtualMachineService virtualMachineService;
    
    @GetMapping
    @Operation(summary = "分页查询虚拟机", description = "分页查询虚拟机列表")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<IPage<VirtualMachineResponse>> getVirtualMachines(@Valid VirtualMachineQueryRequest request) {
        log.debug("分页查询虚拟机: {}", request);
        IPage<VirtualMachineResponse> vms = virtualMachineService.queryVirtualMachines(request);
        return ApiResponse.success(vms);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取虚拟机详情", description = "根据虚拟机ID获取虚拟机详细信息")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<VirtualMachineResponse> getVirtualMachineById(
            @Parameter(description = "虚拟机ID") @PathVariable Long id) {
        log.debug("获取虚拟机详情: vmId={}", id);
        VirtualMachineResponse vm = virtualMachineService.findById(id);
        return ApiResponse.success(vm);
    }
    
    @GetMapping("/by-vmid/{vmid}")
    @Operation(summary = "根据VMID获取虚拟机", description = "根据PVE虚拟机ID获取虚拟机信息")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<VirtualMachineResponse> getVirtualMachineByVmid(
            @Parameter(description = "PVE虚拟机ID") @PathVariable Integer vmid) {
        log.debug("根据VMID获取虚拟机: vmid={}", vmid);
        VirtualMachineResponse vm = virtualMachineService.findByVmid(vmid);
        return ApiResponse.success(vm);
    }
    
    @PostMapping
    @Operation(summary = "创建虚拟机", description = "创建新的虚拟机")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_CREATE')")
    public ApiResponse<VirtualMachineResponse> createVirtualMachine(@Valid @RequestBody VirtualMachineCreateRequest request) {
        log.info("创建虚拟机请求: vmid={}, name={}, userId={}", 
                request.getVmid(), request.getName(), request.getUserId());
        VirtualMachineResponse vm = virtualMachineService.createVirtualMachine(request);
        return ApiResponse.success("创建成功", vm);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新虚拟机", description = "更新指定虚拟机信息")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_UPDATE')")
    public ApiResponse<VirtualMachineResponse> updateVirtualMachine(
            @Parameter(description = "虚拟机ID") @PathVariable Long id,
            @Valid @RequestBody VirtualMachineUpdateRequest request) {
        log.info("更新虚拟机请求: vmId={}", id);
        VirtualMachineResponse vm = virtualMachineService.updateVirtualMachine(id, request);
        return ApiResponse.success("更新成功", vm);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除虚拟机", description = "删除指定虚拟机")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_DELETE')")
    public ApiResponse<Void> deleteVirtualMachine(
            @Parameter(description = "虚拟机ID") @PathVariable Long id) {
        log.info("删除虚拟机请求: vmId={}", id);
        virtualMachineService.deleteVirtualMachine(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除虚拟机", description = "批量删除指定虚拟机")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_DELETE')")
    public ApiResponse<Void> batchDeleteVirtualMachines(@RequestBody List<Long> vmIds) {
        log.info("批量删除虚拟机请求: vmIds={}", vmIds);
        virtualMachineService.batchDeleteVirtualMachines(vmIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @GetMapping("/user/{userId}")
    @Operation(summary = "根据用户ID查询虚拟机", description = "根据用户ID查询虚拟机列表")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<List<VirtualMachineResponse>> getVirtualMachinesByUserId(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        log.debug("根据用户ID查询虚拟机: userId={}", userId);
        List<VirtualMachineResponse> vms = virtualMachineService.findByUserId(userId);
        return ApiResponse.success(vms);
    }
    
    @GetMapping("/my")
    @Operation(summary = "获取当前用户的虚拟机", description = "获取当前登录用户的虚拟机列表")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<List<VirtualMachineResponse>> getMyVirtualMachines(
            @Parameter(description = "用户ID") @RequestParam Long userId) {
        log.debug("获取当前用户的虚拟机: userId={}", userId);
        List<VirtualMachineResponse> vms = virtualMachineService.findByUserId(userId);
        return ApiResponse.success(vms);
    }
    
    @GetMapping("/node/{nodeId}")
    @Operation(summary = "根据节点ID查询虚拟机", description = "根据节点ID查询虚拟机列表")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<List<VirtualMachineResponse>> getVirtualMachinesByNodeId(
            @Parameter(description = "节点ID") @PathVariable Long nodeId) {
        log.debug("根据节点ID查询虚拟机: nodeId={}", nodeId);
        List<VirtualMachineResponse> vms = virtualMachineService.findByNodeId(nodeId);
        return ApiResponse.success(vms);
    }
    
    @GetMapping("/status/{status}")
    @Operation(summary = "根据状态查询虚拟机", description = "根据虚拟机状态查询虚拟机列表")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<List<VirtualMachineResponse>> getVirtualMachinesByStatus(
            @Parameter(description = "虚拟机状态") @PathVariable VmStatus status) {
        log.debug("根据状态查询虚拟机: status={}", status);
        List<VirtualMachineResponse> vms = virtualMachineService.findByStatus(status);
        return ApiResponse.success(vms);
    }
    
    @GetMapping("/running")
    @Operation(summary = "获取运行中的虚拟机", description = "获取所有运行中的虚拟机")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<List<VirtualMachineResponse>> getRunningVirtualMachines() {
        log.debug("获取运行中的虚拟机");
        List<VirtualMachineResponse> vms = virtualMachineService.findRunningVms();
        return ApiResponse.success(vms);
    }
    
    @GetMapping("/stopped")
    @Operation(summary = "获取已停止的虚拟机", description = "获取所有已停止的虚拟机")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<List<VirtualMachineResponse>> getStoppedVirtualMachines() {
        log.debug("获取已停止的虚拟机");
        List<VirtualMachineResponse> vms = virtualMachineService.findStoppedVms();
        return ApiResponse.success(vms);
    }
    
    @PutMapping("/{id}/status")
    @Operation(summary = "更新虚拟机状态", description = "更新指定虚拟机的状态")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_UPDATE')")
    public ApiResponse<VirtualMachineResponse> updateVirtualMachineStatus(
            @Parameter(description = "虚拟机ID") @PathVariable Long id,
            @Parameter(description = "虚拟机状态") @RequestParam VmStatus status) {
        log.info("更新虚拟机状态: vmId={}, status={}", id, status);
        VirtualMachineResponse vm = virtualMachineService.updateVmStatus(id, status);
        return ApiResponse.success("状态更新成功", vm);
    }
    
    @GetMapping("/count/user/{userId}")
    @Operation(summary = "统计用户虚拟机数量", description = "统计指定用户的虚拟机数量")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<Long> countVirtualMachinesByUserId(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        log.debug("统计用户虚拟机数量: userId={}", userId);
        Long count = virtualMachineService.countByUserId(userId);
        return ApiResponse.success(count);
    }
    
    @GetMapping("/count/node/{nodeId}")
    @Operation(summary = "统计节点虚拟机数量", description = "统计指定节点的虚拟机数量")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<Long> countVirtualMachinesByNodeId(
            @Parameter(description = "节点ID") @PathVariable Long nodeId) {
        log.debug("统计节点虚拟机数量: nodeId={}", nodeId);
        Long count = virtualMachineService.countByNodeId(nodeId);
        return ApiResponse.success(count);
    }
    
    @GetMapping("/count/running/node/{nodeId}")
    @Operation(summary = "统计节点运行中虚拟机数量", description = "统计指定节点运行中的虚拟机数量")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<Long> countRunningVirtualMachinesByNodeId(
            @Parameter(description = "节点ID") @PathVariable Long nodeId) {
        log.debug("统计节点运行中虚拟机数量: nodeId={}", nodeId);
        Long count = virtualMachineService.countRunningByNodeId(nodeId);
        return ApiResponse.success(count);
    }
    
    @GetMapping("/check-vmid")
    @Operation(summary = "检查VMID", description = "检查PVE虚拟机ID是否已存在")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
    public ApiResponse<Boolean> checkVmid(@RequestParam Integer vmid) {
        boolean exists = virtualMachineService.existsByVmid(vmid);
        return ApiResponse.success(exists ? "VMID已存在" : "VMID可用", !exists);
    }
}
