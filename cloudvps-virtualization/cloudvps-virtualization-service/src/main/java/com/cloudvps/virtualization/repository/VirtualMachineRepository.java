package com.cloudvps.virtualization.repository;

import com.cloudvps.virtualization.entity.VirtualMachine;
import com.cloudvps.virtualization.api.enums.VmStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 虚拟机Repository
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface VirtualMachineRepository extends JpaRepository<VirtualMachine, Long> {
    
    /**
     * 根据用户ID查找虚拟机
     */
    Page<VirtualMachine> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据用户ID和状态查找虚拟机
     */
    List<VirtualMachine> findByUserIdAndStatus(Long userId, VmStatus status);
    
    /**
     * 根据节点ID查找虚拟机
     */
    List<VirtualMachine> findByNodeId(Long nodeId);
    
    /**
     * 根据节点ID和状态查找虚拟机
     */
    List<VirtualMachine> findByNodeIdAndStatus(Long nodeId, VmStatus status);
    
    /**
     * 根据VMID和节点ID查找虚拟机
     */
    Optional<VirtualMachine> findByVmidAndNodeId(Integer vmid, Long nodeId);
    
    /**
     * 检查VMID在节点上是否存在
     */
    boolean existsByVmidAndNodeId(Integer vmid, Long nodeId);
    
    /**
     * 根据状态查找虚拟机
     */
    List<VirtualMachine> findByStatus(VmStatus status);
    
    /**
     * 根据虚拟机名称模糊查询
     */
    @Query("SELECT vm FROM VirtualMachine vm WHERE vm.name LIKE %:name%")
    Page<VirtualMachine> findByNameContaining(@Param("name") String name, Pageable pageable);
    
    /**
     * 统计用户的虚拟机数量
     */
    long countByUserId(Long userId);
    
    /**
     * 统计用户指定状态的虚拟机数量
     */
    long countByUserIdAndStatus(Long userId, VmStatus status);
    
    /**
     * 统计节点上的虚拟机数量
     */
    long countByNodeId(Long nodeId);
    
    /**
     * 统计节点上指定状态的虚拟机数量
     */
    long countByNodeIdAndStatus(Long nodeId, VmStatus status);
    
    /**
     * 更新虚拟机状态
     */
    @Modifying
    @Query("UPDATE VirtualMachine vm SET vm.status = :status WHERE vm.id = :id")
    int updateVmStatus(@Param("id") Long id, @Param("status") VmStatus status);
    
    /**
     * 更新虚拟机IP地址
     */
    @Modifying
    @Query("UPDATE VirtualMachine vm SET vm.ipAddress = :ipAddress WHERE vm.id = :id")
    int updateVmIpAddress(@Param("id") Long id, @Param("ipAddress") String ipAddress);
    
    /**
     * 查找需要自动启动的虚拟机
     */
    @Query("SELECT vm FROM VirtualMachine vm WHERE vm.autoStart = true AND vm.status = 'STOPPED'")
    List<VirtualMachine> findAutoStartVms();
    
    /**
     * 根据模板ID查找虚拟机
     */
    List<VirtualMachine> findByTemplateId(Long templateId);
    
    /**
     * 获取节点上的下一个可用VMID
     */
    @Query("SELECT COALESCE(MAX(vm.vmid), 99) + 1 FROM VirtualMachine vm WHERE vm.nodeId = :nodeId")
    Integer getNextVmid(@Param("nodeId") Long nodeId);
    
    /**
     * 查找用户在指定节点上的虚拟机
     */
    List<VirtualMachine> findByUserIdAndNodeId(Long userId, Long nodeId);
}
