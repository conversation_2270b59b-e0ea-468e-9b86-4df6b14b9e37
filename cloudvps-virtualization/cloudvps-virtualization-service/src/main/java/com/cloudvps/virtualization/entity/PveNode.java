package com.cloudvps.virtualization.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.virtualization.api.enums.NodeStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * PVE节点实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pve_nodes")
public class PveNode extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 节点名称
     */
    @TableField("node_name")
    private String nodeName;
    
    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口
     */
    private Integer port = 8006;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 是否验证SSL
     */
    @TableField("verify_ssl")
    private Boolean verifySsl = false;
    
    /**
     * 节点状态
     */
    private NodeStatus status = NodeStatus.OFFLINE;

    /**
     * CPU使用率
     */
    @TableField("cpu_usage")
    private BigDecimal cpuUsage = BigDecimal.ZERO;

    /**
     * 内存使用率
     */
    @TableField("memory_usage")
    private BigDecimal memoryUsage = BigDecimal.ZERO;

    /**
     * 磁盘使用率
     */
    @TableField("disk_usage")
    private BigDecimal diskUsage = BigDecimal.ZERO;
    
    /**
     * 运行时间（秒）
     */
    private Long uptime = 0L;

    /**
     * 最后心跳时间
     */
    @TableField("last_heartbeat")
    private LocalDateTime lastHeartbeat;
    

    
    /**
     * 获取节点连接URL
     */
    public String getConnectionUrl() {
        return String.format("https://%s:%d", host, port);
    }
    
    /**
     * 检查节点是否在线
     */
    public boolean isOnline() {
        return NodeStatus.ONLINE.equals(this.status);
    }
    
    /**
     * 检查节点是否可用
     */
    public boolean isAvailable() {
        return NodeStatus.ONLINE.equals(this.status) || NodeStatus.MAINTENANCE.equals(this.status);
    }
}
