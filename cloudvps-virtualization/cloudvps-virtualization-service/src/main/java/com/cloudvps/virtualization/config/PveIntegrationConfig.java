package com.cloudvps.virtualization.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * PVE集成配置
 * 用于控制PVE集成模式和相关参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pve")
public class PveIntegrationConfig {

    /**
     * Mock模式配置
     */
    private MockConfig mock = new MockConfig();

    /**
     * 连接配置
     */
    private ConnectionConfig connection = new ConnectionConfig();

    /**
     * Mock模式配置
     */
    @Data
    public static class MockConfig {
        /**
         * 是否启用Mock模式
         * true: 使用模拟数据和操作
         * false: 使用真实PVE API
         */
        private boolean enabled = true;

        /**
         * 模拟操作延迟（毫秒）
         */
        private int delay = 500;

        /**
         * 模拟资源使用率范围
         */
        private ResourceUsageRange resourceUsage = new ResourceUsageRange();
    }

    /**
     * 连接配置
     */
    @Data
    public static class ConnectionConfig {
        /**
         * 连接超时时间（毫秒）
         */
        private int timeout = 10000;

        /**
         * 重试次数
         */
        private int retryCount = 3;

        /**
         * 是否验证SSL证书
         */
        private boolean verifySsl = false;

        /**
         * 连接池大小
         */
        private int poolSize = 10;
    }

    /**
     * 资源使用率范围配置
     */
    @Data
    public static class ResourceUsageRange {
        /**
         * CPU使用率范围
         */
        private Range cpu = new Range(10.0, 80.0);

        /**
         * 内存使用率范围
         */
        private Range memory = new Range(20.0, 70.0);

        /**
         * 磁盘使用率范围
         */
        private Range disk = new Range(15.0, 60.0);
    }

    /**
     * 数值范围
     */
    @Data
    public static class Range {
        private double min;
        private double max;

        public Range() {}

        public Range(double min, double max) {
            this.min = min;
            this.max = max;
        }

        /**
         * 生成范围内的随机值
         */
        public double randomValue() {
            return min + Math.random() * (max - min);
        }
    }
}
