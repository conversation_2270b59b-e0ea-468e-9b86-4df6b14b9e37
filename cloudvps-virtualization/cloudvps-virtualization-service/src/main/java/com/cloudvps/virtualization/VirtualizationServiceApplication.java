package com.cloudvps.virtualization;

import com.cloudvps.common.security.annotation.EnableCloudVpsSecurity;
import com.cloudvps.common.swagger.annotation.EnableCloudVpsSwagger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 虚拟化服务启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.cloudvps.virtualization", "com.cloudvps.common"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.cloudvps.system.api.client"})
@EnableAsync
@EnableScheduling
// @EnableCloudVpsSecurity  // 暂时禁用，避免循环依赖
@EnableCloudVpsSwagger
public class VirtualizationServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(VirtualizationServiceApplication.class, args);
    }
}
