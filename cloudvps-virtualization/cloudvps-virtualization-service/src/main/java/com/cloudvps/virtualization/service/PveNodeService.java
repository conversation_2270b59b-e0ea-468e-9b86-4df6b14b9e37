package com.cloudvps.virtualization.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.virtualization.api.dto.request.PveNodeCreateRequest;
import com.cloudvps.virtualization.api.dto.request.PveNodeQueryRequest;
import com.cloudvps.virtualization.api.dto.request.PveNodeUpdateRequest;
import com.cloudvps.virtualization.api.dto.response.PveNodeResponse;
import com.cloudvps.virtualization.api.enums.NodeStatus;

import java.math.BigDecimal;
import java.util.List;

/**
 * PVE节点服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PveNodeService {
    
    /**
     * 分页查询PVE节点
     */
    IPage<PveNodeResponse> queryNodes(PveNodeQueryRequest request);
    
    /**
     * 根据ID获取PVE节点
     */
    PveNodeResponse findById(Long id);
    
    /**
     * 根据节点名称获取PVE节点
     */
    PveNodeResponse findByNodeName(String nodeName);
    
    /**
     * 创建PVE节点
     */
    PveNodeResponse createNode(PveNodeCreateRequest request);
    
    /**
     * 更新PVE节点
     */
    PveNodeResponse updateNode(Long id, PveNodeUpdateRequest request);
    
    /**
     * 删除PVE节点
     */
    void deleteNode(Long id);
    
    /**
     * 批量删除PVE节点
     */
    void batchDeleteNodes(List<Long> nodeIds);
    
    /**
     * 检查节点名称是否存在
     */
    boolean existsByNodeName(String nodeName);
    
    /**
     * 根据状态查询节点
     */
    List<PveNodeResponse> findByStatus(NodeStatus status);
    
    /**
     * 获取在线节点
     */
    List<PveNodeResponse> findOnlineNodes();
    
    /**
     * 获取离线节点
     */
    List<PveNodeResponse> findOfflineNodes();
    
    /**
     * 更新节点状态
     */
    PveNodeResponse updateNodeStatus(Long id, NodeStatus status);
    
    /**
     * 更新节点使用率
     */
    PveNodeResponse updateNodeUsage(Long id, BigDecimal cpuUsage, BigDecimal memoryUsage, BigDecimal diskUsage);
    
    /**
     * 更新心跳时间
     */
    void updateHeartbeat(Long id);
    
    /**
     * 获取所有节点
     */
    List<PveNodeResponse> findAllNodes();
}
