package com.cloudvps.virtualization.client;

import com.cloudvps.virtualization.config.PveIntegrationConfig;
import com.cloudvps.virtualization.entity.PveNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * PVE客户端封装类
 * 
 * 注意：由于pve-java-client库可能不存在，这里提供一个模拟实现
 * 在实际项目中，应该使用真实的pve-java-client库
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class PveClient {

    @Autowired
    private PveIntegrationConfig pveConfig;

    /**
     * 连接缓存
     */
    private final Map<String, PveConnection> connectionCache = new ConcurrentHashMap<>();
    
    /**
     * 创建PVE连接
     */
    public PveConnection createConnection(PveNode node) {
        String connectionKey = getConnectionKey(node);

        return connectionCache.computeIfAbsent(connectionKey, key -> {
            log.info("创建PVE连接: {} (Mock模式: {})", node.getConnectionUrl(), pveConfig.getMock().isEnabled());
            return new PveConnection(node, pveConfig);
        });
    }
    
    /**
     * 获取PVE连接
     */
    public PveConnection getConnection(PveNode node) {
        String connectionKey = getConnectionKey(node);
        return connectionCache.get(connectionKey);
    }
    
    /**
     * 移除连接
     */
    public void removeConnection(PveNode node) {
        String connectionKey = getConnectionKey(node);
        PveConnection connection = connectionCache.remove(connectionKey);
        if (connection != null) {
            connection.disconnect();
            log.info("移除PVE连接: {}", node.getConnectionUrl());
        }
    }
    
    /**
     * 测试连接
     */
    public boolean testConnection(PveNode node) {
        try {
            PveConnection connection = createConnection(node);
            return connection.isConnected();
        } catch (Exception e) {
            log.error("测试PVE连接失败: {}", node.getConnectionUrl(), e);
            return false;
        }
    }
    
    /**
     * 获取节点状态
     */
    public NodeStatusInfo getNodeStatus(PveNode node) {
        try {
            PveConnection connection = getConnection(node);
            if (connection == null || !connection.isConnected()) {
                connection = createConnection(node);
            }
            
            return connection.getNodeStatus();
        } catch (Exception e) {
            log.error("获取节点状态失败: {}", node.getConnectionUrl(), e);
            return NodeStatusInfo.offline();
        }
    }
    
    /**
     * 获取虚拟机列表
     */
    public Map<String, Object> getVirtualMachines(PveNode node) {
        try {
            PveConnection connection = getConnection(node);
            if (connection == null || !connection.isConnected()) {
                connection = createConnection(node);
            }
            
            return connection.getVirtualMachines();
        } catch (Exception e) {
            log.error("获取虚拟机列表失败: {}", node.getConnectionUrl(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 创建虚拟机
     */
    public boolean createVirtualMachine(PveNode node, VmCreateRequest request) {
        try {
            PveConnection connection = getConnection(node);
            if (connection == null || !connection.isConnected()) {
                connection = createConnection(node);
            }
            
            return connection.createVirtualMachine(request);
        } catch (Exception e) {
            log.error("创建虚拟机失败: {}", node.getConnectionUrl(), e);
            return false;
        }
    }
    
    /**
     * 启动虚拟机
     */
    public boolean startVirtualMachine(PveNode node, Integer vmid) {
        try {
            PveConnection connection = getConnection(node);
            if (connection == null || !connection.isConnected()) {
                connection = createConnection(node);
            }
            
            return connection.startVirtualMachine(vmid);
        } catch (Exception e) {
            log.error("启动虚拟机失败: {} VMID: {}", node.getConnectionUrl(), vmid, e);
            return false;
        }
    }
    
    /**
     * 停止虚拟机
     */
    public boolean stopVirtualMachine(PveNode node, Integer vmid) {
        try {
            PveConnection connection = getConnection(node);
            if (connection == null || !connection.isConnected()) {
                connection = createConnection(node);
            }
            
            return connection.stopVirtualMachine(vmid);
        } catch (Exception e) {
            log.error("停止虚拟机失败: {} VMID: {}", node.getConnectionUrl(), vmid, e);
            return false;
        }
    }
    
    /**
     * 重启虚拟机
     */
    public boolean restartVirtualMachine(PveNode node, Integer vmid) {
        try {
            PveConnection connection = getConnection(node);
            if (connection == null || !connection.isConnected()) {
                connection = createConnection(node);
            }
            
            return connection.restartVirtualMachine(vmid);
        } catch (Exception e) {
            log.error("重启虚拟机失败: {} VMID: {}", node.getConnectionUrl(), vmid, e);
            return false;
        }
    }
    
    /**
     * 删除虚拟机
     */
    public boolean deleteVirtualMachine(PveNode node, Integer vmid) {
        try {
            PveConnection connection = getConnection(node);
            if (connection == null || !connection.isConnected()) {
                connection = createConnection(node);
            }
            
            return connection.deleteVirtualMachine(vmid);
        } catch (Exception e) {
            log.error("删除虚拟机失败: {} VMID: {}", node.getConnectionUrl(), vmid, e);
            return false;
        }
    }
    
    /**
     * 获取连接键
     */
    private String getConnectionKey(PveNode node) {
        return String.format("%s:%d@%s", node.getHost(), node.getPort(), node.getUsername());
    }
    
    /**
     * 清理所有连接
     */
    public void clearAllConnections() {
        connectionCache.values().forEach(PveConnection::disconnect);
        connectionCache.clear();
        log.info("清理所有PVE连接");
    }
}
