package com.cloudvps.virtualization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.virtualization.api.dto.request.PveNodeCreateRequest;
import com.cloudvps.virtualization.api.dto.request.PveNodeQueryRequest;
import com.cloudvps.virtualization.api.dto.request.PveNodeUpdateRequest;
import com.cloudvps.virtualization.api.dto.response.PveNodeResponse;
import com.cloudvps.virtualization.api.enums.NodeStatus;
import com.cloudvps.virtualization.convert.PveNodeConvert;
import com.cloudvps.virtualization.entity.PveNode;
import com.cloudvps.virtualization.entity.VirtualMachine;
import com.cloudvps.virtualization.mapper.PveNodeDataMapper;
import com.cloudvps.virtualization.mapper.VirtualMachineDataMapper;
import com.cloudvps.virtualization.service.PveNodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * PVE节点服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PveNodeServiceImpl implements PveNodeService {
    
    private final PveNodeDataMapper pveNodeDataMapper;
    private final VirtualMachineDataMapper virtualMachineDataMapper;
    private final PveNodeConvert pveNodeConvert;
    
    @Override
    public IPage<PveNodeResponse> queryNodes(PveNodeQueryRequest request) {
        LambdaQueryWrapper<PveNode> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(request.getNodeName())) {
            queryWrapper.like(PveNode::getNodeName, request.getNodeName());
        }
        if (StringUtils.hasText(request.getHost())) {
            queryWrapper.like(PveNode::getHost, request.getHost());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq(PveNode::getStatus, request.getStatus());
        }
        if (request.getMinCpuUsage() != null) {
            queryWrapper.ge(PveNode::getCpuUsage, request.getMinCpuUsage());
        }
        if (request.getMaxCpuUsage() != null) {
            queryWrapper.le(PveNode::getCpuUsage, request.getMaxCpuUsage());
        }
        if (request.getMinMemoryUsage() != null) {
            queryWrapper.ge(PveNode::getMemoryUsage, request.getMinMemoryUsage());
        }
        if (request.getMaxMemoryUsage() != null) {
            queryWrapper.le(PveNode::getMemoryUsage, request.getMaxMemoryUsage());
        }
        
        // 排序
        queryWrapper.orderByDesc(PveNode::getCreatedTime);
        
        // 分页查询
        Page<PveNode> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<PveNode> nodePage = pveNodeDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return nodePage.convert(pveNodeConvert::toResponse);
    }
    
    @Override
    public PveNodeResponse findById(Long id) {
        PveNode node = pveNodeDataMapper.selectById(id);
        if (node == null) {
            throw new BusinessException("PVE节点不存在");
        }
        return pveNodeConvert.toResponse(node);
    }
    
    @Override
    public PveNodeResponse findByNodeName(String nodeName) {
        LambdaQueryWrapper<PveNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PveNode::getNodeName, nodeName);
        
        PveNode node = pveNodeDataMapper.selectOne(queryWrapper);
        if (node == null) {
            throw new BusinessException("PVE节点不存在");
        }
        return pveNodeConvert.toResponse(node);
    }
    
    @Override
    @Transactional
    public PveNodeResponse createNode(PveNodeCreateRequest request) {
        // 检查节点名称是否已存在
        if (existsByNodeName(request.getNodeName())) {
            throw new BusinessException("节点名称已存在");
        }
        
        PveNode node = pveNodeConvert.toEntity(request);
        pveNodeDataMapper.insert(node);
        
        log.info("创建PVE节点成功: nodeName={}, host={}", 
                node.getNodeName(), node.getHost());
        return pveNodeConvert.toResponse(node);
    }
    
    @Override
    @Transactional
    public PveNodeResponse updateNode(Long id, PveNodeUpdateRequest request) {
        PveNode existingNode = pveNodeDataMapper.selectById(id);
        if (existingNode == null) {
            throw new BusinessException("PVE节点不存在");
        }
        
        // 更新字段
        pveNodeConvert.updateFromRequest(existingNode, request);
        pveNodeDataMapper.updateById(existingNode);
        
        log.info("更新PVE节点成功: nodeId={}, nodeName={}", 
                id, existingNode.getNodeName());
        return pveNodeConvert.toResponse(existingNode);
    }
    
    @Override
    @Transactional
    public void deleteNode(Long id) {
        PveNode node = pveNodeDataMapper.selectById(id);
        if (node == null) {
            throw new BusinessException("PVE节点不存在");
        }
        
        // 检查是否有关联的虚拟机
        LambdaQueryWrapper<VirtualMachine> vmWrapper = new LambdaQueryWrapper<>();
        vmWrapper.eq(VirtualMachine::getNodeId, id);
        long vmCount = virtualMachineDataMapper.selectCount(vmWrapper);
        
        if (vmCount > 0) {
            throw new BusinessException("节点下还有虚拟机，无法删除");
        }
        
        // 删除节点
        pveNodeDataMapper.deleteById(id);
        
        log.info("删除PVE节点成功: nodeId={}, nodeName={}", 
                id, node.getNodeName());
    }
    
    @Override
    @Transactional
    public void batchDeleteNodes(List<Long> nodeIds) {
        if (nodeIds == null || nodeIds.isEmpty()) {
            return;
        }
        
        // 检查是否有关联的虚拟机
        for (Long nodeId : nodeIds) {
            LambdaQueryWrapper<VirtualMachine> vmWrapper = new LambdaQueryWrapper<>();
            vmWrapper.eq(VirtualMachine::getNodeId, nodeId);
            long vmCount = virtualMachineDataMapper.selectCount(vmWrapper);
            
            if (vmCount > 0) {
                throw new BusinessException("存在节点下还有虚拟机，无法删除");
            }
        }
        
        // 批量删除节点
        pveNodeDataMapper.deleteBatchIds(nodeIds);
        
        log.info("批量删除PVE节点成功: nodeIds={}", nodeIds);
    }
    
    @Override
    public boolean existsByNodeName(String nodeName) {
        LambdaQueryWrapper<PveNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PveNode::getNodeName, nodeName);
        return pveNodeDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public List<PveNodeResponse> findByStatus(NodeStatus status) {
        LambdaQueryWrapper<PveNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PveNode::getStatus, status)
                   .orderByDesc(PveNode::getCreatedTime);
        
        List<PveNode> nodes = pveNodeDataMapper.selectList(queryWrapper);
        return nodes.stream()
                   .map(pveNodeConvert::toResponse)
                   .collect(Collectors.toList());
    }
    
    @Override
    public List<PveNodeResponse> findOnlineNodes() {
        return findByStatus(NodeStatus.ONLINE);
    }
    
    @Override
    public List<PveNodeResponse> findOfflineNodes() {
        return findByStatus(NodeStatus.OFFLINE);
    }
    
    @Override
    @Transactional
    public PveNodeResponse updateNodeStatus(Long id, NodeStatus status) {
        PveNode node = pveNodeDataMapper.selectById(id);
        if (node == null) {
            throw new BusinessException("PVE节点不存在");
        }
        
        node.setStatus(status);
        node.setLastHeartbeat(LocalDateTime.now());
        pveNodeDataMapper.updateById(node);
        
        log.info("更新PVE节点状态成功: nodeId={}, status={}", id, status);
        return pveNodeConvert.toResponse(node);
    }
    
    @Override
    @Transactional
    public PveNodeResponse updateNodeUsage(Long id, BigDecimal cpuUsage, BigDecimal memoryUsage, BigDecimal diskUsage) {
        PveNode node = pveNodeDataMapper.selectById(id);
        if (node == null) {
            throw new BusinessException("PVE节点不存在");
        }
        
        node.setCpuUsage(cpuUsage);
        node.setMemoryUsage(memoryUsage);
        node.setDiskUsage(diskUsage);
        node.setLastHeartbeat(LocalDateTime.now());
        pveNodeDataMapper.updateById(node);
        
        log.debug("更新PVE节点使用率成功: nodeId={}, cpu={}, memory={}, disk={}", 
                id, cpuUsage, memoryUsage, diskUsage);
        return pveNodeConvert.toResponse(node);
    }
    
    @Override
    @Transactional
    public void updateHeartbeat(Long id) {
        PveNode node = pveNodeDataMapper.selectById(id);
        if (node == null) {
            throw new BusinessException("PVE节点不存在");
        }
        
        node.setLastHeartbeat(LocalDateTime.now());
        if (node.getStatus() == NodeStatus.OFFLINE) {
            node.setStatus(NodeStatus.ONLINE);
        }
        pveNodeDataMapper.updateById(node);
        
        log.debug("更新PVE节点心跳成功: nodeId={}", id);
    }
    
    @Override
    public List<PveNodeResponse> findAllNodes() {
        LambdaQueryWrapper<PveNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(PveNode::getCreatedTime);
        
        List<PveNode> nodes = pveNodeDataMapper.selectList(queryWrapper);
        return nodes.stream()
                   .map(pveNodeConvert::toResponse)
                   .collect(Collectors.toList());
    }
}
