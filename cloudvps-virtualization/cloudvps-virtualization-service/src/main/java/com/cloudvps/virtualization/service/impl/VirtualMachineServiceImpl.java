package com.cloudvps.virtualization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineCreateRequest;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineQueryRequest;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineUpdateRequest;
import com.cloudvps.virtualization.api.dto.response.VirtualMachineResponse;
import com.cloudvps.virtualization.api.enums.VmStatus;
import com.cloudvps.virtualization.convert.VirtualMachineConvert;
import com.cloudvps.virtualization.entity.PveNode;
import com.cloudvps.virtualization.entity.VirtualMachine;
import com.cloudvps.virtualization.mapper.PveNodeDataMapper;
import com.cloudvps.virtualization.mapper.VirtualMachineDataMapper;
import com.cloudvps.virtualization.service.VirtualMachineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 虚拟机服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VirtualMachineServiceImpl implements VirtualMachineService {
    
    private final VirtualMachineDataMapper virtualMachineDataMapper;
    private final PveNodeDataMapper pveNodeDataMapper;
    private final VirtualMachineConvert virtualMachineConvert;
    
    @Override
    public IPage<VirtualMachineResponse> queryVirtualMachines(VirtualMachineQueryRequest request) {
        LambdaQueryWrapper<VirtualMachine> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (request.getUserId() != null) {
            queryWrapper.eq(VirtualMachine::getUserId, request.getUserId());
        }
        if (request.getNodeId() != null) {
            queryWrapper.eq(VirtualMachine::getNodeId, request.getNodeId());
        }
        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like(VirtualMachine::getName, request.getName());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq(VirtualMachine::getStatus, request.getStatus());
        }
        if (StringUtils.hasText(request.getOsType())) {
            queryWrapper.eq(VirtualMachine::getOsType, request.getOsType());
        }
        if (StringUtils.hasText(request.getIpAddress())) {
            queryWrapper.like(VirtualMachine::getIpAddress, request.getIpAddress());
        }
        if (request.getAutoStart() != null) {
            queryWrapper.eq(VirtualMachine::getAutoStart, request.getAutoStart());
        }
        
        // 排序
        queryWrapper.orderByDesc(VirtualMachine::getCreatedTime);
        
        // 分页查询
        Page<VirtualMachine> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<VirtualMachine> vmPage = virtualMachineDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return vmPage.convert(virtualMachineConvert::toResponse);
    }
    
    @Override
    public VirtualMachineResponse findById(Long id) {
        VirtualMachine vm = virtualMachineDataMapper.selectById(id);
        if (vm == null) {
            throw new BusinessException("虚拟机不存在");
        }
        return virtualMachineConvert.toResponse(vm);
    }
    
    @Override
    public VirtualMachineResponse findByVmid(Integer vmid) {
        LambdaQueryWrapper<VirtualMachine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VirtualMachine::getVmid, vmid);
        
        VirtualMachine vm = virtualMachineDataMapper.selectOne(queryWrapper);
        if (vm == null) {
            throw new BusinessException("虚拟机不存在");
        }
        return virtualMachineConvert.toResponse(vm);
    }
    
    @Override
    @Transactional
    public VirtualMachineResponse createVirtualMachine(VirtualMachineCreateRequest request) {
        // 检查节点是否存在
        PveNode node = pveNodeDataMapper.selectById(request.getNodeId());
        if (node == null) {
            throw new BusinessException("PVE节点不存在");
        }
        
        // 检查VMID是否已存在
        if (existsByVmid(request.getVmid())) {
            throw new BusinessException("虚拟机ID已存在");
        }
        
        VirtualMachine vm = virtualMachineConvert.toEntity(request);
        virtualMachineDataMapper.insert(vm);
        
        log.info("创建虚拟机成功: vmid={}, name={}, userId={}", 
                vm.getVmid(), vm.getName(), vm.getUserId());
        return virtualMachineConvert.toResponse(vm);
    }
    
    @Override
    @Transactional
    public VirtualMachineResponse updateVirtualMachine(Long id, VirtualMachineUpdateRequest request) {
        VirtualMachine existingVm = virtualMachineDataMapper.selectById(id);
        if (existingVm == null) {
            throw new BusinessException("虚拟机不存在");
        }
        
        // 更新字段
        virtualMachineConvert.updateFromRequest(existingVm, request);
        virtualMachineDataMapper.updateById(existingVm);
        
        log.info("更新虚拟机成功: vmId={}, vmid={}", 
                id, existingVm.getVmid());
        return virtualMachineConvert.toResponse(existingVm);
    }
    
    @Override
    @Transactional
    public void deleteVirtualMachine(Long id) {
        VirtualMachine vm = virtualMachineDataMapper.selectById(id);
        if (vm == null) {
            throw new BusinessException("虚拟机不存在");
        }
        
        // 检查虚拟机状态
        if (vm.getStatus() == VmStatus.RUNNING) {
            throw new BusinessException("虚拟机正在运行，无法删除");
        }
        
        // 删除虚拟机
        virtualMachineDataMapper.deleteById(id);
        
        log.info("删除虚拟机成功: vmId={}, vmid={}", 
                id, vm.getVmid());
    }
    
    @Override
    @Transactional
    public void batchDeleteVirtualMachines(List<Long> vmIds) {
        if (vmIds == null || vmIds.isEmpty()) {
            return;
        }
        
        // 检查虚拟机状态
        for (Long vmId : vmIds) {
            VirtualMachine vm = virtualMachineDataMapper.selectById(vmId);
            if (vm != null && vm.getStatus() == VmStatus.RUNNING) {
                throw new BusinessException("存在正在运行的虚拟机，无法删除");
            }
        }
        
        // 批量删除虚拟机
        virtualMachineDataMapper.deleteBatchIds(vmIds);
        
        log.info("批量删除虚拟机成功: vmIds={}", vmIds);
    }
    
    @Override
    public boolean existsByVmid(Integer vmid) {
        LambdaQueryWrapper<VirtualMachine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VirtualMachine::getVmid, vmid);
        return virtualMachineDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public List<VirtualMachineResponse> findByUserId(Long userId) {
        LambdaQueryWrapper<VirtualMachine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VirtualMachine::getUserId, userId)
                   .orderByDesc(VirtualMachine::getCreatedTime);
        
        List<VirtualMachine> vms = virtualMachineDataMapper.selectList(queryWrapper);
        return vms.stream()
                 .map(virtualMachineConvert::toResponse)
                 .collect(Collectors.toList());
    }
    
    @Override
    public List<VirtualMachineResponse> findByNodeId(Long nodeId) {
        LambdaQueryWrapper<VirtualMachine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VirtualMachine::getNodeId, nodeId)
                   .orderByDesc(VirtualMachine::getCreatedTime);
        
        List<VirtualMachine> vms = virtualMachineDataMapper.selectList(queryWrapper);
        return vms.stream()
                 .map(virtualMachineConvert::toResponse)
                 .collect(Collectors.toList());
    }
    
    @Override
    public List<VirtualMachineResponse> findByStatus(VmStatus status) {
        LambdaQueryWrapper<VirtualMachine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VirtualMachine::getStatus, status)
                   .orderByDesc(VirtualMachine::getCreatedTime);
        
        List<VirtualMachine> vms = virtualMachineDataMapper.selectList(queryWrapper);
        return vms.stream()
                 .map(virtualMachineConvert::toResponse)
                 .collect(Collectors.toList());
    }
    
    @Override
    public List<VirtualMachineResponse> findRunningVms() {
        return findByStatus(VmStatus.RUNNING);
    }
    
    @Override
    public List<VirtualMachineResponse> findStoppedVms() {
        return findByStatus(VmStatus.STOPPED);
    }
    
    @Override
    @Transactional
    public VirtualMachineResponse updateVmStatus(Long id, VmStatus status) {
        VirtualMachine vm = virtualMachineDataMapper.selectById(id);
        if (vm == null) {
            throw new BusinessException("虚拟机不存在");
        }
        
        vm.setStatus(status);
        virtualMachineDataMapper.updateById(vm);
        
        log.info("更新虚拟机状态成功: vmId={}, vmid={}, status={}", 
                id, vm.getVmid(), status);
        return virtualMachineConvert.toResponse(vm);
    }
    
    @Override
    public Long countByUserId(Long userId) {
        LambdaQueryWrapper<VirtualMachine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VirtualMachine::getUserId, userId);
        return virtualMachineDataMapper.selectCount(queryWrapper);
    }
    
    @Override
    public Long countByNodeId(Long nodeId) {
        LambdaQueryWrapper<VirtualMachine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VirtualMachine::getNodeId, nodeId);
        return virtualMachineDataMapper.selectCount(queryWrapper);
    }
    
    @Override
    public Long countRunningByNodeId(Long nodeId) {
        LambdaQueryWrapper<VirtualMachine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VirtualMachine::getNodeId, nodeId)
                   .eq(VirtualMachine::getStatus, VmStatus.RUNNING);
        return virtualMachineDataMapper.selectCount(queryWrapper);
    }
}
