package com.cloudvps.virtualization.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.virtualization.api.enums.VmStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 虚拟机实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("virtual_machines")
public class VirtualMachine extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * PVE虚拟机ID
     */
    private Integer vmid;

    /**
     * 所属节点ID
     */
    @TableField("node_id")
    private Long nodeId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 虚拟机名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 虚拟机状态
     */
    private VmStatus status = VmStatus.STOPPED;

    /**
     * 操作系统类型
     */
    @TableField("os_type")
    private String osType = "l26";

    /**
     * CPU核心数
     */
    private Integer cores = 1;

    /**
     * 内存大小（MB）
     */
    private Integer memory = 1024;
    
    /**
     * 磁盘大小（GB）
     */
    @TableField("disk_size")
    private Integer diskSize = 20;

    /**
     * 网络配置
     */
    private String network = "vmbr0";

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * MAC地址
     */
    @TableField("mac_address")
    private String macAddress;

    /**
     * 模板ID
     */
    @TableField("template_id")
    private Long templateId;

    /**
     * 是否自动启动
     */
    @TableField("auto_start")
    private Boolean autoStart = false;
    

    
    /**
     * 检查虚拟机是否运行中
     */
    public boolean isRunning() {
        return VmStatus.RUNNING.equals(this.status);
    }
    
    /**
     * 检查虚拟机是否已停止
     */
    public boolean isStopped() {
        return VmStatus.STOPPED.equals(this.status);
    }
    
    /**
     * 获取虚拟机显示名称
     */
    public String getDisplayName() {
        return String.format("%s (VMID: %d)", name, vmid);
    }
}
