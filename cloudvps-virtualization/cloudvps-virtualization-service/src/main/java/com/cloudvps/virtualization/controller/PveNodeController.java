package com.cloudvps.virtualization.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.virtualization.api.dto.request.PveNodeCreateRequest;
import com.cloudvps.virtualization.api.dto.request.PveNodeQueryRequest;
import com.cloudvps.virtualization.api.dto.request.PveNodeUpdateRequest;
import com.cloudvps.virtualization.api.dto.response.PveNodeResponse;
import com.cloudvps.virtualization.api.enums.NodeStatus;
import com.cloudvps.virtualization.service.PveNodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * PVE节点控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/virtualization/nodes")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "PVE节点管理", description = "PVE节点的增删改查和状态管理")
public class PveNodeController {
    
    private final PveNodeService pveNodeService;
    
    @GetMapping
    @Operation(summary = "分页查询PVE节点", description = "分页查询PVE节点列表")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_VIEW')")
    public ApiResponse<IPage<PveNodeResponse>> getNodes(@Valid PveNodeQueryRequest request) {
        log.debug("分页查询PVE节点: {}", request);
        IPage<PveNodeResponse> nodes = pveNodeService.queryNodes(request);
        return ApiResponse.success(nodes);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取PVE节点详情", description = "根据节点ID获取PVE节点详细信息")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_VIEW')")
    public ApiResponse<PveNodeResponse> getNodeById(
            @Parameter(description = "节点ID") @PathVariable Long id) {
        log.debug("获取PVE节点详情: nodeId={}", id);
        PveNodeResponse node = pveNodeService.findById(id);
        return ApiResponse.success(node);
    }
    
    @GetMapping("/by-name/{nodeName}")
    @Operation(summary = "根据节点名称获取PVE节点", description = "根据节点名称获取PVE节点信息")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_VIEW')")
    public ApiResponse<PveNodeResponse> getNodeByName(
            @Parameter(description = "节点名称") @PathVariable String nodeName) {
        log.debug("根据节点名称获取PVE节点: nodeName={}", nodeName);
        PveNodeResponse node = pveNodeService.findByNodeName(nodeName);
        return ApiResponse.success(node);
    }
    
    @PostMapping
    @Operation(summary = "创建PVE节点", description = "创建新的PVE节点")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_CREATE')")
    public ApiResponse<PveNodeResponse> createNode(@Valid @RequestBody PveNodeCreateRequest request) {
        log.info("创建PVE节点请求: nodeName={}, host={}", request.getNodeName(), request.getHost());
        PveNodeResponse node = pveNodeService.createNode(request);
        return ApiResponse.success("创建成功", node);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新PVE节点", description = "更新指定PVE节点信息")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_UPDATE')")
    public ApiResponse<PveNodeResponse> updateNode(
            @Parameter(description = "节点ID") @PathVariable Long id,
            @Valid @RequestBody PveNodeUpdateRequest request) {
        log.info("更新PVE节点请求: nodeId={}", id);
        PveNodeResponse node = pveNodeService.updateNode(id, request);
        return ApiResponse.success("更新成功", node);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除PVE节点", description = "删除指定PVE节点")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_DELETE')")
    public ApiResponse<Void> deleteNode(
            @Parameter(description = "节点ID") @PathVariable Long id) {
        log.info("删除PVE节点请求: nodeId={}", id);
        pveNodeService.deleteNode(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除PVE节点", description = "批量删除指定PVE节点")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_DELETE')")
    public ApiResponse<Void> batchDeleteNodes(@RequestBody List<Long> nodeIds) {
        log.info("批量删除PVE节点请求: nodeIds={}", nodeIds);
        pveNodeService.batchDeleteNodes(nodeIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @GetMapping("/status/{status}")
    @Operation(summary = "根据状态查询节点", description = "根据节点状态查询PVE节点列表")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_VIEW')")
    public ApiResponse<List<PveNodeResponse>> getNodesByStatus(
            @Parameter(description = "节点状态") @PathVariable NodeStatus status) {
        log.debug("根据状态查询节点: status={}", status);
        List<PveNodeResponse> nodes = pveNodeService.findByStatus(status);
        return ApiResponse.success(nodes);
    }
    
    @GetMapping("/online")
    @Operation(summary = "获取在线节点", description = "获取所有在线的PVE节点")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_VIEW')")
    public ApiResponse<List<PveNodeResponse>> getOnlineNodes() {
        log.debug("获取在线节点");
        List<PveNodeResponse> nodes = pveNodeService.findOnlineNodes();
        return ApiResponse.success(nodes);
    }
    
    @GetMapping("/offline")
    @Operation(summary = "获取离线节点", description = "获取所有离线的PVE节点")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_VIEW')")
    public ApiResponse<List<PveNodeResponse>> getOfflineNodes() {
        log.debug("获取离线节点");
        List<PveNodeResponse> nodes = pveNodeService.findOfflineNodes();
        return ApiResponse.success(nodes);
    }
    
    @PutMapping("/{id}/status")
    @Operation(summary = "更新节点状态", description = "更新指定PVE节点的状态")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_UPDATE')")
    public ApiResponse<PveNodeResponse> updateNodeStatus(
            @Parameter(description = "节点ID") @PathVariable Long id,
            @Parameter(description = "节点状态") @RequestParam NodeStatus status) {
        log.info("更新节点状态: nodeId={}, status={}", id, status);
        PveNodeResponse node = pveNodeService.updateNodeStatus(id, status);
        return ApiResponse.success("状态更新成功", node);
    }
    
    @PutMapping("/{id}/usage")
    @Operation(summary = "更新节点使用率", description = "更新指定PVE节点的资源使用率")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_UPDATE')")
    public ApiResponse<PveNodeResponse> updateNodeUsage(
            @Parameter(description = "节点ID") @PathVariable Long id,
            @Parameter(description = "CPU使用率") @RequestParam BigDecimal cpuUsage,
            @Parameter(description = "内存使用率") @RequestParam BigDecimal memoryUsage,
            @Parameter(description = "磁盘使用率") @RequestParam BigDecimal diskUsage) {
        log.debug("更新节点使用率: nodeId={}, cpu={}, memory={}, disk={}", 
                id, cpuUsage, memoryUsage, diskUsage);
        PveNodeResponse node = pveNodeService.updateNodeUsage(id, cpuUsage, memoryUsage, diskUsage);
        return ApiResponse.success("使用率更新成功", node);
    }
    
    @PostMapping("/{id}/heartbeat")
    @Operation(summary = "更新心跳时间", description = "更新指定PVE节点的心跳时间")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_UPDATE')")
    public ApiResponse<Void> updateHeartbeat(
            @Parameter(description = "节点ID") @PathVariable Long id) {
        log.debug("更新节点心跳: nodeId={}", id);
        pveNodeService.updateHeartbeat(id);
        return ApiResponse.success("心跳更新成功", null);
    }
    
    @GetMapping("/all")
    @Operation(summary = "获取所有节点", description = "获取所有PVE节点列表")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_VIEW')")
    public ApiResponse<List<PveNodeResponse>> getAllNodes() {
        log.debug("获取所有节点");
        List<PveNodeResponse> nodes = pveNodeService.findAllNodes();
        return ApiResponse.success(nodes);
    }
    
    @GetMapping("/check-name")
    @Operation(summary = "检查节点名称", description = "检查节点名称是否已存在")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_VIEW')")
    public ApiResponse<Boolean> checkNodeName(@RequestParam String nodeName) {
        boolean exists = pveNodeService.existsByNodeName(nodeName);
        return ApiResponse.success(exists ? "节点名称已存在" : "节点名称可用", !exists);
    }
}
