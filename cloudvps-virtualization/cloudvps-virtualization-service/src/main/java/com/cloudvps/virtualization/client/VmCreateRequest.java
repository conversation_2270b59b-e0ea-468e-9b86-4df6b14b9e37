package com.cloudvps.virtualization.client;

import lombok.Data;

/**
 * 虚拟机创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class VmCreateRequest {
    
    /**
     * 虚拟机ID
     */
    private Integer vmid;
    
    /**
     * 虚拟机名称
     */
    private String name;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 操作系统类型
     */
    private String osType = "l26";
    
    /**
     * CPU核心数
     */
    private Integer cores = 1;
    
    /**
     * 内存大小（MB）
     */
    private Integer memory = 1024;
    
    /**
     * 磁盘大小（GB）
     */
    private Integer diskSize = 20;
    
    /**
     * 网络配置
     */
    private String network = "vmbr0";
    
    /**
     * 是否自动启动
     */
    private Boolean autoStart = false;
    
    /**
     * 模板ID（如果从模板创建）
     */
    private Integer templateId;
}
