package com.cloudvps.virtualization.convert;

import com.cloudvps.virtualization.api.dto.request.VirtualMachineCreateRequest;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineUpdateRequest;
import com.cloudvps.virtualization.api.dto.response.VirtualMachineResponse;
import com.cloudvps.virtualization.entity.VirtualMachine;
import org.mapstruct.*;

/**
 * 虚拟机对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface VirtualMachineConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    VirtualMachine toEntity(VirtualMachineCreateRequest request);
    
    /**
     * 实体转响应
     */
    @Mapping(target = "nodeName", ignore = true) // 需要在Service层设置
    @Mapping(target = "username", ignore = true) // 需要在Service层设置
    @Mapping(target = "templateName", ignore = true) // 需要在Service层设置
    @Mapping(target = "displayName", source = "name")
    @Mapping(target = "running", expression = "java(virtualMachine.getStatus() == com.cloudvps.virtualization.api.enums.VmStatus.RUNNING)")
    @Mapping(target = "stopped", expression = "java(virtualMachine.getStatus() == com.cloudvps.virtualization.api.enums.VmStatus.STOPPED)")
    @Mapping(target = "connectionUrl", ignore = true) // 需要在Service层设置
    VirtualMachineResponse toResponse(VirtualMachine virtualMachine);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "vmid", ignore = true) // VMID不允许修改
    @Mapping(target = "nodeId", ignore = true) // 节点ID不允许修改
    @Mapping(target = "userId", ignore = true) // 用户ID不允许修改
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget VirtualMachine virtualMachine, VirtualMachineUpdateRequest request);
}
