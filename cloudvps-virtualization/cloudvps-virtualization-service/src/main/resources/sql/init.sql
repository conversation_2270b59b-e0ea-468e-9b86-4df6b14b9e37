-- =============================================
-- CloudVPS 虚拟化服务数据库初始化脚本
-- 数据库: cloudvps_virtualization
-- 版本: 1.0.0
-- 创建时间: 2024-01-01
-- =============================================

-- 创建数据库 (如果不存在)
-- CREATE DATABASE IF NOT EXISTS cloudvps_virtualization;
-- USE cloudvps_virtualization;

-- =============================================
-- 1. PVE节点相关表
-- =============================================

-- PVE节点表
CREATE TABLE IF NOT EXISTS pve_nodes (
    id BIGSERIAL PRIMARY KEY,
    node_id VARCHAR(50) UNIQUE NOT NULL,
    node_name VARCHAR(100) NOT NULL,
    api_url VARCHAR(255) NOT NULL,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    realm VARCHAR(20) DEFAULT 'pam',
    trust_self_signed_certs BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'MAINTENANCE', 'ERROR', 'OFFLINE')),
    datacenter VARCHAR(50),
    region VARCHAR(50),
    cpu_cores INTEGER,
    memory_gb INTEGER,
    storage_gb INTEGER,
    max_vms INTEGER,
    current_vms INTEGER DEFAULT 0,
    priority INTEGER DEFAULT 1,
    description TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_heartbeat TIMESTAMP
);

-- PVE节点资源使用历史表
CREATE TABLE IF NOT EXISTS pve_node_metrics (
    id BIGSERIAL PRIMARY KEY,
    node_id VARCHAR(50) NOT NULL,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    storage_usage DECIMAL(5,2),
    network_in BIGINT,
    network_out BIGINT,
    vm_count INTEGER,
    load_average DECIMAL(5,2),
    uptime BIGINT,
    collected_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (node_id) REFERENCES pve_nodes(node_id) ON DELETE CASCADE
);

-- =============================================
-- 2. 虚拟机模板相关表
-- =============================================

-- 虚拟机模板表
CREATE TABLE IF NOT EXISTS vm_templates (
    id BIGSERIAL PRIMARY KEY,
    template_id VARCHAR(50) UNIQUE NOT NULL,
    template_name VARCHAR(100) NOT NULL,
    os_type VARCHAR(50) NOT NULL,
    os_version VARCHAR(50),
    description TEXT,
    min_cpu INTEGER DEFAULT 1,
    min_memory INTEGER DEFAULT 512,
    min_disk INTEGER DEFAULT 10,
    image_url VARCHAR(255),
    icon_url VARCHAR(255),
    is_public BOOLEAN DEFAULT TRUE,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'DEPRECATED')),
    created_by BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 3. 虚拟机实例相关表
-- =============================================

-- 虚拟机实例表
CREATE TABLE IF NOT EXISTS vm_instances (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    pve_node VARCHAR(50) NOT NULL,
    pve_vmid INTEGER NOT NULL,
    cpu_cores INTEGER NOT NULL,
    memory_mb INTEGER NOT NULL,
    disk_gb INTEGER NOT NULL,
    os_type VARCHAR(50),
    status VARCHAR(20) DEFAULT 'CREATING' CHECK (status IN ('CREATING', 'RUNNING', 'STOPPED', 'STOPPING', 'STARTING', 'RESTARTING', 'DELETING', 'DELETED', 'CREATE_FAILED', 'ERROR')),
    public_ip VARCHAR(15),
    private_ip VARCHAR(15),
    template_id BIGINT,
    order_id VARCHAR(50),
    expire_time TIMESTAMP,
    error_message TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pve_node) REFERENCES pve_nodes(node_id),
    FOREIGN KEY (template_id) REFERENCES vm_templates(id)
);

-- 虚拟机配置变更记录表
CREATE TABLE IF NOT EXISTS vm_config_history (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) NOT NULL,
    change_type VARCHAR(20) NOT NULL CHECK (change_type IN ('CREATE', 'UPDATE', 'DELETE', 'RESIZE', 'MIGRATE')),
    old_config JSONB,
    new_config JSONB,
    operator_id BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vm_id) REFERENCES vm_instances(vm_id) ON DELETE CASCADE
);

-- =============================================
-- 4. 虚拟机操作相关表
-- =============================================

-- 虚拟机操作记录表
CREATE TABLE IF NOT EXISTS vm_operations (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) NOT NULL,
    operation_type VARCHAR(20) NOT NULL CHECK (operation_type IN ('CREATE', 'START', 'STOP', 'RESTART', 'DELETE', 'RESIZE', 'MIGRATE', 'BACKUP', 'RESTORE')),
    operation_status VARCHAR(20) DEFAULT 'PENDING' CHECK (operation_status IN ('PENDING', 'RUNNING', 'SUCCESS', 'FAILED', 'CANCELLED')),
    task_id VARCHAR(100),
    operator_id BIGINT,
    parameters JSONB,
    result TEXT,
    error_message TEXT,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    duration BIGINT,
    FOREIGN KEY (vm_id) REFERENCES vm_instances(vm_id) ON DELETE CASCADE
);

-- =============================================
-- 5. 虚拟机监控相关表
-- =============================================

-- 虚拟机监控指标表
CREATE TABLE IF NOT EXISTS vm_metrics (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) NOT NULL,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    disk_usage DECIMAL(5,2),
    network_in BIGINT,
    network_out BIGINT,
    disk_read BIGINT,
    disk_write BIGINT,
    load_average DECIMAL(5,2),
    collected_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    node_id VARCHAR(50),
    FOREIGN KEY (vm_id) REFERENCES vm_instances(vm_id) ON DELETE CASCADE,
    FOREIGN KEY (node_id) REFERENCES pve_nodes(node_id) ON DELETE SET NULL
);

-- 虚拟机快照表
CREATE TABLE IF NOT EXISTS vm_snapshots (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) NOT NULL,
    snapshot_name VARCHAR(100) NOT NULL,
    description TEXT,
    size_mb BIGINT,
    created_by BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vm_id) REFERENCES vm_instances(vm_id) ON DELETE CASCADE,
    UNIQUE(vm_id, snapshot_name)
);

-- 虚拟机备份表
CREATE TABLE IF NOT EXISTS vm_backups (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) NOT NULL,
    backup_name VARCHAR(100) NOT NULL,
    backup_type VARCHAR(20) DEFAULT 'FULL' CHECK (backup_type IN ('FULL', 'INCREMENTAL')),
    backup_path VARCHAR(255),
    size_mb BIGINT,
    status VARCHAR(20) DEFAULT 'CREATING' CHECK (status IN ('CREATING', 'COMPLETED', 'FAILED')),
    created_by BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_time TIMESTAMP,
    FOREIGN KEY (vm_id) REFERENCES vm_instances(vm_id) ON DELETE CASCADE
);

-- =============================================
-- 6. 网络和存储相关表
-- =============================================

-- 网络配置表
CREATE TABLE IF NOT EXISTS vm_networks (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) NOT NULL,
    interface_name VARCHAR(20) NOT NULL,
    mac_address VARCHAR(17),
    ip_address VARCHAR(15),
    netmask VARCHAR(15),
    gateway VARCHAR(15),
    bridge VARCHAR(50),
    vlan_id INTEGER,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vm_id) REFERENCES vm_instances(vm_id) ON DELETE CASCADE
);

-- 存储配置表
CREATE TABLE IF NOT EXISTS vm_storages (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) NOT NULL,
    storage_type VARCHAR(20) DEFAULT 'DISK' CHECK (storage_type IN ('DISK', 'CDROM', 'CLOUDINIT')),
    storage_name VARCHAR(50) NOT NULL,
    size_gb INTEGER,
    storage_pool VARCHAR(50),
    file_path VARCHAR(255),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vm_id) REFERENCES vm_instances(vm_id) ON DELETE CASCADE
);

-- =============================================
-- 7. 创建索引
-- =============================================

-- PVE节点表索引
CREATE INDEX IF NOT EXISTS idx_pve_nodes_node_id ON pve_nodes(node_id);
CREATE INDEX IF NOT EXISTS idx_pve_nodes_status ON pve_nodes(status);
CREATE INDEX IF NOT EXISTS idx_pve_nodes_datacenter ON pve_nodes(datacenter);
CREATE INDEX IF NOT EXISTS idx_pve_nodes_region ON pve_nodes(region);

-- PVE节点监控索引
CREATE INDEX IF NOT EXISTS idx_pve_node_metrics_node_time ON pve_node_metrics(node_id, collected_time);

-- 虚拟机模板索引
CREATE INDEX IF NOT EXISTS idx_vm_templates_template_id ON vm_templates(template_id);
CREATE INDEX IF NOT EXISTS idx_vm_templates_os_type ON vm_templates(os_type);
CREATE INDEX IF NOT EXISTS idx_vm_templates_status ON vm_templates(status);

-- 虚拟机实例索引
CREATE INDEX IF NOT EXISTS idx_vm_instances_vm_id ON vm_instances(vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_instances_user_id ON vm_instances(user_id);
CREATE INDEX IF NOT EXISTS idx_vm_instances_pve_node ON vm_instances(pve_node);
CREATE INDEX IF NOT EXISTS idx_vm_instances_status ON vm_instances(status);
CREATE INDEX IF NOT EXISTS idx_vm_instances_created_time ON vm_instances(created_time);

-- 虚拟机操作索引
CREATE INDEX IF NOT EXISTS idx_vm_operations_vm_id ON vm_operations(vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_operations_type ON vm_operations(operation_type);
CREATE INDEX IF NOT EXISTS idx_vm_operations_status ON vm_operations(operation_status);
CREATE INDEX IF NOT EXISTS idx_vm_operations_start_time ON vm_operations(start_time);

-- 虚拟机监控索引
CREATE INDEX IF NOT EXISTS idx_vm_metrics_vm_id ON vm_metrics(vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_metrics_collected_time ON vm_metrics(collected_time);
CREATE INDEX IF NOT EXISTS idx_vm_metrics_vm_time ON vm_metrics(vm_id, collected_time);

-- 快照和备份索引
CREATE INDEX IF NOT EXISTS idx_vm_snapshots_vm_id ON vm_snapshots(vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_backups_vm_id ON vm_backups(vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_backups_status ON vm_backups(status);

-- 网络和存储索引
CREATE INDEX IF NOT EXISTS idx_vm_networks_vm_id ON vm_networks(vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_storages_vm_id ON vm_storages(vm_id);

-- =============================================
-- 8. 插入初始数据
-- =============================================

-- 插入测试PVE节点
INSERT INTO pve_nodes (node_id, node_name, api_url, username, password, datacenter, region, cpu_cores, memory_gb, storage_gb, max_vms, priority) VALUES
('pve-node-01', 'PVE节点01', 'https://*************:8006', 'root@pam', 'your-password', 'dc1', 'beijing', 32, 128, 2000, 50, 1),
('pve-node-02', 'PVE节点02', 'https://*************:8006', 'root@pam', 'your-password', 'dc1', 'beijing', 24, 96, 1500, 40, 2),
('pve-node-03', 'PVE节点03', 'https://*************:8006', 'root@pam', 'your-password', 'dc2', 'shanghai', 16, 64, 1000, 30, 3)
ON CONFLICT (node_id) DO NOTHING;

-- 插入虚拟机模板
INSERT INTO vm_templates (template_id, template_name, os_type, os_version, description, min_cpu, min_memory, min_disk, icon_url) VALUES
('ubuntu-20.04', 'Ubuntu 20.04 LTS', 'Linux', '20.04', 'Ubuntu 20.04 长期支持版本', 1, 1024, 20, '/static/icons/ubuntu.png'),
('ubuntu-22.04', 'Ubuntu 22.04 LTS', 'Linux', '22.04', 'Ubuntu 22.04 长期支持版本', 1, 1024, 20, '/static/icons/ubuntu.png'),
('centos-7', 'CentOS 7', 'Linux', '7', 'CentOS 7 稳定版本', 1, 1024, 20, '/static/icons/centos.png'),
('centos-8', 'CentOS 8', 'Linux', '8', 'CentOS 8 最新版本', 1, 1024, 20, '/static/icons/centos.png'),
('debian-11', 'Debian 11', 'Linux', '11', 'Debian 11 稳定版本', 1, 512, 10, '/static/icons/debian.png'),
('windows-server-2019', 'Windows Server 2019', 'Windows', '2019', 'Windows Server 2019 标准版', 2, 4096, 60, '/static/icons/windows.png'),
('windows-server-2022', 'Windows Server 2022', 'Windows', '2022', 'Windows Server 2022 标准版', 2, 4096, 60, '/static/icons/windows.png')
ON CONFLICT (template_id) DO NOTHING;

-- 插入测试虚拟机实例
INSERT INTO vm_instances (vm_id, user_id, name, pve_node, pve_vmid, cpu_cores, memory_mb, disk_gb, os_type, status, template_id) VALUES
('vm-test-001', 1, '测试虚拟机01', 'pve-node-01', 100, 2, 2048, 40, 'Linux', 'STOPPED', 1),
('vm-test-002', 1, '测试虚拟机02', 'pve-node-01', 101, 1, 1024, 20, 'Linux', 'RUNNING', 2),
('vm-demo-001', 2, '演示虚拟机01', 'pve-node-02', 200, 4, 4096, 80, 'Windows', 'STOPPED', 6)
ON CONFLICT (vm_id) DO NOTHING;

-- 更新节点当前VM数量
UPDATE pve_nodes SET current_vms = (
    SELECT COUNT(*) FROM vm_instances 
    WHERE pve_node = pve_nodes.node_id 
    AND status NOT IN ('DELETED', 'CREATE_FAILED')
);

-- 插入虚拟机网络配置
INSERT INTO vm_networks (vm_id, interface_name, mac_address, ip_address, netmask, gateway, bridge) VALUES
('vm-test-001', 'eth0', '52:54:00:12:34:56', '***********0', '*************', '***********', 'vmbr0'),
('vm-test-002', 'eth0', '52:54:00:12:34:57', '***********1', '*************', '***********', 'vmbr0'),
('vm-demo-001', 'eth0', '52:54:00:12:34:58', '************', '*************', '***********', 'vmbr0')
ON CONFLICT DO NOTHING;

-- 插入虚拟机存储配置
INSERT INTO vm_storages (vm_id, storage_type, storage_name, size_gb, storage_pool) VALUES
('vm-test-001', 'DISK', 'scsi0', 40, 'local-lvm'),
('vm-test-002', 'DISK', 'scsi0', 20, 'local-lvm'),
('vm-demo-001', 'DISK', 'scsi0', 80, 'local-lvm')
ON CONFLICT DO NOTHING;

-- 插入一些历史监控数据
INSERT INTO vm_metrics (vm_id, cpu_usage, memory_usage, disk_usage, network_in, network_out, node_id, collected_time) VALUES
('vm-test-002', 15.5, 45.2, 25.8, 1024000, 512000, 'pve-node-01', NOW() - INTERVAL '1 hour'),
('vm-test-002', 18.3, 47.1, 25.9, 1124000, 612000, 'pve-node-01', NOW() - INTERVAL '30 minutes'),
('vm-test-002', 12.1, 44.8, 26.0, 1224000, 712000, 'pve-node-01', NOW() - INTERVAL '15 minutes')
ON CONFLICT DO NOTHING;

-- =============================================
-- 9. 创建触发器 (更新时间自动更新)
-- =============================================

-- 创建更新时间触发器函数 (如果不存在)
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建触发器
CREATE TRIGGER update_pve_nodes_updated_time BEFORE UPDATE ON pve_nodes FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_vm_templates_updated_time BEFORE UPDATE ON vm_templates FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_vm_instances_updated_time BEFORE UPDATE ON vm_instances FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- =============================================
-- 10. 创建视图
-- =============================================

-- 虚拟机详情视图
CREATE OR REPLACE VIEW vm_details AS
SELECT 
    vi.vm_id,
    vi.name,
    vi.user_id,
    vi.status,
    vi.cpu_cores,
    vi.memory_mb,
    vi.disk_gb,
    vi.os_type,
    vi.public_ip,
    vi.private_ip,
    vi.created_time,
    vi.updated_time,
    vt.template_name,
    vt.os_version,
    pn.node_name,
    pn.datacenter,
    pn.region
FROM vm_instances vi
LEFT JOIN vm_templates vt ON vi.template_id = vt.id
LEFT JOIN pve_nodes pn ON vi.pve_node = pn.node_id;

-- 节点资源统计视图
CREATE OR REPLACE VIEW node_resource_stats AS
SELECT 
    pn.node_id,
    pn.node_name,
    pn.status,
    pn.cpu_cores,
    pn.memory_gb,
    pn.storage_gb,
    pn.max_vms,
    pn.current_vms,
    ROUND((pn.current_vms::DECIMAL / pn.max_vms * 100), 2) as vm_usage_percent,
    pn.datacenter,
    pn.region,
    pn.last_heartbeat
FROM pve_nodes pn;

-- =============================================
-- 初始化完成
-- =============================================
SELECT 'CloudVPS 虚拟化服务数据库初始化完成!' as message;
