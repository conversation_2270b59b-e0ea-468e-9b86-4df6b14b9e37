# 生产环境配置
spring:
  datasource:
    # 生产环境使用PostgreSQL数据库
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:cloudvps_virtualization}
    username: ${DB_USERNAME:cloudvps}
    password: ${DB_PASSWORD:cloudvps123}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  jpa:
    hibernate:
      ddl-auto: validate  # 生产环境验证表结构
    show-sql: false  # 生产环境不显示SQL
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
        jdbc:
          time_zone: Asia/Shanghai
          batch_size: 20
        order_inserts: true
        order_updates: true

  cloud:
    consul:
      enabled: true  # 生产环境启用Consul
      host: localhost
      port: 8500
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-interval: 10s
        health-check-path: /actuator/health
        instance-id: ${spring.application.name}:${server.port}
        prefer-ip-address: true

# 生产环境日志配置
logging:
  level:
    com.cloudvps: INFO
    org.hibernate.SQL: WARN
    org.springframework.security: INFO
    org.springframework.cloud: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{50} - %msg%n"
  file:
    name: logs/cloudvps-virtualization-service.log
    max-size: 100MB
    max-history: 30

# 生产环境管理端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# PVE配置（生产环境使用真实连接）
pve:
  mock:
    enabled: ${PVE_MOCK_ENABLED:false}  # 生产环境默认禁用mock模式
    delay: 100     # 生产环境减少延迟
    resource-usage:
      cpu:
        min: 5.0
        max: 95.0
      memory:
        min: 10.0
        max: 90.0
      disk:
        min: 5.0
        max: 85.0
  connection:
    timeout: ${PVE_TIMEOUT:10000}
    retry-count: ${PVE_RETRY_COUNT:3}
    verify-ssl: ${PVE_VERIFY_SSL:false}  # 根据实际PVE环境配置
    pool-size: ${PVE_POOL_SIZE:20}

# 安全配置
security:
  jwt:
    secret: ${JWT_SECRET:your-secret-key-here}
    expiration: 86400000  # 24小时
