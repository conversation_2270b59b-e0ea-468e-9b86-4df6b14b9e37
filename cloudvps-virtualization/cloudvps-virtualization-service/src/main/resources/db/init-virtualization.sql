-- CloudVPS 虚拟化服务数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS cloudvps_virtualization;
USE cloudvps_virtualization;

-- PVE节点表
CREATE TABLE IF NOT EXISTS pve_nodes (
    id BIGSERIAL PRIMARY KEY,
    node_name VARCHAR(100) NOT NULL UNIQUE,
    host VA<PERSON>HAR(255) NOT NULL,
    port INTEGER NOT NULL DEFAULT 8006,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    verify_ssl BOOLEAN NOT NULL DEFAULT false,
    status VARCHAR(20) NOT NULL DEFAULT 'OFFLINE' CHECK (status IN ('ONLINE', 'OFFLINE', 'MAINTENANCE', 'ERROR', 'UNKNOWN')),

    -- 监控信息
    cpu_usage DECIMAL(5,2) DEFAULT 0.00,
    memory_usage DECIMAL(5,2) DEFAULT 0.00,
    disk_usage DECIMAL(5,2) DEFAULT 0.00,
    uptime BIGINT DEFAULT 0, -- 运行时间（秒）
    last_heartbeat TIMESTAMP, -- 最后心跳时间

    -- 时间信息
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT
);

-- 虚拟机表
CREATE TABLE IF NOT EXISTS virtual_machines (
    id BIGSERIAL PRIMARY KEY,
    vmid INTEGER NOT NULL, -- PVE中的VM ID
    node_id BIGINT NOT NULL REFERENCES pve_nodes(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL, -- 所属用户ID（来自系统服务）

    -- 基本信息
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'STOPPED' CHECK (status IN ('RUNNING', 'STOPPED', 'PAUSED', 'SUSPENDED', 'ERROR', 'CREATING', 'DELETING')),

    -- 配置信息
    os_type VARCHAR(50) DEFAULT 'l26',
    cores INTEGER DEFAULT 1,
    memory INTEGER DEFAULT 1024, -- 内存大小（MB）
    disk_size INTEGER DEFAULT 20, -- 磁盘大小（GB）
    network VARCHAR(100) DEFAULT 'vmbr0',
    ip_address VARCHAR(45),
    mac_address VARCHAR(17),
    template_id BIGINT,
    auto_start BOOLEAN NOT NULL DEFAULT false,

    -- 时间信息
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,

    CONSTRAINT uk_vm_node_vmid UNIQUE(node_id, vmid)
);



-- 创建索引
CREATE INDEX IF NOT EXISTS idx_pve_nodes_status ON pve_nodes(status);
CREATE INDEX IF NOT EXISTS idx_pve_nodes_last_heartbeat ON pve_nodes(last_heartbeat);

CREATE INDEX IF NOT EXISTS idx_virtual_machines_node_id ON virtual_machines(node_id);
CREATE INDEX IF NOT EXISTS idx_virtual_machines_user_id ON virtual_machines(user_id);
CREATE INDEX IF NOT EXISTS idx_virtual_machines_status ON virtual_machines(status);

-- 插入初始数据

-- 插入示例PVE节点（需要根据实际情况修改）
INSERT INTO pve_nodes (node_name, host, port, username, password, status) VALUES
('pve-node-01', '*************', 8006, 'root', 'password123', 'OFFLINE'),
('pve-node-02', '*************', 8006, 'root', 'password123', 'OFFLINE')
ON CONFLICT (node_name) DO NOTHING;
