-- 创建CloudVPS虚拟化服务数据库
-- 注意：此脚本需要以PostgreSQL超级用户身份运行

-- 创建数据库
CREATE DATABASE cloudvps_virtualization
    WITH 
    OWNER = cloudvps
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- 创建用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'cloudvps') THEN
        CREATE USER cloudvps WITH PASSWORD 'cloudvps123';
    END IF;
END
$$;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE cloudvps_virtualization TO cloudvps;

-- 连接到新数据库并授予schema权限
\c cloudvps_virtualization;
GRANT ALL ON SCHEMA public TO cloudvps;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO cloudvps;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO cloudvps;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO cloudvps;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO cloudvps;
