spring:
  application:
    name: cloudvps-service-virtualization
  profiles:
    active: dev
  main:
    allow-circular-references: true

  # 数据库配置（标准PostgreSQL配置）
  datasource:
    url: ********************************************************
    username: cloudvps
    password: cloudvps123
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          time_zone: Asia/Shanghai

  redis:
    host: localhost
    port: 6379
    database: 1
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-interval: 10s
        health-check-path: /actuator/health
        instance-id: ${spring.application.name}:${server.port}
    openfeign:
      client:
        config:
          default:
            connect-timeout: 5000
            read-timeout: 5000
          system-service:
            url: http://localhost:8081  # 系统服务地址

  task:
    execution:
      pool:
        core-size: 5
        max-size: 20
        queue-capacity: 100
        keep-alive: 60s
    scheduling:
      pool:
        size: 5

server:
  port: 8082
  servlet:
    context-path: /

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.cloudvps: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# CloudVPS 自定义配置
cloudvps:
  jwt:
    secret: cloudvps-unified-jwt-secret-key-for-all-services-2024
    expiration: 86400  # 24小时（秒）
    issuer: cloudvps

  pve:
    # PVE客户端默认配置
    default:
      auth:
        session-timeout: PT2H
        max-retries: 3
      http:
        connect-timeout: PT30S
        read-timeout: PT5M
        max-idle-connections: 5
      cache:
        session-cache-size: 100
        session-ttl: PT2H
    
    # 心跳检查配置
    heartbeat:
      interval: PT1M
      timeout: PT30S
      
    # 资源监控配置
    monitoring:
      enabled: true
      interval: PT5M
      metrics-retention-days: 30

  swagger:
    title: CloudVPS虚拟化服务API
    description: PVE节点管理、虚拟机生命周期管理等功能
    version: 1.0.0
    contact:
      name: CloudVPS Virtualization Team
      email: <EMAIL>
      url: https://www.cloudvps.com/virtualization
    license:
      name: Apache 2.0
      url: https://www.apache.org/licenses/LICENSE-2.0
    server:
      url: http://localhost:8082
      description: 虚拟化服务开发环境

  security:
    # 不需要认证的路径
    permit-all-paths:
      - /actuator/**
      - /swagger-ui/**
      - /v3/api-docs/**
    # 需要管理员权限的路径
    admin-paths:
      - /virtualization/nodes/**

---
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    root: INFO
    com.cloudvps: DEBUG

---
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    root: WARN
    com.cloudvps: INFO

cloudvps:
  jwt:
    secret: ${JWT_SECRET:cloudvps-unified-jwt-secret-key-for-all-services-2024}
    expiration: ${JWT_EXPIRATION:86400}
    issuer: cloudvps
