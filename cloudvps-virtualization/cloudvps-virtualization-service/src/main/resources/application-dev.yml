# 开发环境配置
spring:
  datasource:
    # 生产环境使用PostgreSQL数据库
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:cloudvps_virtualization}
    username: ${DB_USERNAME:cloudvps}
    password: ${DB_PASSWORD:cloudvps123}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  jpa:
    hibernate:
      ddl-auto: create-drop  # 开发环境自动创建表结构
    show-sql: true  # 开发环境显示SQL
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        jdbc:
          time_zone: Asia/Shanghai

  h2:
    console:
      enabled: true
      path: /h2-console

  cloud:
    consul:
      enabled: false  # 开发环境禁用Consul
      discovery:
        enabled: false

# 开发环境日志配置
logging:
  level:
    com.cloudvps: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 开发环境管理端点
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 开发环境暴露所有端点
  endpoint:
    health:
      show-details: always

# PVE配置（开发环境使用增强模拟模式）
pve:
  mock:
    enabled: true  # 使用增强模拟模式，支持真实PVE架构
    delay: 500     # 模拟操作延迟
    resource-usage:
      cpu:
        min: 10.0
        max: 80.0
      memory:
        min: 20.0
        max: 70.0
      disk:
        min: 15.0
        max: 60.0
  connection:
    timeout: 5000
    retry-count: 3
    verify-ssl: false
    pool-size: 5
