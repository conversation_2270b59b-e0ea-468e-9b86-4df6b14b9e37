-- CloudVPS虚拟化服务 PostgreSQL 初始化脚本
-- 创建数据库和用户

-- 创建数据库
CREATE DATABASE cloudvps_virtualization
    WITH 
    OWNER = cloudvps
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.utf8'
    LC_CTYPE = 'en_US.utf8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- 创建用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'cloudvps') THEN
        CREATE USER cloudvps WITH PASSWORD 'cloudvps123';
    END IF;
END
$$;

-- 授权
GRANT ALL PRIVILEGES ON DATABASE cloudvps_virtualization TO cloudvps;

-- 连接到新数据库
\c cloudvps_virtualization;

-- 授权schema权限
GRANT ALL ON SCHEMA public TO cloudvps;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO cloudvps;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO cloudvps;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO cloudvps;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO cloudvps;

-- 创建表结构（如果使用ddl-auto: validate，需要预先创建表）
-- 注意：这些表结构应该与JPA实体保持一致

-- PVE节点表
CREATE TABLE IF NOT EXISTS pve_nodes (
    id BIGSERIAL PRIMARY KEY,
    node_name VARCHAR(100) NOT NULL UNIQUE,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    verify_ssl BOOLEAN NOT NULL DEFAULT false,
    status VARCHAR(20) NOT NULL CHECK (status IN ('ONLINE','OFFLINE','MAINTENANCE','ERROR','UNKNOWN')),
    cpu_usage NUMERIC(5,2),
    memory_usage NUMERIC(5,2),
    disk_usage NUMERIC(5,2),
    uptime BIGINT,
    last_heartbeat TIMESTAMP(6),
    created_by BIGINT,
    created_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by BIGINT,
    updated_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 虚拟机表
CREATE TABLE IF NOT EXISTS virtual_machines (
    id BIGSERIAL PRIMARY KEY,
    vmid INTEGER NOT NULL,
    node_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL CHECK (status IN ('RUNNING','STOPPED','PAUSED','SUSPENDED','CREATING','DELETING','ERROR','UNKNOWN')),
    os_type VARCHAR(50) NOT NULL,
    cores INTEGER NOT NULL,
    memory INTEGER NOT NULL,
    disk_size INTEGER NOT NULL,
    network VARCHAR(100),
    ip_address VARCHAR(45),
    mac_address VARCHAR(17),
    template_id BIGINT,
    auto_start BOOLEAN NOT NULL DEFAULT false,
    created_by BIGINT,
    created_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by BIGINT,
    updated_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_vm_node_vmid UNIQUE (node_id, vmid),
    CONSTRAINT fk_vm_node FOREIGN KEY (node_id) REFERENCES pve_nodes(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_pve_nodes_status ON pve_nodes (status);
CREATE INDEX IF NOT EXISTS idx_pve_nodes_last_heartbeat ON pve_nodes (last_heartbeat);
CREATE INDEX IF NOT EXISTS idx_virtual_machines_user_id ON virtual_machines (user_id);
CREATE INDEX IF NOT EXISTS idx_virtual_machines_status ON virtual_machines (status);
CREATE INDEX IF NOT EXISTS idx_virtual_machines_node_id ON virtual_machines (node_id);

-- 插入测试数据（可选）
-- 注意：生产环境可能不需要测试数据

-- 测试PVE节点
INSERT INTO pve_nodes (node_name, host, port, username, password, verify_ssl, status, created_time, updated_time)
VALUES 
    ('pve-prod-01', '*************', 8006, 'root', 'pve_password', false, 'OFFLINE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('pve-prod-02', '*************', 8006, 'root', 'pve_password', false, 'OFFLINE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (node_name) DO NOTHING;

-- 创建触发器更新updated_time
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表添加更新时间触发器
DROP TRIGGER IF EXISTS update_pve_nodes_updated_time ON pve_nodes;
CREATE TRIGGER update_pve_nodes_updated_time
    BEFORE UPDATE ON pve_nodes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

DROP TRIGGER IF EXISTS update_virtual_machines_updated_time ON virtual_machines;
CREATE TRIGGER update_virtual_machines_updated_time
    BEFORE UPDATE ON virtual_machines
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 创建视图（可选）
CREATE OR REPLACE VIEW vm_summary AS
SELECT 
    vm.id,
    vm.vmid,
    vm.name,
    vm.status,
    vm.os_type,
    vm.cores,
    vm.memory,
    vm.disk_size,
    vm.user_id,
    pn.node_name,
    pn.host,
    pn.status as node_status,
    vm.created_time,
    vm.updated_time
FROM virtual_machines vm
JOIN pve_nodes pn ON vm.node_id = pn.id;

-- 授权视图权限
GRANT SELECT ON vm_summary TO cloudvps;

-- 完成初始化
SELECT 'CloudVPS虚拟化服务数据库初始化完成' AS message;
