# CloudVPS 虚拟化服务模块

## 模块概述

虚拟化服务是 CloudVPS 平台的核心业务模块，负责与 Proxmox VE (PVE) 集群集成，提供虚拟机生命周期管理、资源监控、模板管理等功能。支持模拟模式和真实 PVE 集成两种运行模式，满足开发测试和生产环境的不同需求。

## 技术架构

### 技术栈
- **框架**: Spring Boot 3.2.1
- **Java 版本**: JDK 21
- **数据库**: H2 (开发) / PostgreSQL (生产)
- **缓存**: Redis 7.2+
- **PVE 集成**: pve-java-client 1.0.0
- **任务调度**: Spring Task Scheduling
- **API 文档**: Swagger/OpenAPI 3
- **构建工具**: Maven 3.9+

### 架构设计模式
```
┌─────────────────────────────────────────┐
│              Controller 层               │
│ PveNodeController │ VirtualMachineController │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│               Service 层                │
│  PveNodeService │ VirtualMachineService │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│              PVE Client 层              │
│     PveClientService │ PveConfig       │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│             PVE Integration             │
│  pve-java-client │ Proxmox VE API      │
└─────────────────────────────────────────┘
```

## 核心功能模块

### 1. PVE 节点管理
- **节点注册**: 添加和配置 PVE 节点
- **连接测试**: 验证节点连通性和认证
- **状态监控**: 实时监控节点运行状态
- **心跳检测**: 定时检查节点可用性
- **负载均衡**: 智能选择最优节点

### 2. 虚拟机生命周期管理
- **虚拟机创建**: 基于模板或自定义配置创建 VM
- **启动停止**: VM 的启动、停止、重启操作
- **配置管理**: CPU、内存、磁盘配置调整
- **快照管理**: VM 快照创建、恢复、删除
- **克隆操作**: VM 克隆和模板制作

### 3. 资源监控模块
- **实时监控**: CPU、内存、磁盘、网络使用率
- **历史数据**: 资源使用历史记录和趋势分析
- **告警机制**: 资源使用异常告警
- **性能优化**: 基于监控数据的性能建议

### 4. 模板管理模块
- **模板创建**: 从现有 VM 创建模板
- **模板库**: 预置操作系统模板管理
- **模板部署**: 基于模板快速部署 VM
- **版本管理**: 模板版本控制和更新

### 5. 定时任务模块
- **心跳检测**: 定时检查 PVE 节点状态
- **自动启动**: 标记为自动启动的 VM 管理
- **资源清理**: 过期资源自动清理
- **数据同步**: 与 PVE 集群数据同步

## PVE 集成设计

### 运行模式配置
```yaml
# application.yml
pve:
  mock:
    enabled: ${PVE_MOCK_ENABLED:true}  # 模拟模式开关
    simulate-delays: true              # 模拟网络延迟
    failure-rate: 0.1                  # 模拟失败率
  
  # 真实 PVE 节点配置
  nodes:
    - name: "pve-node-1"
      host: "*************"
      port: 8006
      username: "root@pam"
      password: "${PVE_PASSWORD:your-password}"
      ssl: true
      verify-ssl: false
    - name: "pve-node-2"
      host: "*************"
      port: 8006
      username: "root@pam"
      password: "${PVE_PASSWORD:your-password}"
      ssl: true
      verify-ssl: false
```

### PVE 客户端封装
```java
@Component
public class PveClientService {
    
    private final PveConfig pveConfig;
    private final Map<String, PveClient> clientCache = new ConcurrentHashMap<>();
    
    /**
     * 获取 PVE 客户端
     */
    public PveClient getClient(String nodeName) {
        return clientCache.computeIfAbsent(nodeName, this::createClient);
    }
    
    /**
     * 创建 PVE 客户端连接
     */
    private PveClient createClient(String nodeName) {
        PveNodeConfig nodeConfig = pveConfig.getNode(nodeName);
        
        if (pveConfig.isMockEnabled()) {
            return new MockPveClient(nodeConfig);
        } else {
            return new RealPveClient(nodeConfig);
        }
    }
    
    /**
     * 测试节点连接
     */
    public boolean testConnection(String nodeName) {
        try {
            PveClient client = getClient(nodeName);
            return client.ping();
        } catch (Exception e) {
            log.error("Failed to connect to PVE node: {}", nodeName, e);
            return false;
        }
    }
}
```

### 模拟模式实现
```java
@Component
@ConditionalOnProperty(name = "pve.mock.enabled", havingValue = "true")
public class MockPveClient implements PveClient {
    
    private final Map<String, VirtualMachine> mockVms = new ConcurrentHashMap<>();
    private final Random random = new Random();
    
    @Override
    public VmCreateResponse createVm(VmCreateRequest request) {
        // 模拟创建延迟
        simulateDelay();
        
        // 模拟失败
        if (shouldSimulateFailure()) {
            throw new PveException("模拟创建失败");
        }
        
        // 创建模拟 VM
        VirtualMachine vm = new VirtualMachine();
        vm.setVmId(generateVmId());
        vm.setName(request.getName());
        vm.setStatus(VmStatus.STOPPED);
        
        mockVms.put(vm.getVmId(), vm);
        
        return VmCreateResponse.builder()
                .vmId(vm.getVmId())
                .status("success")
                .build();
    }
    
    @Override
    public void startVm(String vmId) {
        simulateDelay();
        VirtualMachine vm = mockVms.get(vmId);
        if (vm != null) {
            vm.setStatus(VmStatus.RUNNING);
        }
    }
    
    private void simulateDelay() {
        if (pveConfig.isSimulateDelays()) {
            try {
                Thread.sleep(random.nextInt(1000) + 500); // 500-1500ms
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
    
    private boolean shouldSimulateFailure() {
        return random.nextDouble() < pveConfig.getFailureRate();
    }
}
```

## 数据库设计

### 核心数据表
```sql
-- PVE 节点表
CREATE TABLE pve_nodes (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL DEFAULT 8006,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    ssl_enabled BOOLEAN NOT NULL DEFAULT true,
    verify_ssl BOOLEAN NOT NULL DEFAULT false,
    status VARCHAR(20) NOT NULL DEFAULT 'OFFLINE',
    last_heartbeat TIMESTAMP,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 虚拟机表
CREATE TABLE virtual_machines (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    node_name VARCHAR(100) NOT NULL,
    template_id VARCHAR(50),
    cpu_cores INTEGER NOT NULL DEFAULT 1,
    memory_mb INTEGER NOT NULL DEFAULT 1024,
    disk_gb INTEGER NOT NULL DEFAULT 20,
    network_config TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'STOPPED',
    auto_start BOOLEAN NOT NULL DEFAULT false,
    user_id BIGINT,
    order_id BIGINT,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (node_name) REFERENCES pve_nodes(name)
);

-- VM 模板表
CREATE TABLE vm_templates (
    id BIGSERIAL PRIMARY KEY,
    template_id VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    os_type VARCHAR(50),
    cpu_cores INTEGER NOT NULL DEFAULT 1,
    memory_mb INTEGER NOT NULL DEFAULT 1024,
    disk_gb INTEGER NOT NULL DEFAULT 20,
    image_url VARCHAR(500),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 资源监控表
CREATE TABLE resource_metrics (
    id BIGSERIAL PRIMARY KEY,
    vm_id VARCHAR(50) NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20),
    collected_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vm_id) REFERENCES virtual_machines(vm_id)
);
```

### 索引优化
```sql
-- 虚拟机表索引
CREATE INDEX idx_virtual_machines_vm_id ON virtual_machines(vm_id);
CREATE INDEX idx_virtual_machines_node_name ON virtual_machines(node_name);
CREATE INDEX idx_virtual_machines_status ON virtual_machines(status);
CREATE INDEX idx_virtual_machines_user_id ON virtual_machines(user_id);
CREATE INDEX idx_virtual_machines_auto_start ON virtual_machines(auto_start);

-- 资源监控表索引
CREATE INDEX idx_resource_metrics_vm_id ON resource_metrics(vm_id);
CREATE INDEX idx_resource_metrics_type_time ON resource_metrics(metric_type, collected_time);
CREATE INDEX idx_resource_metrics_collected_time ON resource_metrics(collected_time);
```

## API 接口设计

### PVE 节点管理接口
```java
@RestController
@RequestMapping("/virtualization/nodes")
@PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_VIEW')")
public class PveNodeController {
    
    /**
     * 获取节点列表
     */
    @GetMapping
    public ApiResponse<List<PveNodeResponse>> getNodes() {
        List<PveNodeResponse> nodes = pveNodeService.getAllNodes();
        return ApiResponse.success(nodes);
    }
    
    /**
     * 获取节点详情
     */
    @GetMapping("/{nodeId}")
    public ApiResponse<PveNodeResponse> getNode(@PathVariable Long nodeId) {
        PveNodeResponse node = pveNodeService.getNodeById(nodeId);
        return ApiResponse.success(node);
    }
    
    /**
     * 测试节点连接
     */
    @PostMapping("/{nodeId}/test-connection")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_MANAGE')")
    public ApiResponse<ConnectionTestResponse> testConnection(@PathVariable Long nodeId) {
        ConnectionTestResponse result = pveNodeService.testConnection(nodeId);
        return ApiResponse.success(result);
    }
    
    /**
     * 更新节点状态
     */
    @PutMapping("/{nodeId}/status")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_NODE_MANAGE')")
    public ApiResponse<Void> updateNodeStatus(
            @PathVariable Long nodeId,
            @RequestBody UpdateNodeStatusRequest request) {
        pveNodeService.updateNodeStatus(nodeId, request.getStatus());
        return ApiResponse.success();
    }
}
```

### 虚拟机管理接口
```java
@RestController
@RequestMapping("/virtualization/vms")
@PreAuthorize("hasAuthority('VIRTUALIZATION_VM_VIEW')")
public class VirtualMachineController {
    
    /**
     * 获取虚拟机列表
     */
    @GetMapping
    public ApiResponse<PageResponse<VirtualMachineResponse>> getVirtualMachines(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String nodeName) {
        
        PageResponse<VirtualMachineResponse> vms = virtualMachineService
                .getVirtualMachines(page, size, status, nodeName);
        return ApiResponse.success(vms);
    }
    
    /**
     * 创建虚拟机
     */
    @PostMapping
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_CREATE')")
    public ApiResponse<VirtualMachineResponse> createVirtualMachine(
            @RequestBody CreateVmRequest request) {
        VirtualMachineResponse vm = virtualMachineService.createVirtualMachine(request);
        return ApiResponse.success(vm);
    }
    
    /**
     * 启动虚拟机
     */
    @PostMapping("/{vmId}/start")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_MANAGE')")
    public ApiResponse<Void> startVirtualMachine(@PathVariable String vmId) {
        virtualMachineService.startVirtualMachine(vmId);
        return ApiResponse.success();
    }
    
    /**
     * 停止虚拟机
     */
    @PostMapping("/{vmId}/stop")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_MANAGE')")
    public ApiResponse<Void> stopVirtualMachine(@PathVariable String vmId) {
        virtualMachineService.stopVirtualMachine(vmId);
        return ApiResponse.success();
    }
    
    /**
     * 删除虚拟机
     */
    @DeleteMapping("/{vmId}")
    @PreAuthorize("hasAuthority('VIRTUALIZATION_VM_DELETE')")
    public ApiResponse<Void> deleteVirtualMachine(@PathVariable String vmId) {
        virtualMachineService.deleteVirtualMachine(vmId);
        return ApiResponse.success();
    }
}
```

## 定时任务设计

### 任务调度配置
```java
@Configuration
@EnableScheduling
public class SchedulingConfig {
    
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(10);
        scheduler.setThreadNamePrefix("cloudvps-scheduler-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(60);
        return scheduler;
    }
}
```

### 定时任务实现
```java
@Component
@Slf4j
public class VirtualizationScheduledTasks {
    
    private final PveNodeService pveNodeService;
    private final VirtualMachineService virtualMachineService;
    private final ResourceMonitoringService resourceMonitoringService;
    
    /**
     * 节点心跳检测 - 每分钟执行
     */
    @Scheduled(fixedRate = 60000)
    public void checkNodeHeartbeat() {
        log.debug("开始执行节点心跳检测任务");
        
        List<PveNode> nodes = pveNodeService.getAllActiveNodes();
        for (PveNode node : nodes) {
            try {
                boolean isOnline = pveNodeService.testConnection(node.getId());
                pveNodeService.updateHeartbeat(node.getId(), isOnline);
                
                if (!isOnline) {
                    log.warn("PVE 节点 {} 心跳检测失败", node.getName());
                    // 发送告警通知
                    sendNodeOfflineAlert(node);
                }
            } catch (Exception e) {
                log.error("节点 {} 心跳检测异常", node.getName(), e);
            }
        }
        
        log.debug("节点心跳检测任务完成");
    }
    
    /**
     * 自动启动 VM - 每5分钟执行
     */
    @Scheduled(fixedRate = 300000)
    public void autoStartVirtualMachines() {
        log.debug("开始执行 VM 自动启动任务");
        
        List<VirtualMachine> autoStartVms = virtualMachineService.getAutoStartVms();
        for (VirtualMachine vm : autoStartVms) {
            try {
                if (vm.getStatus() == VmStatus.STOPPED) {
                    virtualMachineService.startVirtualMachine(vm.getVmId());
                    log.info("自动启动 VM: {}", vm.getVmId());
                }
            } catch (Exception e) {
                log.error("自动启动 VM {} 失败", vm.getVmId(), e);
            }
        }
        
        log.debug("VM 自动启动任务完成");
    }
    
    /**
     * 资源监控数据收集 - 每2分钟执行
     */
    @Scheduled(fixedRate = 120000)
    public void collectResourceMetrics() {
        log.debug("开始收集资源监控数据");
        
        List<VirtualMachine> runningVms = virtualMachineService.getRunningVms();
        for (VirtualMachine vm : runningVms) {
            try {
                ResourceMetrics metrics = resourceMonitoringService.collectMetrics(vm.getVmId());
                resourceMonitoringService.saveMetrics(metrics);
            } catch (Exception e) {
                log.error("收集 VM {} 资源数据失败", vm.getVmId(), e);
            }
        }
        
        log.debug("资源监控数据收集完成");
    }
    
    /**
     * 清理过期监控数据 - 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredMetrics() {
        log.info("开始清理过期监控数据");
        
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
        int deletedCount = resourceMonitoringService.deleteMetricsBefore(cutoffTime);
        
        log.info("清理过期监控数据完成，删除 {} 条记录", deletedCount);
    }
    
    private void sendNodeOfflineAlert(PveNode node) {
        // 实现告警通知逻辑
        // 可以发送邮件、短信或推送到监控系统
    }
}
```

## 缓存策略

### Redis 缓存配置
```java
@Configuration
public class VirtualizationCacheConfig {
    
    @Bean
    @Primary
    public CacheManager virtualizationCacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10))  // VM 状态缓存10分钟
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // VM 状态缓存 - 5分钟
        cacheConfigurations.put("vm_status", config.entryTtl(Duration.ofMinutes(5)));
        
        // 节点状态缓存 - 2分钟
        cacheConfigurations.put("node_status", config.entryTtl(Duration.ofMinutes(2)));
        
        // 模板信息缓存 - 1小时
        cacheConfigurations.put("vm_templates", config.entryTtl(Duration.ofHours(1)));
        
        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
```

### 缓存使用示例
```java
@Service
public class VirtualMachineService {
    
    /**
     * 缓存 VM 状态信息
     */
    @Cacheable(value = "vm_status", key = "#vmId")
    public VmStatus getVmStatus(String vmId) {
        // 从 PVE 查询 VM 状态
        return pveClientService.getVmStatus(vmId);
    }
    
    /**
     * 更新 VM 状态时清除缓存
     */
    @CacheEvict(value = "vm_status", key = "#vmId")
    public void updateVmStatus(String vmId, VmStatus status) {
        // 更新 VM 状态
        virtualMachineRepository.updateStatus(vmId, status);
    }
    
    /**
     * 缓存模板信息
     */
    @Cacheable(value = "vm_templates", unless = "#result.isEmpty()")
    public List<VmTemplate> getAvailableTemplates() {
        // 查询可用模板
        return vmTemplateRepository.findByStatus(TemplateStatus.ACTIVE);
    }
}
```

## 监控和告警

### 健康检查指标
```java
@Component
public class VirtualizationHealthIndicator implements HealthIndicator {
    
    private final PveNodeService pveNodeService;
    private final VirtualMachineService virtualMachineService;
    
    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        try {
            // 检查 PVE 节点连通性
            List<PveNode> nodes = pveNodeService.getAllActiveNodes();
            long onlineNodes = nodes.stream()
                    .mapToLong(node -> pveNodeService.testConnection(node.getId()) ? 1 : 0)
                    .sum();
            
            // 检查运行中的 VM 数量
            long runningVms = virtualMachineService.getRunningVmCount();
            
            builder.up()
                    .withDetail("total_nodes", nodes.size())
                    .withDetail("online_nodes", onlineNodes)
                    .withDetail("running_vms", runningVms)
                    .withDetail("pve_mock_enabled", pveConfig.isMockEnabled());
                    
        } catch (Exception e) {
            builder.down().withException(e);
        }
        
        return builder.build();
    }
}
```

### 自定义监控指标
```java
@Component
public class VirtualizationMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter vmCreateCounter;
    private final Counter vmStartCounter;
    private final Gauge runningVmGauge;
    
    public VirtualizationMetrics(MeterRegistry meterRegistry, 
                               VirtualMachineService virtualMachineService) {
        this.meterRegistry = meterRegistry;
        
        // VM 创建计数器
        this.vmCreateCounter = Counter.builder("cloudvps.vm.created")
                .description("Total number of VMs created")
                .register(meterRegistry);
        
        // VM 启动计数器
        this.vmStartCounter = Counter.builder("cloudvps.vm.started")
                .description("Total number of VMs started")
                .register(meterRegistry);
        
        // 运行中 VM 数量
        this.runningVmGauge = Gauge.builder("cloudvps.vm.running")
                .description("Number of running VMs")
                .register(meterRegistry, virtualMachineService, 
                         VirtualMachineService::getRunningVmCount);
    }
    
    public void incrementVmCreated() {
        vmCreateCounter.increment();
    }
    
    public void incrementVmStarted() {
        vmStartCounter.increment();
    }
}
```

## 部署配置

### 应用配置
```yaml
# application.yml
spring:
  application:
    name: cloudvps-virtualization-service
  
  # 开发环境使用 H2
  datasource:
    url: jdbc:h2:mem:virtualization
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DB:1}

# PVE 配置
pve:
  mock:
    enabled: ${PVE_MOCK_ENABLED:true}
    simulate-delays: true
    failure-rate: 0.1
  
  connection:
    timeout: 30000
    retry-attempts: 3
    retry-delay: 5000

# 服务配置
server:
  port: 8082

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

### 生产环境配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:cloudvps_virtualization}
    username: ${DB_USERNAME:cloudvps}
    password: ${DB_PASSWORD:cloudvps123}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

pve:
  mock:
    enabled: false
  nodes:
    - name: "pve-node-1"
      host: "${PVE_NODE1_HOST:*************}"
      port: 8006
      username: "${PVE_USERNAME:root@pam}"
      password: "${PVE_PASSWORD:your-password}"
      ssl: true
      verify-ssl: false

logging:
  level:
    com.cloudvps.virtualization: INFO
    org.springframework.web: WARN
  file:
    name: logs/cloudvps-virtualization.log
```

## 未来扩展计划

### 功能扩展
- **容器支持**: Docker 容器管理
- **网络管理**: 虚拟网络配置和管理
- **存储管理**: 分布式存储集成
- **备份恢复**: 自动化备份和恢复策略
- **迁移功能**: VM 在线迁移支持

### 技术优化
- **异步处理**: VM 操作异步化处理
- **批量操作**: 批量 VM 管理功能
- **资源调度**: 智能资源分配算法
- **性能优化**: PVE API 调用优化

### 集成扩展
- **监控集成**: Prometheus + Grafana 监控
- **日志聚合**: ELK 日志分析
- **告警系统**: 多渠道告警通知
- **自动化运维**: Ansible 自动化部署
