<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cloudvps</groupId>
        <artifactId>cloudvps-platform</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>cloudvps-virtualization</artifactId>
    <packaging>pom</packaging>
    <name>CloudVPS Virtualization Domain</name>
    <description>CloudVPS虚拟化业务域</description>

    <modules>
        <module>cloudvps-virtualization-api</module>
        <module>cloudvps-virtualization-service</module>
    </modules>
</project>
