<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cloudvps</groupId>
        <artifactId>cloudvps-virtualization</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>cloudvps-virtualization-api</artifactId>
    <packaging>jar</packaging>
    <name>CloudVPS Virtualization API</name>
    <description>CloudVPS虚拟化服务API定义</description>

    <dependencies>
        <!-- Common Core -->
        <dependency>
            <groupId>com.cloudvps</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <!-- System API -->
        <dependency>
            <groupId>com.cloudvps</groupId>
            <artifactId>cloudvps-system-api</artifactId>
        </dependency>

        <!-- Spring Cloud OpenFeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

    </dependencies>

</project>
