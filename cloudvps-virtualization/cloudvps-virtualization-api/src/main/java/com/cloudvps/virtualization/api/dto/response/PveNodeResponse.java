package com.cloudvps.virtualization.api.dto.response;

import com.cloudvps.virtualization.api.enums.NodeStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * PVE节点响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PveNodeResponse {
    
    /**
     * 节点ID
     */
    private Long id;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 主机地址
     */
    private String host;
    
    /**
     * 端口
     */
    private Integer port;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 是否验证SSL
     */
    private Boolean verifySsl;
    
    /**
     * 节点状态
     */
    private NodeStatus status;
    
    /**
     * CPU使用率
     */
    private BigDecimal cpuUsage;
    
    /**
     * 内存使用率
     */
    private BigDecimal memoryUsage;
    
    /**
     * 磁盘使用率
     */
    private BigDecimal diskUsage;
    
    /**
     * 运行时间（秒）
     */
    private Long uptime;
    
    /**
     * 最后心跳时间
     */
    private LocalDateTime lastHeartbeat;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 连接URL
     */
    private String connectionUrl;
    
    /**
     * 是否在线
     */
    private Boolean online;
    
    /**
     * 是否可用
     */
    private Boolean available;
    
    /**
     * 虚拟机数量
     */
    private Long vmCount;
    
    /**
     * 运行中的虚拟机数量
     */
    private Long runningVmCount;
}
