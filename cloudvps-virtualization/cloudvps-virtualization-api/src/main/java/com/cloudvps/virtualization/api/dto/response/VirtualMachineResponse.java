package com.cloudvps.virtualization.api.dto.response;

import com.cloudvps.virtualization.api.enums.VmStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 虚拟机响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class VirtualMachineResponse {
    
    /**
     * 虚拟机ID
     */
    private Long id;
    
    /**
     * PVE虚拟机ID
     */
    private Integer vmid;
    
    /**
     * 节点ID
     */
    private Long nodeId;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 虚拟机名称
     */
    private String name;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 虚拟机状态
     */
    private VmStatus status;
    
    /**
     * 操作系统类型
     */
    private String osType;
    
    /**
     * CPU核心数
     */
    private Integer cores;
    
    /**
     * 内存大小（MB）
     */
    private Integer memory;
    
    /**
     * 磁盘大小（GB）
     */
    private Integer diskSize;
    
    /**
     * 网络配置
     */
    private String network;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * MAC地址
     */
    private String macAddress;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 是否自动启动
     */
    private Boolean autoStart;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 显示名称
     */
    private String displayName;
    
    /**
     * 是否运行中
     */
    private Boolean running;
    
    /**
     * 是否已停止
     */
    private Boolean stopped;
    
    /**
     * 连接URL
     */
    private String connectionUrl;
}
