package com.cloudvps.virtualization.api.dto.request;

import com.cloudvps.virtualization.api.enums.NodeStatus;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * PVE节点更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PveNodeUpdateRequest {
    
    /**
     * 主机地址
     */
    @Size(max = 255, message = "主机地址长度不能超过255个字符")
    private String host;
    
    /**
     * 端口
     */
    private Integer port;
    
    /**
     * 用户名
     */
    @Size(max = 100, message = "用户名长度不能超过100个字符")
    private String username;
    
    /**
     * 密码
     */
    @Size(max = 255, message = "密码长度不能超过255个字符")
    private String password;
    
    /**
     * 是否验证SSL
     */
    private Boolean verifySsl;
    
    /**
     * 节点状态
     */
    private NodeStatus status;
    
    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
}
