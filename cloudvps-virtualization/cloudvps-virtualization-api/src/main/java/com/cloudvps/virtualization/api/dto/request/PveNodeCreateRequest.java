package com.cloudvps.virtualization.api.dto.request;

import com.cloudvps.virtualization.api.enums.NodeStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * PVE节点创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PveNodeCreateRequest {
    
    /**
     * 节点名称
     */
    @NotBlank(message = "节点名称不能为空")
    @Size(max = 100, message = "节点名称长度不能超过100个字符")
    private String nodeName;
    
    /**
     * 主机地址
     */
    @NotBlank(message = "主机地址不能为空")
    @Size(max = 255, message = "主机地址长度不能超过255个字符")
    private String host;
    
    /**
     * 端口
     */
    @NotNull(message = "端口不能为空")
    private Integer port = 8006;
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(max = 100, message = "用户名长度不能超过100个字符")
    private String username;
    
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(max = 255, message = "密码长度不能超过255个字符")
    private String password;
    
    /**
     * 是否验证SSL
     */
    private Boolean verifySsl = false;
    
    /**
     * 节点状态
     */
    private NodeStatus status = NodeStatus.OFFLINE;
    
    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
}
