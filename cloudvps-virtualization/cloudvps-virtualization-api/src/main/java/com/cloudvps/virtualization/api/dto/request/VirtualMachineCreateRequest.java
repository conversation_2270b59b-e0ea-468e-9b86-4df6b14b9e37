package com.cloudvps.virtualization.api.dto.request;

import com.cloudvps.virtualization.api.enums.VmStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 虚拟机创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class VirtualMachineCreateRequest {
    
    /**
     * PVE虚拟机ID
     */
    @NotNull(message = "PVE虚拟机ID不能为空")
    private Integer vmid;
    
    /**
     * 所属节点ID
     */
    @NotNull(message = "所属节点ID不能为空")
    private Long nodeId;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 虚拟机名称
     */
    @NotBlank(message = "虚拟机名称不能为空")
    @Size(max = 100, message = "虚拟机名称长度不能超过100个字符")
    private String name;
    
    /**
     * 描述
     */
    @Size(max = 1000, message = "描述长度不能超过1000个字符")
    private String description;
    
    /**
     * 虚拟机状态
     */
    private VmStatus status = VmStatus.STOPPED;
    
    /**
     * 操作系统类型
     */
    @Size(max = 50, message = "操作系统类型长度不能超过50个字符")
    private String osType = "l26";
    
    /**
     * CPU核心数
     */
    @NotNull(message = "CPU核心数不能为空")
    private Integer cores = 1;
    
    /**
     * 内存大小（MB）
     */
    @NotNull(message = "内存大小不能为空")
    private Integer memory = 1024;
    
    /**
     * 磁盘大小（GB）
     */
    @NotNull(message = "磁盘大小不能为空")
    private Integer diskSize = 20;
    
    /**
     * 网络配置
     */
    @Size(max = 100, message = "网络配置长度不能超过100个字符")
    private String network = "vmbr0";
    
    /**
     * IP地址
     */
    @Size(max = 45, message = "IP地址长度不能超过45个字符")
    private String ipAddress;
    
    /**
     * MAC地址
     */
    @Size(max = 17, message = "MAC地址长度不能超过17个字符")
    private String macAddress;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 是否自动启动
     */
    private Boolean autoStart = false;
}
