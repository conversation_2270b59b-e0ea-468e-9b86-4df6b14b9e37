package com.cloudvps.virtualization.api.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * 虚拟机请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class VirtualMachineRequest {
    
    /**
     * 节点ID
     */
    @NotNull(message = "节点ID不能为空")
    private Long nodeId;
    
    /**
     * 虚拟机名称
     */
    @NotBlank(message = "虚拟机名称不能为空")
    @Size(max = 100, message = "虚拟机名称长度不能超过100个字符")
    private String name;
    
    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
    
    /**
     * 操作系统类型
     */
    @NotBlank(message = "操作系统类型不能为空")
    @Size(max = 50, message = "操作系统类型长度不能超过50个字符")
    private String osType = "l26";
    
    /**
     * CPU核心数
     */
    @NotNull(message = "CPU核心数不能为空")
    @Min(value = 1, message = "CPU核心数至少为1")
    @Max(value = 16, message = "CPU核心数不能超过16")
    private Integer cores = 1;
    
    /**
     * 内存大小（MB）
     */
    @NotNull(message = "内存大小不能为空")
    @Min(value = 512, message = "内存大小至少为512MB")
    @Max(value = 32768, message = "内存大小不能超过32GB")
    private Integer memory = 1024;
    
    /**
     * 磁盘大小（GB）
     */
    @NotNull(message = "磁盘大小不能为空")
    @Min(value = 10, message = "磁盘大小至少为10GB")
    @Max(value = 500, message = "磁盘大小不能超过500GB")
    private Integer diskSize = 20;
    
    /**
     * 网络配置
     */
    @Size(max = 100, message = "网络配置长度不能超过100个字符")
    private String network = "vmbr0";
    
    /**
     * 是否自动启动
     */
    private Boolean autoStart = false;
    
    /**
     * 模板ID（如果从模板创建）
     */
    private Long templateId;
    
    /**
     * 指定VMID（可选）
     */
    @Min(value = 100, message = "VMID不能小于100")
    @Max(value = 999999, message = "VMID不能超过999999")
    private Integer vmid;
}
