package com.cloudvps.virtualization.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.virtualization.api.enums.VmStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 虚拟机查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VirtualMachineQueryRequest extends PageRequest {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 节点ID
     */
    private Long nodeId;
    
    /**
     * 虚拟机名称
     */
    private String name;
    
    /**
     * 虚拟机状态
     */
    private VmStatus status;
    
    /**
     * 操作系统类型
     */
    private String osType;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 是否自动启动
     */
    private Boolean autoStart;
}
