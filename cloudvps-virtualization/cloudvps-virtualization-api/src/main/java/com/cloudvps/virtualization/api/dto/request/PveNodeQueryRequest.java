package com.cloudvps.virtualization.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.virtualization.api.enums.NodeStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * PVE节点查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PveNodeQueryRequest extends PageRequest {
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 主机地址
     */
    private String host;
    
    /**
     * 节点状态
     */
    private NodeStatus status;
    
    /**
     * CPU使用率最小值
     */
    private BigDecimal minCpuUsage;
    
    /**
     * CPU使用率最大值
     */
    private BigDecimal maxCpuUsage;
    
    /**
     * 内存使用率最小值
     */
    private BigDecimal minMemoryUsage;
    
    /**
     * 内存使用率最大值
     */
    private BigDecimal maxMemoryUsage;
}
