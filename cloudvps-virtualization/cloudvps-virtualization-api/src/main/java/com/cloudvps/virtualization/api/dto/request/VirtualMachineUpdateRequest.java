package com.cloudvps.virtualization.api.dto.request;

import com.cloudvps.virtualization.api.enums.VmStatus;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 虚拟机更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class VirtualMachineUpdateRequest {
    
    /**
     * 虚拟机名称
     */
    @Size(max = 100, message = "虚拟机名称长度不能超过100个字符")
    private String name;
    
    /**
     * 描述
     */
    @Size(max = 1000, message = "描述长度不能超过1000个字符")
    private String description;
    
    /**
     * 虚拟机状态
     */
    private VmStatus status;
    
    /**
     * 操作系统类型
     */
    @Size(max = 50, message = "操作系统类型长度不能超过50个字符")
    private String osType;
    
    /**
     * CPU核心数
     */
    private Integer cores;
    
    /**
     * 内存大小（MB）
     */
    private Integer memory;
    
    /**
     * 磁盘大小（GB）
     */
    private Integer diskSize;
    
    /**
     * 网络配置
     */
    @Size(max = 100, message = "网络配置长度不能超过100个字符")
    private String network;
    
    /**
     * IP地址
     */
    @Size(max = 45, message = "IP地址长度不能超过45个字符")
    private String ipAddress;
    
    /**
     * MAC地址
     */
    @Size(max = 17, message = "MAC地址长度不能超过17个字符")
    private String macAddress;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 是否自动启动
     */
    private Boolean autoStart;
}
