package com.cloudvps.virtualization.api.client;

import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.common.core.response.PageResponse;
import com.cloudvps.virtualization.api.dto.request.PveNodeRequest;
import com.cloudvps.virtualization.api.dto.request.VirtualMachineRequest;
import com.cloudvps.virtualization.api.dto.response.PveNodeResponse;
import com.cloudvps.virtualization.api.dto.response.VirtualMachineResponse;
import com.cloudvps.virtualization.api.enums.NodeStatus;
import com.cloudvps.virtualization.api.enums.VmStatus;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 虚拟化服务Feign客户端
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@FeignClient(
    name = "virtualization-service",
    url = "${cloudvps.services.virtualization.url:http://localhost:8082}",
    path = "/api/v1/virtualization"
)
public interface VirtualizationServiceClient {
    
    // ==================== PVE节点管理 ====================
    
    /**
     * 添加PVE节点
     */
    @PostMapping("/nodes")
    ApiResponse<PveNodeResponse> addNode(@RequestBody PveNodeRequest request);
    
    /**
     * 获取PVE节点
     */
    @GetMapping("/nodes/{id}")
    ApiResponse<PveNodeResponse> getNodeById(@PathVariable("id") Long id);
    
    /**
     * 获取可用节点列表
     */
    @GetMapping("/nodes/available")
    ApiResponse<List<PveNodeResponse>> getAvailableNodes();
    
    /**
     * 根据状态查询节点
     */
    @GetMapping("/nodes/status/{status}")
    ApiResponse<List<PveNodeResponse>> getNodesByStatus(@PathVariable("status") NodeStatus status);
    
    /**
     * 测试节点连接
     */
    @PostMapping("/nodes/{id}/test-connection")
    ApiResponse<Boolean> testNodeConnection(@PathVariable("id") Long id);
    
    /**
     * 获取节点统计信息
     */
    @GetMapping("/nodes/statistics")
    ApiResponse<Map<String, Object>> getNodeStatistics();
    
    // ==================== 虚拟机管理 ====================
    
    /**
     * 创建虚拟机
     */
    @PostMapping("/vms")
    ApiResponse<VirtualMachineResponse> createVirtualMachine(@RequestBody VirtualMachineRequest request);
    
    /**
     * 从模板创建虚拟机
     */
    @PostMapping("/vms/from-template/{templateId}")
    ApiResponse<VirtualMachineResponse> createFromTemplate(
        @PathVariable("templateId") Long templateId,
        @RequestBody VirtualMachineRequest request
    );
    
    /**
     * 启动虚拟机
     */
    @PostMapping("/vms/{id}/start")
    ApiResponse<VirtualMachineResponse> startVirtualMachine(@PathVariable("id") Long id);
    
    /**
     * 停止虚拟机
     */
    @PostMapping("/vms/{id}/stop")
    ApiResponse<VirtualMachineResponse> stopVirtualMachine(@PathVariable("id") Long id);
    
    /**
     * 重启虚拟机
     */
    @PostMapping("/vms/{id}/restart")
    ApiResponse<VirtualMachineResponse> restartVirtualMachine(@PathVariable("id") Long id);
    
    /**
     * 删除虚拟机
     */
    @DeleteMapping("/vms/{id}")
    ApiResponse<Void> deleteVirtualMachine(@PathVariable("id") Long id);
    
    /**
     * 获取虚拟机信息
     */
    @GetMapping("/vms/{id}")
    ApiResponse<VirtualMachineResponse> getVirtualMachineById(@PathVariable("id") Long id);
    
    /**
     * 查询用户的虚拟机
     */
    @GetMapping("/vms/my")
    ApiResponse<PageResponse<VirtualMachineResponse>> getUserVirtualMachines(
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "20") int size
    );
    
    /**
     * 根据状态查询用户的虚拟机
     */
    @GetMapping("/vms/my/status/{status}")
    ApiResponse<List<VirtualMachineResponse>> getUserVirtualMachinesByStatus(@PathVariable("status") VmStatus status);
    
    /**
     * 搜索虚拟机
     */
    @GetMapping("/vms/search")
    ApiResponse<PageResponse<VirtualMachineResponse>> searchVirtualMachinesByName(
        @RequestParam("name") String name,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "20") int size
    );
    
    /**
     * 批量启动虚拟机
     */
    @PostMapping("/vms/batch/start")
    ApiResponse<Map<Long, Boolean>> batchStartVirtualMachines(@RequestBody List<Long> vmIds);
    
    /**
     * 批量停止虚拟机
     */
    @PostMapping("/vms/batch/stop")
    ApiResponse<Map<Long, Boolean>> batchStopVirtualMachines(@RequestBody List<Long> vmIds);
    
    /**
     * 获取用户虚拟机统计
     */
    @GetMapping("/vms/my/statistics")
    ApiResponse<Map<String, Object>> getUserVmStatistics();
}
