# CloudVPS 平台部署和运维指南

## 部署环境要求

### 硬件要求

#### 最小配置 (开发/测试环境)
- **CPU**: 4 核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 100Mbps

#### 推荐配置 (生产环境)
- **CPU**: 8 核心以上
- **内存**: 16GB RAM 以上
- **存储**: 200GB SSD 以上
- **网络**: 1Gbps 以上

### 软件要求

#### 基础环境
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+) 或 macOS
- **Java**: OpenJDK 21 或 Oracle JDK 21
- **Maven**: 3.9.0 以上
- **Docker**: 20.10+ (容器化部署)
- **Kubernetes**: 1.25+ (K8s 部署)

#### 数据库和中间件
- **PostgreSQL**: 16.0 以上
- **Redis**: 7.2 以上
- **Nginx**: 1.20+ (反向代理，可选)

## 本地开发环境部署

### 1. 环境准备

#### 安装 JDK 21
```bash
# macOS (使用 Homebrew)
brew install openjdk@21

# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-21-jdk

# CentOS/RHEL
sudo dnf install java-21-openjdk-devel
```

#### 安装 PostgreSQL
```bash
# macOS
brew install postgresql@16
brew services start postgresql@16

# Ubuntu/Debian
sudo apt install postgresql-16 postgresql-client-16
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库用户
sudo -u postgres createuser --interactive cloudvps
sudo -u postgres createdb cloudvps_system -O cloudvps
sudo -u postgres createdb cloudvps_order -O cloudvps
sudo -u postgres createdb cloudvps_payment -O cloudvps
```

#### 安装 Redis
```bash
# macOS
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 验证安装
redis-cli ping
```

### 2. 项目构建

#### 克隆项目并构建
```bash
# 进入项目目录
cd cloudvps-platform

# 构建所有模块
mvn clean compile

# 运行测试 (可选)
mvn test

# 打包应用
mvn clean package -DskipTests
```

### 3. 配置文件设置

#### 数据库配置
```yaml
# application-dev.yml (各服务)
spring:
  datasource:
    url: ************************************************
    username: cloudvps
    password: your_password
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
```

#### Redis 配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0  # 不同服务使用不同数据库
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

### 4. 启动服务

#### 启动顺序
```bash
# 1. 启动系统服务 (必须首先启动，提供认证服务)
cd cloudvps-system/cloudvps-system-service
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 2. 启动虚拟化服务
cd cloudvps-virtualization/cloudvps-virtualization-service
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 3. 启动订单服务
cd cloudvps-order/cloudvps-order-service
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 4. 启动支付服务
cd cloudvps-payment/cloudvps-payment-service
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 5. 启动 API 网关 (最后启动)
cd cloudvps-gateway
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### 验证启动
```bash
# 检查所有服务健康状态
curl http://localhost:8080/actuator/health  # 网关
curl http://localhost:8081/actuator/health  # 系统服务
curl http://localhost:8082/actuator/health  # 虚拟化服务
curl http://localhost:8083/actuator/health  # 订单服务
curl http://localhost:8084/actuator/health  # 支付服务
```

## 生产环境部署

### 1. 容器化部署

#### 创建 Docker 镜像
```bash
# 为每个服务创建 Dockerfile
# 示例: cloudvps-gateway/Dockerfile
FROM openjdk:21-jdk-slim

WORKDIR /app
COPY target/cloudvps-gateway-1.0.0-SNAPSHOT.jar app.jar

EXPOSE 8080

ENV JAVA_OPTS="-Xms512m -Xmx1024m"
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]

# 构建镜像
docker build -t cloudvps-gateway:1.0.0 cloudvps-gateway/
docker build -t cloudvps-system:1.0.0 cloudvps-system/cloudvps-system-service/
docker build -t cloudvps-virtualization:1.0.0 cloudvps-virtualization/cloudvps-virtualization-service/
docker build -t cloudvps-order:1.0.0 cloudvps-order/cloudvps-order-service/
docker build -t cloudvps-payment:1.0.0 cloudvps-payment/cloudvps-payment-service/
```

#### Docker Compose 部署
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:16
    environment:
      POSTGRES_DB: cloudvps
      POSTGRES_USER: cloudvps
      POSTGRES_PASSWORD: cloudvps123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7.2
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  cloudvps-system:
    image: cloudvps-system:1.0.0
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_HOST: postgres
      REDIS_HOST: redis
    ports:
      - "8081:8081"
    depends_on:
      - postgres
      - redis

  cloudvps-virtualization:
    image: cloudvps-virtualization:1.0.0
    environment:
      SPRING_PROFILES_ACTIVE: prod
      REDIS_HOST: redis
    ports:
      - "8082:8082"
    depends_on:
      - redis

  cloudvps-order:
    image: cloudvps-order:1.0.0
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_HOST: postgres
      REDIS_HOST: redis
    ports:
      - "8083:8083"
    depends_on:
      - postgres
      - redis

  cloudvps-payment:
    image: cloudvps-payment:1.0.0
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_HOST: postgres
      REDIS_HOST: redis
    ports:
      - "8084:8084"
    depends_on:
      - postgres
      - redis

  cloudvps-gateway:
    image: cloudvps-gateway:1.0.0
    environment:
      SPRING_PROFILES_ACTIVE: prod
      REDIS_HOST: redis
    ports:
      - "8080:8080"
    depends_on:
      - cloudvps-system
      - cloudvps-virtualization
      - cloudvps-order
      - cloudvps-payment

volumes:
  postgres_data:
  redis_data:
```

#### 启动容器化部署
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f cloudvps-gateway
```

### 2. Kubernetes 部署

#### 命名空间和配置
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: cloudvps

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cloudvps-config
  namespace: cloudvps
data:
  postgres.host: "postgres-service"
  redis.host: "redis-service"
  spring.profiles.active: "prod"
```

#### 数据库部署
```yaml
# postgres-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: cloudvps
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:16
        env:
        - name: POSTGRES_DB
          value: "cloudvps"
        - name: POSTGRES_USER
          value: "cloudvps"
        - name: POSTGRES_PASSWORD
          value: "cloudvps123"
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: cloudvps
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
```

#### 应用服务部署
```yaml
# gateway-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloudvps-gateway
  namespace: cloudvps
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cloudvps-gateway
  template:
    metadata:
      labels:
        app: cloudvps-gateway
    spec:
      containers:
      - name: gateway
        image: cloudvps-gateway:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          valueFrom:
            configMapKeyRef:
              name: cloudvps-config
              key: spring.profiles.active
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: cloudvps-config
              key: redis.host
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: cloudvps-gateway-service
  namespace: cloudvps
spec:
  selector:
    app: cloudvps-gateway
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

#### 部署到 Kubernetes
```bash
# 应用配置
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f postgres-deployment.yaml
kubectl apply -f redis-deployment.yaml

# 部署应用服务
kubectl apply -f system-deployment.yaml
kubectl apply -f virtualization-deployment.yaml
kubectl apply -f order-deployment.yaml
kubectl apply -f payment-deployment.yaml
kubectl apply -f gateway-deployment.yaml

# 查看部署状态
kubectl get pods -n cloudvps
kubectl get services -n cloudvps
```

## 监控和运维

### 1. 健康检查

#### 自动化健康检查脚本
```bash
#!/bin/bash
# health-check.sh

SERVICES=(
    "http://localhost:8080/actuator/health:Gateway"
    "http://localhost:8081/actuator/health:System"
    "http://localhost:8082/actuator/health:Virtualization"
    "http://localhost:8083/actuator/health:Order"
    "http://localhost:8084/actuator/health:Payment"
)

for service in "${SERVICES[@]}"; do
    url=$(echo $service | cut -d: -f1-3)
    name=$(echo $service | cut -d: -f4)
    
    status=$(curl -s -o /dev/null -w "%{http_code}" $url)
    if [ $status -eq 200 ]; then
        echo "✅ $name Service: Healthy"
    else
        echo "❌ $name Service: Unhealthy (HTTP $status)"
    fi
done
```

### 2. 日志管理

#### 日志配置
```yaml
# logback-spring.xml
<configuration>
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/cloudvps.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/cloudvps.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

#### 日志收集 (ELK Stack)
```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /app/logs/*.log
  fields:
    service: cloudvps
    environment: production

output.elasticsearch:
  hosts: ["elasticsearch:9200"]

setup.kibana:
  host: "kibana:5601"
```

### 3. 性能监控

#### Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'cloudvps-services'
    static_configs:
      - targets: 
        - 'localhost:8080'  # Gateway
        - 'localhost:8081'  # System
        - 'localhost:8082'  # Virtualization
        - 'localhost:8083'  # Order
        - 'localhost:8084'  # Payment
    metrics_path: '/actuator/prometheus'
```

#### Grafana 仪表板
- **系统指标**: CPU、内存、磁盘使用率
- **应用指标**: 请求量、响应时间、错误率
- **业务指标**: 用户注册量、订单量、支付成功率

### 4. 备份策略

#### 数据库备份
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# PostgreSQL 备份
pg_dump -h localhost -U cloudvps cloudvps_system > $BACKUP_DIR/system.sql
pg_dump -h localhost -U cloudvps cloudvps_order > $BACKUP_DIR/order.sql
pg_dump -h localhost -U cloudvps cloudvps_payment > $BACKUP_DIR/payment.sql

# Redis 备份
redis-cli --rdb $BACKUP_DIR/redis.rdb

# 压缩备份文件
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR

echo "Backup completed: $BACKUP_DIR.tar.gz"
```

### 5. 故障处理

#### 常见问题排查
1. **服务无法启动**
   - 检查端口占用: `netstat -tulpn | grep :8080`
   - 检查日志: `tail -f logs/cloudvps.log`
   - 检查配置: 验证数据库连接配置

2. **数据库连接失败**
   - 检查数据库服务: `systemctl status postgresql`
   - 检查连接参数: 用户名、密码、数据库名
   - 检查网络连通性: `telnet localhost 5432`

3. **Redis 连接失败**
   - 检查 Redis 服务: `systemctl status redis`
   - 检查连接: `redis-cli ping`
   - 检查配置: Redis 主机和端口

4. **网关路由失败**
   - 检查下游服务状态
   - 检查路由配置
   - 检查网络连通性

#### 应急处理流程
1. **服务降级**: 启用熔断器，返回默认响应
2. **流量切换**: 通过负载均衡器切换流量
3. **快速恢复**: 重启服务或回滚版本
4. **问题定位**: 分析日志，定位根本原因

## 安全配置

### 1. 网络安全
- **防火墙**: 只开放必要端口
- **SSL/TLS**: 使用 HTTPS 加密通信
- **VPN**: 管理访问通过 VPN

### 2. 应用安全
- **JWT 密钥**: 使用强密钥，定期轮换
- **数据库**: 使用专用用户，最小权限原则
- **敏感信息**: 使用环境变量或密钥管理系统

### 3. 访问控制
- **API 限流**: 防止 DDoS 攻击
- **权限控制**: 严格的 RBAC 权限模型
- **审计日志**: 记录所有关键操作

## 扩展和升级

### 1. 水平扩展
```bash
# 增加服务实例
docker-compose up --scale cloudvps-gateway=3
docker-compose up --scale cloudvps-system=2

# Kubernetes 扩展
kubectl scale deployment cloudvps-gateway --replicas=5 -n cloudvps
```

### 2. 版本升级
```bash
# 滚动升级
kubectl set image deployment/cloudvps-gateway gateway=cloudvps-gateway:1.1.0 -n cloudvps
kubectl rollout status deployment/cloudvps-gateway -n cloudvps

# 回滚版本
kubectl rollout undo deployment/cloudvps-gateway -n cloudvps
```

### 3. 数据库迁移
```bash
# 使用 Flyway 进行数据库版本管理
mvn flyway:migrate -Dflyway.url=************************************************
```
