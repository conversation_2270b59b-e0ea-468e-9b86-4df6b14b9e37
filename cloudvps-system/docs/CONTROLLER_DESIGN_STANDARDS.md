# CloudVPS Controller接口设计规范

## 1. 用户身份获取规范

### 1.1 必须使用SecurityUtils工具类
需要获取当前用户信息的接口，必须使用`common-security`模块的`SecurityUtils.getRequiredCurrentUserId()`工具类，避免通过Authentication参数查询数据库的低效做法。

**正确示例：**
```java
@GetMapping("/profile")
@Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
@PreAuthorize("hasAuthority('USER_VIEW')")
public ApiResponse<UserResponse> getProfile() {
    Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
    UserResponse userResponse = userService.findById(currentUserId);
    return ApiResponse.success(userResponse);
}
```

**错误示例：**
```java
// 错误：手动从HttpServletRequest提取token
public ApiResponse<UserResponse> getProfile(HttpServletRequest request) {
    String token = extractTokenFromRequest(request);
    User user = authService.validateToken(token);
    // ...
}

// 错误：通过Authentication参数查询数据库
public ApiResponse<UserResponse> getProfile(Authentication authentication) {
    String username = authentication.getName();
    User user = userService.findByUsername(username); // 数据库查询
    // ...
}
```

### 1.2 SecurityUtils工具类方法说明
- `getCurrentUserId()`: 获取当前用户ID，可能返回null
- `getRequiredCurrentUserId()`: 获取当前用户ID，如果未登录则抛出异常
- `getCurrentUsername()`: 获取当前用户名
- `isCurrentUserAdmin()`: 检查当前用户是否为管理员

## 2. 查询接口参数规范

### 2.1 分页查询参数
分页查询必须继承`common-core`模块的`PageRequest`类作为查询参数对象：

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class UserQueryRequest extends PageRequest {
    private String username;
    private String email;
    private UserStatus status;
    // 其他查询条件...
}
```

### 2.2 非分页查询参数
- **参数超过2个或预期未来会增加参数**：使用专门的查询请求对象
- **参数少且固定**：可直接使用方法参数

**查询对象示例：**
```java
@Data
public class UserSearchRequest {
    private String keyword;
    private UserStatus status;
    private String department;
}

@GetMapping("/search")
@PreAuthorize("hasAuthority('USER_VIEW')")
public ApiResponse<List<UserResponse>> searchUsers(@Valid UserSearchRequest request) {
    // ...
}
```

**直接参数示例：**
```java
@GetMapping("/by-email")
@PreAuthorize("hasAuthority('USER_VIEW')")
public ApiResponse<UserResponse> getUserByEmail(@RequestParam String email) {
    // ...
}
```

### 2.3 参数验证
所有查询对象需要添加`@Valid`注解进行参数验证：

```java
@GetMapping
@PreAuthorize("hasAuthority('USER_VIEW')")
public ApiResponse<IPage<UserResponse>> getUsers(@Valid UserQueryRequest request) {
    // ...
}
```

## 3. 接口设计原则

### 3.1 RESTful设计原则
- 使用标准HTTP方法：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 资源路径设计：`/system/users`、`/system/roles`、`/system/menus`
- 避免动词形式的路径，使用名词复数形式

### 3.2 权限控制规范
所有需要权限控制的接口必须添加`@PreAuthorize`注解：

```java
@PreAuthorize("hasAuthority('USER_VIEW')")    // 查看权限
@PreAuthorize("hasAuthority('USER_CREATE')")  // 创建权限
@PreAuthorize("hasAuthority('USER_UPDATE')")  // 更新权限
@PreAuthorize("hasAuthority('USER_DELETE')")  // 删除权限
```

### 3.3 响应格式规范
所有接口必须使用`ApiResponse`包装响应数据：

```java
// 成功响应
return ApiResponse.success(data);
return ApiResponse.success("操作成功", data);

// 错误响应
return ApiResponse.error(400, "参数错误");
```

### 3.4 接口文档规范
所有接口必须添加Swagger注解：

```java
@Operation(summary = "接口简要描述", description = "接口详细描述")
@Parameter(description = "参数描述")
@Tag(name = "模块名称", description = "模块描述")
```

## 4. 标准CRUD接口模板

### 4.1 查询接口
```java
// 分页查询
@GetMapping
@Operation(summary = "分页查询用户", description = "分页查询用户列表")
@PreAuthorize("hasAuthority('USER_VIEW')")
public ApiResponse<IPage<UserResponse>> getUsers(@Valid UserQueryRequest request) {
    IPage<UserResponse> users = userService.queryUsers(request);
    return ApiResponse.success(users);
}

// 详情查询
@GetMapping("/{id}")
@Operation(summary = "获取用户详情", description = "根据用户ID获取用户详细信息")
@PreAuthorize("hasAuthority('USER_VIEW')")
public ApiResponse<UserResponse> getUserById(@PathVariable Long id) {
    UserResponse user = userService.findById(id);
    return ApiResponse.success(user);
}
```

### 4.2 创建接口
```java
@PostMapping
@Operation(summary = "创建用户", description = "创建新用户")
@PreAuthorize("hasAuthority('USER_CREATE')")
public ApiResponse<UserResponse> createUser(@Valid @RequestBody UserCreateRequest request) {
    UserResponse user = userService.createUser(request);
    return ApiResponse.success("创建成功", user);
}
```

### 4.3 更新接口
```java
@PutMapping("/{id}")
@Operation(summary = "更新用户", description = "更新用户信息")
@PreAuthorize("hasAuthority('USER_UPDATE')")
public ApiResponse<UserResponse> updateUser(
        @PathVariable Long id, 
        @Valid @RequestBody UserUpdateRequest request) {
    UserResponse user = userService.updateUser(id, request);
    return ApiResponse.success("更新成功", user);
}
```

### 4.4 删除接口
```java
@DeleteMapping("/{id}")
@Operation(summary = "删除用户", description = "删除指定用户")
@PreAuthorize("hasAuthority('USER_DELETE')")
public ApiResponse<Void> deleteUser(@PathVariable Long id) {
    userService.deleteUser(id);
    return ApiResponse.success("删除成功");
}
```

## 5. 特殊接口规范

### 5.1 当前用户相关接口
```java
@GetMapping("/profile")
@Operation(summary = "获取个人信息", description = "获取当前登录用户的个人信息")
@PreAuthorize("isAuthenticated()")
public ApiResponse<UserResponse> getProfile() {
    Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
    UserResponse user = userService.findById(currentUserId);
    return ApiResponse.success(user);
}

@PutMapping("/profile")
@Operation(summary = "更新个人信息", description = "更新当前登录用户的个人信息")
@PreAuthorize("isAuthenticated()")
public ApiResponse<UserResponse> updateProfile(@Valid @RequestBody UserProfileUpdateRequest request) {
    Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
    UserResponse user = userService.updateProfile(currentUserId, request);
    return ApiResponse.success("更新成功", user);
}
```

### 5.2 批量操作接口
```java
@PostMapping("/batch-delete")
@Operation(summary = "批量删除用户", description = "批量删除指定用户")
@PreAuthorize("hasAuthority('USER_DELETE')")
public ApiResponse<Void> batchDeleteUsers(@RequestBody List<Long> userIds) {
    userService.batchDeleteUsers(userIds);
    return ApiResponse.success("批量删除成功");
}
```

## 6. 错误处理规范

### 6.1 参数验证错误
使用`@Valid`注解自动处理参数验证，全局异常处理器会自动返回标准错误响应。

### 6.2 业务逻辑错误
在Service层抛出`BusinessException`，Controller层不需要特殊处理：

```java
// Service层
if (user == null) {
    throw new BusinessException("用户不存在");
}
```

### 6.3 权限错误
使用`@PreAuthorize`注解自动处理权限验证，无权限时会自动返回403错误。

## 7. 日志记录规范

### 7.1 操作日志
重要操作需要记录日志：

```java
@PostMapping
@PreAuthorize("hasAuthority('USER_CREATE')")
public ApiResponse<UserResponse> createUser(@Valid @RequestBody UserCreateRequest request) {
    log.info("创建用户请求: username={}", request.getUsername());
    UserResponse user = userService.createUser(request);
    log.info("用户创建成功: userId={}", user.getId());
    return ApiResponse.success("创建成功", user);
}
```

### 7.2 调试日志
查询操作使用debug级别：

```java
@GetMapping("/{id}")
@PreAuthorize("hasAuthority('USER_VIEW')")
public ApiResponse<UserResponse> getUserById(@PathVariable Long id) {
    log.debug("获取用户详情: userId={}", id);
    UserResponse user = userService.findById(id);
    return ApiResponse.success(user);
}
```
