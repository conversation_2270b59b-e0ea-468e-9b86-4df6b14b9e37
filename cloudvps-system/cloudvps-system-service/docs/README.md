# CloudVPS 系统服务模块

## 模块概述

系统服务是 CloudVPS 平台的核心基础服务，负责用户管理、身份认证、权限控制、系统配置等基础功能。作为平台的认证中心，为其他所有微服务提供统一的用户身份验证和权限管理服务。

## 技术架构

### 技术栈
- **框架**: Spring Boot 3.2.1
- **Java 版本**: JDK 21
- **数据库**: PostgreSQL 16+
- **缓存**: Redis 7.2+
- **安全**: Spring Security + JWT
- **ORM**: Spring Data JPA + Hibernate
- **API 文档**: Swagger/OpenAPI 3
- **构建工具**: Maven 3.9+

### 架构设计模式
```
┌─────────────────────────────────────────┐
│              Controller 层               │
│  AuthController │ UserController │ ...  │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│               Service 层                │
│   AuthService │ UserService │ ...       │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│             Repository 层               │
│ UserRepository │ RoleRepository │ ...   │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│              Database 层                │
│           PostgreSQL 数据库             │
└─────────────────────────────────────────┘
```

## 核心功能模块

### 1. 用户管理模块
- **用户注册**: 支持用户自主注册，邮箱验证
- **用户信息管理**: 个人资料维护，头像上传
- **用户状态管理**: 激活、冻结、删除用户
- **用户查询**: 分页查询、条件筛选

### 2. 身份认证模块
- **登录认证**: 用户名密码登录，JWT 令牌生成
- **令牌管理**: 令牌刷新、令牌撤销
- **登录日志**: 记录登录历史，异常登录检测
- **会话管理**: Redis 会话存储，单点登录支持

### 3. 权限控制模块 (RBAC)
- **角色管理**: 角色创建、编辑、删除
- **权限管理**: 细粒度权限定义
- **用户角色关联**: 用户角色分配和撤销
- **权限验证**: 接口级权限控制

### 4. 账户管理模块
- **账户信息**: 用户财务账户管理
- **余额管理**: 充值、扣费、冻结、解冻
- **交易记录**: 账户变动历史记录
- **账户安全**: 资金安全控制

### 5. 系统配置模块
- **动态配置**: 系统参数动态调整
- **配置分类**: 按业务模块分类管理
- **配置缓存**: Redis 缓存提升性能
- **配置历史**: 配置变更历史记录

### 6. 字典管理模块
- **数据字典**: 系统枚举值管理
- **字典分类**: 按类型组织字典数据
- **字典缓存**: 高频访问数据缓存
- **多语言支持**: 国际化字典支持

## 数据库设计

### 核心数据表

#### 用户相关表
```sql
-- 用户基本信息表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    nickname VARCHAR(100),
    phone VARCHAR(20),
    avatar VARCHAR(500),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    last_login_time TIMESTAMP,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 用户账户表
CREATE TABLE user_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    balance DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    frozen_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_recharge DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_consumption DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 权限相关表
```sql
-- 角色表
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    role_code VARCHAR(50) NOT NULL UNIQUE,
    role_name VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE permissions (
    id BIGSERIAL PRIMARY KEY,
    permission_code VARCHAR(100) NOT NULL UNIQUE,
    permission_name VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    action VARCHAR(50),
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE'
);

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    role_id BIGINT NOT NULL REFERENCES roles(id),
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL REFERENCES roles(id),
    permission_id BIGINT NOT NULL REFERENCES permissions(id),
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, permission_id)
);
```

### 数据库索引优化
```sql
-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_time ON users(created_time);

-- 账户表索引
CREATE UNIQUE INDEX idx_user_accounts_user_id ON user_accounts(user_id);
CREATE INDEX idx_user_accounts_status ON user_accounts(status);

-- 权限相关索引
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
```

## API 接口设计

### 认证接口
```java
@RestController
@RequestMapping("/system/auth")
public class AuthController {
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@RequestBody LoginRequest request) {
        // 实现用户登录逻辑
    }
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ApiResponse<UserResponse> register(@RequestBody RegisterRequest request) {
        // 实现用户注册逻辑
    }
    
    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public ApiResponse<TokenResponse> refreshToken(@RequestBody RefreshTokenRequest request) {
        // 实现令牌刷新逻辑
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout(HttpServletRequest request) {
        // 实现用户登出逻辑
    }
}
```

### 用户管理接口
```java
@RestController
@RequestMapping("/system/users")
@PreAuthorize("hasAuthority('SYSTEM_USER_VIEW')")
public class UserController {
    
    /**
     * 获取用户列表
     */
    @GetMapping
    public ApiResponse<PageResponse<UserResponse>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String email) {
        // 实现用户列表查询
    }
    
    /**
     * 获取用户详情
     */
    @GetMapping("/{id}")
    public ApiResponse<UserResponse> getUser(@PathVariable Long id) {
        // 实现用户详情查询
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('SYSTEM_USER_UPDATE')")
    public ApiResponse<UserResponse> updateUser(
            @PathVariable Long id, 
            @RequestBody UpdateUserRequest request) {
        // 实现用户信息更新
    }
}
```

## 安全设计

### JWT 令牌设计
```java
@Component
public class JwtTokenProvider {
    
    private final String jwtSecret;
    private final long jwtExpiration;
    
    /**
     * 生成 JWT 令牌
     */
    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("username", userDetails.getUsername());
        claims.put("authorities", userDetails.getAuthorities());
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + jwtExpiration))
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }
    
    /**
     * 验证 JWT 令牌
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            return false;
        }
    }
}
```

### 权限控制实现
```java
@Service
public class PermissionService {
    
    /**
     * 检查用户是否具有指定权限
     */
    public boolean hasPermission(Long userId, String permissionCode) {
        // 从缓存或数据库查询用户权限
        Set<String> userPermissions = getUserPermissions(userId);
        return userPermissions.contains(permissionCode);
    }
    
    /**
     * 获取用户所有权限
     */
    @Cacheable(value = "user_permissions", key = "#userId")
    public Set<String> getUserPermissions(Long userId) {
        // 通过用户角色查询权限
        return userRepository.findUserPermissions(userId);
    }
}
```

## 缓存策略

### Redis 缓存设计
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .build();
    }
}
```

### 缓存使用示例
```java
@Service
public class UserService {
    
    /**
     * 缓存用户信息
     */
    @Cacheable(value = "users", key = "#id")
    public UserResponse getUserById(Long id) {
        // 从数据库查询用户信息
    }
    
    /**
     * 更新时清除缓存
     */
    @CacheEvict(value = "users", key = "#id")
    public UserResponse updateUser(Long id, UpdateUserRequest request) {
        // 更新用户信息
    }
    
    /**
     * 缓存用户权限
     */
    @Cacheable(value = "user_permissions", key = "#userId", unless = "#result.isEmpty()")
    public Set<String> getUserPermissions(Long userId) {
        // 查询用户权限
    }
}
```

## 配置管理

### 应用配置
```yaml
# application.yml
spring:
  application:
    name: cloudvps-system-service
  
  datasource:
    url: ************************************************
    username: ${DB_USERNAME:cloudvps}
    password: ${DB_PASSWORD:cloudvps123}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: ${DDL_AUTO:update}
    show-sql: ${SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DB:0}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# JWT 配置
cloudvps:
  jwt:
    secret: ${JWT_SECRET:cloudvps-system-jwt-secret-key}
    expiration: ${JWT_EXPIRATION:86400000}  # 24小时
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000}  # 7天

# 服务配置
server:
  port: 8081
  servlet:
    context-path: /

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

## 测试策略

### 单元测试
```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
class UserServiceTest {
    
    @Autowired
    private UserService userService;
    
    @MockBean
    private UserRepository userRepository;
    
    @Test
    @DisplayName("应该成功创建用户")
    void shouldCreateUserSuccessfully() {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        
        when(userRepository.save(any(User.class))).thenReturn(user);
        
        // When
        UserResponse response = userService.createUser(request);
        
        // Then
        assertThat(response.getId()).isEqualTo(1L);
        assertThat(response.getUsername()).isEqualTo("testuser");
    }
}
```

### 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Testcontainers
class AuthControllerIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16")
            .withDatabaseName("test")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    @DisplayName("用户登录集成测试")
    void testUserLogin() {
        // Given
        LoginRequest request = new LoginRequest();
        request.setUsername("admin");
        request.setPassword("admin123");
        
        // When
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
                "/system/auth/login", request, ApiResponse.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
    }
}
```

## 部署配置

### Docker 配置
```dockerfile
FROM openjdk:21-jdk-slim

WORKDIR /app

COPY target/cloudvps-system-service-1.0.0-SNAPSHOT.jar app.jar

EXPOSE 8081

ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

### 环境变量配置
```bash
# 开发环境
export SPRING_PROFILES_ACTIVE=dev
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=cloudvps_system
export REDIS_HOST=localhost
export REDIS_PORT=6379

# 生产环境
export SPRING_PROFILES_ACTIVE=prod
export DB_HOST=postgres-cluster
export DB_PORT=5432
export DB_NAME=cloudvps_system
export REDIS_HOST=redis-cluster
export REDIS_PORT=6379
export JWT_SECRET=production-jwt-secret-key
```

## 监控和运维

### 健康检查
- **数据库连接**: PostgreSQL 连接状态
- **缓存连接**: Redis 连接状态
- **磁盘空间**: 应用磁盘使用情况
- **内存使用**: JVM 内存使用情况

### 关键指标
- **用户注册量**: 每日新增用户数
- **登录成功率**: 登录成功/总登录次数
- **API 响应时间**: 各接口平均响应时间
- **错误率**: 4xx/5xx 错误比例

### 日志配置
```yaml
logging:
  level:
    com.cloudvps.system: DEBUG
    org.springframework.web: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/cloudvps-system.log
```

## 未来扩展计划

### 功能扩展
- **多因子认证**: 短信验证码、邮箱验证
- **社交登录**: 微信、QQ、GitHub 登录
- **用户画像**: 用户行为分析和标签
- **审计增强**: 详细的操作审计日志

### 技术优化
- **读写分离**: 数据库读写分离优化
- **分库分表**: 大数据量时的数据分片
- **缓存优化**: 多级缓存策略
- **性能优化**: 接口性能调优

### 集成扩展
- **消息队列**: 异步事件处理
- **搜索引擎**: Elasticsearch 用户搜索
- **文件存储**: OSS 文件上传下载
- **监控告警**: 完善的监控告警体系
