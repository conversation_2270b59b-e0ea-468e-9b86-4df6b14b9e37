# CloudVPS 菜单管理功能测试指南

## 测试环境准备

### 1. 数据库初始化

```bash
# 1. 创建菜单表
psql -h localhost -U cloudvps -d cloudvps_system -f sql/menu_tables.sql

# 2. 插入测试数据
psql -h localhost -U cloudvps -d cloudvps_system -f sql/menu_test_data.sql
```

### 2. 服务启动

```bash
# 启动系统服务
cd cloudvps-system/cloudvps-system-service
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 3. 获取认证Token

```bash
# 登录获取Token
curl -X POST http://localhost:8081/system/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'

# 响应示例
{
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "user": {...}
  }
}
```

## 功能测试用例

### 测试用例1：菜单基础CRUD操作

#### 1.1 创建目录菜单
```bash
curl -X POST http://localhost:8081/system/menu \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-directory",
    "title": "测试目录",
    "icon": "folder",
    "path": "/test",
    "type": "DIRECTORY",
    "sort": 99,
    "visible": true,
    "enabled": true,
    "permission": "TEST_DIRECTORY",
    "description": "测试用目录"
  }'
```

**预期结果**: 返回201状态码，包含创建的菜单信息

#### 1.2 创建页面菜单
```bash
curl -X POST http://localhost:8081/system/menu \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-page",
    "title": "测试页面",
    "icon": "file",
    "path": "/test/page",
    "component": "test/Page",
    "type": "MENU",
    "parentId": {directory_id},
    "sort": 1,
    "visible": true,
    "enabled": true,
    "permission": "TEST_PAGE_VIEW",
    "description": "测试页面"
  }'
```

#### 1.3 创建按钮权限
```bash
curl -X POST http://localhost:8081/system/menu \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-page-create",
    "title": "新增按钮",
    "type": "BUTTON",
    "parentId": {page_id},
    "sort": 1,
    "visible": false,
    "enabled": true,
    "permission": "TEST_PAGE_CREATE",
    "description": "新增按钮权限"
  }'
```

#### 1.4 查询菜单
```bash
# 查询单个菜单
curl -X GET http://localhost:8081/system/menu/{id} \
  -H "Authorization: Bearer {token}"

# 查询菜单树
curl -X GET http://localhost:8081/system/menu/tree \
  -H "Authorization: Bearer {token}"

# 查询菜单列表
curl -X GET http://localhost:8081/system/menu/list \
  -H "Authorization: Bearer {token}"
```

#### 1.5 更新菜单
```bash
curl -X PUT http://localhost:8081/system/menu/{id} \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的标题",
    "description": "更新后的描述",
    "sort": 2
  }'
```

#### 1.6 删除菜单
```bash
# 删除单个菜单（先删除子菜单）
curl -X DELETE http://localhost:8081/system/menu/{button_id} \
  -H "Authorization: Bearer {token}"

curl -X DELETE http://localhost:8081/system/menu/{page_id} \
  -H "Authorization: Bearer {token}"

curl -X DELETE http://localhost:8081/system/menu/{directory_id} \
  -H "Authorization: Bearer {token}"
```

### 测试用例2：菜单层级结构测试

#### 2.1 查询子菜单
```bash
curl -X GET http://localhost:8081/system/menu/children/{parent_id} \
  -H "Authorization: Bearer {token}"
```

#### 2.2 移动菜单
```bash
curl -X PUT http://localhost:8081/system/menu/{id}/move \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "parentId": {new_parent_id}
  }'
```

#### 2.3 调整排序
```bash
curl -X PUT http://localhost:8081/system/menu/{id}/sort \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "sort": 5
  }'
```

### 测试用例3：用户菜单权限测试

#### 3.1 获取用户菜单树
```bash
# 获取当前用户菜单树
curl -X GET http://localhost:8081/system/menu/user/tree \
  -H "Authorization: Bearer {token}"

# 获取指定用户菜单树
curl -X GET http://localhost:8081/system/menu/user/{userId}/tree \
  -H "Authorization: Bearer {token}"
```

#### 3.2 获取用户权限列表
```bash
curl -X GET http://localhost:8081/system/menu/user/permissions \
  -H "Authorization: Bearer {token}"
```

#### 3.3 检查用户权限
```bash
curl -X GET http://localhost:8081/system/menu/user/permission/SYSTEM_USER_VIEW \
  -H "Authorization: Bearer {token}"
```

### 测试用例4：角色菜单管理测试

#### 4.1 查询角色菜单
```bash
curl -X GET http://localhost:8081/system/menu/role/{roleId} \
  -H "Authorization: Bearer {token}"
```

#### 4.2 分配角色菜单权限
```bash
curl -X POST http://localhost:8081/system/menu/role/{roleId}/assign \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '[1, 2, 11, 21, 22, 23]'
```

#### 4.3 移除角色菜单权限
```bash
curl -X POST http://localhost:8081/system/menu/role/{roleId}/remove \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '[23]'
```

### 测试用例5：菜单状态管理测试

#### 5.1 启用/禁用菜单
```bash
# 禁用菜单
curl -X PUT http://localhost:8081/system/menu/{id}/status \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": false
  }'

# 启用菜单
curl -X PUT http://localhost:8081/system/menu/{id}/status \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true
  }'
```

#### 5.2 显示/隐藏菜单
```bash
# 隐藏菜单
curl -X PUT http://localhost:8081/system/menu/{id}/visibility \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "visible": false
  }'
```

### 测试用例6：数据验证测试

#### 6.1 重复名称验证
```bash
# 检查菜单名称是否存在
curl -X GET http://localhost:8081/system/menu/check/name/system-user \
  -H "Authorization: Bearer {token}"

# 尝试创建重复名称的菜单（应该失败）
curl -X POST http://localhost:8081/system/menu \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "system-user",
    "title": "重复菜单",
    "type": "MENU"
  }'
```

#### 6.2 权限标识验证
```bash
# 检查权限标识是否存在
curl -X GET http://localhost:8081/system/menu/check/permission/SYSTEM_USER_VIEW \
  -H "Authorization: Bearer {token}"
```

#### 6.3 子菜单检查
```bash
# 检查菜单是否有子菜单
curl -X GET http://localhost:8081/system/menu/{id}/has-children \
  -H "Authorization: Bearer {token}"
```

### 测试用例7：错误场景测试

#### 7.1 无权限访问
```bash
# 使用无权限的token访问（应该返回403）
curl -X POST http://localhost:8081/system/menu \
  -H "Authorization: Bearer {invalid_token}" \
  -H "Content-Type: application/json" \
  -d '{...}'
```

#### 7.2 删除有子菜单的菜单
```bash
# 尝试删除有子菜单的菜单（应该失败）
curl -X DELETE http://localhost:8081/system/menu/2 \
  -H "Authorization: Bearer {token}"
```

#### 7.3 循环移动菜单
```bash
# 尝试将菜单移动到自己的子菜单下（应该失败）
curl -X PUT http://localhost:8081/system/menu/2/move \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "parentId": 21
  }'
```

## 自动化测试脚本

### 完整测试脚本示例

```bash
#!/bin/bash

# 配置
BASE_URL="http://localhost:8081"
TOKEN=""

# 获取Token
echo "=== 获取认证Token ==="
response=$(curl -s -X POST $BASE_URL/system/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

TOKEN=$(echo $response | jq -r '.data.token')
echo "Token: $TOKEN"

# 测试创建菜单
echo "=== 测试创建菜单 ==="
create_response=$(curl -s -X POST $BASE_URL/system/menu \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-menu",
    "title": "测试菜单",
    "type": "MENU",
    "sort": 99
  }')

menu_id=$(echo $create_response | jq -r '.data.id')
echo "Created menu ID: $menu_id"

# 测试查询菜单
echo "=== 测试查询菜单 ==="
curl -s -X GET $BASE_URL/system/menu/$menu_id \
  -H "Authorization: Bearer $TOKEN" | jq

# 测试更新菜单
echo "=== 测试更新菜单 ==="
curl -s -X PUT $BASE_URL/system/menu/$menu_id \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的测试菜单"
  }' | jq

# 测试删除菜单
echo "=== 测试删除菜单 ==="
curl -s -X DELETE $BASE_URL/system/menu/$menu_id \
  -H "Authorization: Bearer $TOKEN" | jq

echo "=== 测试完成 ==="
```

## 性能测试

### 并发测试
```bash
# 使用 Apache Bench 进行并发测试
ab -n 1000 -c 10 -H "Authorization: Bearer {token}" \
  http://localhost:8081/system/menu/tree
```

### 大数据量测试
```bash
# 创建大量菜单数据进行测试
for i in {1..1000}; do
  curl -X POST http://localhost:8081/system/menu \
    -H "Authorization: Bearer {token}" \
    -H "Content-Type: application/json" \
    -d "{
      \"name\": \"test-menu-$i\",
      \"title\": \"测试菜单$i\",
      \"type\": \"MENU\",
      \"sort\": $i
    }"
done
```

## 测试检查清单

- [ ] 菜单创建功能正常
- [ ] 菜单更新功能正常
- [ ] 菜单删除功能正常
- [ ] 菜单查询功能正常
- [ ] 菜单树构建正确
- [ ] 用户菜单权限过滤正确
- [ ] 角色菜单分配功能正常
- [ ] 菜单排序功能正常
- [ ] 菜单移动功能正常
- [ ] 菜单状态切换功能正常
- [ ] 数据验证规则生效
- [ ] 权限控制正常
- [ ] 错误处理正确
- [ ] 性能表现良好
