# CloudVPS 系统服务 - 菜单管理 API 文档

## 概述

菜单管理功能提供完整的系统菜单管理能力，包括菜单的增删改查、层级结构管理、权限控制等功能。

## 基础信息

- **服务名称**: cloudvps-system-service
- **服务端口**: 8081
- **API 前缀**: `/system/menu`
- **认证方式**: JWT Token
- **权限控制**: 基于 Spring Security 的 `@PreAuthorize` 注解

## 菜单类型

| 类型 | 代码 | 描述 | 特点 |
|------|------|------|------|
| 目录 | DIRECTORY | 包含子菜单的目录 | 可以有子菜单，不对应具体页面 |
| 菜单 | MENU | 具体的页面菜单 | 对应具体页面，需要设置路径 |
| 按钮 | BUTTON | 页面内的操作按钮 | 不可见，仅用于权限控制 |

## API 接口列表

### 1. 菜单基础管理

#### 1.1 创建菜单
```http
POST /system/menu
Authorization: Bearer {token}
Content-Type: application/json
```

**权限要求**: `SYSTEM_MENU_CREATE`

**请求体**:
```json
{
  "name": "system-user",
  "title": "用户管理",
  "icon": "user",
  "path": "/system/user",
  "component": "system/User",
  "type": "MENU",
  "parentId": 2,
  "sort": 1,
  "visible": true,
  "enabled": true,
  "permission": "SYSTEM_USER_VIEW",
  "description": "用户管理页面",
  "cache": false,
  "external": false
}
```

**响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 21,
    "name": "system-user",
    "title": "用户管理",
    "icon": "user",
    "path": "/system/user",
    "component": "system/User",
    "type": "MENU",
    "parentId": 2,
    "sort": 1,
    "visible": true,
    "enabled": true,
    "permission": "SYSTEM_USER_VIEW",
    "description": "用户管理页面",
    "cache": false,
    "external": false,
    "createTime": "2024-01-01T10:00:00",
    "updateTime": "2024-01-01T10:00:00"
  }
}
```

#### 1.2 更新菜单
```http
PUT /system/menu/{id}
Authorization: Bearer {token}
Content-Type: application/json
```

**权限要求**: `SYSTEM_MENU_UPDATE`

#### 1.3 删除菜单
```http
DELETE /system/menu/{id}
Authorization: Bearer {token}
```

**权限要求**: `SYSTEM_MENU_DELETE`

#### 1.4 批量删除菜单
```http
DELETE /system/menu/batch
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**:
```json
[1, 2, 3]
```

### 2. 菜单查询

#### 2.1 根据ID查询菜单
```http
GET /system/menu/{id}
Authorization: Bearer {token}
```

**权限要求**: `SYSTEM_MENU_VIEW`

#### 2.2 查询菜单树结构
```http
GET /system/menu/tree
Authorization: Bearer {token}
```

**权限要求**: `SYSTEM_MENU_VIEW`

**响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "dashboard",
      "title": "仪表盘",
      "icon": "dashboard",
      "path": "/dashboard",
      "type": "DIRECTORY",
      "sort": 1,
      "visible": true,
      "enabled": true,
      "children": [
        {
          "id": 11,
          "name": "dashboard-overview",
          "title": "总览",
          "icon": "pie-chart",
          "path": "/dashboard/overview",
          "component": "dashboard/Overview",
          "type": "MENU",
          "parentId": 1,
          "sort": 1,
          "visible": true,
          "enabled": true,
          "permission": "DASHBOARD_OVERVIEW"
        }
      ]
    }
  ]
}
```

#### 2.3 查询所有菜单列表
```http
GET /system/menu/list
Authorization: Bearer {token}
```

#### 2.4 根据类型查询菜单
```http
GET /system/menu/type/{type}
Authorization: Bearer {token}
```

**路径参数**:
- `type`: 菜单类型 (DIRECTORY, MENU, BUTTON)

### 3. 用户菜单

#### 3.1 获取当前用户菜单树
```http
GET /system/menu/user/tree
Authorization: Bearer {token}
```

**说明**: 返回当前用户有权限访问的菜单树，自动过滤不可见菜单和按钮类型菜单。

#### 3.2 获取指定用户菜单树
```http
GET /system/menu/user/{userId}/tree
Authorization: Bearer {token}
```

**权限要求**: `SYSTEM_MENU_VIEW`

#### 3.3 获取当前用户菜单权限
```http
GET /system/menu/user/permissions
Authorization: Bearer {token}
```

**响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    "DASHBOARD_OVERVIEW",
    "SYSTEM_USER_VIEW",
    "SYSTEM_USER_CREATE",
    "SYSTEM_MENU_VIEW"
  ]
}
```

#### 3.4 检查用户菜单权限
```http
GET /system/menu/user/permission/{permission}
Authorization: Bearer {token}
```

### 4. 角色菜单管理

#### 4.1 查询角色关联菜单
```http
GET /system/menu/role/{roleId}
Authorization: Bearer {token}
```

**权限要求**: `SYSTEM_ROLE_VIEW`

#### 4.2 为角色分配菜单权限
```http
POST /system/menu/role/{roleId}/assign
Authorization: Bearer {token}
Content-Type: application/json
```

**权限要求**: `SYSTEM_ROLE_UPDATE`

**请求体**:
```json
[1, 2, 11, 21, 22]
```

#### 4.3 移除角色菜单权限
```http
POST /system/menu/role/{roleId}/remove
Authorization: Bearer {token}
Content-Type: application/json
```

### 5. 菜单管理操作

#### 5.1 调整菜单排序
```http
PUT /system/menu/{id}/sort
Authorization: Bearer {token}
Content-Type: application/json
```

**权限要求**: `SYSTEM_MENU_SORT`

**请求体**:
```json
{
  "sort": 5
}
```

#### 5.2 移动菜单
```http
PUT /system/menu/{id}/move
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**:
```json
{
  "parentId": 3
}
```

#### 5.3 启用/禁用菜单
```http
PUT /system/menu/{id}/status
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**:
```json
{
  "enabled": false
}
```

#### 5.4 显示/隐藏菜单
```http
PUT /system/menu/{id}/visibility
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**:
```json
{
  "visible": false
}
```

### 6. 菜单验证

#### 6.1 检查菜单名称是否存在
```http
GET /system/menu/check/name/{name}
Authorization: Bearer {token}
```

#### 6.2 检查权限标识是否存在
```http
GET /system/menu/check/permission/{permission}
Authorization: Bearer {token}
```

#### 6.3 检查菜单是否有子菜单
```http
GET /system/menu/{id}/has-children
Authorization: Bearer {token}
```

## 权限列表

| 权限标识 | 描述 | 适用接口 |
|----------|------|----------|
| SYSTEM_MENU_VIEW | 查看菜单 | 所有查询接口 |
| SYSTEM_MENU_CREATE | 创建菜单 | 创建菜单接口 |
| SYSTEM_MENU_UPDATE | 更新菜单 | 更新、移动、状态切换接口 |
| SYSTEM_MENU_DELETE | 删除菜单 | 删除菜单接口 |
| SYSTEM_MENU_SORT | 菜单排序 | 排序接口 |
| SYSTEM_ROLE_VIEW | 查看角色 | 角色菜单查询接口 |
| SYSTEM_ROLE_UPDATE | 更新角色 | 角色菜单分配接口 |

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 未认证 | 提供有效的 JWT Token |
| 403 | 权限不足 | 确保用户具有相应权限 |
| 404 | 菜单不存在 | 检查菜单ID是否正确 |
| 409 | 菜单名称或权限标识已存在 | 使用不同的名称或权限标识 |

## 使用示例

### 创建完整的菜单结构

1. **创建系统管理目录**:
```bash
curl -X POST http://localhost:8081/system/menu \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "system",
    "title": "系统管理",
    "icon": "system",
    "path": "/system",
    "type": "DIRECTORY",
    "sort": 1,
    "visible": true,
    "enabled": true,
    "permission": "SYSTEM_MANAGE"
  }'
```

2. **创建用户管理菜单**:
```bash
curl -X POST http://localhost:8081/system/menu \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "system-user",
    "title": "用户管理",
    "icon": "user",
    "path": "/system/user",
    "component": "system/User",
    "type": "MENU",
    "parentId": 2,
    "sort": 1,
    "visible": true,
    "enabled": true,
    "permission": "SYSTEM_USER_VIEW"
  }'
```

3. **创建用户操作按钮**:
```bash
curl -X POST http://localhost:8081/system/menu \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "system-user-create",
    "title": "新增用户",
    "type": "BUTTON",
    "parentId": 21,
    "sort": 1,
    "visible": false,
    "enabled": true,
    "permission": "SYSTEM_USER_CREATE"
  }'
```

### 为角色分配菜单权限

```bash
curl -X POST http://localhost:8081/system/menu/role/1/assign \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '[1, 2, 21, 211, 212, 213]'
```

## 注意事项

1. **菜单层级**: 支持无限层级，但建议不超过3级
2. **权限继承**: 子菜单权限不会自动继承父菜单权限
3. **删除限制**: 有子菜单的菜单无法删除
4. **移动限制**: 不能将菜单移动到自己的子菜单下
5. **按钮菜单**: 按钮类型菜单不会在菜单树中显示，仅用于权限控制
