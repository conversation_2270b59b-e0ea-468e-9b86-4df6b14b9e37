package com.cloudvps.system.service;

import com.cloudvps.system.api.dto.request.UserLoginRequest;
import com.cloudvps.system.api.dto.response.LoginResponse;
import com.cloudvps.system.entity.User;

/**
 * 认证服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AuthService {
    
    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    LoginResponse login(UserLoginRequest request);
    
    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的登录响应
     */
    LoginResponse refreshToken(String refreshToken);
    
    /**
     * 用户登出
     *
     * @param token 访问令牌
     */
    void logout(String token);
    
    /**
     * 验证令牌
     *
     * @param token 访问令牌
     * @return 用户信息
     */
    User validateToken(String token);
    
    /**
     * 生成访问令牌
     *
     * @param user 用户信息
     * @return 访问令牌
     */
    String generateAccessToken(User user);
    
    /**
     * 生成刷新令牌
     *
     * @param user 用户信息
     * @return 刷新令牌
     */
    String generateRefreshToken(User user);
    
    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    String getUsernameFromToken(String token);
    
    /**
     * 检查令牌是否有效
     *
     * @param token 令牌
     * @return 是否有效
     */
    boolean isTokenValid(String token);
}
