package com.cloudvps.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.system.api.dto.request.SystemConfigCreateRequest;
import com.cloudvps.system.api.dto.request.SystemConfigQueryRequest;
import com.cloudvps.system.api.dto.request.SystemConfigUpdateRequest;
import com.cloudvps.system.api.dto.response.SystemConfigResponse;
import com.cloudvps.system.api.enums.ConfigType;
import com.cloudvps.system.service.SystemConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统配置管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/configs")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "系统配置管理", description = "系统配置相关接口")
public class SystemConfigController {
    
    private final SystemConfigService systemConfigService;
    
    @GetMapping
    @Operation(summary = "分页查询系统配置", description = "分页查询系统配置列表")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_VIEW')")
    public ApiResponse<IPage<SystemConfigResponse>> getConfigs(@Valid SystemConfigQueryRequest request) {
        log.debug("分页查询系统配置: {}", request);
        IPage<SystemConfigResponse> configs = systemConfigService.queryConfigs(request);
        return ApiResponse.success(configs);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取系统配置详情", description = "根据配置ID获取系统配置详细信息")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_VIEW')")
    public ApiResponse<SystemConfigResponse> getConfigById(
            @Parameter(description = "配置ID") @PathVariable Long id) {
        log.debug("获取系统配置详情: configId={}", id);
        SystemConfigResponse config = systemConfigService.findById(id);
        return ApiResponse.success(config);
    }
    
    @GetMapping("/by-key/{configKey}")
    @Operation(summary = "根据配置键获取配置", description = "根据配置键获取系统配置")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_VIEW')")
    public ApiResponse<SystemConfigResponse> getConfigByKey(
            @Parameter(description = "配置键") @PathVariable String configKey) {
        log.debug("根据配置键获取配置: configKey={}", configKey);
        SystemConfigResponse config = systemConfigService.findByConfigKey(configKey);
        return ApiResponse.success(config);
    }
    
    @PostMapping
    @Operation(summary = "创建系统配置", description = "创建新的系统配置")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_CREATE')")
    public ApiResponse<SystemConfigResponse> createConfig(@Valid @RequestBody SystemConfigCreateRequest request) {
        log.info("创建系统配置请求: configKey={}", request.getConfigKey());
        SystemConfigResponse config = systemConfigService.createConfig(request);
        return ApiResponse.success("创建成功", config);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新系统配置", description = "更新系统配置信息")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_UPDATE')")
    public ApiResponse<SystemConfigResponse> updateConfig(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Valid @RequestBody SystemConfigUpdateRequest request) {
        log.info("更新系统配置请求: configId={}", id);
        SystemConfigResponse config = systemConfigService.updateConfig(id, request);
        return ApiResponse.success("更新成功", config);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除系统配置", description = "删除指定系统配置")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_DELETE')")
    public ApiResponse<Void> deleteConfig(
            @Parameter(description = "配置ID") @PathVariable Long id) {
        log.info("删除系统配置请求: configId={}", id);
        systemConfigService.deleteConfig(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除系统配置", description = "批量删除指定系统配置")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_DELETE')")
    public ApiResponse<Void> batchDeleteConfigs(@RequestBody List<Long> configIds) {
        log.info("批量删除系统配置请求: configIds={}", configIds);
        systemConfigService.batchDeleteConfigs(configIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @GetMapping("/check-key")
    @Operation(summary = "检查配置键", description = "检查配置键是否已存在")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_VIEW')")
    public ApiResponse<Boolean> checkConfigKey(@RequestParam String configKey) {
        boolean exists = systemConfigService.existsByConfigKey(configKey);
        return ApiResponse.success(exists ? "配置键已存在" : "配置键可用", !exists);
    }
    
    @GetMapping("/by-type")
    @Operation(summary = "根据配置类型获取配置", description = "根据配置类型获取配置列表")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_VIEW')")
    public ApiResponse<List<SystemConfigResponse>> getConfigsByType(
            @Parameter(description = "配置类型") @RequestParam ConfigType configType) {
        log.debug("根据配置类型获取配置: configType={}", configType);
        List<SystemConfigResponse> configs = systemConfigService.findByConfigType(configType);
        return ApiResponse.success(configs);
    }
    
    @GetMapping("/system")
    @Operation(summary = "获取系统配置", description = "获取所有系统配置列表")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_VIEW')")
    public ApiResponse<List<SystemConfigResponse>> getSystemConfigs() {
        log.debug("获取系统配置");
        List<SystemConfigResponse> configs = systemConfigService.findSystemConfigs();
        return ApiResponse.success(configs);
    }
    
    @GetMapping("/user")
    @Operation(summary = "获取用户配置", description = "获取所有用户配置列表")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_VIEW')")
    public ApiResponse<List<SystemConfigResponse>> getUserConfigs() {
        log.debug("获取用户配置");
        List<SystemConfigResponse> configs = systemConfigService.findUserConfigs();
        return ApiResponse.success(configs);
    }
    
    @GetMapping("/value/{configKey}")
    @Operation(summary = "获取配置值", description = "根据配置键获取配置值")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_VIEW')")
    public ApiResponse<String> getConfigValue(
            @Parameter(description = "配置键") @PathVariable String configKey,
            @Parameter(description = "默认值") @RequestParam(required = false) String defaultValue) {
        log.debug("获取配置值: configKey={}", configKey);
        String value = systemConfigService.getConfigValue(configKey, defaultValue);
        return ApiResponse.success(value);
    }
    
    @PutMapping("/value/{configKey}")
    @Operation(summary = "更新配置值", description = "根据配置键更新配置值")
    @PreAuthorize("hasAuthority('SYSTEM_CONFIG_UPDATE')")
    public ApiResponse<Void> updateConfigValue(
            @Parameter(description = "配置键") @PathVariable String configKey,
            @Parameter(description = "配置值") @RequestParam String configValue) {
        log.info("更新配置值: configKey={}", configKey);
        systemConfigService.updateConfigValue(configKey, configValue);
        return ApiResponse.success("更新成功", null);
    }
}
