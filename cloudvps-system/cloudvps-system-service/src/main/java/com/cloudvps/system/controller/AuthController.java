package com.cloudvps.system.controller;

import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.common.security.util.SecurityUtils;
import com.cloudvps.system.api.dto.request.UserLoginRequest;
import com.cloudvps.system.api.dto.response.LoginResponse;
import com.cloudvps.system.api.dto.response.UserResponse;
import com.cloudvps.system.service.AuthService;
import com.cloudvps.system.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "用户认证", description = "用户认证相关接口")
public class AuthController {

    private final AuthService authService;
    private final UserService userService;
    
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录获取访问令牌")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody UserLoginRequest request) {
        log.info("用户登录请求: username={}", request.getUsername());
        LoginResponse response = authService.login(request);
        return ApiResponse.success("登录成功", response);
    }
    
    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "使用刷新令牌获取新的访问令牌")
    public ApiResponse<LoginResponse> refreshToken(
            @Parameter(description = "刷新令牌") @RequestParam String refreshToken) {
        log.info("刷新令牌请求");
        LoginResponse response = authService.refreshToken(refreshToken);
        return ApiResponse.success("令牌刷新成功", response);
    }
    
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，使令牌失效")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> logout() {
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        log.info("用户登出请求: userId={}", currentUserId);
        // TODO: 实现基于SecurityUtils的登出逻辑
        // authService.logout(currentUserId);
        return ApiResponse.success("登出成功", null);
    }
    
    @GetMapping("/verify")
    @Operation(summary = "验证令牌", description = "验证访问令牌的有效性")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<UserResponse> verifyToken() {
        log.debug("验证令牌请求");
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        UserResponse userResponse = userService.findById(currentUserId);

        return ApiResponse.success("令牌有效", userResponse);
    }
    
    @GetMapping("/user")
    @Operation(summary = "获取当前用户", description = "根据令牌获取当前登录用户信息")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<UserResponse> getCurrentUser() {
        log.debug("获取当前用户请求");
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        UserResponse userResponse = userService.findById(currentUserId);

        return ApiResponse.success(userResponse);
    }

}
