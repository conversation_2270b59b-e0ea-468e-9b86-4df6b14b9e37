package com.cloudvps.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.system.api.enums.MenuType;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 菜单实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("sys_menu")
@Data
@EqualsAndHashCode(callSuper = true)
public class Menu extends BaseEntity {

    /**
     * 菜单名称
     */
    @TableField("name")
    private String name;

    /**
     * 菜单标题（显示名称）
     */
    @TableField("title")
    private String title;

    /**
     * 菜单图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 菜单路径
     */
    @TableField("path")
    private String path;

    /**
     * 组件路径
     */
    @TableField("component")
    private String component;

    /**
     * 菜单类型
     */
    @TableField("type")
    private MenuType type = MenuType.MENU;

    /**
     * 父菜单ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 排序号
     */
    @TableField("sort")
    private Integer sort = 0;

    /**
     * 是否可见
     */
    @TableField("visible")
    private Boolean visible = true;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled = true;

    /**
     * 权限标识
     */
    @TableField("permission")
    private String permission;

    /**
     * 菜单描述
     */
    @TableField("description")
    private String description;

    /**
     * 路由参数
     */
    @TableField("params")
    private String params;

    /**
     * 是否缓存
     */
    @TableField("cache")
    private Boolean cache = false;

    /**
     * 是否外链
     */
    @TableField("external")
    private Boolean external = false;

    /**
     * 是否为根菜单
     */
    public boolean isRoot() {
        return this.parentId == null;
    }
}
