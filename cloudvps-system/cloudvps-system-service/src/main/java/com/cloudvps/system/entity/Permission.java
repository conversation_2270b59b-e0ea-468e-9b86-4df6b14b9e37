package com.cloudvps.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.system.api.enums.ResourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 权限实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("permissions")
@Data
@EqualsAndHashCode(callSuper = true)
public class Permission extends BaseEntity {
    
    /**
     * 权限编码
     */
    @TableField("permission_code")
    private String permissionCode;

    /**
     * 权限名称
     */
    @TableField("permission_name")
    private String permissionName;

    /**
     * 资源类型
     */
    @TableField("resource_type")
    private ResourceType resourceType;

    /**
     * 资源路径
     */
    @TableField("resource_path")
    private String resourcePath;

    /**
     * HTTP方法
     */
    @TableField("http_method")
    private String httpMethod;

    /**
     * 权限描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder = 0;

    // 注意：权限树形关系和角色权限关联已迁移到MyBatis-Plus，复杂关系查询通过Service层处理
}
