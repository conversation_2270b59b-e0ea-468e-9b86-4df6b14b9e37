package com.cloudvps.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.common.security.util.SecurityUtils;
import com.cloudvps.system.api.dto.request.MenuCreateRequest;
import com.cloudvps.system.api.dto.request.MenuQueryRequest;
import com.cloudvps.system.api.dto.request.MenuUpdateRequest;
import com.cloudvps.system.api.dto.response.MenuResponse;
import com.cloudvps.system.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/menus")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "菜单管理", description = "菜单相关接口")
public class MenuController {
    
    private final MenuService menuService;
    
    @GetMapping
    @Operation(summary = "分页查询菜单", description = "分页查询菜单列表")
    @PreAuthorize("hasAuthority('MENU_VIEW')")
    public ApiResponse<IPage<MenuResponse>> getMenus(@Valid MenuQueryRequest request) {
        log.debug("分页查询菜单: {}", request);
        IPage<MenuResponse> menus = menuService.queryMenus(request);
        return ApiResponse.success(menus);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取菜单详情", description = "根据菜单ID获取菜单详细信息")
    @PreAuthorize("hasAuthority('MENU_VIEW')")
    public ApiResponse<MenuResponse> getMenuById(
            @Parameter(description = "菜单ID") @PathVariable Long id) {
        log.debug("获取菜单详情: menuId={}", id);
        MenuResponse menu = menuService.findById(id);
        return ApiResponse.success(menu);
    }
    
    @PostMapping
    @Operation(summary = "创建菜单", description = "创建新菜单")
    @PreAuthorize("hasAuthority('MENU_CREATE')")
    public ApiResponse<MenuResponse> createMenu(@Valid @RequestBody MenuCreateRequest request) {
        log.info("创建菜单请求: name={}", request.getName());
        MenuResponse menu = menuService.createMenu(request);
        return ApiResponse.success("创建成功", menu);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新菜单", description = "更新菜单信息")
    @PreAuthorize("hasAuthority('MENU_UPDATE')")
    public ApiResponse<MenuResponse> updateMenu(
            @Parameter(description = "菜单ID") @PathVariable Long id,
            @Valid @RequestBody MenuUpdateRequest request) {
        log.info("更新菜单请求: menuId={}", id);
        MenuResponse menu = menuService.updateMenu(id, request);
        return ApiResponse.success("更新成功", menu);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除菜单", description = "删除指定菜单")
    @PreAuthorize("hasAuthority('MENU_DELETE')")
    public ApiResponse<Void> deleteMenu(
            @Parameter(description = "菜单ID") @PathVariable Long id) {
        log.info("删除菜单请求: menuId={}", id);
        menuService.deleteMenu(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除菜单", description = "批量删除指定菜单")
    @PreAuthorize("hasAuthority('MENU_DELETE')")
    public ApiResponse<Void> batchDeleteMenus(@RequestBody List<Long> menuIds) {
        log.info("批量删除菜单请求: menuIds={}", menuIds);
        menuService.batchDeleteMenus(menuIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @PutMapping("/{id}/enabled")
    @Operation(summary = "更新菜单状态", description = "更新指定菜单的启用状态")
    @PreAuthorize("hasAuthority('MENU_UPDATE')")
    public ApiResponse<MenuResponse> updateMenuEnabled(
            @Parameter(description = "菜单ID") @PathVariable Long id,
            @Parameter(description = "是否启用") @RequestParam Boolean enabled) {
        log.info("更新菜单状态: menuId={}, enabled={}", id, enabled);
        MenuResponse menu = menuService.updateEnabled(id, enabled);
        return ApiResponse.success("状态更新成功", menu);
    }
    
    @PutMapping("/{id}/visible")
    @Operation(summary = "更新菜单可见性", description = "更新指定菜单的可见性")
    @PreAuthorize("hasAuthority('MENU_UPDATE')")
    public ApiResponse<MenuResponse> updateMenuVisible(
            @Parameter(description = "菜单ID") @PathVariable Long id,
            @Parameter(description = "是否可见") @RequestParam Boolean visible) {
        log.info("更新菜单可见性: menuId={}, visible={}", id, visible);
        MenuResponse menu = menuService.updateVisible(id, visible);
        return ApiResponse.success("可见性更新成功", menu);
    }
    
    @GetMapping("/check-name")
    @Operation(summary = "检查菜单名称", description = "检查菜单名称是否已存在")
    @PreAuthorize("hasAuthority('MENU_VIEW')")
    public ApiResponse<Boolean> checkMenuName(@RequestParam String name) {
        boolean exists = menuService.existsByName(name);
        return ApiResponse.success(exists ? "菜单名称已存在" : "菜单名称可用", !exists);
    }
    
    @GetMapping("/tree")
    @Operation(summary = "获取菜单树", description = "获取完整的菜单树结构")
    @PreAuthorize("hasAuthority('MENU_VIEW')")
    public ApiResponse<List<MenuResponse>> getMenuTree() {
        log.debug("获取菜单树");
        List<MenuResponse> menuTree = menuService.getMenuTree();
        return ApiResponse.success(menuTree);
    }

    @GetMapping("/user/tree")
    @Operation(summary = "获取当前用户菜单树", description = "获取当前用户有权限访问的菜单树")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<List<MenuResponse>> getCurrentUserMenuTree() {
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        log.debug("获取当前用户菜单树: userId={}", currentUserId);
        List<MenuResponse> menuTree = menuService.getUserMenuTree(currentUserId);
        return ApiResponse.success(menuTree);
    }

    @GetMapping("/user/{userId}/tree")
    @Operation(summary = "获取指定用户菜单树", description = "获取指定用户有权限访问的菜单树")
    @PreAuthorize("hasAuthority('MENU_VIEW')")
    public ApiResponse<List<MenuResponse>> getUserMenuTree(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        log.debug("获取用户菜单树: userId={}", userId);
        List<MenuResponse> menuTree = menuService.getUserMenuTree(userId);
        return ApiResponse.success(menuTree);
    }
    
    @GetMapping("/children/{parentId}")
    @Operation(summary = "获取子菜单", description = "获取指定父菜单的子菜单列表")
    @PreAuthorize("hasAuthority('MENU_VIEW')")
    public ApiResponse<List<MenuResponse>> getChildMenus(
            @Parameter(description = "父菜单ID") @PathVariable Long parentId) {
        log.debug("获取子菜单: parentId={}", parentId);
        List<MenuResponse> children = menuService.getChildrenByParentId(parentId);
        return ApiResponse.success(children);
    }
    
    @GetMapping("/user-tree")
    @Operation(summary = "获取用户菜单树", description = "获取当前用户有权限的菜单树")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<List<MenuResponse>> getUserMenuTree() {
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        log.debug("获取用户菜单树: userId={}", currentUserId);
        List<MenuResponse> menuTree = menuService.getUserMenuTree(currentUserId);
        return ApiResponse.success(menuTree);
    }
    
    @GetMapping("/role-tree/{roleId}")
    @Operation(summary = "获取角色菜单树", description = "获取指定角色的菜单树")
    @PreAuthorize("hasAuthority('MENU_VIEW')")
    public ApiResponse<List<MenuResponse>> getRoleMenuTree(
            @Parameter(description = "角色ID") @PathVariable Long roleId) {
        log.debug("获取角色菜单树: roleId={}", roleId);
        List<MenuResponse> menuTree = menuService.getRoleMenuTree(roleId);
        return ApiResponse.success(menuTree);
    }
}
