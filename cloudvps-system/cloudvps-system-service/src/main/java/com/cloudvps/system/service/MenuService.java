package com.cloudvps.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.system.api.dto.request.MenuCreateRequest;
import com.cloudvps.system.api.dto.request.MenuQueryRequest;
import com.cloudvps.system.api.dto.request.MenuUpdateRequest;
import com.cloudvps.system.api.dto.response.MenuResponse;

import java.util.List;

/**
 * 菜单服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MenuService {
    
    /**
     * 分页查询菜单
     */
    IPage<MenuResponse> queryMenus(MenuQueryRequest request);
    
    /**
     * 根据ID获取菜单
     */
    MenuResponse findById(Long id);
    
    /**
     * 根据菜单名称获取菜单
     */
    MenuResponse findByName(String name);
    
    /**
     * 创建菜单
     */
    MenuResponse createMenu(MenuCreateRequest request);
    
    /**
     * 更新菜单
     */
    MenuResponse updateMenu(Long id, MenuUpdateRequest request);
    
    /**
     * 删除菜单
     */
    void deleteMenu(Long id);
    
    /**
     * 批量删除菜单
     */
    void batchDeleteMenus(List<Long> menuIds);
    
    /**
     * 更新菜单状态
     */
    MenuResponse updateEnabled(Long id, Boolean enabled);
    
    /**
     * 更新菜单可见性
     */
    MenuResponse updateVisible(Long id, Boolean visible);
    
    /**
     * 检查菜单名称是否存在
     */
    boolean existsByName(String name);
    
    /**
     * 获取菜单树
     */
    List<MenuResponse> getMenuTree();
    
    /**
     * 根据父菜单ID获取子菜单
     */
    List<MenuResponse> getChildrenByParentId(Long parentId);
    
    /**
     * 获取用户菜单树
     */
    List<MenuResponse> getUserMenuTree(Long userId);
    
    /**
     * 获取角色菜单树
     */
    List<MenuResponse> getRoleMenuTree(Long roleId);
}
