package com.cloudvps.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.system.api.dto.request.DictTypeCreateRequest;
import com.cloudvps.system.api.dto.request.DictTypeQueryRequest;
import com.cloudvps.system.api.dto.request.DictTypeUpdateRequest;
import com.cloudvps.system.api.dto.response.DictTypeResponse;
import com.cloudvps.system.api.enums.DictStatus;
import com.cloudvps.system.service.DictTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 字典类型管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/dict-types")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "字典类型管理", description = "字典类型相关接口")
public class DictTypeController {
    
    private final DictTypeService dictTypeService;
    
    @GetMapping
    @Operation(summary = "分页查询字典类型", description = "分页查询字典类型列表")
    @PreAuthorize("hasAuthority('DICT_TYPE_VIEW')")
    public ApiResponse<IPage<DictTypeResponse>> getDictTypes(@Valid DictTypeQueryRequest request) {
        log.debug("分页查询字典类型: {}", request);
        IPage<DictTypeResponse> dictTypes = dictTypeService.queryDictTypes(request);
        return ApiResponse.success(dictTypes);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取字典类型详情", description = "根据字典类型ID获取详细信息")
    @PreAuthorize("hasAuthority('DICT_TYPE_VIEW')")
    public ApiResponse<DictTypeResponse> getDictTypeById(
            @Parameter(description = "字典类型ID") @PathVariable Long id) {
        log.debug("获取字典类型详情: dictTypeId={}", id);
        DictTypeResponse dictType = dictTypeService.findById(id);
        return ApiResponse.success(dictType);
    }
    
    @GetMapping("/by-code/{dictCode}")
    @Operation(summary = "根据字典编码获取字典类型", description = "根据字典编码获取字典类型")
    @PreAuthorize("hasAuthority('DICT_TYPE_VIEW')")
    public ApiResponse<DictTypeResponse> getDictTypeByCode(
            @Parameter(description = "字典编码") @PathVariable String dictCode) {
        log.debug("根据字典编码获取字典类型: dictCode={}", dictCode);
        DictTypeResponse dictType = dictTypeService.findByDictCode(dictCode);
        return ApiResponse.success(dictType);
    }
    
    @PostMapping
    @Operation(summary = "创建字典类型", description = "创建新的字典类型")
    @PreAuthorize("hasAuthority('DICT_TYPE_CREATE')")
    public ApiResponse<DictTypeResponse> createDictType(@Valid @RequestBody DictTypeCreateRequest request) {
        log.info("创建字典类型请求: dictCode={}", request.getDictCode());
        DictTypeResponse dictType = dictTypeService.createDictType(request);
        return ApiResponse.success("创建成功", dictType);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新字典类型", description = "更新字典类型信息")
    @PreAuthorize("hasAuthority('DICT_TYPE_UPDATE')")
    public ApiResponse<DictTypeResponse> updateDictType(
            @Parameter(description = "字典类型ID") @PathVariable Long id,
            @Valid @RequestBody DictTypeUpdateRequest request) {
        log.info("更新字典类型请求: dictTypeId={}", id);
        DictTypeResponse dictType = dictTypeService.updateDictType(id, request);
        return ApiResponse.success("更新成功", dictType);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除字典类型", description = "删除指定字典类型")
    @PreAuthorize("hasAuthority('DICT_TYPE_DELETE')")
    public ApiResponse<Void> deleteDictType(
            @Parameter(description = "字典类型ID") @PathVariable Long id) {
        log.info("删除字典类型请求: dictTypeId={}", id);
        dictTypeService.deleteDictType(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除字典类型", description = "批量删除指定字典类型")
    @PreAuthorize("hasAuthority('DICT_TYPE_DELETE')")
    public ApiResponse<Void> batchDeleteDictTypes(@RequestBody List<Long> dictTypeIds) {
        log.info("批量删除字典类型请求: dictTypeIds={}", dictTypeIds);
        dictTypeService.batchDeleteDictTypes(dictTypeIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @PutMapping("/{id}/status")
    @Operation(summary = "更新字典类型状态", description = "更新指定字典类型的状态")
    @PreAuthorize("hasAuthority('DICT_TYPE_UPDATE')")
    public ApiResponse<DictTypeResponse> updateDictTypeStatus(
            @Parameter(description = "字典类型ID") @PathVariable Long id,
            @Parameter(description = "状态") @RequestParam DictStatus status) {
        log.info("更新字典类型状态: dictTypeId={}, status={}", id, status);
        DictTypeResponse dictType = dictTypeService.updateStatus(id, status);
        return ApiResponse.success("状态更新成功", dictType);
    }
    
    @GetMapping("/check-code")
    @Operation(summary = "检查字典编码", description = "检查字典编码是否已存在")
    @PreAuthorize("hasAuthority('DICT_TYPE_VIEW')")
    public ApiResponse<Boolean> checkDictCode(@RequestParam String dictCode) {
        boolean exists = dictTypeService.existsByDictCode(dictCode);
        return ApiResponse.success(exists ? "字典编码已存在" : "字典编码可用", !exists);
    }
    
    @GetMapping("/check-name")
    @Operation(summary = "检查字典名称", description = "检查字典名称是否已存在")
    @PreAuthorize("hasAuthority('DICT_TYPE_VIEW')")
    public ApiResponse<Boolean> checkDictName(@RequestParam String dictName) {
        boolean exists = dictTypeService.existsByDictName(dictName);
        return ApiResponse.success(exists ? "字典名称已存在" : "字典名称可用", !exists);
    }
    
    @GetMapping("/by-status")
    @Operation(summary = "根据状态获取字典类型", description = "根据状态获取字典类型列表")
    @PreAuthorize("hasAuthority('DICT_TYPE_VIEW')")
    public ApiResponse<List<DictTypeResponse>> getDictTypesByStatus(
            @Parameter(description = "状态") @RequestParam DictStatus status) {
        log.debug("根据状态获取字典类型: status={}", status);
        List<DictTypeResponse> dictTypes = dictTypeService.findByStatus(status);
        return ApiResponse.success(dictTypes);
    }
    
    @GetMapping("/active")
    @Operation(summary = "获取启用的字典类型", description = "获取所有启用状态的字典类型")
    @PreAuthorize("hasAuthority('DICT_TYPE_VIEW')")
    public ApiResponse<List<DictTypeResponse>> getActiveDictTypes() {
        log.debug("获取启用的字典类型");
        List<DictTypeResponse> dictTypes = dictTypeService.findActiveDictTypes();
        return ApiResponse.success(dictTypes);
    }
    
    @GetMapping("/all")
    @Operation(summary = "获取所有字典类型", description = "获取所有字典类型列表")
    @PreAuthorize("hasAuthority('DICT_TYPE_VIEW')")
    public ApiResponse<List<DictTypeResponse>> getAllDictTypes() {
        log.debug("获取所有字典类型");
        List<DictTypeResponse> dictTypes = dictTypeService.findAllDictTypes();
        return ApiResponse.success(dictTypes);
    }
}
