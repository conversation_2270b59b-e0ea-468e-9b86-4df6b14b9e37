package com.cloudvps.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.system.api.dto.request.DictDataCreateRequest;
import com.cloudvps.system.api.dto.request.DictDataQueryRequest;
import com.cloudvps.system.api.dto.request.DictDataUpdateRequest;
import com.cloudvps.system.api.dto.response.DictDataResponse;
import com.cloudvps.system.api.enums.DictStatus;
import com.cloudvps.system.service.DictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 字典数据管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/dict-data")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "字典数据管理", description = "字典数据相关接口")
public class DictDataController {
    
    private final DictDataService dictDataService;
    
    @GetMapping
    @Operation(summary = "分页查询字典数据", description = "分页查询字典数据列表")
    @PreAuthorize("hasAuthority('DICT_DATA_VIEW')")
    public ApiResponse<IPage<DictDataResponse>> getDictData(@Valid DictDataQueryRequest request) {
        log.debug("分页查询字典数据: {}", request);
        IPage<DictDataResponse> dictData = dictDataService.queryDictData(request);
        return ApiResponse.success(dictData);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取字典数据详情", description = "根据字典数据ID获取详细信息")
    @PreAuthorize("hasAuthority('DICT_DATA_VIEW')")
    public ApiResponse<DictDataResponse> getDictDataById(
            @Parameter(description = "字典数据ID") @PathVariable Long id) {
        log.debug("获取字典数据详情: dictDataId={}", id);
        DictDataResponse dictData = dictDataService.findById(id);
        return ApiResponse.success(dictData);
    }
    
    @PostMapping
    @Operation(summary = "创建字典数据", description = "创建新的字典数据")
    @PreAuthorize("hasAuthority('DICT_DATA_CREATE')")
    public ApiResponse<DictDataResponse> createDictData(@Valid @RequestBody DictDataCreateRequest request) {
        log.info("创建字典数据请求: dictTypeId={}, dictLabel={}", 
                request.getDictTypeId(), request.getDictLabel());
        DictDataResponse dictData = dictDataService.createDictData(request);
        return ApiResponse.success("创建成功", dictData);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新字典数据", description = "更新字典数据信息")
    @PreAuthorize("hasAuthority('DICT_DATA_UPDATE')")
    public ApiResponse<DictDataResponse> updateDictData(
            @Parameter(description = "字典数据ID") @PathVariable Long id,
            @Valid @RequestBody DictDataUpdateRequest request) {
        log.info("更新字典数据请求: dictDataId={}", id);
        DictDataResponse dictData = dictDataService.updateDictData(id, request);
        return ApiResponse.success("更新成功", dictData);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除字典数据", description = "删除指定字典数据")
    @PreAuthorize("hasAuthority('DICT_DATA_DELETE')")
    public ApiResponse<Void> deleteDictData(
            @Parameter(description = "字典数据ID") @PathVariable Long id) {
        log.info("删除字典数据请求: dictDataId={}", id);
        dictDataService.deleteDictData(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除字典数据", description = "批量删除指定字典数据")
    @PreAuthorize("hasAuthority('DICT_DATA_DELETE')")
    public ApiResponse<Void> batchDeleteDictData(@RequestBody List<Long> dictDataIds) {
        log.info("批量删除字典数据请求: dictDataIds={}", dictDataIds);
        dictDataService.batchDeleteDictData(dictDataIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @PutMapping("/{id}/status")
    @Operation(summary = "更新字典数据状态", description = "更新指定字典数据的状态")
    @PreAuthorize("hasAuthority('DICT_DATA_UPDATE')")
    public ApiResponse<DictDataResponse> updateDictDataStatus(
            @Parameter(description = "字典数据ID") @PathVariable Long id,
            @Parameter(description = "状态") @RequestParam DictStatus status) {
        log.info("更新字典数据状态: dictDataId={}, status={}", id, status);
        DictDataResponse dictData = dictDataService.updateStatus(id, status);
        return ApiResponse.success("状态更新成功", dictData);
    }
    
    @GetMapping("/by-type/{dictTypeId}")
    @Operation(summary = "根据字典类型获取数据", description = "根据字典类型ID获取字典数据列表")
    @PreAuthorize("hasAuthority('DICT_DATA_VIEW')")
    public ApiResponse<List<DictDataResponse>> getDictDataByTypeId(
            @Parameter(description = "字典类型ID") @PathVariable Long dictTypeId) {
        log.debug("根据字典类型获取数据: dictTypeId={}", dictTypeId);
        List<DictDataResponse> dictData = dictDataService.findByDictTypeId(dictTypeId);
        return ApiResponse.success(dictData);
    }
    
    @GetMapping("/by-type/{dictTypeId}/status/{status}")
    @Operation(summary = "根据字典类型和状态获取数据", description = "根据字典类型ID和状态获取字典数据列表")
    @PreAuthorize("hasAuthority('DICT_DATA_VIEW')")
    public ApiResponse<List<DictDataResponse>> getDictDataByTypeIdAndStatus(
            @Parameter(description = "字典类型ID") @PathVariable Long dictTypeId,
            @Parameter(description = "状态") @PathVariable DictStatus status) {
        log.debug("根据字典类型和状态获取数据: dictTypeId={}, status={}", dictTypeId, status);
        List<DictDataResponse> dictData = dictDataService.findByDictTypeIdAndStatus(dictTypeId, status);
        return ApiResponse.success(dictData);
    }
    
    @GetMapping("/by-code/{dictCode}")
    @Operation(summary = "根据字典编码获取数据", description = "根据字典编码获取字典数据列表")
    @PreAuthorize("hasAuthority('DICT_DATA_VIEW')")
    public ApiResponse<List<DictDataResponse>> getDictDataByCode(
            @Parameter(description = "字典编码") @PathVariable String dictCode) {
        log.debug("根据字典编码获取数据: dictCode={}", dictCode);
        List<DictDataResponse> dictData = dictDataService.findByDictCode(dictCode);
        return ApiResponse.success(dictData);
    }
    
    @GetMapping("/by-code/{dictCode}/status/{status}")
    @Operation(summary = "根据字典编码和状态获取数据", description = "根据字典编码和状态获取字典数据列表")
    @PreAuthorize("hasAuthority('DICT_DATA_VIEW')")
    public ApiResponse<List<DictDataResponse>> getDictDataByCodeAndStatus(
            @Parameter(description = "字典编码") @PathVariable String dictCode,
            @Parameter(description = "状态") @PathVariable DictStatus status) {
        log.debug("根据字典编码和状态获取数据: dictCode={}, status={}", dictCode, status);
        List<DictDataResponse> dictData = dictDataService.findByDictCodeAndStatus(dictCode, status);
        return ApiResponse.success(dictData);
    }
    
    @GetMapping("/by-type/{dictTypeId}/value/{dictValue}")
    @Operation(summary = "根据字典类型和值获取数据", description = "根据字典类型ID和字典值获取字典数据")
    @PreAuthorize("hasAuthority('DICT_DATA_VIEW')")
    public ApiResponse<DictDataResponse> getDictDataByTypeIdAndValue(
            @Parameter(description = "字典类型ID") @PathVariable Long dictTypeId,
            @Parameter(description = "字典值") @PathVariable String dictValue) {
        log.debug("根据字典类型和值获取数据: dictTypeId={}, dictValue={}", dictTypeId, dictValue);
        DictDataResponse dictData = dictDataService.findByDictTypeIdAndValue(dictTypeId, dictValue);
        return ApiResponse.success(dictData);
    }
    
    @GetMapping("/by-type/{dictTypeId}/label/{dictLabel}")
    @Operation(summary = "根据字典类型和标签获取数据", description = "根据字典类型ID和字典标签获取字典数据")
    @PreAuthorize("hasAuthority('DICT_DATA_VIEW')")
    public ApiResponse<DictDataResponse> getDictDataByTypeIdAndLabel(
            @Parameter(description = "字典类型ID") @PathVariable Long dictTypeId,
            @Parameter(description = "字典标签") @PathVariable String dictLabel) {
        log.debug("根据字典类型和标签获取数据: dictTypeId={}, dictLabel={}", dictTypeId, dictLabel);
        DictDataResponse dictData = dictDataService.findByDictTypeIdAndLabel(dictTypeId, dictLabel);
        return ApiResponse.success(dictData);
    }
    
    @GetMapping("/default/by-type/{dictTypeId}")
    @Operation(summary = "获取默认字典数据", description = "根据字典类型ID获取默认字典数据")
    @PreAuthorize("hasAuthority('DICT_DATA_VIEW')")
    public ApiResponse<DictDataResponse> getDefaultDictData(
            @Parameter(description = "字典类型ID") @PathVariable Long dictTypeId) {
        log.debug("获取默认字典数据: dictTypeId={}", dictTypeId);
        DictDataResponse dictData = dictDataService.findDefaultByDictTypeId(dictTypeId);
        return ApiResponse.success(dictData);
    }
}
