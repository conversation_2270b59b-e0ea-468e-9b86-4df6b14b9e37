package com.cloudvps.system.util;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 用户代理解析工具
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class UserAgentParser {
    
    /**
     * 解析用户代理字符串
     */
    public UserAgentInfo parse(String userAgent) {
        if (userAgent == null || userAgent.trim().isEmpty()) {
            return new UserAgentInfo("Unknown", "Unknown", "Unknown");
        }
        
        String browser = parseBrowser(userAgent);
        String os = parseOperatingSystem(userAgent);
        String device = parseDeviceType(userAgent);
        
        return new UserAgentInfo(browser, os, device);
    }
    
    /**
     * 解析浏览器信息
     */
    private String parseBrowser(String userAgent) {
        String ua = userAgent.toLowerCase();
        
        if (ua.contains("edg/")) {
            return "Microsoft Edge";
        } else if (ua.contains("chrome/") && !ua.contains("edg/")) {
            return "Google Chrome";
        } else if (ua.contains("firefox/")) {
            return "Mozilla Firefox";
        } else if (ua.contains("safari/") && !ua.contains("chrome/")) {
            return "Safari";
        } else if (ua.contains("opera/") || ua.contains("opr/")) {
            return "Opera";
        } else if (ua.contains("msie") || ua.contains("trident/")) {
            return "Internet Explorer";
        } else {
            return "Unknown Browser";
        }
    }
    
    /**
     * 解析操作系统信息
     */
    private String parseOperatingSystem(String userAgent) {
        String ua = userAgent.toLowerCase();
        
        if (ua.contains("windows nt 10.0")) {
            return "Windows 10";
        } else if (ua.contains("windows nt 6.3")) {
            return "Windows 8.1";
        } else if (ua.contains("windows nt 6.2")) {
            return "Windows 8";
        } else if (ua.contains("windows nt 6.1")) {
            return "Windows 7";
        } else if (ua.contains("windows")) {
            return "Windows";
        } else if (ua.contains("mac os x")) {
            return "macOS";
        } else if (ua.contains("linux")) {
            return "Linux";
        } else if (ua.contains("android")) {
            return "Android";
        } else if (ua.contains("iphone") || ua.contains("ipad")) {
            return "iOS";
        } else {
            return "Unknown OS";
        }
    }
    
    /**
     * 解析设备类型
     */
    private String parseDeviceType(String userAgent) {
        String ua = userAgent.toLowerCase();
        
        if (ua.contains("mobile") || ua.contains("android") || ua.contains("iphone")) {
            return "Mobile";
        } else if (ua.contains("tablet") || ua.contains("ipad")) {
            return "Tablet";
        } else {
            return "Desktop";
        }
    }
    
    /**
     * 用户代理信息
     */
    @Data
    public static class UserAgentInfo {
        private final String browser;
        private final String operatingSystem;
        private final String deviceType;
        
        public UserAgentInfo(String browser, String operatingSystem, String deviceType) {
            this.browser = browser;
            this.operatingSystem = operatingSystem;
            this.deviceType = deviceType;
        }
    }
}
