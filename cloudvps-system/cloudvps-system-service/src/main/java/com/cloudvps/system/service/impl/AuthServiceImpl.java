package com.cloudvps.system.service.impl;

import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.common.core.response.ResponseCode;
import com.cloudvps.common.security.util.JwtUtils;
import com.cloudvps.system.api.dto.request.UserLoginRequest;
import com.cloudvps.system.api.dto.response.LoginResponse;
import com.cloudvps.system.entity.User;
import com.cloudvps.system.service.AuthService;
import com.cloudvps.system.service.UserService;
import com.cloudvps.system.convert.UserConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 认证服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl implements AuthService {
    
    private final UserService userService;
    private final UserConvert userConvert;
    private final JwtUtils jwtUtils;

    // 使用内存存储替代Redis（生产环境应使用Redis）
    private final ConcurrentHashMap<String, String> tokenBlacklist = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Long, String> refreshTokenStore = new ConcurrentHashMap<>();
    
    @Value("${cloudvps.jwt.expiration}")
    private Long jwtExpiration;
    
    @Override
    public LoginResponse login(UserLoginRequest request) {
        log.info("用户登录: username={}", request.getUsername());
        
        // 验证用户凭据
        User user = userService.authenticate(request.getUsername(), request.getPassword());
        
        // 生成令牌
        String accessToken = generateAccessToken(user);
        String refreshToken = generateRefreshToken(user);
        
        // 存储刷新令牌到内存
        refreshTokenStore.put(user.getId(), refreshToken);
        
        // 更新最后登录时间
        userService.updateLastLoginTime(user.getId());
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(accessToken);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(jwtExpiration / 1000);
        response.setUser(userConvert.toResponse(user));
        
        log.info("用户登录成功: userId={}, username={}", user.getId(), user.getUsername());
        return response;
    }
    
    @Override
    public LoginResponse refreshToken(String refreshToken) {
        log.debug("刷新令牌: refreshToken={}", refreshToken);
        
        try {
            // 验证刷新令牌
            if (!jwtUtils.validateToken(refreshToken)) {
                throw new BusinessException(ResponseCode.UNAUTHORIZED, "刷新令牌无效");
            }

            String username = jwtUtils.getUsernameFromToken(refreshToken);
            User user = userService.findByUsername(username);
            
            // 检查内存中的刷新令牌
            String storedRefreshToken = refreshTokenStore.get(user.getId());

            if (!refreshToken.equals(storedRefreshToken)) {
                throw new BusinessException(ResponseCode.UNAUTHORIZED, "刷新令牌无效");
            }
            
            // 生成新的令牌
            String newAccessToken = generateAccessToken(user);
            String newRefreshToken = generateRefreshToken(user);
            
            // 更新内存中的刷新令牌
            refreshTokenStore.put(user.getId(), newRefreshToken);
            
            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setToken(newAccessToken);
            response.setRefreshToken(newRefreshToken);
            response.setExpiresIn(jwtExpiration / 1000);
            response.setUser(userConvert.toResponse(user));
            
            log.info("令牌刷新成功: userId={}, username={}", user.getId(), user.getUsername());
            return response;
            
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            throw new BusinessException(ResponseCode.UNAUTHORIZED, "刷新令牌失败");
        }
    }
    
    @Override
    public void logout(String token) {
        log.info("用户登出: token={}", token);
        
        try {
            // 将令牌加入黑名单
            tokenBlacklist.put(token, "blacklisted");

            // 获取用户信息并删除刷新令牌
            String username = jwtUtils.getUsernameFromToken(token);
            User user = userService.findByUsername(username);
            refreshTokenStore.remove(user.getId());
            
            log.info("用户登出成功: userId={}, username={}", user.getId(), user.getUsername());
            
        } catch (Exception e) {
            log.error("用户登出失败", e);
            // 登出失败不抛异常，只记录日志
        }
    }
    
    @Override
    public User validateToken(String token) {
        try {
            // 检查令牌是否在黑名单中
            if (tokenBlacklist.containsKey(token)) {
                throw new BusinessException(ResponseCode.UNAUTHORIZED, "令牌已失效");
            }
            
            // 验证令牌
            if (!jwtUtils.validateToken(token)) {
                throw new BusinessException(ResponseCode.UNAUTHORIZED, "令牌无效");
            }

            String username = jwtUtils.getUsernameFromToken(token);
            return userService.findByUsername(username);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("令牌验证失败", e);
            throw new BusinessException(ResponseCode.UNAUTHORIZED, "令牌验证失败");
        }
    }
    
    @Override
    public String generateAccessToken(User user) {
        return jwtUtils.generateToken(user.getId(), user.getUsername());
    }

    @Override
    public String generateRefreshToken(User user) {
        // 使用JwtUtils生成刷新令牌（可以设置更长的过期时间）
        return jwtUtils.generateToken(user.getId(), user.getUsername());
    }

    @Override
    public String getUsernameFromToken(String token) {
        return jwtUtils.getUsernameFromToken(token);
    }

    @Override
    public boolean isTokenValid(String token) {
        try {
            // 检查令牌是否在黑名单中
            if (tokenBlacklist.containsKey(token)) {
                return false;
            }

            return jwtUtils.validateToken(token);
        } catch (Exception e) {
            return false;
        }
    }
}
