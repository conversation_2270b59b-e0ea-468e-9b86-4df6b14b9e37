package com.cloudvps.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudvps.system.entity.UserLoginLog;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户登录日志数据访问Mapper
 * 使用MyBatis-Plus实现登录日志的数据访问操作
 * 只保留实际使用的方法，其他CRUD操作使用MyBatis-Plus自带方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface UserLoginLogDataMapper extends BaseMapper<UserLoginLog> {
    // 所有需要的方法都通过MyBatis-Plus的BaseMapper提供
    // 包括：selectPage, selectById, selectOne, insert, updateById,
    //      deleteById, deleteBatchIds, selectCount, selectList等
    // 复杂查询通过LambdaQueryWrapper在Service层实现
}
