package com.cloudvps.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.system.api.enums.ConfigType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统配置实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("system_configs")
public class SystemConfig extends BaseEntity {
    
    /**
     * 配置键
     */
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;
    
    /**
     * 配置类型
     */
    @TableField("config_type")
    private ConfigType configType = ConfigType.STRING;
    
    /**
     * 配置描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 是否加密
     */
    @TableField("is_encrypted")
    private Boolean isEncrypted = false;
    
    /**
     * 是否系统配置
     */
    @TableField("is_system")
    private Boolean isSystem = false;
    
    /**
     * 获取配置值（根据类型转换）
     */
    public Object getTypedValue() {
        if (configValue == null) {
            return null;
        }
        
        return switch (configType) {
            case INTEGER -> Integer.valueOf(configValue);
            case BOOLEAN -> Boolean.valueOf(configValue);
            case JSON -> configValue; // JSON字符串，由调用方解析
            default -> configValue;
        };
    }
    
    /**
     * 设置配置值（自动转换为字符串）
     */
    public void setTypedValue(Object value) {
        if (value == null) {
            this.configValue = null;
        } else {
            this.configValue = value.toString();
        }
    }
}
