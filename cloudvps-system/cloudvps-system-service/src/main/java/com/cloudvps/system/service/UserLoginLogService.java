package com.cloudvps.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.system.api.dto.request.UserLoginLogCreateRequest;
import com.cloudvps.system.api.dto.request.UserLoginLogQueryRequest;
import com.cloudvps.system.api.dto.response.UserLoginLogResponse;
import com.cloudvps.system.api.enums.LoginResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户登录日志服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserLoginLogService {
    
    /**
     * 分页查询登录日志
     */
    IPage<UserLoginLogResponse> queryLoginLogs(UserLoginLogQueryRequest request);
    
    /**
     * 根据ID获取登录日志
     */
    UserLoginLogResponse findById(Long id);
    
    /**
     * 创建登录日志
     */
    UserLoginLogResponse createLoginLog(UserLoginLogCreateRequest request);
    
    /**
     * 删除登录日志
     */
    void deleteLoginLog(Long id);
    
    /**
     * 批量删除登录日志
     */
    void batchDeleteLoginLogs(List<Long> logIds);
    
    /**
     * 根据用户ID获取登录日志
     */
    List<UserLoginLogResponse> findByUserId(Long userId);
    
    /**
     * 根据用户ID和时间范围获取登录日志
     */
    List<UserLoginLogResponse> findByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据登录结果获取日志
     */
    List<UserLoginLogResponse> findByLoginResult(LoginResult loginResult);
    
    /**
     * 根据IP地址获取登录日志
     */
    List<UserLoginLogResponse> findByLoginIp(String loginIp);
    
    /**
     * 根据时间范围获取登录日志
     */
    List<UserLoginLogResponse> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计用户登录次数
     */
    Long countByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计登录失败次数
     */
    Long countFailuresByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 批量创建登录日志
     */
    void batchCreateLoginLogs(List<UserLoginLogCreateRequest> requests);
    
    /**
     * 删除指定时间之前的登录日志
     */
    void deleteLogsBefore(LocalDateTime beforeTime);
    
    /**
     * 记录登录成功日志
     */
    UserLoginLogResponse recordLoginSuccess(Long userId, String loginIp, String loginLocation, String userAgent);
    
    /**
     * 记录登录失败日志
     */
    UserLoginLogResponse recordLoginFailure(Long userId, String loginIp, String loginLocation, String userAgent, String failureReason);
}
