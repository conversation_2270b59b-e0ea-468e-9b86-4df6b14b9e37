package com.cloudvps.system.service;

import com.cloudvps.system.api.dto.request.UserCreateRequest;
import com.cloudvps.system.api.dto.request.UserQueryRequest;
import com.cloudvps.system.api.dto.request.UserRegisterRequest;
import com.cloudvps.system.api.dto.request.UserUpdateRequest;
import com.cloudvps.system.api.dto.response.UserResponse;
import com.cloudvps.system.entity.User;
import com.cloudvps.system.api.enums.UserStatus;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserService {
    
    /**
     * 创建用户
     *
     * @param request 创建请求
     * @return 用户信息
     */
    UserResponse createUser(UserCreateRequest request);

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 用户信息
     */
    UserResponse register(UserRegisterRequest request);
    
    /**
     * 用户登录验证
     *
     * @param username 用户名或邮箱
     * @param password 密码
     * @return 用户实体
     */
    User authenticate(String username, String password);
    
    /**
     * 根据ID查找用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    UserResponse findById(Long id);
    
    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户实体
     */
    User findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户实体
     */
    User findByEmail(String email);
    
    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param request 更新请求
     * @return 更新后的用户信息
     */
    UserResponse updateUser(Long userId, UserUpdateRequest request);

    /**
     * 更新个人信息
     *
     * @param userId 用户ID
     * @param request 更新请求
     * @return 更新后的用户信息
     */
    UserResponse updateProfile(Long userId, UserUpdateRequest request);
    
    /**
     * 修改密码
     *
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     */
    void changePassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 更新用户状态
     *
     * @param userId 用户ID
     * @param status 新状态
     * @return 更新后的用户信息
     */
    UserResponse updateStatus(Long userId, UserStatus status);
    
    /**
     * 根据查询条件分页查询用户
     *
     * @param request 查询请求
     * @return 用户分页列表
     */
    IPage<UserResponse> queryUsers(UserQueryRequest request);
    
    /**
     * 获取用户角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getUserRoleIds(Long userId);

    /**
     * 为用户分配角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void assignRoles(Long userId, List<Long> roleIds);

    /**
     * 移除用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void removeRoles(Long userId, List<Long> roleIds);
    
    /**
     * 更新最后登录时间
     *
     * @param userId 用户ID
     */
    void updateLastLoginTime(Long userId);
    
    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     */
    void deleteUser(Long userId);

    /**
     * 批量删除用户
     *
     * @param userIds 用户ID列表
     */
    void batchDeleteUsers(List<Long> userIds);
}
