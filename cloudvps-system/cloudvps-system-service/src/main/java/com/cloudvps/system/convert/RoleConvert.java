package com.cloudvps.system.convert;

import com.cloudvps.system.api.dto.request.RoleCreateRequest;
import com.cloudvps.system.api.dto.request.RoleUpdateRequest;
import com.cloudvps.system.api.dto.response.RoleResponse;
import com.cloudvps.system.entity.Role;
import org.mapstruct.*;

/**
 * 角色对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface RoleConvert {
    
    /**
     * 创建请求转实体
     */
    Role toEntity(RoleCreateRequest request);
    
    /**
     * 实体转响应
     */
    RoleResponse toResponse(Role role);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateFromRequest(@MappingTarget Role role, RoleUpdateRequest request);
}
