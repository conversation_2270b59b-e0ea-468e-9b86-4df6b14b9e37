package com.cloudvps.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.system.api.dto.request.PermissionCreateRequest;
import com.cloudvps.system.api.dto.request.PermissionQueryRequest;
import com.cloudvps.system.api.dto.request.PermissionUpdateRequest;
import com.cloudvps.system.api.dto.response.PermissionResponse;
import com.cloudvps.system.api.enums.ResourceType;

import java.util.List;

/**
 * 权限服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PermissionService {
    
    /**
     * 分页查询权限
     */
    IPage<PermissionResponse> queryPermissions(PermissionQueryRequest request);
    
    /**
     * 根据ID获取权限
     */
    PermissionResponse findById(Long id);
    
    /**
     * 根据权限编码获取权限
     */
    PermissionResponse findByPermissionCode(String permissionCode);
    
    /**
     * 创建权限
     */
    PermissionResponse createPermission(PermissionCreateRequest request);
    
    /**
     * 更新权限
     */
    PermissionResponse updatePermission(Long id, PermissionUpdateRequest request);
    
    /**
     * 删除权限
     */
    void deletePermission(Long id);
    
    /**
     * 批量删除权限
     */
    void batchDeletePermissions(List<Long> permissionIds);
    
    /**
     * 检查权限编码是否存在
     */
    boolean existsByPermissionCode(String permissionCode);
    
    /**
     * 根据资源类型获取权限列表
     */
    List<PermissionResponse> findByResourceType(ResourceType resourceType);
    
    /**
     * 根据资源路径获取权限列表
     */
    List<PermissionResponse> findByResourcePath(String resourcePath);
    
    /**
     * 获取所有权限列表
     */
    List<PermissionResponse> findAllPermissions();
}
