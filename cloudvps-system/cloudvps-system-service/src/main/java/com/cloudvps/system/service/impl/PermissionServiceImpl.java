package com.cloudvps.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.system.api.dto.request.PermissionCreateRequest;
import com.cloudvps.system.api.dto.request.PermissionQueryRequest;
import com.cloudvps.system.api.dto.request.PermissionUpdateRequest;
import com.cloudvps.system.api.dto.response.PermissionResponse;
import com.cloudvps.system.api.enums.ResourceType;
import com.cloudvps.system.convert.PermissionConvert;
import com.cloudvps.system.entity.Permission;
import com.cloudvps.system.mapper.PermissionDataMapper;
import com.cloudvps.system.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PermissionServiceImpl implements PermissionService {
    
    private final PermissionDataMapper permissionDataMapper;
    private final PermissionConvert permissionConvert;
    
    @Override
    public IPage<PermissionResponse> queryPermissions(PermissionQueryRequest request) {
        LambdaQueryWrapper<Permission> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(request.getPermissionCode())) {
            queryWrapper.like(Permission::getPermissionCode, request.getPermissionCode());
        }
        if (StringUtils.hasText(request.getPermissionName())) {
            queryWrapper.like(Permission::getPermissionName, request.getPermissionName());
        }
        if (request.getResourceType() != null) {
            queryWrapper.eq(Permission::getResourceType, request.getResourceType());
        }
        if (StringUtils.hasText(request.getResourcePath())) {
            queryWrapper.like(Permission::getResourcePath, request.getResourcePath());
        }
        if (StringUtils.hasText(request.getHttpMethod())) {
            queryWrapper.eq(Permission::getHttpMethod, request.getHttpMethod());
        }
        
        // 排序
        queryWrapper.orderByAsc(Permission::getSortOrder)
                   .orderByDesc(Permission::getCreatedTime);
        
        // 分页查询
        Page<Permission> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<Permission> permissionPage = permissionDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return permissionPage.convert(permissionConvert::toResponse);
    }
    
    @Override
    public PermissionResponse findById(Long id) {
        Permission permission = permissionDataMapper.selectById(id);
        if (permission == null) {
            throw new BusinessException("权限不存在");
        }
        return permissionConvert.toResponse(permission);
    }
    
    @Override
    public PermissionResponse findByPermissionCode(String permissionCode) {
        LambdaQueryWrapper<Permission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Permission::getPermissionCode, permissionCode);
        
        Permission permission = permissionDataMapper.selectOne(queryWrapper);
        if (permission == null) {
            throw new BusinessException("权限不存在");
        }
        return permissionConvert.toResponse(permission);
    }
    
    @Override
    @Transactional
    public PermissionResponse createPermission(PermissionCreateRequest request) {
        // 检查权限编码是否已存在
        if (existsByPermissionCode(request.getPermissionCode())) {
            throw new BusinessException("权限编码已存在");
        }
        
        Permission permission = permissionConvert.toEntity(request);
        permissionDataMapper.insert(permission);
        
        log.info("创建权限成功: permissionCode={}, permissionName={}", 
                permission.getPermissionCode(), permission.getPermissionName());
        return permissionConvert.toResponse(permission);
    }
    
    @Override
    @Transactional
    public PermissionResponse updatePermission(Long id, PermissionUpdateRequest request) {
        Permission existingPermission = permissionDataMapper.selectById(id);
        if (existingPermission == null) {
            throw new BusinessException("权限不存在");
        }
        
        // 更新字段
        permissionConvert.updateFromRequest(existingPermission, request);
        permissionDataMapper.updateById(existingPermission);
        
        log.info("更新权限成功: permissionId={}, permissionName={}", 
                id, existingPermission.getPermissionName());
        return permissionConvert.toResponse(existingPermission);
    }
    
    @Override
    @Transactional
    public void deletePermission(Long id) {
        Permission permission = permissionDataMapper.selectById(id);
        if (permission == null) {
            throw new BusinessException("权限不存在");
        }
        
        // 删除权限
        permissionDataMapper.deleteById(id);
        
        log.info("删除权限成功: permissionId={}, permissionCode={}", 
                id, permission.getPermissionCode());
    }
    
    @Override
    @Transactional
    public void batchDeletePermissions(List<Long> permissionIds) {
        if (permissionIds == null || permissionIds.isEmpty()) {
            return;
        }
        
        // 批量删除权限
        permissionDataMapper.deleteBatchIds(permissionIds);
        
        log.info("批量删除权限成功: permissionIds={}", permissionIds);
    }
    
    @Override
    public boolean existsByPermissionCode(String permissionCode) {
        LambdaQueryWrapper<Permission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Permission::getPermissionCode, permissionCode);
        return permissionDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public List<PermissionResponse> findByResourceType(ResourceType resourceType) {
        LambdaQueryWrapper<Permission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Permission::getResourceType, resourceType)
                   .orderByAsc(Permission::getSortOrder);
        
        List<Permission> permissions = permissionDataMapper.selectList(queryWrapper);
        return permissions.stream()
                         .map(permissionConvert::toResponse)
                         .collect(Collectors.toList());
    }
    
    @Override
    public List<PermissionResponse> findByResourcePath(String resourcePath) {
        LambdaQueryWrapper<Permission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Permission::getResourcePath, resourcePath)
                   .orderByAsc(Permission::getSortOrder);
        
        List<Permission> permissions = permissionDataMapper.selectList(queryWrapper);
        return permissions.stream()
                         .map(permissionConvert::toResponse)
                         .collect(Collectors.toList());
    }
    
    @Override
    public List<PermissionResponse> findAllPermissions() {
        LambdaQueryWrapper<Permission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(Permission::getSortOrder)
                   .orderByDesc(Permission::getCreatedTime);
        
        List<Permission> permissions = permissionDataMapper.selectList(queryWrapper);
        return permissions.stream()
                         .map(permissionConvert::toResponse)
                         .collect(Collectors.toList());
    }
}
