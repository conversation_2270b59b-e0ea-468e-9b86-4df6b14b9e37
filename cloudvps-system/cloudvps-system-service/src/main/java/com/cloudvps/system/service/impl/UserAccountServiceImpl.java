package com.cloudvps.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.system.api.dto.request.UserAccountCreateRequest;
import com.cloudvps.system.api.dto.request.UserAccountQueryRequest;
import com.cloudvps.system.api.dto.request.UserAccountUpdateRequest;
import com.cloudvps.system.api.dto.response.UserAccountResponse;
import com.cloudvps.system.convert.UserAccountConvert;
import com.cloudvps.system.entity.User;
import com.cloudvps.system.entity.UserAccount;
import com.cloudvps.system.mapper.UserAccountDataMapper;
import com.cloudvps.system.mapper.UserDataMapper;
import com.cloudvps.system.service.UserAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户账户服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserAccountServiceImpl implements UserAccountService {
    
    private final UserAccountDataMapper userAccountDataMapper;
    private final UserDataMapper userDataMapper;
    private final UserAccountConvert userAccountConvert;
    
    @Override
    public IPage<UserAccountResponse> queryAccounts(UserAccountQueryRequest request) {
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (request.getUserId() != null) {
            queryWrapper.eq(UserAccount::getUserId, request.getUserId());
        }
        if (request.getMinBalance() != null) {
            queryWrapper.ge(UserAccount::getBalance, request.getMinBalance());
        }
        if (request.getMaxBalance() != null) {
            queryWrapper.le(UserAccount::getBalance, request.getMaxBalance());
        }
        if (request.getIsLocked() != null) {
            queryWrapper.eq(UserAccount::getIsLocked, request.getIsLocked());
        }
        if (request.getLockReason() != null) {
            queryWrapper.like(UserAccount::getLockReason, request.getLockReason());
        }
        
        // 排序
        queryWrapper.orderByDesc(UserAccount::getCreatedTime);
        
        // 分页查询
        Page<UserAccount> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<UserAccount> accountPage = userAccountDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return accountPage.convert(userAccountConvert::toResponse);
    }
    
    @Override
    public UserAccountResponse findById(Long id) {
        UserAccount account = userAccountDataMapper.selectById(id);
        if (account == null) {
            throw new BusinessException("用户账户不存在");
        }
        return userAccountConvert.toResponse(account);
    }
    
    @Override
    public UserAccountResponse findByUserId(Long userId) {
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccount::getUserId, userId);
        
        UserAccount account = userAccountDataMapper.selectOne(queryWrapper);
        if (account == null) {
            throw new BusinessException("用户账户不存在");
        }
        return userAccountConvert.toResponse(account);
    }
    
    @Override
    @Transactional
    public UserAccountResponse createAccount(UserAccountCreateRequest request) {
        // 检查用户是否存在
        User user = userDataMapper.selectById(request.getUserId());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查用户是否已有账户
        if (existsByUserId(request.getUserId())) {
            throw new BusinessException("用户已有账户");
        }
        
        UserAccount account = userAccountConvert.toEntity(request);
        userAccountDataMapper.insert(account);
        
        log.info("创建用户账户成功: userId={}, balance={}", 
                account.getUserId(), account.getBalance());
        return userAccountConvert.toResponse(account);
    }
    
    @Override
    @Transactional
    public UserAccountResponse updateAccount(Long id, UserAccountUpdateRequest request) {
        UserAccount existingAccount = userAccountDataMapper.selectById(id);
        if (existingAccount == null) {
            throw new BusinessException("用户账户不存在");
        }
        
        // 更新字段
        userAccountConvert.updateFromRequest(existingAccount, request);
        userAccountDataMapper.updateById(existingAccount);
        
        log.info("更新用户账户成功: accountId={}, userId={}", 
                id, existingAccount.getUserId());
        return userAccountConvert.toResponse(existingAccount);
    }
    
    @Override
    @Transactional
    public void deleteAccount(Long id) {
        UserAccount account = userAccountDataMapper.selectById(id);
        if (account == null) {
            throw new BusinessException("用户账户不存在");
        }
        
        // 检查账户余额
        if (account.getBalance().compareTo(BigDecimal.ZERO) > 0) {
            throw new BusinessException("账户有余额，无法删除");
        }
        
        // 删除账户
        userAccountDataMapper.deleteById(id);
        
        log.info("删除用户账户成功: accountId={}, userId={}", 
                id, account.getUserId());
    }
    
    @Override
    @Transactional
    public void batchDeleteAccounts(List<Long> accountIds) {
        if (accountIds == null || accountIds.isEmpty()) {
            return;
        }
        
        // 检查账户余额
        for (Long accountId : accountIds) {
            UserAccount account = userAccountDataMapper.selectById(accountId);
            if (account != null && account.getBalance().compareTo(BigDecimal.ZERO) > 0) {
                throw new BusinessException("存在有余额的账户，无法删除");
            }
        }
        
        // 批量删除账户
        userAccountDataMapper.deleteBatchIds(accountIds);
        
        log.info("批量删除用户账户成功: accountIds={}", accountIds);
    }
    
    @Override
    public boolean existsByUserId(Long userId) {
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccount::getUserId, userId);
        return userAccountDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    @Transactional
    public UserAccountResponse lockAccount(Long id, String lockReason) {
        UserAccount account = userAccountDataMapper.selectById(id);
        if (account == null) {
            throw new BusinessException("用户账户不存在");
        }
        
        if (account.getIsLocked()) {
            throw new BusinessException("账户已被锁定");
        }
        
        account.setIsLocked(true);
        account.setLockReason(lockReason);
        account.setLastOperationTime(LocalDateTime.now());
        account.setLastOperationDescription("锁定账户: " + lockReason);
        userAccountDataMapper.updateById(account);
        
        log.info("锁定用户账户成功: accountId={}, userId={}, reason={}", 
                id, account.getUserId(), lockReason);
        return userAccountConvert.toResponse(account);
    }
    
    @Override
    @Transactional
    public UserAccountResponse unlockAccount(Long id) {
        UserAccount account = userAccountDataMapper.selectById(id);
        if (account == null) {
            throw new BusinessException("用户账户不存在");
        }
        
        if (!account.getIsLocked()) {
            throw new BusinessException("账户未被锁定");
        }
        
        account.setIsLocked(false);
        account.setLockReason(null);
        account.setLastOperationTime(LocalDateTime.now());
        account.setLastOperationDescription("解锁账户");
        userAccountDataMapper.updateById(account);
        
        log.info("解锁用户账户成功: accountId={}, userId={}", 
                id, account.getUserId());
        return userAccountConvert.toResponse(account);
    }
    
    @Override
    @Transactional
    public UserAccountResponse recharge(Long userId, BigDecimal amount, String description) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("充值金额必须大于0");
        }
        
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccount::getUserId, userId);
        UserAccount account = userAccountDataMapper.selectOne(queryWrapper);
        
        if (account == null) {
            throw new BusinessException("用户账户不存在");
        }
        
        if (account.getIsLocked()) {
            throw new BusinessException("账户已被锁定，无法充值");
        }
        
        // 更新余额和累计充值
        account.setBalance(account.getBalance().add(amount));
        account.setTotalRecharge(account.getTotalRecharge().add(amount));
        account.setLastOperationTime(LocalDateTime.now());
        account.setLastOperationDescription(description != null ? description : "充值: " + amount);
        userAccountDataMapper.updateById(account);
        
        log.info("用户充值成功: userId={}, amount={}, balance={}", 
                userId, amount, account.getBalance());
        return userAccountConvert.toResponse(account);
    }
    
    @Override
    @Transactional
    public UserAccountResponse freezeAmount(Long userId, BigDecimal amount, String description) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("冻结金额必须大于0");
        }
        
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccount::getUserId, userId);
        UserAccount account = userAccountDataMapper.selectOne(queryWrapper);
        
        if (account == null) {
            throw new BusinessException("用户账户不存在");
        }
        
        if (account.getIsLocked()) {
            throw new BusinessException("账户已被锁定，无法操作");
        }
        
        // 检查可用余额
        BigDecimal availableBalance = account.getBalance().subtract(account.getFrozenAmount());
        if (availableBalance.compareTo(amount) < 0) {
            throw new BusinessException("可用余额不足");
        }
        
        // 冻结金额
        account.setFrozenAmount(account.getFrozenAmount().add(amount));
        account.setLastOperationTime(LocalDateTime.now());
        account.setLastOperationDescription(description != null ? description : "冻结金额: " + amount);
        userAccountDataMapper.updateById(account);
        
        log.info("冻结金额成功: userId={}, amount={}, frozenAmount={}", 
                userId, amount, account.getFrozenAmount());
        return userAccountConvert.toResponse(account);
    }
    
    @Override
    @Transactional
    public UserAccountResponse unfreezeAmount(Long userId, BigDecimal amount, String description) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("解冻金额必须大于0");
        }
        
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccount::getUserId, userId);
        UserAccount account = userAccountDataMapper.selectOne(queryWrapper);
        
        if (account == null) {
            throw new BusinessException("用户账户不存在");
        }
        
        if (account.getFrozenAmount().compareTo(amount) < 0) {
            throw new BusinessException("冻结金额不足");
        }
        
        // 解冻金额
        account.setFrozenAmount(account.getFrozenAmount().subtract(amount));
        account.setLastOperationTime(LocalDateTime.now());
        account.setLastOperationDescription(description != null ? description : "解冻金额: " + amount);
        userAccountDataMapper.updateById(account);
        
        log.info("解冻金额成功: userId={}, amount={}, frozenAmount={}", 
                userId, amount, account.getFrozenAmount());
        return userAccountConvert.toResponse(account);
    }
    
    @Override
    @Transactional
    public UserAccountResponse deductBalance(Long userId, BigDecimal amount, String description) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("扣减金额必须大于0");
        }
        
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccount::getUserId, userId);
        UserAccount account = userAccountDataMapper.selectOne(queryWrapper);
        
        if (account == null) {
            throw new BusinessException("用户账户不存在");
        }
        
        if (account.getFrozenAmount().compareTo(amount) < 0) {
            throw new BusinessException("冻结金额不足");
        }
        
        // 从冻结金额和余额中扣减
        account.setBalance(account.getBalance().subtract(amount));
        account.setFrozenAmount(account.getFrozenAmount().subtract(amount));
        account.setTotalConsume(account.getTotalConsume().add(amount));
        account.setLastOperationTime(LocalDateTime.now());
        account.setLastOperationDescription(description != null ? description : "扣减余额: " + amount);
        userAccountDataMapper.updateById(account);
        
        log.info("扣减余额成功: userId={}, amount={}, balance={}", 
                userId, amount, account.getBalance());
        return userAccountConvert.toResponse(account);
    }
    
    @Override
    public List<UserAccountResponse> findLockedAccounts() {
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccount::getIsLocked, true)
                   .orderByDesc(UserAccount::getCreatedTime);
        
        List<UserAccount> accounts = userAccountDataMapper.selectList(queryWrapper);
        return accounts.stream()
                      .map(userAccountConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    public List<UserAccountResponse> findUnlockedAccounts() {
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccount::getIsLocked, false)
                   .orderByDesc(UserAccount::getCreatedTime);
        
        List<UserAccount> accounts = userAccountDataMapper.selectList(queryWrapper);
        return accounts.stream()
                      .map(userAccountConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    public List<UserAccountResponse> findByBalanceRange(BigDecimal minBalance, BigDecimal maxBalance) {
        LambdaQueryWrapper<UserAccount> queryWrapper = new LambdaQueryWrapper<>();
        
        if (minBalance != null) {
            queryWrapper.ge(UserAccount::getBalance, minBalance);
        }
        if (maxBalance != null) {
            queryWrapper.le(UserAccount::getBalance, maxBalance);
        }
        
        queryWrapper.orderByDesc(UserAccount::getBalance);
        
        List<UserAccount> accounts = userAccountDataMapper.selectList(queryWrapper);
        return accounts.stream()
                      .map(userAccountConvert::toResponse)
                      .collect(Collectors.toList());
    }
}
