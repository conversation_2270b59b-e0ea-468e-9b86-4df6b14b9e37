package com.cloudvps.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.system.api.dto.request.SystemConfigCreateRequest;
import com.cloudvps.system.api.dto.request.SystemConfigQueryRequest;
import com.cloudvps.system.api.dto.request.SystemConfigUpdateRequest;
import com.cloudvps.system.api.dto.response.SystemConfigResponse;
import com.cloudvps.system.api.enums.ConfigType;

import java.util.List;

/**
 * 系统配置服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SystemConfigService {
    
    /**
     * 分页查询系统配置
     */
    IPage<SystemConfigResponse> queryConfigs(SystemConfigQueryRequest request);
    
    /**
     * 根据ID获取系统配置
     */
    SystemConfigResponse findById(Long id);
    
    /**
     * 根据配置键获取系统配置
     */
    SystemConfigResponse findByConfigKey(String configKey);
    
    /**
     * 创建系统配置
     */
    SystemConfigResponse createConfig(SystemConfigCreateRequest request);
    
    /**
     * 更新系统配置
     */
    SystemConfigResponse updateConfig(Long id, SystemConfigUpdateRequest request);
    
    /**
     * 删除系统配置
     */
    void deleteConfig(Long id);
    
    /**
     * 批量删除系统配置
     */
    void batchDeleteConfigs(List<Long> configIds);
    
    /**
     * 检查配置键是否存在
     */
    boolean existsByConfigKey(String configKey);
    
    /**
     * 根据配置类型获取配置列表
     */
    List<SystemConfigResponse> findByConfigType(ConfigType configType);
    
    /**
     * 获取系统配置列表
     */
    List<SystemConfigResponse> findSystemConfigs();
    
    /**
     * 获取非系统配置列表
     */
    List<SystemConfigResponse> findUserConfigs();
    
    /**
     * 根据配置键获取配置值
     */
    String getConfigValue(String configKey);
    
    /**
     * 根据配置键获取配置值（带默认值）
     */
    String getConfigValue(String configKey, String defaultValue);
    
    /**
     * 根据配置键获取类型化配置值
     */
    <T> T getTypedConfigValue(String configKey, Class<T> type);
    
    /**
     * 根据配置键获取类型化配置值（带默认值）
     */
    <T> T getTypedConfigValue(String configKey, Class<T> type, T defaultValue);
    
    /**
     * 更新配置值
     */
    void updateConfigValue(String configKey, String configValue);
}
