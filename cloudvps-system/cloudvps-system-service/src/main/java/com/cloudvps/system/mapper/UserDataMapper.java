package com.cloudvps.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.system.api.dto.request.UserQueryRequest;
import com.cloudvps.system.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户数据访问接口 - MyBatis-Plus实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface UserDataMapper extends BaseMapper<User> {
    
    /**
     * 根据ID查找用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    User findById(@Param("id") Long id);

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    User findByEmail(@Param("email") String email);

    /**
     * 根据用户名或邮箱查找用户
     *
     * @param username 用户名
     * @param email 邮箱
     * @return 用户信息
     */
    User findByUsernameOrEmail(@Param("username") String username, @Param("email") String email);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(@Param("email") String email);

    /**
     * 插入用户
     *
     * @param user 用户实体
     * @return 影响行数
     */
    int insertUser(User user);

    /**
     * 更新用户信息
     *
     * @param user 用户实体
     * @return 影响行数
     */
    int updateUser(User user);

    /**
     * 更新用户状态
     *
     * @param userId 用户ID
     * @param status 新状态
     * @param version 版本号（乐观锁）
     * @return 影响行数
     */
    int updateUserStatus(@Param("userId") Long userId, @Param("status") String status, @Param("version") Long version);

    /**
     * 更新最后登录时间
     *
     * @param userId 用户ID
     * @param lastLoginTime 最后登录时间
     * @param version 版本号（乐观锁）
     * @return 影响行数
     */
    int updateLastLoginTime(@Param("userId") Long userId, @Param("lastLoginTime") java.time.LocalDateTime lastLoginTime, @Param("version") Long version);

    /**
     * 更新用户密码
     *
     * @param userId 用户ID
     * @param password 新密码（已加密）
     * @param version 版本号（乐观锁）
     * @return 影响行数
     */
    int updatePassword(@Param("userId") Long userId, @Param("password") String password, @Param("version") Long version);

    /**
     * 动态条件查询用户（分页）
     * 使用XML实现复杂的动态查询
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页查询结果
     */
    IPage<User> queryUsersWithConditions(Page<User> page, @Param("request") UserQueryRequest request);
}
