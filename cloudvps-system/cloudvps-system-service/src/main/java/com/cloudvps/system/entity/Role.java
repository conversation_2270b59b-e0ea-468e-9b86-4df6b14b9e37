package com.cloudvps.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.system.api.enums.RoleStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 角色实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("roles")
@Data
@EqualsAndHashCode(callSuper = true)
public class Role extends BaseEntity {
    
    /**
     * 角色编码
     */
    @TableField("role_code")
    private String roleCode;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 角色描述
     */
    @TableField("description")
    private String description;

    /**
     * 角色状态
     */
    @TableField("status")
    private RoleStatus status = RoleStatus.ACTIVE;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder = 0;
}
