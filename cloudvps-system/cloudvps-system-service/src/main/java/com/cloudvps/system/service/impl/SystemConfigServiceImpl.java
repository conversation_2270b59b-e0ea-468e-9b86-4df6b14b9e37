package com.cloudvps.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.system.api.dto.request.SystemConfigCreateRequest;
import com.cloudvps.system.api.dto.request.SystemConfigQueryRequest;
import com.cloudvps.system.api.dto.request.SystemConfigUpdateRequest;
import com.cloudvps.system.api.dto.response.SystemConfigResponse;
import com.cloudvps.system.api.enums.ConfigType;
import com.cloudvps.system.convert.SystemConfigConvert;
import com.cloudvps.system.entity.SystemConfig;
import com.cloudvps.system.mapper.SystemConfigDataMapper;
import com.cloudvps.system.service.SystemConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统配置服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SystemConfigServiceImpl implements SystemConfigService {
    
    private final SystemConfigDataMapper systemConfigDataMapper;
    private final SystemConfigConvert systemConfigConvert;
    
    @Override
    public IPage<SystemConfigResponse> queryConfigs(SystemConfigQueryRequest request) {
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(request.getConfigKey())) {
            queryWrapper.like(SystemConfig::getConfigKey, request.getConfigKey());
        }
        if (request.getConfigType() != null) {
            queryWrapper.eq(SystemConfig::getConfigType, request.getConfigType());
        }
        if (request.getIsEncrypted() != null) {
            queryWrapper.eq(SystemConfig::getIsEncrypted, request.getIsEncrypted());
        }
        if (request.getIsSystem() != null) {
            queryWrapper.eq(SystemConfig::getIsSystem, request.getIsSystem());
        }
        
        // 排序
        queryWrapper.orderByDesc(SystemConfig::getCreatedTime);
        
        // 分页查询
        Page<SystemConfig> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<SystemConfig> configPage = systemConfigDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return configPage.convert(systemConfigConvert::toResponse);
    }
    
    @Override
    public SystemConfigResponse findById(Long id) {
        SystemConfig config = systemConfigDataMapper.selectById(id);
        if (config == null) {
            throw new BusinessException("系统配置不存在");
        }
        return systemConfigConvert.toResponse(config);
    }
    
    @Override
    public SystemConfigResponse findByConfigKey(String configKey) {
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getConfigKey, configKey);
        
        SystemConfig config = systemConfigDataMapper.selectOne(queryWrapper);
        if (config == null) {
            throw new BusinessException("系统配置不存在");
        }
        return systemConfigConvert.toResponse(config);
    }
    
    @Override
    @Transactional
    public SystemConfigResponse createConfig(SystemConfigCreateRequest request) {
        // 检查配置键是否已存在
        if (existsByConfigKey(request.getConfigKey())) {
            throw new BusinessException("配置键已存在");
        }
        
        SystemConfig config = systemConfigConvert.toEntity(request);
        systemConfigDataMapper.insert(config);
        
        log.info("创建系统配置成功: configKey={}", config.getConfigKey());
        return systemConfigConvert.toResponse(config);
    }
    
    @Override
    @Transactional
    public SystemConfigResponse updateConfig(Long id, SystemConfigUpdateRequest request) {
        SystemConfig existingConfig = systemConfigDataMapper.selectById(id);
        if (existingConfig == null) {
            throw new BusinessException("系统配置不存在");
        }
        
        // 系统配置不允许修改某些字段
        if (existingConfig.getIsSystem() && request.getIsSystem() != null && !request.getIsSystem()) {
            throw new BusinessException("系统配置不允许修改为非系统配置");
        }
        
        // 更新字段
        systemConfigConvert.updateFromRequest(existingConfig, request);
        systemConfigDataMapper.updateById(existingConfig);
        
        log.info("更新系统配置成功: configId={}, configKey={}", id, existingConfig.getConfigKey());
        return systemConfigConvert.toResponse(existingConfig);
    }
    
    @Override
    @Transactional
    public void deleteConfig(Long id) {
        SystemConfig config = systemConfigDataMapper.selectById(id);
        if (config == null) {
            throw new BusinessException("系统配置不存在");
        }
        
        // 系统配置不允许删除
        if (config.getIsSystem()) {
            throw new BusinessException("系统配置不允许删除");
        }
        
        // 删除配置
        systemConfigDataMapper.deleteById(id);
        
        log.info("删除系统配置成功: configId={}, configKey={}", id, config.getConfigKey());
    }
    
    @Override
    @Transactional
    public void batchDeleteConfigs(List<Long> configIds) {
        if (configIds == null || configIds.isEmpty()) {
            return;
        }
        
        // 检查是否包含系统配置
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SystemConfig::getId, configIds)
                   .eq(SystemConfig::getIsSystem, true);
        long systemConfigCount = systemConfigDataMapper.selectCount(queryWrapper);
        if (systemConfigCount > 0) {
            throw new BusinessException("不能删除系统配置");
        }
        
        // 批量删除配置
        systemConfigDataMapper.deleteBatchIds(configIds);
        
        log.info("批量删除系统配置成功: configIds={}", configIds);
    }
    
    @Override
    public boolean existsByConfigKey(String configKey) {
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getConfigKey, configKey);
        return systemConfigDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public List<SystemConfigResponse> findByConfigType(ConfigType configType) {
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getConfigType, configType)
                   .orderByDesc(SystemConfig::getCreatedTime);
        
        List<SystemConfig> configs = systemConfigDataMapper.selectList(queryWrapper);
        return configs.stream()
                     .map(systemConfigConvert::toResponse)
                     .collect(Collectors.toList());
    }
    
    @Override
    public List<SystemConfigResponse> findSystemConfigs() {
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getIsSystem, true)
                   .orderByDesc(SystemConfig::getCreatedTime);
        
        List<SystemConfig> configs = systemConfigDataMapper.selectList(queryWrapper);
        return configs.stream()
                     .map(systemConfigConvert::toResponse)
                     .collect(Collectors.toList());
    }
    
    @Override
    public List<SystemConfigResponse> findUserConfigs() {
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getIsSystem, false)
                   .orderByDesc(SystemConfig::getCreatedTime);
        
        List<SystemConfig> configs = systemConfigDataMapper.selectList(queryWrapper);
        return configs.stream()
                     .map(systemConfigConvert::toResponse)
                     .collect(Collectors.toList());
    }
    
    @Override
    public String getConfigValue(String configKey) {
        return getConfigValue(configKey, null);
    }
    
    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getConfigKey, configKey);
        
        SystemConfig config = systemConfigDataMapper.selectOne(queryWrapper);
        return config != null ? config.getConfigValue() : defaultValue;
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> T getTypedConfigValue(String configKey, Class<T> type) {
        return getTypedConfigValue(configKey, type, null);
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> T getTypedConfigValue(String configKey, Class<T> type, T defaultValue) {
        String value = getConfigValue(configKey);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            if (type == String.class) {
                return (T) value;
            } else if (type == Integer.class) {
                return (T) Integer.valueOf(value);
            } else if (type == Boolean.class) {
                return (T) Boolean.valueOf(value);
            } else if (type == Long.class) {
                return (T) Long.valueOf(value);
            } else if (type == Double.class) {
                return (T) Double.valueOf(value);
            } else {
                return (T) value;
            }
        } catch (Exception e) {
            log.warn("配置值类型转换失败: configKey={}, value={}, type={}", configKey, value, type.getSimpleName());
            return defaultValue;
        }
    }
    
    @Override
    @Transactional
    public void updateConfigValue(String configKey, String configValue) {
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getConfigKey, configKey);
        
        SystemConfig config = systemConfigDataMapper.selectOne(queryWrapper);
        if (config == null) {
            throw new BusinessException("系统配置不存在");
        }
        
        config.setConfigValue(configValue);
        systemConfigDataMapper.updateById(config);
        
        log.info("更新配置值成功: configKey={}", configKey);
    }
}
