package com.cloudvps.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.system.api.enums.LoginResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户登录日志实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_login_logs")
public class UserLoginLog extends BaseEntity {
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 登录IP地址
     */
    @TableField("login_ip")
    private String loginIp;

    /**
     * 登录地点
     */
    @TableField("login_location")
    private String loginLocation;

    /**
     * 用户代理信息
     */
    @TableField("user_agent")
    private String userAgent;
    
    /**
     * 登录时间
     */
    @TableField("login_time")
    private LocalDateTime loginTime;

    /**
     * 登录结果
     */
    @TableField("login_result")
    private LoginResult loginResult = LoginResult.SUCCESS;

    /**
     * 失败原因
     */
    @TableField("failure_reason")
    private String failureReason;
}
