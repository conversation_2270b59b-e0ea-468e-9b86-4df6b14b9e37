package com.cloudvps.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.system.api.dto.request.RoleCreateRequest;
import com.cloudvps.system.api.dto.request.RoleQueryRequest;
import com.cloudvps.system.api.dto.request.RoleUpdateRequest;
import com.cloudvps.system.api.dto.response.RoleResponse;
import com.cloudvps.system.api.enums.RoleStatus;
import com.cloudvps.system.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/roles")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "角色管理", description = "角色相关接口")
public class RoleController {
    
    private final RoleService roleService;
    
    @GetMapping
    @Operation(summary = "分页查询角色", description = "分页查询角色列表")
    @PreAuthorize("hasAuthority('ROLE_VIEW')")
    public ApiResponse<IPage<RoleResponse>> getRoles(@Valid RoleQueryRequest request) {
        log.debug("分页查询角色: {}", request);
        IPage<RoleResponse> roles = roleService.queryRoles(request);
        return ApiResponse.success(roles);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取角色详情", description = "根据角色ID获取角色详细信息")
    @PreAuthorize("hasAuthority('ROLE_VIEW')")
    public ApiResponse<RoleResponse> getRoleById(
            @Parameter(description = "角色ID") @PathVariable Long id) {
        log.debug("获取角色详情: roleId={}", id);
        RoleResponse role = roleService.findById(id);
        return ApiResponse.success(role);
    }
    
    @PostMapping
    @Operation(summary = "创建角色", description = "创建新角色")
    @PreAuthorize("hasAuthority('ROLE_CREATE')")
    public ApiResponse<RoleResponse> createRole(@Valid @RequestBody RoleCreateRequest request) {
        log.info("创建角色请求: roleCode={}", request.getRoleCode());
        RoleResponse role = roleService.createRole(request);
        return ApiResponse.success("创建成功", role);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新角色", description = "更新角色信息")
    @PreAuthorize("hasAuthority('ROLE_UPDATE')")
    public ApiResponse<RoleResponse> updateRole(
            @Parameter(description = "角色ID") @PathVariable Long id,
            @Valid @RequestBody RoleUpdateRequest request) {
        log.info("更新角色请求: roleId={}", id);
        RoleResponse role = roleService.updateRole(id, request);
        return ApiResponse.success("更新成功", role);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除角色", description = "删除指定角色")
    @PreAuthorize("hasAuthority('ROLE_DELETE')")
    public ApiResponse<Void> deleteRole(
            @Parameter(description = "角色ID") @PathVariable Long id) {
        log.info("删除角色请求: roleId={}", id);
        roleService.deleteRole(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除角色", description = "批量删除指定角色")
    @PreAuthorize("hasAuthority('ROLE_DELETE')")
    public ApiResponse<Void> batchDeleteRoles(@RequestBody List<Long> roleIds) {
        log.info("批量删除角色请求: roleIds={}", roleIds);
        roleService.batchDeleteRoles(roleIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @PutMapping("/{id}/status")
    @Operation(summary = "更新角色状态", description = "更新指定角色的状态")
    @PreAuthorize("hasAuthority('ROLE_UPDATE')")
    public ApiResponse<RoleResponse> updateRoleStatus(
            @Parameter(description = "角色ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam RoleStatus status) {
        log.info("更新角色状态: roleId={}, status={}", id, status);
        RoleResponse role = roleService.updateStatus(id, status);
        return ApiResponse.success("状态更新成功", role);
    }
    
    @GetMapping("/check-code")
    @Operation(summary = "检查角色编码", description = "检查角色编码是否已存在")
    @PreAuthorize("hasAuthority('ROLE_VIEW')")
    public ApiResponse<Boolean> checkRoleCode(@RequestParam String roleCode) {
        boolean exists = roleService.existsByRoleCode(roleCode);
        return ApiResponse.success(exists ? "角色编码已存在" : "角色编码可用", !exists);
    }
    
    @GetMapping("/active")
    @Operation(summary = "获取启用角色", description = "获取所有启用状态的角色")
    @PreAuthorize("hasAuthority('ROLE_VIEW')")
    public ApiResponse<List<RoleResponse>> getActiveRoles() {
        log.debug("获取启用角色列表");
        List<RoleResponse> roles = roleService.findAllActiveRoles();
        return ApiResponse.success(roles);
    }
    
    @PostMapping("/{id}/menus")
    @Operation(summary = "分配角色菜单", description = "为指定角色分配菜单")
    @PreAuthorize("hasAuthority('ROLE_UPDATE')")
    public ApiResponse<Void> assignMenus(
            @Parameter(description = "角色ID") @PathVariable Long id,
            @Parameter(description = "菜单ID列表") @RequestBody List<Long> menuIds) {
        log.info("分配角色菜单: roleId={}, menuIds={}", id, menuIds);
        roleService.assignMenus(id, menuIds);
        return ApiResponse.success("菜单分配成功", null);
    }
    
    @GetMapping("/{id}/menus")
    @Operation(summary = "获取角色菜单", description = "获取指定角色的菜单ID列表")
    @PreAuthorize("hasAuthority('ROLE_VIEW')")
    public ApiResponse<List<Long>> getRoleMenus(
            @Parameter(description = "角色ID") @PathVariable Long id) {
        log.debug("获取角色菜单: roleId={}", id);
        List<Long> menuIds = roleService.getRoleMenuIds(id);
        return ApiResponse.success(menuIds);
    }
}
