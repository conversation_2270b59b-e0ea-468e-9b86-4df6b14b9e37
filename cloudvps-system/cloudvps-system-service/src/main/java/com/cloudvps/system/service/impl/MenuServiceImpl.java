package com.cloudvps.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.system.api.dto.request.MenuCreateRequest;
import com.cloudvps.system.api.dto.request.MenuQueryRequest;
import com.cloudvps.system.api.dto.request.MenuUpdateRequest;
import com.cloudvps.system.api.dto.response.MenuResponse;
import com.cloudvps.system.entity.Menu;
import com.cloudvps.system.mapper.MenuDataMapper;
import com.cloudvps.system.convert.MenuConvert;
import com.cloudvps.system.service.MenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 菜单服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MenuServiceImpl implements MenuService {
    
    private final MenuDataMapper menuDataMapper;
    private final MenuConvert menuConvert;
    
    @Override
    public IPage<MenuResponse> queryMenus(MenuQueryRequest request) {
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like(Menu::getName, request.getName());
        }
        if (StringUtils.hasText(request.getTitle())) {
            queryWrapper.like(Menu::getTitle, request.getTitle());
        }
        if (request.getType() != null) {
            queryWrapper.eq(Menu::getType, request.getType());
        }
        if (request.getParentId() != null) {
            queryWrapper.eq(Menu::getParentId, request.getParentId());
        }
        if (request.getVisible() != null) {
            queryWrapper.eq(Menu::getVisible, request.getVisible());
        }
        if (request.getEnabled() != null) {
            queryWrapper.eq(Menu::getEnabled, request.getEnabled());
        }
        
        // 排序
        queryWrapper.orderByAsc(Menu::getSort)
                   .orderByDesc(Menu::getCreatedTime);
        
        // 分页查询
        Page<Menu> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<Menu> menuPage = menuDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return menuPage.convert(menuConvert::toResponse);
    }
    
    @Override
    public MenuResponse findById(Long id) {
        Menu menu = menuDataMapper.selectById(id);
        if (menu == null) {
            throw new BusinessException("菜单不存在");
        }
        return menuConvert.toResponse(menu);
    }
    
    @Override
    public MenuResponse findByName(String name) {
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getName, name);
        
        Menu menu = menuDataMapper.selectOne(queryWrapper);
        if (menu == null) {
            throw new BusinessException("菜单不存在");
        }
        return menuConvert.toResponse(menu);
    }
    
    @Override
    @Transactional
    public MenuResponse createMenu(MenuCreateRequest request) {
        // 检查菜单名称是否已存在
        if (existsByName(request.getName())) {
            throw new BusinessException("菜单名称已存在");
        }
        
        // 如果有父菜单，检查父菜单是否存在
        if (request.getParentId() != null) {
            Menu parentMenu = menuDataMapper.selectById(request.getParentId());
            if (parentMenu == null) {
                throw new BusinessException("父菜单不存在");
            }
        }
        
        Menu menu = menuConvert.toEntity(request);
        menuDataMapper.insert(menu);

        log.info("创建菜单成功: name={}, title={}", menu.getName(), menu.getTitle());
        return menuConvert.toResponse(menu);
    }
    
    @Override
    @Transactional
    public MenuResponse updateMenu(Long id, MenuUpdateRequest request) {
        Menu existingMenu = menuDataMapper.selectById(id);
        if (existingMenu == null) {
            throw new BusinessException("菜单不存在");
        }
        
        // 如果有父菜单，检查父菜单是否存在且不是自己
        if (request.getParentId() != null) {
            if (request.getParentId().equals(id)) {
                throw new BusinessException("不能将自己设置为父菜单");
            }
            Menu parentMenu = menuDataMapper.selectById(request.getParentId());
            if (parentMenu == null) {
                throw new BusinessException("父菜单不存在");
            }
        }
        
        // 更新字段
        menuConvert.updateFromRequest(existingMenu, request);
        menuDataMapper.updateById(existingMenu);

        log.info("更新菜单成功: menuId={}, title={}", id, existingMenu.getTitle());
        return menuConvert.toResponse(existingMenu);
    }
    
    @Override
    @Transactional
    public void deleteMenu(Long id) {
        Menu menu = menuDataMapper.selectById(id);
        if (menu == null) {
            throw new BusinessException("菜单不存在");
        }
        
        // 检查是否有子菜单
        LambdaQueryWrapper<Menu> childWrapper = new LambdaQueryWrapper<>();
        childWrapper.eq(Menu::getParentId, id);
        long childCount = menuDataMapper.selectCount(childWrapper);
        if (childCount > 0) {
            throw new BusinessException("存在子菜单，无法删除");
        }
        
        // 删除菜单
        menuDataMapper.deleteById(id);
        
        log.info("删除菜单成功: menuId={}, name={}", id, menu.getName());
    }
    
    @Override
    @Transactional
    public void batchDeleteMenus(List<Long> menuIds) {
        if (menuIds == null || menuIds.isEmpty()) {
            return;
        }
        
        // 检查是否有子菜单
        for (Long menuId : menuIds) {
            LambdaQueryWrapper<Menu> childWrapper = new LambdaQueryWrapper<>();
            childWrapper.eq(Menu::getParentId, menuId);
            long childCount = menuDataMapper.selectCount(childWrapper);
            if (childCount > 0) {
                throw new BusinessException("存在子菜单，无法删除");
            }
        }
        
        // 批量删除菜单
        menuDataMapper.deleteBatchIds(menuIds);
        
        log.info("批量删除菜单成功: menuIds={}", menuIds);
    }
    
    @Override
    @Transactional
    public MenuResponse updateEnabled(Long id, Boolean enabled) {
        Menu menu = menuDataMapper.selectById(id);
        if (menu == null) {
            throw new BusinessException("菜单不存在");
        }
        
        menu.setEnabled(enabled);
        menuDataMapper.updateById(menu);
        
        log.info("更新菜单状态成功: menuId={}, enabled={}", id, enabled);
        return menuConvert.toResponse(menu);
    }
    
    @Override
    @Transactional
    public MenuResponse updateVisible(Long id, Boolean visible) {
        Menu menu = menuDataMapper.selectById(id);
        if (menu == null) {
            throw new BusinessException("菜单不存在");
        }
        
        menu.setVisible(visible);
        menuDataMapper.updateById(menu);
        
        log.info("更新菜单可见性成功: menuId={}, visible={}", id, visible);
        return menuConvert.toResponse(menu);
    }
    
    @Override
    public boolean existsByName(String name) {
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getName, name);
        return menuDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public List<MenuResponse> getMenuTree() {
        // 查询所有启用的菜单
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getEnabled, true)
                   .orderByAsc(Menu::getSort);
        
        List<Menu> allMenus = menuDataMapper.selectList(queryWrapper);
        List<MenuResponse> menuResponses = allMenus.stream()
                .map(menuConvert::toResponse)
                .collect(Collectors.toList());
        
        return buildMenuTree(menuResponses);
    }
    
    @Override
    public List<MenuResponse> getChildrenByParentId(Long parentId) {
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getParentId, parentId)
                   .eq(Menu::getEnabled, true)
                   .orderByAsc(Menu::getSort);
        
        List<Menu> children = menuDataMapper.selectList(queryWrapper);
        return children.stream()
                      .map(menuConvert::toResponse)
                      .collect(Collectors.toList());
    }
    
    @Override
    public List<MenuResponse> getUserMenuTree(Long userId) {
        // TODO: 根据用户角色权限获取菜单树
        // 这里需要关联用户角色和角色菜单表
        return getMenuTree();
    }
    
    @Override
    public List<MenuResponse> getRoleMenuTree(Long roleId) {
        // TODO: 根据角色权限获取菜单树
        // 这里需要关联角色菜单表
        return getMenuTree();
    }
    
    /**
     * 构建菜单树
     */
    private List<MenuResponse> buildMenuTree(List<MenuResponse> allMenus) {
        Map<Long, List<MenuResponse>> parentChildMap = allMenus.stream()
                .filter(menu -> menu.getParentId() != null)
                .collect(Collectors.groupingBy(MenuResponse::getParentId));
        
        // 设置子菜单
        allMenus.forEach(menu -> {
            List<MenuResponse> children = parentChildMap.get(menu.getId());
            menu.setChildren(children != null ? children : new ArrayList<>());
        });
        
        // 返回根菜单
        return allMenus.stream()
                      .filter(menu -> menu.getParentId() == null)
                      .collect(Collectors.toList());
    }
}
