package com.cloudvps.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.system.api.dto.request.UserLoginLogCreateRequest;
import com.cloudvps.system.api.dto.request.UserLoginLogQueryRequest;
import com.cloudvps.system.api.dto.response.UserLoginLogResponse;
import com.cloudvps.system.api.enums.LoginResult;
import com.cloudvps.system.service.UserLoginLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户登录日志管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/login-logs")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "用户登录日志管理", description = "用户登录日志相关接口")
public class UserLoginLogController {
    
    private final UserLoginLogService userLoginLogService;
    
    @GetMapping
    @Operation(summary = "分页查询登录日志", description = "分页查询用户登录日志列表")
    @PreAuthorize("hasAuthority('LOGIN_LOG_VIEW')")
    public ApiResponse<IPage<UserLoginLogResponse>> getLoginLogs(@Valid UserLoginLogQueryRequest request) {
        log.debug("分页查询登录日志: {}", request);
        IPage<UserLoginLogResponse> logs = userLoginLogService.queryLoginLogs(request);
        return ApiResponse.success(logs);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取登录日志详情", description = "根据日志ID获取登录日志详细信息")
    @PreAuthorize("hasAuthority('LOGIN_LOG_VIEW')")
    public ApiResponse<UserLoginLogResponse> getLoginLogById(
            @Parameter(description = "日志ID") @PathVariable Long id) {
        log.debug("获取登录日志详情: logId={}", id);
        UserLoginLogResponse loginLog = userLoginLogService.findById(id);
        return ApiResponse.success(loginLog);
    }
    
    @PostMapping
    @Operation(summary = "创建登录日志", description = "创建新的登录日志")
    @PreAuthorize("hasAuthority('LOGIN_LOG_CREATE')")
    public ApiResponse<UserLoginLogResponse> createLoginLog(@Valid @RequestBody UserLoginLogCreateRequest request) {
        log.info("创建登录日志请求: userId={}, loginIp={}", request.getUserId(), request.getLoginIp());
        UserLoginLogResponse loginLog = userLoginLogService.createLoginLog(request);
        return ApiResponse.success("创建成功", loginLog);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除登录日志", description = "删除指定登录日志")
    @PreAuthorize("hasAuthority('LOGIN_LOG_DELETE')")
    public ApiResponse<Void> deleteLoginLog(
            @Parameter(description = "日志ID") @PathVariable Long id) {
        log.info("删除登录日志请求: logId={}", id);
        userLoginLogService.deleteLoginLog(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除登录日志", description = "批量删除指定登录日志")
    @PreAuthorize("hasAuthority('LOGIN_LOG_DELETE')")
    public ApiResponse<Void> batchDeleteLoginLogs(@RequestBody List<Long> logIds) {
        log.info("批量删除登录日志请求: logIds={}", logIds);
        userLoginLogService.batchDeleteLoginLogs(logIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @GetMapping("/by-user/{userId}")
    @Operation(summary = "根据用户ID获取登录日志", description = "根据用户ID获取登录日志列表")
    @PreAuthorize("hasAuthority('LOGIN_LOG_VIEW')")
    public ApiResponse<List<UserLoginLogResponse>> getLoginLogsByUserId(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        log.debug("根据用户ID获取登录日志: userId={}", userId);
        List<UserLoginLogResponse> logs = userLoginLogService.findByUserId(userId);
        return ApiResponse.success(logs);
    }
    
    @GetMapping("/by-user/{userId}/time-range")
    @Operation(summary = "根据用户ID和时间范围获取登录日志", description = "根据用户ID和时间范围获取登录日志列表")
    @PreAuthorize("hasAuthority('LOGIN_LOG_VIEW')")
    public ApiResponse<List<UserLoginLogResponse>> getLoginLogsByUserIdAndTimeRange(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        log.debug("根据用户ID和时间范围获取登录日志: userId={}, startTime={}, endTime={}", userId, startTime, endTime);
        List<UserLoginLogResponse> logs = userLoginLogService.findByUserIdAndTimeRange(userId, startTime, endTime);
        return ApiResponse.success(logs);
    }
    
    @GetMapping("/by-result/{loginResult}")
    @Operation(summary = "根据登录结果获取日志", description = "根据登录结果获取登录日志列表")
    @PreAuthorize("hasAuthority('LOGIN_LOG_VIEW')")
    public ApiResponse<List<UserLoginLogResponse>> getLoginLogsByResult(
            @Parameter(description = "登录结果") @PathVariable LoginResult loginResult) {
        log.debug("根据登录结果获取日志: loginResult={}", loginResult);
        List<UserLoginLogResponse> logs = userLoginLogService.findByLoginResult(loginResult);
        return ApiResponse.success(logs);
    }
    
    @GetMapping("/by-ip/{loginIp}")
    @Operation(summary = "根据IP地址获取登录日志", description = "根据IP地址获取登录日志列表")
    @PreAuthorize("hasAuthority('LOGIN_LOG_VIEW')")
    public ApiResponse<List<UserLoginLogResponse>> getLoginLogsByIp(
            @Parameter(description = "登录IP") @PathVariable String loginIp) {
        log.debug("根据IP地址获取登录日志: loginIp={}", loginIp);
        List<UserLoginLogResponse> logs = userLoginLogService.findByLoginIp(loginIp);
        return ApiResponse.success(logs);
    }
    
    @GetMapping("/by-time-range")
    @Operation(summary = "根据时间范围获取登录日志", description = "根据时间范围获取登录日志列表")
    @PreAuthorize("hasAuthority('LOGIN_LOG_VIEW')")
    public ApiResponse<List<UserLoginLogResponse>> getLoginLogsByTimeRange(
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        log.debug("根据时间范围获取登录日志: startTime={}, endTime={}", startTime, endTime);
        List<UserLoginLogResponse> logs = userLoginLogService.findByTimeRange(startTime, endTime);
        return ApiResponse.success(logs);
    }
    
    @GetMapping("/count/by-user/{userId}")
    @Operation(summary = "统计用户登录次数", description = "统计指定用户在时间范围内的登录次数")
    @PreAuthorize("hasAuthority('LOGIN_LOG_VIEW')")
    public ApiResponse<Long> countLoginsByUserIdAndTimeRange(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        log.debug("统计用户登录次数: userId={}, startTime={}, endTime={}", userId, startTime, endTime);
        Long count = userLoginLogService.countByUserIdAndTimeRange(userId, startTime, endTime);
        return ApiResponse.success(count);
    }
    
    @GetMapping("/count/failures/by-user/{userId}")
    @Operation(summary = "统计登录失败次数", description = "统计指定用户在时间范围内的登录失败次数")
    @PreAuthorize("hasAuthority('LOGIN_LOG_VIEW')")
    public ApiResponse<Long> countFailuresByUserIdAndTimeRange(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        log.debug("统计登录失败次数: userId={}, startTime={}, endTime={}", userId, startTime, endTime);
        Long count = userLoginLogService.countFailuresByUserIdAndTimeRange(userId, startTime, endTime);
        return ApiResponse.success(count);
    }
    
    @PostMapping("/batch-create")
    @Operation(summary = "批量创建登录日志", description = "批量创建登录日志")
    @PreAuthorize("hasAuthority('LOGIN_LOG_CREATE')")
    public ApiResponse<Void> batchCreateLoginLogs(@Valid @RequestBody List<UserLoginLogCreateRequest> requests) {
        log.info("批量创建登录日志请求: count={}", requests.size());
        userLoginLogService.batchCreateLoginLogs(requests);
        return ApiResponse.success("批量创建成功", null);
    }
    
    @DeleteMapping("/before/{beforeTime}")
    @Operation(summary = "删除指定时间之前的日志", description = "删除指定时间之前的所有登录日志")
    @PreAuthorize("hasAuthority('LOGIN_LOG_DELETE')")
    public ApiResponse<Void> deleteLogsBefore(
            @Parameter(description = "时间点") @PathVariable 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime beforeTime) {
        log.info("删除指定时间之前的日志: beforeTime={}", beforeTime);
        userLoginLogService.deleteLogsBefore(beforeTime);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/record-success")
    @Operation(summary = "记录登录成功日志", description = "记录用户登录成功日志")
    @PreAuthorize("hasAuthority('LOGIN_LOG_CREATE')")
    public ApiResponse<UserLoginLogResponse> recordLoginSuccess(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "登录IP") @RequestParam String loginIp,
            @Parameter(description = "登录地点") @RequestParam(required = false) String loginLocation,
            @Parameter(description = "用户代理") @RequestParam(required = false) String userAgent) {
        log.info("记录登录成功日志: userId={}, loginIp={}", userId, loginIp);
        UserLoginLogResponse loginLog = userLoginLogService.recordLoginSuccess(userId, loginIp, loginLocation, userAgent);
        return ApiResponse.success("记录成功", loginLog);
    }
    
    @PostMapping("/record-failure")
    @Operation(summary = "记录登录失败日志", description = "记录用户登录失败日志")
    @PreAuthorize("hasAuthority('LOGIN_LOG_CREATE')")
    public ApiResponse<UserLoginLogResponse> recordLoginFailure(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "登录IP") @RequestParam String loginIp,
            @Parameter(description = "登录地点") @RequestParam(required = false) String loginLocation,
            @Parameter(description = "用户代理") @RequestParam(required = false) String userAgent,
            @Parameter(description = "失败原因") @RequestParam String failureReason) {
        log.info("记录登录失败日志: userId={}, loginIp={}, reason={}", userId, loginIp, failureReason);
        UserLoginLogResponse loginLog = userLoginLogService.recordLoginFailure(userId, loginIp, loginLocation, userAgent, failureReason);
        return ApiResponse.success("记录成功", loginLog);
    }
}
