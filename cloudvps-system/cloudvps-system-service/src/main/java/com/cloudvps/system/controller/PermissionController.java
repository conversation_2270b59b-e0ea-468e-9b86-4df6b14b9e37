package com.cloudvps.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.system.api.dto.request.PermissionCreateRequest;
import com.cloudvps.system.api.dto.request.PermissionQueryRequest;
import com.cloudvps.system.api.dto.request.PermissionUpdateRequest;
import com.cloudvps.system.api.dto.response.PermissionResponse;
import com.cloudvps.system.api.enums.ResourceType;
import com.cloudvps.system.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/permissions")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "权限管理", description = "权限相关接口")
public class PermissionController {
    
    private final PermissionService permissionService;
    
    @GetMapping
    @Operation(summary = "分页查询权限", description = "分页查询权限列表")
    @PreAuthorize("hasAuthority('PERMISSION_VIEW')")
    public ApiResponse<IPage<PermissionResponse>> getPermissions(@Valid PermissionQueryRequest request) {
        log.debug("分页查询权限: {}", request);
        IPage<PermissionResponse> permissions = permissionService.queryPermissions(request);
        return ApiResponse.success(permissions);
    }

    @GetMapping("/all")
    @Operation(summary = "获取所有权限", description = "获取所有权限列表")
    @PreAuthorize("hasAuthority('PERMISSION_VIEW')")
    public ApiResponse<List<PermissionResponse>> getAllPermissions() {
        log.debug("获取所有权限列表");
        List<PermissionResponse> permissions = permissionService.findAllPermissions();
        return ApiResponse.success(permissions);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取权限详情", description = "根据权限ID获取权限详细信息")
    @PreAuthorize("hasAuthority('PERMISSION_VIEW')")
    public ApiResponse<PermissionResponse> getPermissionById(
            @Parameter(description = "权限ID") @PathVariable Long id) {
        log.debug("获取权限详情: permissionId={}", id);
        PermissionResponse permission = permissionService.findById(id);
        return ApiResponse.success(permission);
    }
    
    @PostMapping
    @Operation(summary = "创建权限", description = "创建新权限")
    @PreAuthorize("hasAuthority('PERMISSION_CREATE')")
    public ApiResponse<PermissionResponse> createPermission(@Valid @RequestBody PermissionCreateRequest request) {
        log.info("创建权限请求: permissionCode={}", request.getPermissionCode());
        PermissionResponse permission = permissionService.createPermission(request);
        return ApiResponse.success("创建成功", permission);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新权限", description = "更新权限信息")
    @PreAuthorize("hasAuthority('PERMISSION_UPDATE')")
    public ApiResponse<PermissionResponse> updatePermission(
            @Parameter(description = "权限ID") @PathVariable Long id,
            @Valid @RequestBody PermissionUpdateRequest request) {
        log.info("更新权限请求: permissionId={}", id);
        PermissionResponse permission = permissionService.updatePermission(id, request);
        return ApiResponse.success("更新成功", permission);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除权限", description = "删除指定权限")
    @PreAuthorize("hasAuthority('PERMISSION_DELETE')")
    public ApiResponse<Void> deletePermission(
            @Parameter(description = "权限ID") @PathVariable Long id) {
        log.info("删除权限请求: permissionId={}", id);
        permissionService.deletePermission(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除权限", description = "批量删除指定权限")
    @PreAuthorize("hasAuthority('PERMISSION_DELETE')")
    public ApiResponse<Void> batchDeletePermissions(@RequestBody List<Long> permissionIds) {
        log.info("批量删除权限请求: permissionIds={}", permissionIds);
        permissionService.batchDeletePermissions(permissionIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @GetMapping("/check-code")
    @Operation(summary = "检查权限编码", description = "检查权限编码是否已存在")
    @PreAuthorize("hasAuthority('PERMISSION_VIEW')")
    public ApiResponse<Boolean> checkPermissionCode(@RequestParam String permissionCode) {
        boolean exists = permissionService.existsByPermissionCode(permissionCode);
        return ApiResponse.success(exists ? "权限编码已存在" : "权限编码可用", !exists);
    }
    
    @GetMapping("/by-resource-type")
    @Operation(summary = "根据资源类型获取权限", description = "根据资源类型获取权限列表")
    @PreAuthorize("hasAuthority('PERMISSION_VIEW')")
    public ApiResponse<List<PermissionResponse>> getPermissionsByResourceType(
            @Parameter(description = "资源类型") @RequestParam ResourceType resourceType) {
        log.debug("根据资源类型获取权限: resourceType={}", resourceType);
        List<PermissionResponse> permissions = permissionService.findByResourceType(resourceType);
        return ApiResponse.success(permissions);
    }
    
    @GetMapping("/by-resource-path")
    @Operation(summary = "根据资源路径获取权限", description = "根据资源路径获取权限列表")
    @PreAuthorize("hasAuthority('PERMISSION_VIEW')")
    public ApiResponse<List<PermissionResponse>> getPermissionsByResourcePath(
            @Parameter(description = "资源路径") @RequestParam String resourcePath) {
        log.debug("根据资源路径获取权限: resourcePath={}", resourcePath);
        List<PermissionResponse> permissions = permissionService.findByResourcePath(resourcePath);
        return ApiResponse.success(permissions);
    }

}
