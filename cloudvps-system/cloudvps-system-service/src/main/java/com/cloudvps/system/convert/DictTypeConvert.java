package com.cloudvps.system.convert;

import com.cloudvps.system.api.dto.request.DictTypeCreateRequest;
import com.cloudvps.system.api.dto.request.DictTypeUpdateRequest;
import com.cloudvps.system.api.dto.response.DictTypeResponse;
import com.cloudvps.system.entity.DictType;
import org.mapstruct.*;

/**
 * 字典类型对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface DictTypeConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    DictType toEntity(DictTypeCreateRequest request);
    
    /**
     * 实体转响应
     */
    DictTypeResponse toResponse(DictType dictType);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "dictCode", ignore = true) // 字典编码不允许修改
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget DictType dictType, DictTypeUpdateRequest request);
}
