package com.cloudvps.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.system.api.dto.request.DictTypeCreateRequest;
import com.cloudvps.system.api.dto.request.DictTypeQueryRequest;
import com.cloudvps.system.api.dto.request.DictTypeUpdateRequest;
import com.cloudvps.system.api.dto.response.DictTypeResponse;
import com.cloudvps.system.api.enums.DictStatus;
import com.cloudvps.system.convert.DictTypeConvert;
import com.cloudvps.system.entity.DictData;
import com.cloudvps.system.entity.DictType;
import com.cloudvps.system.mapper.DictDataDataMapper;
import com.cloudvps.system.mapper.DictTypeDataMapper;
import com.cloudvps.system.service.DictTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典类型服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DictTypeServiceImpl implements DictTypeService {
    
    private final DictTypeDataMapper dictTypeDataMapper;
    private final DictDataDataMapper dictDataDataMapper;
    private final DictTypeConvert dictTypeConvert;
    
    @Override
    public IPage<DictTypeResponse> queryDictTypes(DictTypeQueryRequest request) {
        LambdaQueryWrapper<DictType> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(request.getDictCode())) {
            queryWrapper.like(DictType::getDictCode, request.getDictCode());
        }
        if (StringUtils.hasText(request.getDictName())) {
            queryWrapper.like(DictType::getDictName, request.getDictName());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq(DictType::getStatus, request.getStatus());
        }
        
        // 排序
        queryWrapper.orderByDesc(DictType::getCreatedTime);
        
        // 分页查询
        Page<DictType> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<DictType> dictTypePage = dictTypeDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return dictTypePage.convert(dictTypeConvert::toResponse);
    }
    
    @Override
    public DictTypeResponse findById(Long id) {
        DictType dictType = dictTypeDataMapper.selectById(id);
        if (dictType == null) {
            throw new BusinessException("字典类型不存在");
        }
        return dictTypeConvert.toResponse(dictType);
    }
    
    @Override
    public DictTypeResponse findByDictCode(String dictCode) {
        LambdaQueryWrapper<DictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictType::getDictCode, dictCode);
        
        DictType dictType = dictTypeDataMapper.selectOne(queryWrapper);
        if (dictType == null) {
            throw new BusinessException("字典类型不存在");
        }
        return dictTypeConvert.toResponse(dictType);
    }
    
    @Override
    @Transactional
    public DictTypeResponse createDictType(DictTypeCreateRequest request) {
        // 检查字典编码是否已存在
        if (existsByDictCode(request.getDictCode())) {
            throw new BusinessException("字典编码已存在");
        }
        
        // 检查字典名称是否已存在
        if (existsByDictName(request.getDictName())) {
            throw new BusinessException("字典名称已存在");
        }
        
        DictType dictType = dictTypeConvert.toEntity(request);
        dictTypeDataMapper.insert(dictType);
        
        log.info("创建字典类型成功: dictCode={}, dictName={}", 
                dictType.getDictCode(), dictType.getDictName());
        return dictTypeConvert.toResponse(dictType);
    }
    
    @Override
    @Transactional
    public DictTypeResponse updateDictType(Long id, DictTypeUpdateRequest request) {
        DictType existingDictType = dictTypeDataMapper.selectById(id);
        if (existingDictType == null) {
            throw new BusinessException("字典类型不存在");
        }
        
        // 检查字典名称是否已存在（排除自己）
        if (StringUtils.hasText(request.getDictName()) && 
            !request.getDictName().equals(existingDictType.getDictName()) &&
            existsByDictName(request.getDictName())) {
            throw new BusinessException("字典名称已存在");
        }
        
        // 更新字段
        dictTypeConvert.updateFromRequest(existingDictType, request);
        dictTypeDataMapper.updateById(existingDictType);
        
        log.info("更新字典类型成功: dictTypeId={}, dictName={}", 
                id, existingDictType.getDictName());
        return dictTypeConvert.toResponse(existingDictType);
    }
    
    @Override
    @Transactional
    public void deleteDictType(Long id) {
        DictType dictType = dictTypeDataMapper.selectById(id);
        if (dictType == null) {
            throw new BusinessException("字典类型不存在");
        }
        
        // 检查是否有关联的字典数据
        LambdaQueryWrapper<DictData> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(DictData::getDictTypeId, id);
        long dataCount = dictDataDataMapper.selectCount(dataWrapper);
        if (dataCount > 0) {
            throw new BusinessException("存在关联的字典数据，无法删除");
        }
        
        // 删除字典类型
        dictTypeDataMapper.deleteById(id);
        
        log.info("删除字典类型成功: dictTypeId={}, dictCode={}", 
                id, dictType.getDictCode());
    }
    
    @Override
    @Transactional
    public void batchDeleteDictTypes(List<Long> dictTypeIds) {
        if (dictTypeIds == null || dictTypeIds.isEmpty()) {
            return;
        }
        
        // 检查是否有关联的字典数据
        for (Long dictTypeId : dictTypeIds) {
            LambdaQueryWrapper<DictData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.eq(DictData::getDictTypeId, dictTypeId);
            long dataCount = dictDataDataMapper.selectCount(dataWrapper);
            if (dataCount > 0) {
                throw new BusinessException("存在关联的字典数据，无法删除");
            }
        }
        
        // 批量删除字典类型
        dictTypeDataMapper.deleteBatchIds(dictTypeIds);
        
        log.info("批量删除字典类型成功: dictTypeIds={}", dictTypeIds);
    }
    
    @Override
    @Transactional
    public DictTypeResponse updateStatus(Long id, DictStatus status) {
        DictType dictType = dictTypeDataMapper.selectById(id);
        if (dictType == null) {
            throw new BusinessException("字典类型不存在");
        }
        
        dictType.setStatus(status);
        dictTypeDataMapper.updateById(dictType);
        
        log.info("更新字典类型状态成功: dictTypeId={}, status={}", id, status);
        return dictTypeConvert.toResponse(dictType);
    }
    
    @Override
    public boolean existsByDictCode(String dictCode) {
        LambdaQueryWrapper<DictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictType::getDictCode, dictCode);
        return dictTypeDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public boolean existsByDictName(String dictName) {
        LambdaQueryWrapper<DictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictType::getDictName, dictName);
        return dictTypeDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public List<DictTypeResponse> findByStatus(DictStatus status) {
        LambdaQueryWrapper<DictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictType::getStatus, status)
                   .orderByDesc(DictType::getCreatedTime);
        
        List<DictType> dictTypes = dictTypeDataMapper.selectList(queryWrapper);
        return dictTypes.stream()
                       .map(dictTypeConvert::toResponse)
                       .collect(Collectors.toList());
    }
    
    @Override
    public List<DictTypeResponse> findActiveDictTypes() {
        return findByStatus(DictStatus.ACTIVE);
    }
    
    @Override
    public List<DictTypeResponse> findAllDictTypes() {
        LambdaQueryWrapper<DictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(DictType::getCreatedTime);
        
        List<DictType> dictTypes = dictTypeDataMapper.selectList(queryWrapper);
        return dictTypes.stream()
                       .map(dictTypeConvert::toResponse)
                       .collect(Collectors.toList());
    }
}
