package com.cloudvps.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.system.api.dto.request.DictDataCreateRequest;
import com.cloudvps.system.api.dto.request.DictDataQueryRequest;
import com.cloudvps.system.api.dto.request.DictDataUpdateRequest;
import com.cloudvps.system.api.dto.response.DictDataResponse;
import com.cloudvps.system.api.enums.DictStatus;
import com.cloudvps.system.convert.DictDataConvert;
import com.cloudvps.system.entity.DictData;
import com.cloudvps.system.entity.DictType;
import com.cloudvps.system.mapper.DictDataDataMapper;
import com.cloudvps.system.mapper.DictTypeDataMapper;
import com.cloudvps.system.service.DictDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典数据服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DictDataServiceImpl implements DictDataService {
    
    private final DictDataDataMapper dictDataDataMapper;
    private final DictTypeDataMapper dictTypeDataMapper;
    private final DictDataConvert dictDataConvert;
    
    @Override
    public IPage<DictDataResponse> queryDictData(DictDataQueryRequest request) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (request.getDictTypeId() != null) {
            queryWrapper.eq(DictData::getDictTypeId, request.getDictTypeId());
        }
        if (StringUtils.hasText(request.getDictLabel())) {
            queryWrapper.like(DictData::getDictLabel, request.getDictLabel());
        }
        if (StringUtils.hasText(request.getDictValue())) {
            queryWrapper.like(DictData::getDictValue, request.getDictValue());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq(DictData::getStatus, request.getStatus());
        }
        if (request.getIsDefault() != null) {
            queryWrapper.eq(DictData::getIsDefault, request.getIsDefault());
        }
        
        // 排序
        queryWrapper.orderByAsc(DictData::getSortOrder)
                   .orderByDesc(DictData::getCreatedTime);
        
        // 分页查询
        Page<DictData> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<DictData> dictDataPage = dictDataDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return dictDataPage.convert(dictDataConvert::toResponse);
    }
    
    @Override
    public DictDataResponse findById(Long id) {
        DictData dictData = dictDataDataMapper.selectById(id);
        if (dictData == null) {
            throw new BusinessException("字典数据不存在");
        }
        return dictDataConvert.toResponse(dictData);
    }
    
    @Override
    @Transactional
    public DictDataResponse createDictData(DictDataCreateRequest request) {
        // 检查字典类型是否存在
        DictType dictType = dictTypeDataMapper.selectById(request.getDictTypeId());
        if (dictType == null) {
            throw new BusinessException("字典类型不存在");
        }
        
        // 检查字典值是否已存在
        if (existsByDictTypeIdAndValue(request.getDictTypeId(), request.getDictValue())) {
            throw new BusinessException("字典值已存在");
        }
        
        // 检查字典标签是否已存在
        if (existsByDictTypeIdAndLabel(request.getDictTypeId(), request.getDictLabel())) {
            throw new BusinessException("字典标签已存在");
        }
        
        // 如果设置为默认，需要取消其他默认项
        if (request.getIsDefault() != null && request.getIsDefault()) {
            clearDefaultByDictTypeId(request.getDictTypeId());
        }
        
        DictData dictData = dictDataConvert.toEntity(request);
        dictDataDataMapper.insert(dictData);
        
        log.info("创建字典数据成功: dictTypeId={}, dictLabel={}, dictValue={}", 
                dictData.getDictTypeId(), dictData.getDictLabel(), dictData.getDictValue());
        return dictDataConvert.toResponse(dictData);
    }
    
    @Override
    @Transactional
    public DictDataResponse updateDictData(Long id, DictDataUpdateRequest request) {
        DictData existingDictData = dictDataDataMapper.selectById(id);
        if (existingDictData == null) {
            throw new BusinessException("字典数据不存在");
        }
        
        // 检查字典值是否已存在（排除自己）
        if (StringUtils.hasText(request.getDictValue()) && 
            !request.getDictValue().equals(existingDictData.getDictValue()) &&
            existsByDictTypeIdAndValue(existingDictData.getDictTypeId(), request.getDictValue())) {
            throw new BusinessException("字典值已存在");
        }
        
        // 检查字典标签是否已存在（排除自己）
        if (StringUtils.hasText(request.getDictLabel()) && 
            !request.getDictLabel().equals(existingDictData.getDictLabel()) &&
            existsByDictTypeIdAndLabel(existingDictData.getDictTypeId(), request.getDictLabel())) {
            throw new BusinessException("字典标签已存在");
        }
        
        // 如果设置为默认，需要取消其他默认项
        if (request.getIsDefault() != null && request.getIsDefault() && 
            !existingDictData.getIsDefault()) {
            clearDefaultByDictTypeId(existingDictData.getDictTypeId());
        }
        
        // 更新字段
        dictDataConvert.updateFromRequest(existingDictData, request);
        dictDataDataMapper.updateById(existingDictData);
        
        log.info("更新字典数据成功: dictDataId={}, dictLabel={}", 
                id, existingDictData.getDictLabel());
        return dictDataConvert.toResponse(existingDictData);
    }
    
    @Override
    @Transactional
    public void deleteDictData(Long id) {
        DictData dictData = dictDataDataMapper.selectById(id);
        if (dictData == null) {
            throw new BusinessException("字典数据不存在");
        }
        
        // 删除字典数据
        dictDataDataMapper.deleteById(id);
        
        log.info("删除字典数据成功: dictDataId={}, dictLabel={}", 
                id, dictData.getDictLabel());
    }
    
    @Override
    @Transactional
    public void batchDeleteDictData(List<Long> dictDataIds) {
        if (dictDataIds == null || dictDataIds.isEmpty()) {
            return;
        }
        
        // 批量删除字典数据
        dictDataDataMapper.deleteBatchIds(dictDataIds);
        
        log.info("批量删除字典数据成功: dictDataIds={}", dictDataIds);
    }
    
    @Override
    @Transactional
    public DictDataResponse updateStatus(Long id, DictStatus status) {
        DictData dictData = dictDataDataMapper.selectById(id);
        if (dictData == null) {
            throw new BusinessException("字典数据不存在");
        }
        
        dictData.setStatus(status);
        dictDataDataMapper.updateById(dictData);
        
        log.info("更新字典数据状态成功: dictDataId={}, status={}", id, status);
        return dictDataConvert.toResponse(dictData);
    }
    
    @Override
    public List<DictDataResponse> findByDictTypeId(Long dictTypeId) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictData::getDictTypeId, dictTypeId)
                   .orderByAsc(DictData::getSortOrder);
        
        List<DictData> dictDataList = dictDataDataMapper.selectList(queryWrapper);
        return dictDataList.stream()
                          .map(dictDataConvert::toResponse)
                          .collect(Collectors.toList());
    }
    
    @Override
    public List<DictDataResponse> findByDictTypeIdAndStatus(Long dictTypeId, DictStatus status) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictData::getDictTypeId, dictTypeId)
                   .eq(DictData::getStatus, status)
                   .orderByAsc(DictData::getSortOrder);
        
        List<DictData> dictDataList = dictDataDataMapper.selectList(queryWrapper);
        return dictDataList.stream()
                          .map(dictDataConvert::toResponse)
                          .collect(Collectors.toList());
    }
    
    @Override
    public List<DictDataResponse> findByDictCode(String dictCode) {
        // 先根据字典编码查找字典类型
        LambdaQueryWrapper<DictType> typeWrapper = new LambdaQueryWrapper<>();
        typeWrapper.eq(DictType::getDictCode, dictCode);
        DictType dictType = dictTypeDataMapper.selectOne(typeWrapper);
        
        if (dictType == null) {
            throw new BusinessException("字典类型不存在");
        }
        
        return findByDictTypeId(dictType.getId());
    }
    
    @Override
    public List<DictDataResponse> findByDictCodeAndStatus(String dictCode, DictStatus status) {
        // 先根据字典编码查找字典类型
        LambdaQueryWrapper<DictType> typeWrapper = new LambdaQueryWrapper<>();
        typeWrapper.eq(DictType::getDictCode, dictCode);
        DictType dictType = dictTypeDataMapper.selectOne(typeWrapper);
        
        if (dictType == null) {
            throw new BusinessException("字典类型不存在");
        }
        
        return findByDictTypeIdAndStatus(dictType.getId(), status);
    }
    
    @Override
    public DictDataResponse findByDictTypeIdAndValue(Long dictTypeId, String dictValue) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictData::getDictTypeId, dictTypeId)
                   .eq(DictData::getDictValue, dictValue);
        
        DictData dictData = dictDataDataMapper.selectOne(queryWrapper);
        if (dictData == null) {
            throw new BusinessException("字典数据不存在");
        }
        return dictDataConvert.toResponse(dictData);
    }
    
    @Override
    public DictDataResponse findByDictTypeIdAndLabel(Long dictTypeId, String dictLabel) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictData::getDictTypeId, dictTypeId)
                   .eq(DictData::getDictLabel, dictLabel);
        
        DictData dictData = dictDataDataMapper.selectOne(queryWrapper);
        if (dictData == null) {
            throw new BusinessException("字典数据不存在");
        }
        return dictDataConvert.toResponse(dictData);
    }
    
    @Override
    public boolean existsByDictTypeIdAndValue(Long dictTypeId, String dictValue) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictData::getDictTypeId, dictTypeId)
                   .eq(DictData::getDictValue, dictValue);
        return dictDataDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public boolean existsByDictTypeIdAndLabel(Long dictTypeId, String dictLabel) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictData::getDictTypeId, dictTypeId)
                   .eq(DictData::getDictLabel, dictLabel);
        return dictDataDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public DictDataResponse findDefaultByDictTypeId(Long dictTypeId) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictData::getDictTypeId, dictTypeId)
                   .eq(DictData::getIsDefault, true);
        
        DictData dictData = dictDataDataMapper.selectOne(queryWrapper);
        return dictData != null ? dictDataConvert.toResponse(dictData) : null;
    }
    
    @Override
    @Transactional
    public void deleteByDictTypeId(Long dictTypeId) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictData::getDictTypeId, dictTypeId);
        
        dictDataDataMapper.delete(queryWrapper);
        
        log.info("根据字典类型ID删除字典数据成功: dictTypeId={}", dictTypeId);
    }
    
    /**
     * 清除指定字典类型的默认项
     */
    private void clearDefaultByDictTypeId(Long dictTypeId) {
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictData::getDictTypeId, dictTypeId)
                   .eq(DictData::getIsDefault, true);
        
        List<DictData> defaultItems = dictDataDataMapper.selectList(queryWrapper);
        for (DictData item : defaultItems) {
            item.setIsDefault(false);
            dictDataDataMapper.updateById(item);
        }
    }
}
