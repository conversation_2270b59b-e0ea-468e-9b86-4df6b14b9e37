package com.cloudvps.system.convert;

import com.cloudvps.system.api.dto.request.SystemConfigCreateRequest;
import com.cloudvps.system.api.dto.request.SystemConfigUpdateRequest;
import com.cloudvps.system.api.dto.response.SystemConfigResponse;
import com.cloudvps.system.entity.SystemConfig;
import org.mapstruct.*;

/**
 * 系统配置对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface SystemConfigConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    SystemConfig toEntity(SystemConfigCreateRequest request);
    
    /**
     * 实体转响应
     */
    SystemConfigResponse toResponse(SystemConfig systemConfig);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "configKey", ignore = true) // 配置键不允许修改
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget SystemConfig systemConfig, SystemConfigUpdateRequest request);
}
