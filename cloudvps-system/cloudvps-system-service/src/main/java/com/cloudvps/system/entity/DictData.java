package com.cloudvps.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.system.api.enums.DictStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典数据实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dict_data")
public class DictData extends BaseEntity {
    
    /**
     * 字典类型ID
     */
    @TableField("dict_type_id")
    private Long dictTypeId;

    /**
     * 字典标签
     */
    @TableField("dict_label")
    private String dictLabel;

    /**
     * 字典值
     */
    @TableField("dict_value")
    private String dictValue;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder = 0;
    
    /**
     * 状态
     */
    @TableField("status")
    private DictStatus status = DictStatus.ACTIVE;

    /**
     * 是否默认
     */
    @TableField("is_default")
    private Boolean isDefault = false;

    /**
     * CSS类名
     */
    @TableField("css_class")
    private String cssClass;

    /**
     * 列表类名
     */
    @TableField("list_class")
    private String listClass;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

}
