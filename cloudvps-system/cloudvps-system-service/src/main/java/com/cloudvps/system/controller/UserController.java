package com.cloudvps.system.controller;

import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.common.security.util.SecurityUtils;
import com.cloudvps.system.api.dto.request.ChangePasswordRequest;
import com.cloudvps.system.api.dto.request.UserCreateRequest;
import com.cloudvps.system.api.dto.request.UserQueryRequest;
import com.cloudvps.system.api.dto.request.UserRegisterRequest;
import com.cloudvps.system.api.dto.request.UserUpdateRequest;
import com.cloudvps.system.api.dto.response.UserResponse;
import com.cloudvps.system.api.enums.UserStatus;
import com.cloudvps.system.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/users")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "用户管理", description = "用户相关接口")
public class UserController {
    
    private final UserService userService;
    
    @PostMapping
    @Operation(summary = "创建用户", description = "管理员创建新用户")
    @PreAuthorize("hasAuthority('USER_CREATE')")
    public ApiResponse<UserResponse> createUser(@Valid @RequestBody UserCreateRequest request) {
        log.info("创建用户请求: username={}", request.getUsername());
        UserResponse user = userService.createUser(request);
        return ApiResponse.success("创建成功", user);
    }

    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "新用户注册")
    public ApiResponse<UserResponse> register(@Valid @RequestBody UserRegisterRequest request) {
        log.info("用户注册请求: username={}", request.getUsername());
        UserResponse user = userService.register(request);
        return ApiResponse.success("注册成功", user);
    }
    
    @GetMapping("/profile")
    @Operation(summary = "获取个人信息", description = "获取当前登录用户的详细信息")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<UserResponse> getProfile() {
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        log.debug("获取个人信息: userId={}", currentUserId);

        UserResponse userResponse = userService.findById(currentUserId);
        return ApiResponse.success(userResponse);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新用户信息", description = "管理员更新用户信息")
    @PreAuthorize("hasAuthority('USER_UPDATE')")
    public ApiResponse<UserResponse> updateUser(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Valid @RequestBody UserUpdateRequest request) {
        log.info("更新用户信息: userId={}", id);
        UserResponse updatedUser = userService.updateUser(id, request);
        return ApiResponse.success("更新成功", updatedUser);
    }


    @PutMapping("/profile")
    @Operation(summary = "更新个人信息", description = "更新当前登录用户的信息")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<UserResponse> updateProfile(@Valid @RequestBody UserUpdateRequest request) {
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        log.info("更新个人信息: userId={}", currentUserId);

        UserResponse updatedUser = userService.updateProfile(currentUserId, request);
        return ApiResponse.success("信息更新成功", updatedUser);
    }
    
    @PostMapping("/change-password")
    @Operation(summary = "修改密码", description = "修改当前登录用户的密码")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        Long currentUserId = SecurityUtils.getRequiredCurrentUserId();
        log.info("修改用户密码: userId={}", currentUserId);

        userService.changePassword(currentUserId, request.getOldPassword(), request.getNewPassword());
        return ApiResponse.success("密码修改成功", null);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取用户详情", description = "根据用户ID获取用户详细信息")
    @PreAuthorize("hasAuthority('USER_VIEW')")
    public ApiResponse<UserResponse> getUserById(
            @Parameter(description = "用户ID") @PathVariable Long id) {
        log.debug("获取用户详情: userId={}", id);
        UserResponse user = userService.findById(id);
        return ApiResponse.success(user);
    }
    
    @GetMapping
    @Operation(summary = "分页查询用户", description = "分页查询用户列表")
    @PreAuthorize("hasAuthority('USER_VIEW')")
    public ApiResponse<IPage<UserResponse>> getUsers(@Valid UserQueryRequest request) {

        log.debug("分页查询用户: {}", request);

        IPage<UserResponse> users = userService.queryUsers(request);

        return ApiResponse.success(users);
    }
    
    @PutMapping("/{id}/status")
    @Operation(summary = "更新用户状态", description = "更新指定用户的状态")
    @PreAuthorize("hasAuthority('USER_UPDATE')")
    public ApiResponse<UserResponse> updateUserStatus(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam UserStatus status) {
        log.info("更新用户状态: userId={}, status={}", id, status);
        UserResponse user = userService.updateStatus(id, status);
        return ApiResponse.success("状态更新成功", user);
    }

    @GetMapping("/{id}/roles")
    @Operation(summary = "获取用户角色", description = "获取指定用户的角色ID列表")
    @PreAuthorize("hasAuthority('USER_VIEW')")
    public ApiResponse<List<Long>> getUserRoles(
            @Parameter(description = "用户ID") @PathVariable Long id) {
        log.debug("获取用户角色: userId={}", id);
        List<Long> roleIds = userService.getUserRoleIds(id);
        return ApiResponse.success(roleIds);
    }

    @PostMapping("/{id}/roles")
    @Operation(summary = "分配用户角色", description = "为指定用户分配角色")
    @PreAuthorize("hasAuthority('USER_UPDATE')")
    public ApiResponse<Void> assignRoles(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Parameter(description = "角色ID列表") @RequestBody List<Long> roleIds) {
        log.info("分配用户角色: userId={}, roleIds={}", id, roleIds);
        userService.assignRoles(id, roleIds);
        return ApiResponse.success("角色分配成功", null);
    }

    @DeleteMapping("/{id}/roles")
    @Operation(summary = "移除用户角色", description = "移除指定用户的角色")
    @PreAuthorize("hasAuthority('USER_UPDATE')")
    public ApiResponse<Void> removeRoles(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Parameter(description = "角色ID列表") @RequestBody List<Long> roleIds) {
        log.info("移除用户角色: userId={}, roleIds={}", id, roleIds);
        userService.removeRoles(id, roleIds);
        return ApiResponse.success("角色移除成功", null);
    }
    
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名", description = "检查用户名是否已存在")
    public ApiResponse<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = userService.existsByUsername(username);
        return ApiResponse.success(exists ? "用户名已存在" : "用户名可用", !exists);
    }
    
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱", description = "检查邮箱是否已存在")
    public ApiResponse<Boolean> checkEmail(@RequestParam String email) {
        boolean exists = userService.existsByEmail(email);
        return ApiResponse.success(exists ? "邮箱已存在" : "邮箱可用", !exists);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "删除指定用户")
    @PreAuthorize("hasAuthority('USER_DELETE')")
    public ApiResponse<Void> deleteUser(
            @Parameter(description = "用户ID") @PathVariable Long id) {
        log.info("删除用户请求: userId={}", id);
        userService.deleteUser(id);
        return ApiResponse.success("删除成功", null);
    }

    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除用户", description = "批量删除指定用户")
    @PreAuthorize("hasAuthority('USER_DELETE')")
    public ApiResponse<Void> batchDeleteUsers(@RequestBody List<Long> userIds) {
        log.info("批量删除用户请求: userIds={}", userIds);
        userService.batchDeleteUsers(userIds);
        return ApiResponse.success("批量删除成功", null);
    }
}
