package com.cloudvps.system.convert;

import com.cloudvps.system.api.dto.request.UserAccountCreateRequest;
import com.cloudvps.system.api.dto.request.UserAccountUpdateRequest;
import com.cloudvps.system.api.dto.response.UserAccountResponse;
import com.cloudvps.system.entity.UserAccount;
import org.mapstruct.*;

/**
 * 用户账户对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface UserAccountConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "frozenAmount", constant = "0.00")
    @Mapping(target = "totalRecharge", constant = "0.00")
    @Mapping(target = "totalConsume", constant = "0.00")
    @Mapping(target = "lastOperationTime", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "lastOperationDescription", source = "operationDescription")
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    UserAccount toEntity(UserAccountCreateRequest request);
    
    /**
     * 实体转响应
     */
    @Mapping(target = "availableBalance", expression = "java(userAccount.getBalance().subtract(userAccount.getFrozenAmount()))")
    UserAccountResponse toResponse(UserAccount userAccount);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", ignore = true) // 用户ID不允许修改
    @Mapping(target = "balance", ignore = true) // 余额通过专门接口修改
    @Mapping(target = "frozenAmount", ignore = true) // 冻结金额通过专门接口修改
    @Mapping(target = "totalRecharge", ignore = true) // 累计充值通过专门接口修改
    @Mapping(target = "totalConsume", ignore = true) // 累计消费通过专门接口修改
    @Mapping(target = "lastOperationTime", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "lastOperationDescription", source = "operationDescription")
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget UserAccount userAccount, UserAccountUpdateRequest request);
}
