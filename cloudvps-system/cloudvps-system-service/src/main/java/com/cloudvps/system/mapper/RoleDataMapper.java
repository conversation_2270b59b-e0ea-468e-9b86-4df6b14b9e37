package com.cloudvps.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudvps.system.entity.Role;
import org.apache.ibatis.annotations.Mapper;

/**
 * 角色数据访问Mapper
 * 使用MyBatis-Plus实现角色的数据访问操作
 * 只保留实际使用的方法，其他CRUD操作使用MyBatis-Plus自带方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface RoleDataMapper extends BaseMapper<Role> {
    // 所有需要的方法都通过MyBatis-Plus的BaseMapper提供
    // 包括：selectPage, selectById, selectOne, insert, updateById,
    //      deleteById, deleteBatchIds, selectCount, selectList等
}
