package com.cloudvps.system.convert;

import com.cloudvps.system.api.dto.request.MenuCreateRequest;
import com.cloudvps.system.api.dto.request.MenuUpdateRequest;
import com.cloudvps.system.api.dto.response.MenuResponse;
import com.cloudvps.system.entity.Menu;
import org.mapstruct.*;

/**
 * 菜单对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface MenuConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    Menu toEntity(MenuCreateRequest request);
    
    /**
     * 实体转响应
     */
    @Mapping(target = "children", ignore = true) // 子菜单需要在Service层单独处理
    MenuResponse toResponse(Menu menu);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "name", ignore = true) // 菜单名称不允许修改
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget Menu menu, MenuUpdateRequest request);
}
