package com.cloudvps.system.convert;

import com.cloudvps.system.api.dto.request.UserLoginLogCreateRequest;
import com.cloudvps.system.api.dto.response.UserLoginLogResponse;
import com.cloudvps.system.entity.UserLoginLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 用户登录日志对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface UserLoginLogConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    UserLoginLog toEntity(UserLoginLogCreateRequest request);
    
    /**
     * 实体转响应
     */
    UserLoginLogResponse toResponse(UserLoginLog userLoginLog);
}
