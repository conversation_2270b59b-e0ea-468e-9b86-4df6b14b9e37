package com.cloudvps.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.system.api.dto.request.DictDataCreateRequest;
import com.cloudvps.system.api.dto.request.DictDataQueryRequest;
import com.cloudvps.system.api.dto.request.DictDataUpdateRequest;
import com.cloudvps.system.api.dto.response.DictDataResponse;
import com.cloudvps.system.api.enums.DictStatus;

import java.util.List;

/**
 * 字典数据服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DictDataService {
    
    /**
     * 分页查询字典数据
     */
    IPage<DictDataResponse> queryDictData(DictDataQueryRequest request);
    
    /**
     * 根据ID获取字典数据
     */
    DictDataResponse findById(Long id);
    
    /**
     * 创建字典数据
     */
    DictDataResponse createDictData(DictDataCreateRequest request);
    
    /**
     * 更新字典数据
     */
    DictDataResponse updateDictData(Long id, DictDataUpdateRequest request);
    
    /**
     * 删除字典数据
     */
    void deleteDictData(Long id);
    
    /**
     * 批量删除字典数据
     */
    void batchDeleteDictData(List<Long> dictDataIds);
    
    /**
     * 更新字典数据状态
     */
    DictDataResponse updateStatus(Long id, DictStatus status);
    
    /**
     * 根据字典类型ID获取字典数据列表
     */
    List<DictDataResponse> findByDictTypeId(Long dictTypeId);
    
    /**
     * 根据字典类型ID和状态获取字典数据列表
     */
    List<DictDataResponse> findByDictTypeIdAndStatus(Long dictTypeId, DictStatus status);
    
    /**
     * 根据字典类型编码获取字典数据列表
     */
    List<DictDataResponse> findByDictCode(String dictCode);
    
    /**
     * 根据字典类型编码和状态获取字典数据列表
     */
    List<DictDataResponse> findByDictCodeAndStatus(String dictCode, DictStatus status);
    
    /**
     * 根据字典类型ID和字典值获取字典数据
     */
    DictDataResponse findByDictTypeIdAndValue(Long dictTypeId, String dictValue);
    
    /**
     * 根据字典类型ID和字典标签获取字典数据
     */
    DictDataResponse findByDictTypeIdAndLabel(Long dictTypeId, String dictLabel);
    
    /**
     * 检查字典值是否存在
     */
    boolean existsByDictTypeIdAndValue(Long dictTypeId, String dictValue);
    
    /**
     * 检查字典标签是否存在
     */
    boolean existsByDictTypeIdAndLabel(Long dictTypeId, String dictLabel);
    
    /**
     * 获取默认字典数据
     */
    DictDataResponse findDefaultByDictTypeId(Long dictTypeId);
    
    /**
     * 根据字典类型ID删除所有字典数据
     */
    void deleteByDictTypeId(Long dictTypeId);
}
