package com.cloudvps.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.system.api.dto.request.UserLoginLogCreateRequest;
import com.cloudvps.system.api.dto.request.UserLoginLogQueryRequest;
import com.cloudvps.system.api.dto.response.UserLoginLogResponse;
import com.cloudvps.system.api.enums.LoginResult;
import com.cloudvps.system.convert.UserLoginLogConvert;
import com.cloudvps.system.entity.UserLoginLog;
import com.cloudvps.system.mapper.UserLoginLogDataMapper;
import com.cloudvps.system.service.UserLoginLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户登录日志服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserLoginLogServiceImpl implements UserLoginLogService {
    
    private final UserLoginLogDataMapper userLoginLogDataMapper;
    private final UserLoginLogConvert userLoginLogConvert;
    
    @Override
    public IPage<UserLoginLogResponse> queryLoginLogs(UserLoginLogQueryRequest request) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (request.getUserId() != null) {
            queryWrapper.eq(UserLoginLog::getUserId, request.getUserId());
        }
        if (StringUtils.hasText(request.getLoginIp())) {
            queryWrapper.like(UserLoginLog::getLoginIp, request.getLoginIp());
        }
        if (StringUtils.hasText(request.getLoginLocation())) {
            queryWrapper.like(UserLoginLog::getLoginLocation, request.getLoginLocation());
        }
        if (request.getLoginResult() != null) {
            queryWrapper.eq(UserLoginLog::getLoginResult, request.getLoginResult());
        }
        if (request.getLoginTimeStart() != null) {
            queryWrapper.ge(UserLoginLog::getLoginTime, request.getLoginTimeStart());
        }
        if (request.getLoginTimeEnd() != null) {
            queryWrapper.le(UserLoginLog::getLoginTime, request.getLoginTimeEnd());
        }
        
        // 排序
        queryWrapper.orderByDesc(UserLoginLog::getLoginTime);
        
        // 分页查询
        Page<UserLoginLog> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<UserLoginLog> logPage = userLoginLogDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return logPage.convert(userLoginLogConvert::toResponse);
    }
    
    @Override
    public UserLoginLogResponse findById(Long id) {
        UserLoginLog log = userLoginLogDataMapper.selectById(id);
        if (log == null) {
            throw new BusinessException("登录日志不存在");
        }
        return userLoginLogConvert.toResponse(log);
    }
    
    @Override
    @Transactional
    public UserLoginLogResponse createLoginLog(UserLoginLogCreateRequest request) {
        UserLoginLog loginLog = userLoginLogConvert.toEntity(request);
        userLoginLogDataMapper.insert(loginLog);
        
        log.info("创建登录日志成功: userId={}, loginIp={}, result={}",
                loginLog.getUserId(), loginLog.getLoginIp(), loginLog.getLoginResult());
        return userLoginLogConvert.toResponse(loginLog);
    }
    
    @Override
    @Transactional
    public void deleteLoginLog(Long id) {
        UserLoginLog loginLog = userLoginLogDataMapper.selectById(id);
        if (loginLog == null) {
            throw new BusinessException("登录日志不存在");
        }

        userLoginLogDataMapper.deleteById(id);

        log.info("删除登录日志成功: logId={}, userId={}", id, loginLog.getUserId());
    }
    
    @Override
    @Transactional
    public void batchDeleteLoginLogs(List<Long> logIds) {
        if (logIds == null || logIds.isEmpty()) {
            return;
        }
        
        // 批量删除登录日志
        userLoginLogDataMapper.deleteBatchIds(logIds);
        
        log.info("批量删除登录日志成功: logIds={}", logIds);
    }
    
    @Override
    public List<UserLoginLogResponse> findByUserId(Long userId) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLoginLog::getUserId, userId)
                   .orderByDesc(UserLoginLog::getLoginTime);
        
        List<UserLoginLog> logs = userLoginLogDataMapper.selectList(queryWrapper);
        return logs.stream()
                  .map(userLoginLogConvert::toResponse)
                  .collect(Collectors.toList());
    }
    
    @Override
    public List<UserLoginLogResponse> findByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLoginLog::getUserId, userId);
        
        if (startTime != null) {
            queryWrapper.ge(UserLoginLog::getLoginTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(UserLoginLog::getLoginTime, endTime);
        }
        
        queryWrapper.orderByDesc(UserLoginLog::getLoginTime);
        
        List<UserLoginLog> logs = userLoginLogDataMapper.selectList(queryWrapper);
        return logs.stream()
                  .map(userLoginLogConvert::toResponse)
                  .collect(Collectors.toList());
    }
    
    @Override
    public List<UserLoginLogResponse> findByLoginResult(LoginResult loginResult) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLoginLog::getLoginResult, loginResult)
                   .orderByDesc(UserLoginLog::getLoginTime);
        
        List<UserLoginLog> logs = userLoginLogDataMapper.selectList(queryWrapper);
        return logs.stream()
                  .map(userLoginLogConvert::toResponse)
                  .collect(Collectors.toList());
    }
    
    @Override
    public List<UserLoginLogResponse> findByLoginIp(String loginIp) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLoginLog::getLoginIp, loginIp)
                   .orderByDesc(UserLoginLog::getLoginTime);
        
        List<UserLoginLog> logs = userLoginLogDataMapper.selectList(queryWrapper);
        return logs.stream()
                  .map(userLoginLogConvert::toResponse)
                  .collect(Collectors.toList());
    }
    
    @Override
    public List<UserLoginLogResponse> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        
        if (startTime != null) {
            queryWrapper.ge(UserLoginLog::getLoginTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(UserLoginLog::getLoginTime, endTime);
        }
        
        queryWrapper.orderByDesc(UserLoginLog::getLoginTime);
        
        List<UserLoginLog> logs = userLoginLogDataMapper.selectList(queryWrapper);
        return logs.stream()
                  .map(userLoginLogConvert::toResponse)
                  .collect(Collectors.toList());
    }
    
    @Override
    public Long countByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLoginLog::getUserId, userId);
        
        if (startTime != null) {
            queryWrapper.ge(UserLoginLog::getLoginTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(UserLoginLog::getLoginTime, endTime);
        }
        
        return userLoginLogDataMapper.selectCount(queryWrapper);
    }
    
    @Override
    public Long countFailuresByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLoginLog::getUserId, userId)
                   .eq(UserLoginLog::getLoginResult, LoginResult.FAILED);
        
        if (startTime != null) {
            queryWrapper.ge(UserLoginLog::getLoginTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(UserLoginLog::getLoginTime, endTime);
        }
        
        return userLoginLogDataMapper.selectCount(queryWrapper);
    }
    
    @Override
    @Transactional
    public void batchCreateLoginLogs(List<UserLoginLogCreateRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            return;
        }
        
        List<UserLoginLog> logs = requests.stream()
                                         .map(userLoginLogConvert::toEntity)
                                         .collect(Collectors.toList());
        
        // 批量插入（使用MyBatis-Plus的批量插入）
        for (UserLoginLog loginLogEntry : logs) {
            userLoginLogDataMapper.insert(loginLogEntry);
        }
        
        log.info("批量创建登录日志成功: count={}", logs.size());
    }
    
    @Override
    @Transactional
    public void deleteLogsBefore(LocalDateTime beforeTime) {
        LambdaQueryWrapper<UserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(UserLoginLog::getLoginTime, beforeTime);
        
        int deletedCount = userLoginLogDataMapper.delete(queryWrapper);
        
        log.info("删除指定时间之前的登录日志成功: beforeTime={}, deletedCount={}", beforeTime, deletedCount);
    }
    
    @Override
    @Transactional
    public UserLoginLogResponse recordLoginSuccess(Long userId, String loginIp, String loginLocation, String userAgent) {
        UserLoginLogCreateRequest request = new UserLoginLogCreateRequest();
        request.setUserId(userId);
        request.setLoginIp(loginIp);
        request.setLoginLocation(loginLocation);
        request.setUserAgent(userAgent);
        request.setLoginTime(LocalDateTime.now());
        request.setLoginResult(LoginResult.SUCCESS);
        
        return createLoginLog(request);
    }
    
    @Override
    @Transactional
    public UserLoginLogResponse recordLoginFailure(Long userId, String loginIp, String loginLocation, String userAgent, String failureReason) {
        UserLoginLogCreateRequest request = new UserLoginLogCreateRequest();
        request.setUserId(userId);
        request.setLoginIp(loginIp);
        request.setLoginLocation(loginLocation);
        request.setUserAgent(userAgent);
        request.setLoginTime(LocalDateTime.now());
        request.setLoginResult(LoginResult.FAILED);
        request.setFailureReason(failureReason);
        
        return createLoginLog(request);
    }
}
