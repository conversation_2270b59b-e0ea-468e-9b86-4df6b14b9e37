package com.cloudvps.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudvps.system.entity.SystemConfig;
import org.apache.ibatis.annotations.Mapper;

/**
 * 系统配置数据访问Mapper
 * 使用MyBatis-Plus实现系统配置的数据访问操作
 * 只保留实际使用的方法，其他CRUD操作使用MyBatis-Plus自带方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SystemConfigDataMapper extends BaseMapper<SystemConfig> {
    // 所有需要的方法都通过MyBatis-Plus的BaseMapper提供
    // 包括：selectPage, selectById, selectOne, insert, updateById,
    //      deleteById, deleteBatchIds, selectCount, selectList等
    // 复杂查询通过LambdaQueryWrapper在Service层实现
}
