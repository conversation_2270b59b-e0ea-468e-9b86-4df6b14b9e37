package com.cloudvps.system.convert;

import com.cloudvps.system.api.dto.request.PermissionCreateRequest;
import com.cloudvps.system.api.dto.request.PermissionUpdateRequest;
import com.cloudvps.system.api.dto.response.PermissionResponse;
import com.cloudvps.system.entity.Permission;
import org.mapstruct.*;

/**
 * 权限对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface PermissionConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    Permission toEntity(PermissionCreateRequest request);
    
    /**
     * 实体转响应
     */
    PermissionResponse toResponse(Permission permission);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "permissionCode", ignore = true) // 权限编码不允许修改
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget Permission permission, PermissionUpdateRequest request);
}
