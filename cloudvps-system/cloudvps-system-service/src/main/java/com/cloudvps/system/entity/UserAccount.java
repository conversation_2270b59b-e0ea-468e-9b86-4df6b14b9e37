package com.cloudvps.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户账户实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_accounts")
public class UserAccount extends BaseEntity {
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 账户余额
     */
    @TableField("balance")
    private BigDecimal balance = BigDecimal.ZERO;

    /**
     * 冻结金额
     */
    @TableField("frozen_amount")
    private BigDecimal frozenAmount = BigDecimal.ZERO;

    /**
     * 累计充值金额
     */
    @TableField("total_recharge")
    private BigDecimal totalRecharge = BigDecimal.ZERO;
    
    /**
     * 累计消费金额
     */
    @TableField("total_consume")
    private BigDecimal totalConsume = BigDecimal.ZERO;

    /**
     * 账户是否锁定
     */
    @TableField("is_locked")
    private Boolean isLocked = false;

    /**
     * 锁定原因
     */
    @TableField("lock_reason")
    private String lockReason;

    /**
     * 最后操作时间
     */
    @TableField("last_operation_time")
    private LocalDateTime lastOperationTime;

    /**
     * 最后操作描述
     */
    @TableField("last_operation_description")
    private String lastOperationDescription;

    
    /**
     * 获取可用余额（余额 - 冻结金额）
     */
    public BigDecimal getAvailableBalance() {
        return balance.subtract(frozenAmount);
    }
    
    /**
     * 检查余额是否足够
     */
    public boolean hasEnoughBalance(BigDecimal amount) {
        return getAvailableBalance().compareTo(amount) >= 0;
    }
}
