package com.cloudvps.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.system.api.dto.request.UserAccountCreateRequest;
import com.cloudvps.system.api.dto.request.UserAccountQueryRequest;
import com.cloudvps.system.api.dto.request.UserAccountUpdateRequest;
import com.cloudvps.system.api.dto.response.UserAccountResponse;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户账户服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserAccountService {
    
    /**
     * 分页查询用户账户
     */
    IPage<UserAccountResponse> queryAccounts(UserAccountQueryRequest request);
    
    /**
     * 根据ID获取用户账户
     */
    UserAccountResponse findById(Long id);
    
    /**
     * 根据用户ID获取用户账户
     */
    UserAccountResponse findByUserId(Long userId);
    
    /**
     * 创建用户账户
     */
    UserAccountResponse createAccount(UserAccountCreateRequest request);
    
    /**
     * 更新用户账户
     */
    UserAccountResponse updateAccount(Long id, UserAccountUpdateRequest request);
    
    /**
     * 删除用户账户
     */
    void deleteAccount(Long id);
    
    /**
     * 批量删除用户账户
     */
    void batchDeleteAccounts(List<Long> accountIds);
    
    /**
     * 检查用户是否有账户
     */
    boolean existsByUserId(Long userId);
    
    /**
     * 锁定账户
     */
    UserAccountResponse lockAccount(Long id, String lockReason);
    
    /**
     * 解锁账户
     */
    UserAccountResponse unlockAccount(Long id);
    
    /**
     * 充值
     */
    UserAccountResponse recharge(Long userId, BigDecimal amount, String description);
    
    /**
     * 冻结金额
     */
    UserAccountResponse freezeAmount(Long userId, BigDecimal amount, String description);
    
    /**
     * 解冻金额
     */
    UserAccountResponse unfreezeAmount(Long userId, BigDecimal amount, String description);
    
    /**
     * 扣减余额（从冻结金额中扣减）
     */
    UserAccountResponse deductBalance(Long userId, BigDecimal amount, String description);
    
    /**
     * 获取锁定的账户列表
     */
    List<UserAccountResponse> findLockedAccounts();
    
    /**
     * 获取未锁定的账户列表
     */
    List<UserAccountResponse> findUnlockedAccounts();
    
    /**
     * 根据余额范围查询账户
     */
    List<UserAccountResponse> findByBalanceRange(BigDecimal minBalance, BigDecimal maxBalance);
}
