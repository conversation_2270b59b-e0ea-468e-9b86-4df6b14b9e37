package com.cloudvps.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudvps.system.entity.RoleMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单关联数据访问Mapper
 * 使用MyBatis-Plus实现角色菜单关联的数据访问操作
 * 只保留实际使用的方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface RoleMenuDataMapper extends BaseMapper<RoleMenu> {

    /**
     * 批量插入角色菜单关联
     *
     * @param roleMenus 角色菜单关联列表
     * @return 影响行数
     */
    int batchInsertRoleMenus(@Param("roleMenus") List<RoleMenu> roleMenus);
}
