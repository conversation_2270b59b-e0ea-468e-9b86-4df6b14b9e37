package com.cloudvps.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.system.api.dto.request.RoleCreateRequest;
import com.cloudvps.system.api.dto.request.RoleQueryRequest;
import com.cloudvps.system.api.dto.request.RoleUpdateRequest;
import com.cloudvps.system.api.dto.response.RoleResponse;
import com.cloudvps.system.api.enums.RoleStatus;

import java.util.List;

/**
 * 角色服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RoleService {
    
    /**
     * 分页查询角色
     */
    IPage<RoleResponse> queryRoles(RoleQueryRequest request);
    
    /**
     * 根据ID获取角色
     */
    RoleResponse findById(Long id);
    
    /**
     * 根据角色编码获取角色
     */
    RoleResponse findByRoleCode(String roleCode);
    
    /**
     * 创建角色
     */
    RoleResponse createRole(RoleCreateRequest request);
    
    /**
     * 更新角色
     */
    RoleResponse updateRole(Long id, RoleUpdateRequest request);
    
    /**
     * 删除角色
     */
    void deleteRole(Long id);
    
    /**
     * 批量删除角色
     */
    void batchDeleteRoles(List<Long> roleIds);
    
    /**
     * 更新角色状态
     */
    RoleResponse updateStatus(Long id, RoleStatus status);
    
    /**
     * 检查角色编码是否存在
     */
    boolean existsByRoleCode(String roleCode);
    
    /**
     * 获取所有启用的角色
     */
    List<RoleResponse> findAllActiveRoles();
    
    /**
     * 为角色分配菜单
     */
    void assignMenus(Long roleId, List<Long> menuIds);
    
    /**
     * 获取角色的菜单ID列表
     */
    List<Long> getRoleMenuIds(Long roleId);
}
