package com.cloudvps.system.convert;

import com.cloudvps.system.api.dto.request.DictDataCreateRequest;
import com.cloudvps.system.api.dto.request.DictDataUpdateRequest;
import com.cloudvps.system.api.dto.response.DictDataResponse;
import com.cloudvps.system.entity.DictData;
import org.mapstruct.*;

/**
 * 字典数据对象转换接口 - 使用MapStruct自动生成转换代码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface DictDataConvert {
    
    /**
     * 创建请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    DictData toEntity(DictDataCreateRequest request);
    
    /**
     * 实体转响应
     */
    DictDataResponse toResponse(DictData dictData);
    
    /**
     * 更新请求更新实体
     * 忽略null值，只更新非null字段
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "dictTypeId", ignore = true) // 字典类型ID不允许修改
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateFromRequest(@MappingTarget DictData dictData, DictDataUpdateRequest request);
}
