package com.cloudvps.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.system.api.dto.request.UserAccountCreateRequest;
import com.cloudvps.system.api.dto.request.UserAccountQueryRequest;
import com.cloudvps.system.api.dto.request.UserAccountUpdateRequest;
import com.cloudvps.system.api.dto.response.UserAccountResponse;
import com.cloudvps.system.service.UserAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户账户管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/system/user-accounts")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "用户账户管理", description = "用户账户相关接口")
public class UserAccountController {
    
    private final UserAccountService userAccountService;
    
    @GetMapping
    @Operation(summary = "分页查询用户账户", description = "分页查询用户账户列表")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_VIEW')")
    public ApiResponse<IPage<UserAccountResponse>> getAccounts(@Valid UserAccountQueryRequest request) {
        log.debug("分页查询用户账户: {}", request);
        IPage<UserAccountResponse> accounts = userAccountService.queryAccounts(request);
        return ApiResponse.success(accounts);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取用户账户详情", description = "根据账户ID获取用户账户详细信息")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_VIEW')")
    public ApiResponse<UserAccountResponse> getAccountById(
            @Parameter(description = "账户ID") @PathVariable Long id) {
        log.debug("获取用户账户详情: accountId={}", id);
        UserAccountResponse account = userAccountService.findById(id);
        return ApiResponse.success(account);
    }
    
    @GetMapping("/by-user/{userId}")
    @Operation(summary = "根据用户ID获取账户", description = "根据用户ID获取用户账户")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_VIEW')")
    public ApiResponse<UserAccountResponse> getAccountByUserId(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        log.debug("根据用户ID获取账户: userId={}", userId);
        UserAccountResponse account = userAccountService.findByUserId(userId);
        return ApiResponse.success(account);
    }
    
    @PostMapping
    @Operation(summary = "创建用户账户", description = "创建新的用户账户")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_CREATE')")
    public ApiResponse<UserAccountResponse> createAccount(@Valid @RequestBody UserAccountCreateRequest request) {
        log.info("创建用户账户请求: userId={}", request.getUserId());
        UserAccountResponse account = userAccountService.createAccount(request);
        return ApiResponse.success("创建成功", account);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新用户账户", description = "更新用户账户信息")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_UPDATE')")
    public ApiResponse<UserAccountResponse> updateAccount(
            @Parameter(description = "账户ID") @PathVariable Long id,
            @Valid @RequestBody UserAccountUpdateRequest request) {
        log.info("更新用户账户请求: accountId={}", id);
        UserAccountResponse account = userAccountService.updateAccount(id, request);
        return ApiResponse.success("更新成功", account);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户账户", description = "删除指定用户账户")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_DELETE')")
    public ApiResponse<Void> deleteAccount(
            @Parameter(description = "账户ID") @PathVariable Long id) {
        log.info("删除用户账户请求: accountId={}", id);
        userAccountService.deleteAccount(id);
        return ApiResponse.success("删除成功", null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除用户账户", description = "批量删除指定用户账户")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_DELETE')")
    public ApiResponse<Void> batchDeleteAccounts(@RequestBody List<Long> accountIds) {
        log.info("批量删除用户账户请求: accountIds={}", accountIds);
        userAccountService.batchDeleteAccounts(accountIds);
        return ApiResponse.success("批量删除成功", null);
    }
    
    @PutMapping("/{id}/lock")
    @Operation(summary = "锁定账户", description = "锁定指定用户账户")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_UPDATE')")
    public ApiResponse<UserAccountResponse> lockAccount(
            @Parameter(description = "账户ID") @PathVariable Long id,
            @Parameter(description = "锁定原因") @RequestParam String lockReason) {
        log.info("锁定账户请求: accountId={}, reason={}", id, lockReason);
        UserAccountResponse account = userAccountService.lockAccount(id, lockReason);
        return ApiResponse.success("锁定成功", account);
    }
    
    @PutMapping("/{id}/unlock")
    @Operation(summary = "解锁账户", description = "解锁指定用户账户")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_UPDATE')")
    public ApiResponse<UserAccountResponse> unlockAccount(
            @Parameter(description = "账户ID") @PathVariable Long id) {
        log.info("解锁账户请求: accountId={}", id);
        UserAccountResponse account = userAccountService.unlockAccount(id);
        return ApiResponse.success("解锁成功", account);
    }
    
    @PostMapping("/recharge")
    @Operation(summary = "充值", description = "为用户账户充值")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_RECHARGE')")
    public ApiResponse<UserAccountResponse> recharge(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "充值金额") @RequestParam BigDecimal amount,
            @Parameter(description = "操作描述") @RequestParam(required = false) String description) {
        log.info("充值请求: userId={}, amount={}", userId, amount);
        UserAccountResponse account = userAccountService.recharge(userId, amount, description);
        return ApiResponse.success("充值成功", account);
    }
    
    @PostMapping("/freeze")
    @Operation(summary = "冻结金额", description = "冻结用户账户金额")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_FREEZE')")
    public ApiResponse<UserAccountResponse> freezeAmount(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "冻结金额") @RequestParam BigDecimal amount,
            @Parameter(description = "操作描述") @RequestParam(required = false) String description) {
        log.info("冻结金额请求: userId={}, amount={}", userId, amount);
        UserAccountResponse account = userAccountService.freezeAmount(userId, amount, description);
        return ApiResponse.success("冻结成功", account);
    }
    
    @PostMapping("/unfreeze")
    @Operation(summary = "解冻金额", description = "解冻用户账户金额")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_FREEZE')")
    public ApiResponse<UserAccountResponse> unfreezeAmount(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "解冻金额") @RequestParam BigDecimal amount,
            @Parameter(description = "操作描述") @RequestParam(required = false) String description) {
        log.info("解冻金额请求: userId={}, amount={}", userId, amount);
        UserAccountResponse account = userAccountService.unfreezeAmount(userId, amount, description);
        return ApiResponse.success("解冻成功", account);
    }
    
    @PostMapping("/deduct")
    @Operation(summary = "扣减余额", description = "从冻结金额中扣减余额")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_DEDUCT')")
    public ApiResponse<UserAccountResponse> deductBalance(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "扣减金额") @RequestParam BigDecimal amount,
            @Parameter(description = "操作描述") @RequestParam(required = false) String description) {
        log.info("扣减余额请求: userId={}, amount={}", userId, amount);
        UserAccountResponse account = userAccountService.deductBalance(userId, amount, description);
        return ApiResponse.success("扣减成功", account);
    }
    
    @GetMapping("/locked")
    @Operation(summary = "获取锁定账户", description = "获取所有锁定的用户账户")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_VIEW')")
    public ApiResponse<List<UserAccountResponse>> getLockedAccounts() {
        log.debug("获取锁定账户");
        List<UserAccountResponse> accounts = userAccountService.findLockedAccounts();
        return ApiResponse.success(accounts);
    }
    
    @GetMapping("/unlocked")
    @Operation(summary = "获取未锁定账户", description = "获取所有未锁定的用户账户")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_VIEW')")
    public ApiResponse<List<UserAccountResponse>> getUnlockedAccounts() {
        log.debug("获取未锁定账户");
        List<UserAccountResponse> accounts = userAccountService.findUnlockedAccounts();
        return ApiResponse.success(accounts);
    }
    
    @GetMapping("/by-balance-range")
    @Operation(summary = "根据余额范围查询", description = "根据余额范围查询用户账户")
    @PreAuthorize("hasAuthority('USER_ACCOUNT_VIEW')")
    public ApiResponse<List<UserAccountResponse>> getAccountsByBalanceRange(
            @Parameter(description = "最小余额") @RequestParam(required = false) BigDecimal minBalance,
            @Parameter(description = "最大余额") @RequestParam(required = false) BigDecimal maxBalance) {
        log.debug("根据余额范围查询: minBalance={}, maxBalance={}", minBalance, maxBalance);
        List<UserAccountResponse> accounts = userAccountService.findByBalanceRange(minBalance, maxBalance);
        return ApiResponse.success(accounts);
    }
}
