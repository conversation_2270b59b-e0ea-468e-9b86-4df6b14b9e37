package com.cloudvps.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cloudvps.common.mybatis.entity.BaseEntity;
import com.cloudvps.system.api.enums.UserStatus;
import com.cloudvps.system.api.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("users")
@Data
@EqualsAndHashCode(callSuper = true)
public class User extends BaseEntity {
    
    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 头像URL
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 用户状态
     */
    @TableField("status")
    private UserStatus status = UserStatus.ACTIVE;

    /**
     * 用户类型
     */
    @TableField("user_type")
    private UserType userType = UserType.REGULAR;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;
}
