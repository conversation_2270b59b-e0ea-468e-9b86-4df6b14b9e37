package com.cloudvps.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudvps.system.api.dto.request.DictTypeCreateRequest;
import com.cloudvps.system.api.dto.request.DictTypeQueryRequest;
import com.cloudvps.system.api.dto.request.DictTypeUpdateRequest;
import com.cloudvps.system.api.dto.response.DictTypeResponse;
import com.cloudvps.system.api.enums.DictStatus;

import java.util.List;

/**
 * 字典类型服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DictTypeService {
    
    /**
     * 分页查询字典类型
     */
    IPage<DictTypeResponse> queryDictTypes(DictTypeQueryRequest request);
    
    /**
     * 根据ID获取字典类型
     */
    DictTypeResponse findById(Long id);
    
    /**
     * 根据字典编码获取字典类型
     */
    DictTypeResponse findByDictCode(String dictCode);
    
    /**
     * 创建字典类型
     */
    DictTypeResponse createDictType(DictTypeCreateRequest request);
    
    /**
     * 更新字典类型
     */
    DictTypeResponse updateDictType(Long id, DictTypeUpdateRequest request);
    
    /**
     * 删除字典类型
     */
    void deleteDictType(Long id);
    
    /**
     * 批量删除字典类型
     */
    void batchDeleteDictTypes(List<Long> dictTypeIds);
    
    /**
     * 更新字典类型状态
     */
    DictTypeResponse updateStatus(Long id, DictStatus status);
    
    /**
     * 检查字典编码是否存在
     */
    boolean existsByDictCode(String dictCode);
    
    /**
     * 检查字典名称是否存在
     */
    boolean existsByDictName(String dictName);
    
    /**
     * 根据状态获取字典类型列表
     */
    List<DictTypeResponse> findByStatus(DictStatus status);
    
    /**
     * 获取所有启用的字典类型
     */
    List<DictTypeResponse> findActiveDictTypes();
    
    /**
     * 获取所有字典类型
     */
    List<DictTypeResponse> findAllDictTypes();
}
