package com.cloudvps.system.service.impl;

import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.common.core.response.ResponseCode;
import com.cloudvps.system.api.dto.request.UserCreateRequest;
import com.cloudvps.system.api.dto.request.UserQueryRequest;
import com.cloudvps.system.api.dto.request.UserRegisterRequest;
import com.cloudvps.system.api.dto.request.UserUpdateRequest;
import com.cloudvps.system.api.dto.response.UserResponse;
import com.cloudvps.system.entity.Role;
import com.cloudvps.system.entity.User;
import com.cloudvps.system.api.enums.UserStatus;


import com.cloudvps.system.mapper.UserDataMapper;
import com.cloudvps.system.mapper.RoleDataMapper;
import com.cloudvps.system.service.UserService;
import com.cloudvps.system.convert.UserConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.time.LocalDateTime;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService {
    
    // MyBatis-Plus 数据访问层
    private final UserDataMapper userDataMapper;

    // JPA 数据访问层（暂时保留）
    private final RoleDataMapper roleDataMapper;

    // 其他依赖
    private final PasswordEncoder passwordEncoder;
    private final UserConvert userConvert;

    @Override
    @Transactional
    public UserResponse createUser(UserCreateRequest request) {
        log.info("创建用户: username={}, email={}", request.getUsername(), request.getEmail());

        // 检查用户名是否已存在
        if (userDataMapper.existsByUsername(request.getUsername())) {
            throw new BusinessException(ResponseCode.BAD_REQUEST, "用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userDataMapper.existsByEmail(request.getEmail())) {
            throw new BusinessException(ResponseCode.BAD_REQUEST, "邮箱已存在");
        }

        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setPhone(request.getPhone());
        user.setRealName(request.getRealName());
        user.setStatus(request.getStatus() != null ? request.getStatus() : UserStatus.ACTIVE);

        // 设置创建时间和版本号
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        user.setCreatedTime(now);
        user.setUpdatedTime(now);
        user.setVersion(1L);
        user.setDeleted(false);

        // 使用MyBatis-Plus插入用户
        int result = userDataMapper.insertUser(user);
        if (result != 1) {
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "用户创建失败");
        }

        // 如果指定了角色，分配角色
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            assignRoles(user.getId(), request.getRoleIds());
        }

        log.info("用户创建成功: userId={}, username={}", user.getId(), user.getUsername());
        return userConvert.toResponse(user);
    }

    @Override
    @Transactional
    public UserResponse register(UserRegisterRequest request) {
        log.info("用户注册: username={}, email={}", request.getUsername(), request.getEmail());
        
        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException(ResponseCode.BAD_REQUEST, "密码和确认密码不一致");
        }
        
        // 检查用户名是否已存在
        if (userDataMapper.existsByUsername(request.getUsername())) {
            throw new BusinessException(ResponseCode.BAD_REQUEST, "用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userDataMapper.existsByEmail(request.getEmail())) {
            throw new BusinessException(ResponseCode.BAD_REQUEST, "邮箱已存在");
        }
        
        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setPhone(request.getPhone());
        user.setRealName(request.getRealName());
        user.setStatus(UserStatus.ACTIVE);

        // 设置创建时间和版本号
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        user.setCreatedTime(now);
        user.setUpdatedTime(now);
        user.setVersion(1L);
        user.setDeleted(false);

        // 使用MyBatis-Plus插入用户
        int result = userDataMapper.insertUser(user);
        if (result != 1) {
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "用户创建失败");
        }

        // 分配默认角色（使用MyBatis-Plus）
        LambdaQueryWrapper<Role> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(Role::getRoleCode, "USER");
        Role defaultRole = roleDataMapper.selectOne(roleWrapper);
        if (defaultRole == null) {
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "默认角色不存在");
        }

        // 注意：角色关联关系现在通过Service层处理，不再使用实体关联方法

        log.info("用户注册成功: userId={}, username={}", user.getId(), user.getUsername());

        return userConvert.toResponse(user);
    }
    
    @Override
    public User authenticate(String username, String password) {
        log.debug("用户登录验证: username={}", username);
        
        // 根据用户名或邮箱查找用户
        User user = userDataMapper.findByUsernameOrEmail(username, username);
        if (user == null) {
            throw new BusinessException(ResponseCode.UNAUTHORIZED, "用户名或密码错误");
        }
        
        // 检查用户状态
        if (user.getStatus() != UserStatus.ACTIVE) {
            throw new BusinessException(ResponseCode.FORBIDDEN, "用户账号已被禁用");
        }
        
        // 验证密码
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BusinessException(ResponseCode.UNAUTHORIZED, "用户名或密码错误");
        }
        
        log.info("用户登录验证成功: userId={}, username={}", user.getId(), user.getUsername());
        return user;
    }
    
    @Override
    public UserResponse findById(Long id) {
        User user = userDataMapper.findById(id);
        if (user == null) {
            throw new BusinessException(ResponseCode.NOT_FOUND, "用户不存在");
        }
        return userConvert.toResponse(user);
    }
    
    @Override
    public User findByUsername(String username) {
        User user = userDataMapper.findByUsername(username);
        if (user == null) {
            throw new BusinessException(ResponseCode.NOT_FOUND, "用户不存在");
        }
        return user;
    }

    @Override
    public User findByEmail(String email) {
        User user = userDataMapper.findByEmail(email);
        if (user == null) {
            throw new BusinessException(ResponseCode.NOT_FOUND, "用户不存在");
        }
        return user;
    }
    
    @Override
    @Transactional
    public UserResponse updateUser(Long userId, UserUpdateRequest request) {
        log.info("更新用户信息: userId={}", userId);

        User user = userDataMapper.findById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.NOT_FOUND, "用户不存在");
        }

        // 更新邮箱
        if (StringUtils.hasText(request.getEmail()) && !request.getEmail().equals(user.getEmail())) {
            if (userDataMapper.existsByEmail(request.getEmail())) {
                throw new BusinessException(ResponseCode.BAD_REQUEST, "邮箱已存在");
            }
            user.setEmail(request.getEmail());
        }

        // 更新手机号
        if (StringUtils.hasText(request.getPhone()) && !request.getPhone().equals(user.getPhone())) {
            user.setPhone(request.getPhone());
        }

        // 更新真实姓名
        if (StringUtils.hasText(request.getRealName())) {
            user.setRealName(request.getRealName());
        }

        // 使用MyBatis-Plus更新用户信息
        user.setUpdatedTime(java.time.LocalDateTime.now());
        int result = userDataMapper.updateById(user);
        if (result != 1) {
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "用户信息更新失败");
        }

        log.info("用户信息更新成功: userId={}", userId);
        return userConvert.toResponse(user);
    }

    @Override
    @Transactional
    public UserResponse updateProfile(Long userId, UserUpdateRequest request) {
        log.info("更新用户信息: userId={}", userId);

        User user = userDataMapper.findById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.NOT_FOUND, "用户不存在");
        }

        // 更新邮箱
        if (StringUtils.hasText(request.getEmail()) && !request.getEmail().equals(user.getEmail())) {
            if (userDataMapper.existsByEmail(request.getEmail())) {
                throw new BusinessException(ResponseCode.BAD_REQUEST, "邮箱已存在");
            }
            user.setEmail(request.getEmail());
            // 邮箱验证功能暂时移除
        }

        // 更新手机号
        if (StringUtils.hasText(request.getPhone()) && !request.getPhone().equals(user.getPhone())) {
            user.setPhone(request.getPhone());
            // 手机验证功能暂时移除
        }

        // 更新真实姓名
        if (StringUtils.hasText(request.getRealName())) {
            user.setRealName(request.getRealName());
        }

        // 设置更新时间
        user.setUpdatedTime(java.time.LocalDateTime.now());

        // 使用MyBatis-Plus更新用户
        int result = userDataMapper.updateUser(user);
        if (result != 1) {
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "用户信息更新失败，可能是数据已被其他用户修改");
        }

        log.info("用户信息更新成功: userId={}", userId);

        return userConvert.toResponse(user);
    }
    
    @Override
    @Transactional
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        log.info("修改用户密码: userId={}", userId);

        User user = userDataMapper.findById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.NOT_FOUND, "用户不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException(ResponseCode.BAD_REQUEST, "原密码错误");
        }

        // 使用MyBatis-Plus更新密码
        String encodedPassword = passwordEncoder.encode(newPassword);
        int result = userDataMapper.updatePassword(userId, encodedPassword, user.getVersion());
        if (result != 1) {
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "密码更新失败，可能是数据已被其他用户修改");
        }

        log.info("用户密码修改成功: userId={}", userId);
    }
    
    @Override
    @Transactional
    public UserResponse updateStatus(Long userId, UserStatus status) {
        log.info("更新用户状态: userId={}, status={}", userId, status);

        User user = userDataMapper.findById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.NOT_FOUND, "用户不存在");
        }

        // 使用MyBatis-Plus更新用户状态
        int result = userDataMapper.updateUserStatus(userId, status.name(), user.getVersion());
        if (result != 1) {
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "用户状态更新失败，可能是数据已被其他用户修改");
        }

        // 更新本地对象状态
        user.setStatus(status);
        user.setVersion(user.getVersion() + 1);

        log.info("用户状态更新成功: userId={}, status={}", userId, status);
        return userConvert.toResponse(user);
    }
    


    @Override
    public IPage<UserResponse> queryUsers(UserQueryRequest request) {
        log.debug("根据条件查询用户: {}", request);

        // 使用MyBatis-Plus实现
        // 构建分页参数 (MyBatis-Plus页码从1开始)
        Page<User> page = new Page<>(request.getCurrent(), request.getPageSize());

        // 执行查询 - 使用MyBatis-Plus实现
        IPage<User> users = userDataMapper.queryUsersWithConditions(page, request);

        log.debug("查询用户结果: total={}, current={}, size={}",
            users.getTotal(), users.getCurrent(), users.getSize());

        // 转换为响应DTO
        IPage<UserResponse> result = new Page<>(users.getCurrent(), users.getSize(), users.getTotal());
        result.setRecords(users.getRecords().stream()
            .map(userConvert::toResponse)
            .collect(java.util.stream.Collectors.toList()));

        return result;
    }




    
    @Override
    public List<Long> getUserRoleIds(Long userId) {
        log.debug("获取用户角色: userId={}", userId);

        // 使用MyBatis-Plus查找用户
        User user = userDataMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 角色查询功能暂时简化，实际应通过用户角色关联表实现
        log.warn("用户角色查询功能需要完善用户角色关联表操作");

        // 返回空列表，实际应查询用户角色关联表
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public void assignRoles(Long userId, List<Long> roleIds) {
        log.info("为用户分配角色: userId={}, roleIds={}", userId, roleIds);

        // 使用MyBatis-Plus查找用户
        User user = userDataMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证所有角色是否存在（使用MyBatis-Plus）
        for (Long roleId : roleIds) {
            Role role = roleDataMapper.selectById(roleId);
            if (role == null) {
                throw new BusinessException("角色不存在: " + roleId);
            }
        }

        // 注意：角色分配功能需要通过用户角色关联表实现，暂时简化处理

        // 角色分配功能暂时简化，实际应通过用户角色关联表实现
        log.warn("角色分配功能需要完善用户角色关联表操作");

        log.info("用户角色分配成功: userId={}, roleCount={}", userId, roleIds.size());
    }
    
    @Override
    @Transactional
    public void removeRoles(Long userId, List<Long> roleIds) {
        log.info("移除用户角色: userId={}, roleIds={}", userId, roleIds);

        // 使用MyBatis-Plus查找用户
        User user = userDataMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 角色移除功能暂时简化，实际应通过用户角色关联表实现
        log.warn("角色移除功能需要完善用户角色关联表操作");

        log.info("用户角色移除成功: userId={}, roleCount={}", userId, roleIds.size());
    }
    
    @Override
    @Transactional
    public void updateLastLoginTime(Long userId) {
        User user = userDataMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        java.time.LocalDateTime now = java.time.LocalDateTime.now();

        // 使用MyBatis-Plus更新最后登录时间
        user.setLastLoginTime(now);
        userDataMapper.updateById(user);
    }
    
    @Override
    public boolean existsByUsername(String username) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        return userDataMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByEmail(String email) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getEmail, email);
        return userDataMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    @Transactional
    public void deleteUser(Long userId) {
        User user = userDataMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查用户是否有关联数据（如账户、角色等）
        // 这里可以根据业务需求添加相关检查

        // 删除用户
        userDataMapper.deleteById(userId);

        log.info("删除用户成功: userId={}, username={}", userId, user.getUsername());
    }

    @Override
    @Transactional
    public void batchDeleteUsers(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }

        // 检查用户是否存在
        for (Long userId : userIds) {
            User user = userDataMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在: " + userId);
            }
        }

        // 批量删除用户
        userDataMapper.deleteBatchIds(userIds);

        log.info("批量删除用户成功: userIds={}", userIds);
    }
}
