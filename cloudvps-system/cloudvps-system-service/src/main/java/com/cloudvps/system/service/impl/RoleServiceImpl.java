package com.cloudvps.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudvps.common.core.exception.BusinessException;
import com.cloudvps.system.api.dto.request.RoleCreateRequest;
import com.cloudvps.system.api.dto.request.RoleQueryRequest;
import com.cloudvps.system.api.dto.request.RoleUpdateRequest;
import com.cloudvps.system.api.dto.response.RoleResponse;
import com.cloudvps.system.api.enums.RoleStatus;
import com.cloudvps.system.entity.Role;
import com.cloudvps.system.entity.RoleMenu;
import com.cloudvps.system.mapper.RoleDataMapper;
import com.cloudvps.system.mapper.RoleMenuDataMapper;
import com.cloudvps.system.service.RoleService;
import com.cloudvps.system.convert.RoleConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RoleServiceImpl extends ServiceImpl<RoleDataMapper, Role> implements RoleService {
    
    private final RoleDataMapper roleDataMapper;
    private final RoleMenuDataMapper roleMenuDataMapper;
    private final RoleConvert roleConvert;
    
    @Override
    public IPage<RoleResponse> queryRoles(RoleQueryRequest request) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(request.getRoleCode())) {
            queryWrapper.like(Role::getRoleCode, request.getRoleCode());
        }
        if (StringUtils.hasText(request.getRoleName())) {
            queryWrapper.like(Role::getRoleName, request.getRoleName());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq(Role::getStatus, request.getStatus());
        }
        
        // 排序
        queryWrapper.orderByAsc(Role::getSortOrder)
                   .orderByDesc(Role::getCreatedTime);
        
        // 分页查询
        Page<Role> page = new Page<>(request.getCurrent(), request.getPageSize());
        IPage<Role> rolePage = roleDataMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        return rolePage.convert(roleConvert::toResponse);
    }
    
    @Override
    public RoleResponse findById(Long id) {
        Role role = roleDataMapper.selectById(id);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }
        return roleConvert.toResponse(role);
    }
    
    @Override
    public RoleResponse findByRoleCode(String roleCode) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getRoleCode, roleCode);
        
        Role role = roleDataMapper.selectOne(queryWrapper);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }
        return roleConvert.toResponse(role);
    }
    
    @Override
    @Transactional
    public RoleResponse createRole(RoleCreateRequest request) {
        // 检查角色编码是否已存在
        if (existsByRoleCode(request.getRoleCode())) {
            throw new BusinessException("角色编码已存在");
        }
        
        Role role = roleConvert.toEntity(request);
        roleDataMapper.insert(role);

        log.info("创建角色成功: roleCode={}, roleName={}", role.getRoleCode(), role.getRoleName());
        return roleConvert.toResponse(role);
    }
    
    @Override
    @Transactional
    public RoleResponse updateRole(Long id, RoleUpdateRequest request) {
        Role existingRole = roleDataMapper.selectById(id);
        if (existingRole == null) {
            throw new BusinessException("角色不存在");
        }
        
        // 更新字段
        roleConvert.updateFromRequest(existingRole, request);
        roleDataMapper.updateById(existingRole);

        log.info("更新角色成功: roleId={}, roleName={}", id, existingRole.getRoleName());
        return roleConvert.toResponse(existingRole);
    }
    
    @Override
    @Transactional
    public void deleteRole(Long id) {
        Role role = roleDataMapper.selectById(id);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }
        
        // 删除角色菜单关联
        LambdaQueryWrapper<RoleMenu> roleMenuWrapper = new LambdaQueryWrapper<>();
        roleMenuWrapper.eq(RoleMenu::getRoleId, id);
        roleMenuDataMapper.delete(roleMenuWrapper);
        
        // 删除角色
        roleDataMapper.deleteById(id);
        
        log.info("删除角色成功: roleId={}, roleCode={}", id, role.getRoleCode());
    }
    
    @Override
    @Transactional
    public void batchDeleteRoles(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return;
        }
        
        // 删除角色菜单关联
        LambdaQueryWrapper<RoleMenu> roleMenuWrapper = new LambdaQueryWrapper<>();
        roleMenuWrapper.in(RoleMenu::getRoleId, roleIds);
        roleMenuDataMapper.delete(roleMenuWrapper);
        
        // 批量删除角色
        roleDataMapper.deleteBatchIds(roleIds);
        
        log.info("批量删除角色成功: roleIds={}", roleIds);
    }
    
    @Override
    @Transactional
    public RoleResponse updateStatus(Long id, RoleStatus status) {
        Role role = roleDataMapper.selectById(id);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }
        
        role.setStatus(status);
        roleDataMapper.updateById(role);
        
        log.info("更新角色状态成功: roleId={}, status={}", id, status);
        return roleConvert.toResponse(role);
    }
    
    @Override
    public boolean existsByRoleCode(String roleCode) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getRoleCode, roleCode);
        return roleDataMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public List<RoleResponse> findAllActiveRoles() {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getStatus, RoleStatus.ACTIVE)
                   .orderByAsc(Role::getSortOrder);
        
        List<Role> roles = roleDataMapper.selectList(queryWrapper);
        return roles.stream()
                   .map(roleConvert::toResponse)
                   .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public void assignMenus(Long roleId, List<Long> menuIds) {
        // 检查角色是否存在
        Role role = roleDataMapper.selectById(roleId);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }
        
        // 删除现有的角色菜单关联
        LambdaQueryWrapper<RoleMenu> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(RoleMenu::getRoleId, roleId);
        roleMenuDataMapper.delete(deleteWrapper);
        
        // 添加新的角色菜单关联
        if (menuIds != null && !menuIds.isEmpty()) {
            List<RoleMenu> roleMenus = menuIds.stream()
                    .map(menuId -> {
                        RoleMenu roleMenu = new RoleMenu();
                        roleMenu.setRoleId(roleId);
                        roleMenu.setMenuId(menuId);
                        return roleMenu;
                    })
                    .toList();

            // 使用批量插入提高性能
            roleMenuDataMapper.batchInsertRoleMenus(roleMenus);
        }
        
        log.info("为角色分配菜单成功: roleId={}, menuIds={}", roleId, menuIds);
    }
    
    @Override
    public List<Long> getRoleMenuIds(Long roleId) {
        LambdaQueryWrapper<RoleMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RoleMenu::getRoleId, roleId);
        
        List<RoleMenu> roleMenus = roleMenuDataMapper.selectList(queryWrapper);
        return roleMenus.stream()
                       .map(RoleMenu::getMenuId)
                       .collect(Collectors.toList());
    }
}
