package com.cloudvps.system;

import com.cloudvps.common.swagger.annotation.EnableCloudVpsSwagger;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 系统服务启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(
    scanBasePackages = {"com.cloudvps.system", "com.cloudvps.common"},
    exclude = {
        org.springframework.cloud.consul.ConsulAutoConfiguration.class,
        org.springframework.cloud.consul.discovery.ConsulDiscoveryClientConfiguration.class,
        org.springframework.cloud.consul.serviceregistry.ConsulAutoServiceRegistrationAutoConfiguration.class,
        org.springframework.cloud.consul.serviceregistry.ConsulServiceRegistryAutoConfiguration.class
    }
)
@EnableCloudVpsSwagger
@MapperScan("com.cloudvps.system.mapper")
public class SystemServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(SystemServiceApplication.class, args);
    }
}
