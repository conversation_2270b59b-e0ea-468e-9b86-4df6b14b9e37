spring:
  application:
    name: cloudvps-service-system
  profiles:
    active: dev
  datasource:
    url: ************************************************
    username: cloudvps
    password: cloudvps123
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.cloudvps.system.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: true
      logic-not-delete-value: false
      table-prefix: ""
    banner: false

  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  cloud:
    consul:
      enabled: false  # 本地开发暂时禁用
      discovery:
        enabled: false
      config:
        enabled: false

server:
  port: 8081
  servlet:
    context-path: /

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.cloudvps: DEBUG
    org.springframework.web: INFO
    com.cloudvps.system.mapper: DEBUG  # MyBatis-Plus SQL日志
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# CloudVPS 自定义配置
cloudvps:
  jwt:
    secret: cloudvps-unified-jwt-secret-key-for-all-services-2024
    expiration: 86400  # 24小时（秒）
    issuer: cloudvps

  swagger:
    title: CloudVPS系统服务API
    description: 用户管理、权限管理、系统配置等核心功能
    version: 1.0.0
    contact:
      name: CloudVPS System Team
      email: <EMAIL>
      url: https://www.cloudvps.com/system
    license:
      name: Apache 2.0
      url: https://www.apache.org/licenses/LICENSE-2.0
    server:
      url: http://localhost:8081
      description: 系统服务开发环境

  security:
    # 不需要认证的路径
    permit-all-paths:
      - /system/auth/register
      - /system/auth/login
      - /actuator/**
      - /swagger-ui/**
      - /v3/api-docs/**
    # 需要管理员权限的路径
    admin-paths:
      - /system/users/admin/**
      - /system/roles/**
      - /system/permissions/**

---
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    root: INFO
    com.cloudvps: DEBUG

---
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    root: WARN
    com.cloudvps: INFO

cloudvps:
  jwt:
    secret: ${JWT_SECRET:cloudvps-unified-jwt-secret-key-for-all-services-2024}
    expiration: ${JWT_EXPIRATION:86400}
    issuer: cloudvps
