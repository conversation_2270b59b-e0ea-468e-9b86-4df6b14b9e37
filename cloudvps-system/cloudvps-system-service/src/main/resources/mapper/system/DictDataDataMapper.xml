<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudvps.system.mapper.DictDataDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cloudvps.system.entity.DictData">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="dict_type_id" property="dictTypeId" jdbcType="BIGINT"/>
        <result column="dict_label" property="dictLabel" jdbcType="VARCHAR"/>
        <result column="dict_value" property="dictValue" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="is_default" property="isDefault" jdbcType="BOOLEAN"/>
        <result column="css_class" property="cssClass" jdbcType="VARCHAR"/>
        <result column="list_class" property="listClass" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, created_time, updated_time, created_by, updated_by, is_deleted, version,
        dict_type_id, dict_label, dict_value, sort_order, status, is_default, css_class, list_class, remark
    </sql>

    <!-- 根据字典类型ID查找字典数据 -->
    <select id="findByDictTypeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_data
        WHERE dict_type_id = #{dictTypeId} AND is_deleted = false
        ORDER BY sort_order ASC, dict_value ASC
    </select>

    <!-- 根据字典类型ID和状态查找字典数据 -->
    <select id="findByDictTypeIdAndStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_data
        WHERE dict_type_id = #{dictTypeId} AND status = #{status} AND is_deleted = false
        ORDER BY sort_order ASC, dict_value ASC
    </select>

    <!-- 根据字典类型编码查找字典数据 -->
    <select id="findByDictCode" resultMap="BaseResultMap">
        SELECT
        dd.id, dd.created_time, dd.updated_time, dd.created_by, dd.updated_by, dd.is_deleted, dd.version,
        dd.dict_type_id, dd.dict_label, dd.dict_value, dd.sort_order, dd.status, dd.is_default, dd.css_class, dd.list_class, dd.remark
        FROM dict_data dd
        INNER JOIN dict_types dt ON dd.dict_type_id = dt.id
        WHERE dt.dict_code = #{dictCode} AND dd.is_deleted = false AND dt.is_deleted = false
        ORDER BY dd.sort_order ASC, dd.dict_value ASC
    </select>

    <!-- 根据字典类型编码和状态查找字典数据 -->
    <select id="findByDictCodeAndStatus" resultMap="BaseResultMap">
        SELECT
        dd.id, dd.created_time, dd.updated_time, dd.created_by, dd.updated_by, dd.is_deleted, dd.version,
        dd.dict_type_id, dd.dict_label, dd.dict_value, dd.sort_order, dd.status, dd.is_default, dd.css_class, dd.list_class, dd.remark
        FROM dict_data dd
        INNER JOIN dict_types dt ON dd.dict_type_id = dt.id
        WHERE dt.dict_code = #{dictCode} AND dd.status = #{status} AND dd.is_deleted = false AND dt.is_deleted = false
        ORDER BY dd.sort_order ASC, dd.dict_value ASC
    </select>

    <!-- 根据字典值查找字典数据 -->
    <select id="findByDictTypeIdAndValue" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_data
        WHERE dict_type_id = #{dictTypeId} AND dict_value = #{dictValue} AND is_deleted = false
    </select>

    <!-- 根据字典标签查找字典数据 -->
    <select id="findByDictTypeIdAndLabel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_data
        WHERE dict_type_id = #{dictTypeId} AND dict_label = #{dictLabel} AND is_deleted = false
    </select>

    <!-- 检查字典值是否存在 -->
    <select id="existsByDictTypeIdAndValue" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM dict_data
        WHERE dict_type_id = #{dictTypeId} AND dict_value = #{dictValue} AND is_deleted = false
    </select>

    <!-- 检查字典标签是否存在 -->
    <select id="existsByDictTypeIdAndLabel" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM dict_data
        WHERE dict_type_id = #{dictTypeId} AND dict_label = #{dictLabel} AND is_deleted = false
    </select>

    <!-- 查找默认字典数据 -->
    <select id="findDefaultByDictTypeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_data
        WHERE dict_type_id = #{dictTypeId} AND is_default = true AND is_deleted = false
        LIMIT 1
    </select>

    <!-- 插入字典数据 -->
    <insert id="insertDictData" parameterType="com.cloudvps.system.entity.DictData" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dict_data (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            dict_type_id, dict_label, dict_value, sort_order, status, is_default, css_class, list_class, remark
        ) VALUES (
            #{createdTime}, #{updatedTime}, #{createdBy}, #{updatedBy}, #{deleted}, #{version},
            #{dictTypeId}, #{dictLabel}, #{dictValue}, #{sortOrder}, #{status}, #{isDefault}, #{cssClass}, #{listClass}, #{remark}
        )
    </insert>

    <!-- 更新字典数据 -->
    <update id="updateDictData" parameterType="com.cloudvps.system.entity.DictData">
        UPDATE dict_data
        <set>
            updated_time = #{updatedTime},
            updated_by = #{updatedBy},
            version = version + 1,
            <if test="dictLabel != null">dict_label = #{dictLabel},</if>
            <if test="dictValue != null">dict_value = #{dictValue},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="cssClass != null">css_class = #{cssClass},</if>
            <if test="listClass != null">list_class = #{listClass},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新字典数据状态 -->
    <update id="updateStatus">
        UPDATE dict_data
        SET status = #{status}, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 软删除字典数据 -->
    <update id="softDeleteDictData">
        UPDATE dict_data
        SET is_deleted = true, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 根据字典类型ID删除所有字典数据 -->
    <update id="deleteByDictTypeId">
        UPDATE dict_data
        SET is_deleted = true, 
            updated_time = NOW(), 
            version = version + 1
        WHERE dict_type_id = #{dictTypeId} AND is_deleted = false
    </update>

    <!-- 分页查询字典数据 -->
    <select id="queryDataWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_data
        WHERE is_deleted = false
        <if test="dictTypeId != null">
            AND dict_type_id = #{dictTypeId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                dict_label LIKE CONCAT('%', #{keyword}, '%') 
                OR dict_value LIKE CONCAT('%', #{keyword}, '%')
                OR remark LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        ORDER BY sort_order ASC, dict_value ASC
    </select>

</mapper>
