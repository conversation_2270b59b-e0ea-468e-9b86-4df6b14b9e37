<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudvps.system.mapper.PermissionDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cloudvps.system.entity.Permission">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="permission_code" property="permissionCode" jdbcType="VARCHAR"/>
        <result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
        <result column="resource_type" property="resourceType" jdbcType="VARCHAR"/>
        <result column="resource_path" property="resourcePath" jdbcType="VARCHAR"/>
        <result column="http_method" property="httpMethod" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, created_time, updated_time, created_by, updated_by, is_deleted, version,
        permission_code, permission_name, resource_type, resource_path, http_method, description, parent_id
    </sql>

    <!-- 根据权限编码查找权限 -->
    <select id="findByPermissionCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM permissions
        WHERE permission_code = #{permissionCode} AND is_deleted = false
    </select>

    <!-- 检查权限编码是否存在 -->
    <select id="existsByPermissionCode" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM permissions
        WHERE permission_code = #{permissionCode} AND is_deleted = false
    </select>

    <!-- 根据权限名称查找权限 -->
    <select id="findByPermissionName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM permissions
        WHERE permission_name = #{permissionName} AND is_deleted = false
    </select>

    <!-- 根据资源类型查找权限 -->
    <select id="findByResourceType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM permissions
        WHERE resource_type = #{resourceType} AND is_deleted = false
        ORDER BY permission_code ASC
    </select>

    <!-- 根据资源路径查找权限 -->
    <select id="findByResourcePath" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM permissions
        WHERE resource_path = #{resourcePath} AND is_deleted = false
        ORDER BY permission_code ASC
    </select>

    <!-- 根据父权限ID查找子权限 -->
    <select id="findByParentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM permissions
        WHERE parent_id = #{parentId} AND is_deleted = false
        ORDER BY permission_code ASC
    </select>

    <!-- 查找根权限（父权限为空的权限） -->
    <select id="findRootPermissions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM permissions
        WHERE parent_id IS NULL AND is_deleted = false
        ORDER BY permission_code ASC
    </select>

    <!-- 根据关键字搜索权限 -->
    <select id="searchByKeyword" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM permissions
        WHERE (
            permission_code LIKE CONCAT('%', #{keyword}, '%') 
            OR permission_name LIKE CONCAT('%', #{keyword}, '%')
            OR description LIKE CONCAT('%', #{keyword}, '%')
            OR resource_path LIKE CONCAT('%', #{keyword}, '%')
        ) AND is_deleted = false
        ORDER BY permission_code ASC
    </select>

    <!-- 插入权限 -->
    <insert id="insertPermission" parameterType="com.cloudvps.system.entity.Permission" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO permissions (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            permission_code, permission_name, resource_type, resource_path, http_method, description, parent_id
        ) VALUES (
            #{createdTime}, #{updatedTime}, #{createdBy}, #{updatedBy}, #{deleted}, #{version},
            #{permissionCode}, #{permissionName}, #{resourceType}, #{resourcePath}, #{httpMethod}, #{description}, #{parentId}
        )
    </insert>

    <!-- 更新权限 -->
    <update id="updatePermission" parameterType="com.cloudvps.system.entity.Permission">
        UPDATE permissions
        <set>
            updated_time = #{updatedTime},
            updated_by = #{updatedBy},
            version = version + 1,
            <if test="permissionName != null">permission_name = #{permissionName},</if>
            <if test="resourceType != null">resource_type = #{resourceType},</if>
            <if test="resourcePath != null">resource_path = #{resourcePath},</if>
            <if test="httpMethod != null">http_method = #{httpMethod},</if>
            <if test="description != null">description = #{description},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
        </set>
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 软删除权限 -->
    <update id="softDeletePermission">
        UPDATE permissions
        SET is_deleted = true, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 分页查询权限 -->
    <select id="queryPermissionsWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM permissions
        WHERE is_deleted = false
        <if test="resourceType != null">
            AND resource_type = #{resourceType}
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                permission_code LIKE CONCAT('%', #{keyword}, '%') 
                OR permission_name LIKE CONCAT('%', #{keyword}, '%')
                OR description LIKE CONCAT('%', #{keyword}, '%')
                OR resource_path LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        ORDER BY permission_code ASC
    </select>

</mapper>
