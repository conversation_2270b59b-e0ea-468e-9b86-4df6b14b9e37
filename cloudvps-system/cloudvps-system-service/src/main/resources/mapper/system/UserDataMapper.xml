<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudvps.system.mapper.UserDataMapper">

    <!-- 用户基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cloudvps.system.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="VARCHAR"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, created_time, updated_time, created_by, updated_by, is_deleted, version,
        username, email, password, phone, real_name, avatar, status, user_type, last_login_time
    </sql>

    <!-- 根据ID查找用户 -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE id = #{id} AND is_deleted = false
    </select>

    <!-- 根据用户名查找用户 -->
    <select id="findByUsername" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE username = #{username} AND is_deleted = false
    </select>

    <!-- 根据邮箱查找用户 -->
    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE email = #{email} AND is_deleted = false
    </select>

    <!-- 根据用户名或邮箱查找用户 -->
    <select id="findByUsernameOrEmail" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE (username = #{username} OR email = #{email}) AND is_deleted = false
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users
        WHERE username = #{username} AND is_deleted = false
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users
        WHERE email = #{email} AND is_deleted = false
    </select>

    <!-- 插入用户 -->
    <insert id="insertUser" parameterType="com.cloudvps.system.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            username, email, password, phone, real_name, avatar, status, user_type, last_login_time
        ) VALUES (
            #{createdTime}, #{updatedTime}, #{createdBy}, #{updatedBy}, #{deleted}, #{version},
            #{username}, #{email}, #{password}, #{phone}, #{realName}, #{avatar}, #{status}, #{userType}, #{lastLoginTime}
        )
    </insert>

    <!-- 更新用户信息 -->
    <update id="updateUser" parameterType="com.cloudvps.system.entity.User">
        UPDATE users
        <set>
            updated_time = #{updatedTime},
            updated_by = #{updatedBy},
            version = version + 1,
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
        </set>
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新用户状态 -->
    <update id="updateUserStatus">
        UPDATE users
        SET status = #{status},
            updated_time = NOW(),
            version = version + 1
        WHERE id = #{userId} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新最后登录时间 -->
    <update id="updateLastLoginTime">
        UPDATE users
        SET last_login_time = #{lastLoginTime},
            updated_time = NOW(),
            version = version + 1
        WHERE id = #{userId} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新用户密码 -->
    <update id="updatePassword">
        UPDATE users
        SET password = #{password},
            updated_time = NOW(),
            version = version + 1
        WHERE id = #{userId} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 动态条件查询用户（分页） -->
    <select id="queryUsersWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        <where>
            is_deleted = false
            <if test="request.status != null">
                AND status = #{request.status}
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                username LIKE CONCAT('%', #{request.keyword}, '%')
                OR email LIKE CONCAT('%', #{request.keyword}, '%')
                OR real_name LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
            <if test="request.username != null and request.username != ''">
                AND username LIKE CONCAT('%', #{request.username}, '%')
            </if>
            <if test="request.email != null and request.email != ''">
                AND email LIKE CONCAT('%', #{request.email}, '%')
            </if>
            <if test="request.realName != null and request.realName != ''">
                AND real_name LIKE CONCAT('%', #{request.realName}, '%')
            </if>
            <if test="request.phone != null and request.phone != ''">
                AND phone LIKE CONCAT('%', #{request.phone}, '%')
            </if>
            <if test="request.createdAtStart != null and request.createdAtStart != ''">
                AND created_time >= CAST(#{request.createdAtStart} AS TIMESTAMP)
            </if>
            <if test="request.createdAtEnd != null and request.createdAtEnd != ''">
                AND created_time &lt;= CAST(#{request.createdAtEnd} AS TIMESTAMP)
            </if>
        </where>
        <choose>
            <when test="request.sortField != null and request.sortField != ''">
                ORDER BY
                <choose>
                    <when test="request.sortField == 'id'">id</when>
                    <when test="request.sortField == 'username'">username</when>
                    <when test="request.sortField == 'email'">email</when>
                    <when test="request.sortField == 'realName' or request.sortField == 'real_name'">real_name</when>
                    <when test="request.sortField == 'phone'">phone</when>
                    <when test="request.sortField == 'status'">status</when>
                    <when test="request.sortField == 'createdAt' or request.sortField == 'created_at' or request.sortField == 'createdTime'">created_time</when>
                    <when test="request.sortField == 'updatedAt' or request.sortField == 'updated_at' or request.sortField == 'updatedTime'">updated_time</when>
                    <when test="request.sortField == 'lastLoginTime' or request.sortField == 'last_login_time'">last_login_time</when>
                    <otherwise>created_time</otherwise>
                </choose>
                <choose>
                    <when test="request.sortOrder != null and request.sortOrder.toLowerCase() == 'asc'">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY created_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
