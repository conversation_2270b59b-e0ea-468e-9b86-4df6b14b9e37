<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudvps.system.mapper.DictTypeDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cloudvps.system.entity.DictType">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="dict_code" property="dictCode" jdbcType="VARCHAR"/>
        <result column="dict_name" property="dictName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, created_time, updated_time, created_by, updated_by, is_deleted, version,
        dict_code, dict_name, description, status
    </sql>

    <!-- 根据字典编码查找字典类型 -->
    <select id="findByDictCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_types
        WHERE dict_code = #{dictCode} AND is_deleted = false
    </select>

    <!-- 检查字典编码是否存在 -->
    <select id="existsByDictCode" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM dict_types
        WHERE dict_code = #{dictCode} AND is_deleted = false
    </select>

    <!-- 根据字典名称查找字典类型 -->
    <select id="findByDictName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_types
        WHERE dict_name = #{dictName} AND is_deleted = false
    </select>

    <!-- 检查字典名称是否存在 -->
    <select id="existsByDictName" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM dict_types
        WHERE dict_name = #{dictName} AND is_deleted = false
    </select>

    <!-- 根据状态查找字典类型 -->
    <select id="findByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_types
        WHERE status = #{status} AND is_deleted = false
        ORDER BY dict_code ASC
    </select>

    <!-- 查找所有启用的字典类型 -->
    <select id="findActiveTypes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_types
        WHERE status = 'ACTIVE' AND is_deleted = false
        ORDER BY dict_code ASC
    </select>

    <!-- 根据关键字搜索字典类型 -->
    <select id="searchByKeyword" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_types
        WHERE (
            dict_code LIKE CONCAT('%', #{keyword}, '%') 
            OR dict_name LIKE CONCAT('%', #{keyword}, '%')
            OR description LIKE CONCAT('%', #{keyword}, '%')
        ) AND is_deleted = false
        ORDER BY dict_code ASC
    </select>

    <!-- 插入字典类型 -->
    <insert id="insertDictType" parameterType="com.cloudvps.system.entity.DictType" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dict_types (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            dict_code, dict_name, description, status
        ) VALUES (
            #{createdTime}, #{updatedTime}, #{createdBy}, #{updatedBy}, #{deleted}, #{version},
            #{dictCode}, #{dictName}, #{description}, #{status}
        )
    </insert>

    <!-- 更新字典类型 -->
    <update id="updateDictType" parameterType="com.cloudvps.system.entity.DictType">
        UPDATE dict_types
        <set>
            updated_time = #{updatedTime},
            updated_by = #{updatedBy},
            version = version + 1,
            <if test="dictName != null">dict_name = #{dictName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
        </set>
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新字典类型状态 -->
    <update id="updateStatus">
        UPDATE dict_types
        SET status = #{status}, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 软删除字典类型 -->
    <update id="softDeleteDictType">
        UPDATE dict_types
        SET is_deleted = true, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 分页查询字典类型 -->
    <select id="queryTypesWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dict_types
        WHERE is_deleted = false
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                dict_code LIKE CONCAT('%', #{keyword}, '%') 
                OR dict_name LIKE CONCAT('%', #{keyword}, '%')
                OR description LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        ORDER BY dict_code ASC
    </select>

</mapper>
