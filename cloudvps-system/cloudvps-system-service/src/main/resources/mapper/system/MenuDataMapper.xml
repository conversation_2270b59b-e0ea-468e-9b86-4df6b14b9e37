<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudvps.system.mapper.MenuDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cloudvps.system.entity.Menu">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="visible" property="visible" jdbcType="BOOLEAN"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="permission" property="permission" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
        <result column="cache" property="cache" jdbcType="BOOLEAN"/>
        <result column="external" property="external" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, created_time, updated_time, created_by, updated_by, is_deleted, version,
        name, title, icon, path, component, type, parent_id, sort, visible, enabled, 
        permission, description, params, cache, external
    </sql>

    <!-- 根据菜单名称查找菜单 -->
    <select id="findByName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE name = #{name} AND is_deleted = false
    </select>

    <!-- 检查菜单名称是否存在 -->
    <select id="existsByName" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM sys_menu
        WHERE name = #{name} AND is_deleted = false
    </select>

    <!-- 根据父菜单ID查找子菜单 -->
    <select id="findByParentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE parent_id = #{parentId} AND is_deleted = false
        ORDER BY sort ASC, id ASC
    </select>

    <!-- 查找根菜单（父菜单为空的菜单） -->
    <select id="findRootMenus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE parent_id IS NULL AND is_deleted = false
        ORDER BY sort ASC, id ASC
    </select>

    <!-- 根据菜单类型查找菜单 -->
    <select id="findByType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE type = #{type} AND is_deleted = false
        ORDER BY sort ASC, id ASC
    </select>

    <!-- 查找启用的菜单 -->
    <select id="findEnabledMenus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE enabled = true AND is_deleted = false
        ORDER BY sort ASC, id ASC
    </select>

    <!-- 查找可见的菜单 -->
    <select id="findVisibleMenus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE visible = true AND is_deleted = false
        ORDER BY sort ASC, id ASC
    </select>

    <!-- 根据权限标识查找菜单 -->
    <select id="findByPermission" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE permission = #{permission} AND is_deleted = false
        ORDER BY sort ASC, id ASC
    </select>

    <!-- 根据关键字搜索菜单 -->
    <select id="searchByKeyword" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE (
            name LIKE CONCAT('%', #{keyword}, '%') 
            OR title LIKE CONCAT('%', #{keyword}, '%')
            OR description LIKE CONCAT('%', #{keyword}, '%')
            OR path LIKE CONCAT('%', #{keyword}, '%')
        ) AND is_deleted = false
        ORDER BY sort ASC, id ASC
    </select>

    <!-- 插入菜单 -->
    <insert id="insertMenu" parameterType="com.cloudvps.system.entity.Menu" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sys_menu (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            name, title, icon, path, component, type, parent_id, sort, visible, enabled, 
            permission, description, params, cache, external
        ) VALUES (
            #{createdTime}, #{updatedTime}, #{createdBy}, #{updatedBy}, #{deleted}, #{version},
            #{name}, #{title}, #{icon}, #{path}, #{component}, #{type}, #{parentId}, #{sort}, #{visible}, #{enabled}, 
            #{permission}, #{description}, #{params}, #{cache}, #{external}
        )
    </insert>

    <!-- 更新菜单 -->
    <update id="updateMenu" parameterType="com.cloudvps.system.entity.Menu">
        UPDATE sys_menu
        <set>
            updated_time = #{updatedTime},
            updated_by = #{updatedBy},
            version = version + 1,
            <if test="title != null">title = #{title},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="path != null">path = #{path},</if>
            <if test="component != null">component = #{component},</if>
            <if test="type != null">type = #{type},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="permission != null">permission = #{permission},</if>
            <if test="description != null">description = #{description},</if>
            <if test="params != null">params = #{params},</if>
            <if test="cache != null">cache = #{cache},</if>
            <if test="external != null">external = #{external},</if>
        </set>
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新菜单状态 -->
    <update id="updateEnabled">
        UPDATE sys_menu
        SET enabled = #{enabled}, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新菜单可见性 -->
    <update id="updateVisible">
        UPDATE sys_menu
        SET visible = #{visible}, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 软删除菜单 -->
    <update id="softDeleteMenu">
        UPDATE sys_menu
        SET is_deleted = true, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 分页查询菜单 -->
    <select id="queryMenusWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE is_deleted = false
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="enabled != null">
            AND enabled = #{enabled}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                name LIKE CONCAT('%', #{keyword}, '%') 
                OR title LIKE CONCAT('%', #{keyword}, '%')
                OR description LIKE CONCAT('%', #{keyword}, '%')
                OR path LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        ORDER BY sort ASC, id ASC
    </select>

</mapper>
