<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudvps.system.mapper.RoleMenuDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cloudvps.system.entity.RoleMenu">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="menu_id" property="menuId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, created_time, updated_time, created_by, updated_by, is_deleted, version,
        role_id, menu_id
    </sql>



    <!-- 批量插入角色菜单关联 -->
    <insert id="batchInsertRoleMenus" parameterType="java.util.List">
        INSERT INTO sys_role_menu (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            role_id, menu_id
        ) VALUES
        <foreach collection="roleMenus" item="roleMenu" separator=",">
            (#{roleMenu.createdTime}, #{roleMenu.updatedTime}, #{roleMenu.createdBy}, #{roleMenu.updatedBy}, #{roleMenu.deleted}, #{roleMenu.version},
             #{roleMenu.roleId}, #{roleMenu.menuId})
        </foreach>
    </insert>



</mapper>
