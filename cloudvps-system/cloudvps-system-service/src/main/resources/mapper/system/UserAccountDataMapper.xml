<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudvps.system.mapper.UserAccountDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cloudvps.system.entity.UserAccount">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="balance" property="balance" jdbcType="DECIMAL"/>
        <result column="frozen_amount" property="frozenAmount" jdbcType="DECIMAL"/>
        <result column="total_recharge" property="totalRecharge" jdbcType="DECIMAL"/>
        <result column="total_consume" property="totalConsume" jdbcType="DECIMAL"/>
        <result column="is_locked" property="isLocked" jdbcType="BOOLEAN"/>
        <result column="lock_reason" property="lockReason" jdbcType="VARCHAR"/>
        <result column="last_operation_time" property="lastOperationTime" jdbcType="TIMESTAMP"/>
        <result column="last_operation_description" property="lastOperationDescription" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, created_time, updated_time, created_by, updated_by, is_deleted, version,
        user_id, balance, frozen_amount, total_recharge, total_consume, is_locked, 
        lock_reason, last_operation_time, last_operation_description
    </sql>

    <!-- 根据用户ID查找账户 -->
    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_accounts
        WHERE user_id = #{userId} AND is_deleted = false
        ORDER BY id DESC
        LIMIT 1
    </select>

    <!-- 检查用户是否有账户 -->
    <select id="existsByUserId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_accounts
        WHERE user_id = #{userId} AND is_deleted = false
    </select>

    <!-- 查找余额大于指定金额的账户 -->
    <select id="findByBalanceGreaterThan" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_accounts
        WHERE balance > #{amount} AND is_deleted = false
        ORDER BY balance DESC
    </select>

    <!-- 查找锁定的账户 -->
    <select id="findLockedAccounts" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_accounts
        WHERE is_locked = true AND is_deleted = false
        ORDER BY updated_time DESC
    </select>

    <!-- 查找未锁定的账户 -->
    <select id="findUnlockedAccounts" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_accounts
        WHERE is_locked = false AND is_deleted = false
        ORDER BY updated_time DESC
    </select>

    <!-- 插入用户账户 -->
    <insert id="insertUserAccount" parameterType="com.cloudvps.system.entity.UserAccount" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_accounts (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            user_id, balance, frozen_amount, total_recharge, total_consume, is_locked, 
            lock_reason, last_operation_time, last_operation_description
        ) VALUES (
            #{createdTime}, #{updatedTime}, #{createdBy}, #{updatedBy}, #{deleted}, #{version},
            #{userId}, #{balance}, #{frozenAmount}, #{totalRecharge}, #{totalConsume}, #{isLocked}, 
            #{lockReason}, #{lastOperationTime}, #{lastOperationDescription}
        )
    </insert>

    <!-- 更新用户账户 -->
    <update id="updateUserAccount" parameterType="com.cloudvps.system.entity.UserAccount">
        UPDATE user_accounts
        <set>
            updated_time = #{updatedTime},
            updated_by = #{updatedBy},
            version = version + 1,
            <if test="balance != null">balance = #{balance},</if>
            <if test="frozenAmount != null">frozen_amount = #{frozenAmount},</if>
            <if test="totalRecharge != null">total_recharge = #{totalRecharge},</if>
            <if test="totalConsume != null">total_consume = #{totalConsume},</if>
            <if test="isLocked != null">is_locked = #{isLocked},</if>
            <if test="lockReason != null">lock_reason = #{lockReason},</if>
            <if test="lastOperationTime != null">last_operation_time = #{lastOperationTime},</if>
            <if test="lastOperationDescription != null">last_operation_description = #{lastOperationDescription},</if>
        </set>
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新账户余额（原子操作） -->
    <update id="updateBalance">
        UPDATE user_accounts
        SET balance = balance + #{amount}, 
            updated_time = NOW(), 
            last_operation_time = NOW(),
            last_operation_description = CONCAT('余额变更: ', #{amount}),
            version = version + 1
        WHERE user_id = #{userId} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 冻结金额（原子操作） -->
    <update id="freezeAmount">
        UPDATE user_accounts
        SET frozen_amount = frozen_amount + #{amount}, 
            updated_time = NOW(), 
            last_operation_time = NOW(),
            last_operation_description = CONCAT('冻结金额: ', #{amount}),
            version = version + 1
        WHERE user_id = #{userId} AND version = #{version} AND is_deleted = false
        AND (balance - frozen_amount) >= #{amount}
    </update>

    <!-- 解冻金额（原子操作） -->
    <update id="unfreezeAmount">
        UPDATE user_accounts
        SET frozen_amount = frozen_amount - #{amount}, 
            updated_time = NOW(), 
            last_operation_time = NOW(),
            last_operation_description = CONCAT('解冻金额: ', #{amount}),
            version = version + 1
        WHERE user_id = #{userId} AND version = #{version} AND is_deleted = false
        AND frozen_amount >= #{amount}
    </update>

    <!-- 扣减余额（从冻结金额中扣减，原子操作） -->
    <update id="deductBalance">
        UPDATE user_accounts
        SET balance = balance - #{amount}, 
            frozen_amount = frozen_amount - #{amount}, 
            updated_time = NOW(), 
            last_operation_time = NOW(),
            last_operation_description = CONCAT('扣减余额: ', #{amount}),
            version = version + 1
        WHERE user_id = #{userId} AND version = #{version} AND is_deleted = false
        AND frozen_amount >= #{amount}
    </update>

    <!-- 更新累计充值金额 -->
    <update id="updateTotalRecharge">
        UPDATE user_accounts
        SET total_recharge = total_recharge + #{amount}, 
            updated_time = NOW(), 
            last_operation_time = NOW(),
            last_operation_description = CONCAT('充值: ', #{amount}),
            version = version + 1
        WHERE user_id = #{userId} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新累计消费金额 -->
    <update id="updateTotalConsume">
        UPDATE user_accounts
        SET total_consume = total_consume + #{amount}, 
            updated_time = NOW(), 
            last_operation_time = NOW(),
            last_operation_description = CONCAT('消费: ', #{amount}),
            version = version + 1
        WHERE user_id = #{userId} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 锁定账户 -->
    <update id="lockAccount">
        UPDATE user_accounts
        SET is_locked = true, 
            lock_reason = #{lockReason}, 
            updated_time = NOW(), 
            last_operation_time = NOW(),
            last_operation_description = CONCAT('账户锁定: ', #{lockReason}),
            version = version + 1
        WHERE user_id = #{userId} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 解锁账户 -->
    <update id="unlockAccount">
        UPDATE user_accounts
        SET is_locked = false, 
            lock_reason = NULL, 
            updated_time = NOW(), 
            last_operation_time = NOW(),
            last_operation_description = '账户解锁',
            version = version + 1
        WHERE user_id = #{userId} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 更新最后操作信息 -->
    <update id="updateLastOperation">
        UPDATE user_accounts
        SET last_operation_time = NOW(),
            last_operation_description = #{operationDescription},
            updated_time = NOW(), 
            version = version + 1
        WHERE user_id = #{userId} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 软删除用户账户 -->
    <update id="softDeleteAccount">
        UPDATE user_accounts
        SET is_deleted = true, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 分页查询用户账户 -->
    <select id="queryAccountsWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_accounts
        WHERE is_deleted = false
        <if test="isLocked != null">
            AND is_locked = #{isLocked}
        </if>
        <if test="minBalance != null">
            AND balance >= #{minBalance}
        </if>
        <if test="maxBalance != null">
            AND balance &lt;= #{maxBalance}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                lock_reason LIKE CONCAT('%', #{keyword}, '%') 
                OR last_operation_description LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        ORDER BY updated_time DESC
    </select>

</mapper>
