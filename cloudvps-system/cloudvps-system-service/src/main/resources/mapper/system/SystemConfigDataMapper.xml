<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudvps.system.mapper.SystemConfigDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cloudvps.system.entity.SystemConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="config_key" property="configKey" jdbcType="VARCHAR"/>
        <result column="config_value" property="configValue" jdbcType="VARCHAR"/>
        <result column="config_type" property="configType" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="is_encrypted" property="isEncrypted" jdbcType="BOOLEAN"/>
        <result column="is_system" property="isSystem" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, created_time, updated_time, created_by, updated_by, is_deleted, version,
        config_key, config_value, config_type, description, is_encrypted, is_system
    </sql>

    <!-- 根据配置键查找配置 -->
    <select id="findByConfigKey" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM system_configs
        WHERE config_key = #{configKey} AND is_deleted = false
    </select>

    <!-- 检查配置键是否存在 -->
    <select id="existsByConfigKey" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM system_configs
        WHERE config_key = #{configKey} AND is_deleted = false
    </select>

    <!-- 根据配置类型查找配置 -->
    <select id="findByConfigType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM system_configs
        WHERE config_type = #{configType} AND is_deleted = false
        ORDER BY config_key ASC
    </select>

    <!-- 查找所有非系统配置 -->
    <select id="findByIsSystemFalse" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM system_configs
        WHERE is_system = false AND is_deleted = false
        ORDER BY config_key ASC
    </select>

    <!-- 查找所有系统配置 -->
    <select id="findByIsSystemTrue" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM system_configs
        WHERE is_system = true AND is_deleted = false
        ORDER BY config_key ASC
    </select>

    <!-- 查找所有加密配置 -->
    <select id="findByIsEncryptedTrue" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM system_configs
        WHERE is_encrypted = true AND is_deleted = false
        ORDER BY config_key ASC
    </select>

    <!-- 根据配置键前缀查找配置 -->
    <select id="findByConfigKeyPrefix" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM system_configs
        WHERE config_key LIKE CONCAT(#{prefix}, '%') AND is_deleted = false
        ORDER BY config_key ASC
    </select>

    <!-- 批量查找配置 -->
    <select id="findByConfigKeys" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM system_configs
        WHERE config_key IN
        <foreach collection="keys" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
        AND is_deleted = false
        ORDER BY config_key ASC
    </select>

    <!-- 插入系统配置 -->
    <insert id="insertConfig" parameterType="com.cloudvps.system.entity.SystemConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO system_configs (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            config_key, config_value, config_type, description, is_encrypted, is_system
        ) VALUES (
            #{createdTime}, #{updatedTime}, #{createdBy}, #{updatedBy}, #{deleted}, #{version},
            #{configKey}, #{configValue}, #{configType}, #{description}, #{isEncrypted}, #{isSystem}
        )
    </insert>

    <!-- 更新系统配置 -->
    <update id="updateConfig" parameterType="com.cloudvps.system.entity.SystemConfig">
        UPDATE system_configs
        <set>
            updated_time = #{updatedTime},
            updated_by = #{updatedBy},
            version = version + 1,
            <if test="configValue != null">config_value = #{configValue},</if>
            <if test="configType != null">config_type = #{configType},</if>
            <if test="description != null">description = #{description},</if>
            <if test="isEncrypted != null">is_encrypted = #{isEncrypted},</if>
            <if test="isSystem != null">is_system = #{isSystem},</if>
        </set>
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 根据配置键更新配置值 -->
    <update id="updateConfigValue">
        UPDATE system_configs
        SET config_value = #{configValue}, 
            updated_time = NOW(), 
            version = version + 1
        WHERE config_key = #{configKey} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 软删除配置 -->
    <update id="softDeleteConfig">
        UPDATE system_configs
        SET is_deleted = true, 
            updated_time = NOW(), 
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND is_deleted = false
    </update>

    <!-- 分页查询系统配置 -->
    <select id="queryConfigsWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM system_configs
        WHERE is_deleted = false
        <if test="configType != null">
            AND config_type = #{configType}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                config_key LIKE CONCAT('%', #{keyword}, '%') 
                OR config_value LIKE CONCAT('%', #{keyword}, '%')
                OR description LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        ORDER BY config_key ASC
    </select>

</mapper>
