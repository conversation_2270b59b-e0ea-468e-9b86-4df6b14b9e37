<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudvps.system.mapper.UserLoginLogDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cloudvps.system.entity.UserLoginLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="login_ip" property="loginIp" jdbcType="VARCHAR"/>
        <result column="login_location" property="loginLocation" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="login_result" property="loginResult" jdbcType="VARCHAR"/>
        <result column="failure_reason" property="failureReason" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, created_time, updated_time, created_by, updated_by, is_deleted, version,
        user_id, login_ip, login_location, user_agent, login_time, login_result, failure_reason
    </sql>

    <!-- 根据用户ID查找登录日志 -->
    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_login_logs
        WHERE user_id = #{userId} AND is_deleted = false
        ORDER BY login_time DESC
    </select>

    <!-- 根据用户ID和时间范围查找登录日志 -->
    <select id="findByUserIdAndTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_login_logs
        WHERE user_id = #{userId} 
        AND login_time BETWEEN #{startTime} AND #{endTime}
        AND is_deleted = false
        ORDER BY login_time DESC
    </select>

    <!-- 根据登录结果查找日志 -->
    <select id="findByLoginResult" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_login_logs
        WHERE login_result = #{loginResult} AND is_deleted = false
        ORDER BY login_time DESC
    </select>

    <!-- 根据IP地址查找登录日志 -->
    <select id="findByLoginIp" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_login_logs
        WHERE login_ip = #{loginIp} AND is_deleted = false
        ORDER BY login_time DESC
    </select>

    <!-- 查找指定时间范围内的登录日志 -->
    <select id="findByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_login_logs
        WHERE login_time BETWEEN #{startTime} AND #{endTime}
        AND is_deleted = false
        ORDER BY login_time DESC
    </select>

    <!-- 统计用户登录次数 -->
    <select id="countByUserIdAndTimeRange" resultType="long">
        SELECT COUNT(1)
        FROM user_login_logs
        WHERE user_id = #{userId} 
        AND login_time BETWEEN #{startTime} AND #{endTime}
        AND is_deleted = false
    </select>

    <!-- 统计登录失败次数 -->
    <select id="countFailuresByUserIdAndTimeRange" resultType="long">
        SELECT COUNT(1)
        FROM user_login_logs
        WHERE user_id = #{userId} 
        AND login_time BETWEEN #{startTime} AND #{endTime}
        AND login_result != 'SUCCESS'
        AND is_deleted = false
    </select>

    <!-- 插入登录日志 -->
    <insert id="insertLoginLog" parameterType="com.cloudvps.system.entity.UserLoginLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_login_logs (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            user_id, login_ip, login_location, user_agent, login_time, login_result, failure_reason
        ) VALUES (
            #{createdTime}, #{updatedTime}, #{createdBy}, #{updatedBy}, #{deleted}, #{version},
            #{userId}, #{loginIp}, #{loginLocation}, #{userAgent}, #{loginTime}, #{loginResult}, #{failureReason}
        )
    </insert>

    <!-- 批量插入登录日志 -->
    <insert id="batchInsertLoginLogs" parameterType="java.util.List">
        INSERT INTO user_login_logs (
            created_time, updated_time, created_by, updated_by, is_deleted, version,
            user_id, login_ip, login_location, user_agent, login_time, login_result, failure_reason
        ) VALUES
        <foreach collection="logs" item="log" separator=",">
            (#{log.createdTime}, #{log.updatedTime}, #{log.createdBy}, #{log.updatedBy}, #{log.deleted}, #{log.version},
             #{log.userId}, #{log.loginIp}, #{log.loginLocation}, #{log.userAgent}, #{log.loginTime}, #{log.loginResult}, #{log.failureReason})
        </foreach>
    </insert>

    <!-- 删除指定时间之前的登录日志 -->
    <delete id="deleteLogsBefore">
        DELETE FROM user_login_logs
        WHERE login_time &lt; #{beforeTime}
    </delete>

    <!-- 分页查询登录日志 -->
    <select id="queryLogsWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_login_logs
        WHERE is_deleted = false
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="loginResult != null">
            AND login_result = #{loginResult}
        </if>
        <if test="startTime != null">
            AND login_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND login_time &lt;= #{endTime}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                login_ip LIKE CONCAT('%', #{keyword}, '%') 
                OR login_location LIKE CONCAT('%', #{keyword}, '%')
                OR user_agent LIKE CONCAT('%', #{keyword}, '%')
                OR failure_reason LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        ORDER BY login_time DESC
    </select>

</mapper>
