-- =============================================
-- CloudVPS 系统服务数据库初始化脚本
-- 数据库: cloudvps_system
-- 版本: 1.0.0
-- 创建时间: 2024-01-01
-- =============================================

-- 创建数据库 (如果不存在)
-- CREATE DATABASE IF NOT EXISTS cloudvps_system;
-- USE cloudvps_system;

-- =============================================
-- 1. 用户相关表
-- =============================================

-- 用户基础信息表
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50),
    phone VARCHAR(20),
    avatar VARCHAR(255),
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'LOCKED', 'DELETED')),
    user_type VARCHAR(20) DEFAULT 'REGULAR' CHECK (user_type IN ('REGULAR', 'ADMIN', 'SYSTEM')),
    last_login_time TIMESTAMP,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 用户账户表
CREATE TABLE IF NOT EXISTS user_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    balance DECIMAL(10,2) DEFAULT 0.00,
    frozen_amount DECIMAL(10,2) DEFAULT 0.00,
    total_recharge DECIMAL(10,2) DEFAULT 0.00,
    total_consume DECIMAL(10,2) DEFAULT 0.00,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 用户登录日志表
CREATE TABLE IF NOT EXISTS user_login_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    login_ip VARCHAR(45),
    login_location VARCHAR(100),
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    login_result VARCHAR(20) DEFAULT 'SUCCESS' CHECK (login_result IN ('SUCCESS', 'FAILED', 'LOCKED')),
    failure_reason VARCHAR(255),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- =============================================
-- 2. 角色权限相关表
-- =============================================

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id BIGSERIAL PRIMARY KEY,
    role_code VARCHAR(50) UNIQUE NOT NULL,
    role_name VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE')),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id BIGSERIAL PRIMARY KEY,
    permission_code VARCHAR(100) UNIQUE NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN ('MENU', 'BUTTON', 'API', 'DATA')),
    resource_path VARCHAR(255),
    http_method VARCHAR(10),
    parent_id BIGINT,
    sort_order INTEGER DEFAULT 0,
    description TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES permissions(id) ON DELETE SET NULL
);

-- 用户角色关系表
CREATE TABLE IF NOT EXISTS user_roles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(user_id, role_id)
);

-- 角色权限关系表
CREATE TABLE IF NOT EXISTS role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(role_id, permission_id)
);

-- =============================================
-- 3. 系统配置相关表
-- =============================================

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'STRING' CHECK (config_type IN ('STRING', 'INTEGER', 'BOOLEAN', 'JSON')),
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    is_system BOOLEAN DEFAULT FALSE,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 字典类型表
CREATE TABLE IF NOT EXISTS dict_types (
    id BIGSERIAL PRIMARY KEY,
    dict_code VARCHAR(50) UNIQUE NOT NULL,
    dict_name VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE')),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 字典数据表
CREATE TABLE IF NOT EXISTS dict_data (
    id BIGSERIAL PRIMARY KEY,
    dict_type_id BIGINT NOT NULL,
    dict_label VARCHAR(100) NOT NULL,
    dict_value VARCHAR(100) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE')),
    is_default BOOLEAN DEFAULT FALSE,
    css_class VARCHAR(100),
    list_class VARCHAR(100),
    remark TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dict_type_id) REFERENCES dict_types(id) ON DELETE CASCADE
);

-- =============================================
-- 4. 创建索引
-- =============================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_time ON users(created_time);

-- 用户账户表索引
CREATE INDEX IF NOT EXISTS idx_user_accounts_user_id ON user_accounts(user_id);

-- 登录日志表索引
CREATE INDEX IF NOT EXISTS idx_user_login_logs_user_id ON user_login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_login_logs_login_time ON user_login_logs(login_time);
CREATE INDEX IF NOT EXISTS idx_user_login_logs_login_ip ON user_login_logs(login_ip);

-- 角色表索引
CREATE INDEX IF NOT EXISTS idx_roles_code ON roles(role_code);
CREATE INDEX IF NOT EXISTS idx_roles_status ON roles(status);

-- 权限表索引
CREATE INDEX IF NOT EXISTS idx_permissions_code ON permissions(permission_code);
CREATE INDEX IF NOT EXISTS idx_permissions_parent_id ON permissions(parent_id);

-- 用户角色关系表索引
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);

-- 角色权限关系表索引
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);

-- 系统配置表索引
CREATE INDEX IF NOT EXISTS idx_system_configs_key ON system_configs(config_key);
CREATE INDEX IF NOT EXISTS idx_system_configs_type ON system_configs(config_type);

-- 字典表索引
CREATE INDEX IF NOT EXISTS idx_dict_types_code ON dict_types(dict_code);
CREATE INDEX IF NOT EXISTS idx_dict_data_type_id ON dict_data(dict_type_id);
CREATE INDEX IF NOT EXISTS idx_dict_data_value ON dict_data(dict_value);

-- =============================================
-- 5. 插入初始数据
-- =============================================

-- 插入系统管理员用户
INSERT INTO users (username, email, password, real_name, status, user_type) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwKhUOKmwQDRjtbgFaO6VWvG', '系统管理员', 'ACTIVE', 'ADMIN'),
('system', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwKhUOKmwQDRjtbgFaO6VWvG', '系统用户', 'ACTIVE', 'SYSTEM')
ON CONFLICT (username) DO NOTHING;

-- 插入测试用户
INSERT INTO users (username, email, password, real_name, phone, status, user_type) VALUES
('testuser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwKhUOKmwQDRjtbgFaO6VWvG', '测试用户', '***********', 'ACTIVE', 'REGULAR'),
('demo', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwKhUOKmwQDRjtbgFaO6VWvG', '演示用户', '***********', 'ACTIVE', 'REGULAR')
ON CONFLICT (username) DO NOTHING;

-- 为用户创建账户
INSERT INTO user_accounts (user_id, balance) 
SELECT id, 1000.00 FROM users WHERE username IN ('testuser', 'demo')
ON CONFLICT DO NOTHING;

-- 插入角色数据
INSERT INTO roles (role_code, role_name, description) VALUES
('SUPER_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限'),
('ADMIN', '管理员', '系统管理员，拥有管理权限'),
('USER', '普通用户', '普通用户，拥有基础功能权限'),
('VIP_USER', 'VIP用户', 'VIP用户，拥有高级功能权限')
ON CONFLICT (role_code) DO NOTHING;

-- 插入权限数据
INSERT INTO permissions (permission_code, permission_name, resource_type, resource_path, http_method, description) VALUES
-- 系统管理权限
('system:user:list', '用户列表', 'API', '/api/v1/users', 'GET', '查看用户列表'),
('system:user:create', '创建用户', 'API', '/api/v1/users', 'POST', '创建新用户'),
('system:user:update', '更新用户', 'API', '/api/v1/users/*', 'PUT', '更新用户信息'),
('system:user:delete', '删除用户', 'API', '/api/v1/users/*', 'DELETE', '删除用户'),
('system:role:list', '角色列表', 'API', '/api/v1/roles', 'GET', '查看角色列表'),
('system:role:create', '创建角色', 'API', '/api/v1/roles', 'POST', '创建新角色'),
('system:role:update', '更新角色', 'API', '/api/v1/roles/*', 'PUT', '更新角色信息'),
('system:role:delete', '删除角色', 'API', '/api/v1/roles/*', 'DELETE', '删除角色'),
-- 用户基础权限
('user:profile:view', '查看个人信息', 'API', '/api/v1/users/profile', 'GET', '查看个人资料'),
('user:profile:update', '更新个人信息', 'API', '/api/v1/users/profile', 'PUT', '更新个人资料'),
('user:password:change', '修改密码', 'API', '/api/v1/users/change-password', 'POST', '修改登录密码')
ON CONFLICT (permission_code) DO NOTHING;

-- 分配角色权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.role_code = 'SUPER_ADMIN'
ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.role_code = 'ADMIN' AND p.permission_code LIKE 'system:%'
ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.role_code IN ('USER', 'VIP_USER') AND p.permission_code LIKE 'user:%'
ON CONFLICT DO NOTHING;

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id FROM users u, roles r 
WHERE u.username = 'admin' AND r.role_code = 'SUPER_ADMIN'
ON CONFLICT DO NOTHING;

INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id FROM users u, roles r 
WHERE u.username IN ('testuser', 'demo') AND r.role_code = 'USER'
ON CONFLICT DO NOTHING;

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_system) VALUES
('system.name', 'CloudVPS管理平台', 'STRING', '系统名称', true),
('system.version', '1.0.0', 'STRING', '系统版本', true),
('system.copyright', 'Copyright © 2024 CloudVPS', 'STRING', '版权信息', true),
('user.default.avatar', '/static/images/default-avatar.png', 'STRING', '用户默认头像', false),
('user.password.min.length', '6', 'INTEGER', '密码最小长度', false),
('user.login.max.attempts', '5', 'INTEGER', '登录最大尝试次数', false),
('jwt.expiration.hours', '24', 'INTEGER', 'JWT过期时间(小时)', false)
ON CONFLICT (config_key) DO NOTHING;

-- 插入字典类型
INSERT INTO dict_types (dict_code, dict_name, description) VALUES
('user_status', '用户状态', '用户账户状态字典'),
('user_type', '用户类型', '用户类型字典'),
('gender', '性别', '性别字典')
ON CONFLICT (dict_code) DO NOTHING;

-- 插入字典数据
INSERT INTO dict_data (dict_type_id, dict_label, dict_value, sort_order) 
SELECT dt.id, '正常', 'ACTIVE', 1 FROM dict_types dt WHERE dt.dict_code = 'user_status'
UNION ALL
SELECT dt.id, '禁用', 'INACTIVE', 2 FROM dict_types dt WHERE dt.dict_code = 'user_status'
UNION ALL
SELECT dt.id, '锁定', 'LOCKED', 3 FROM dict_types dt WHERE dt.dict_code = 'user_status'
UNION ALL
SELECT dt.id, '普通用户', 'REGULAR', 1 FROM dict_types dt WHERE dt.dict_code = 'user_type'
UNION ALL
SELECT dt.id, '管理员', 'ADMIN', 2 FROM dict_types dt WHERE dt.dict_code = 'user_type'
UNION ALL
SELECT dt.id, '男', 'MALE', 1 FROM dict_types dt WHERE dt.dict_code = 'gender'
UNION ALL
SELECT dt.id, '女', 'FEMALE', 2 FROM dict_types dt WHERE dt.dict_code = 'gender'
ON CONFLICT DO NOTHING;

-- =============================================
-- 6. 创建触发器 (更新时间自动更新)
-- =============================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建触发器
CREATE TRIGGER update_users_updated_time BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_user_accounts_updated_time BEFORE UPDATE ON user_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_roles_updated_time BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_permissions_updated_time BEFORE UPDATE ON permissions FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_system_configs_updated_time BEFORE UPDATE ON system_configs FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_dict_types_updated_time BEFORE UPDATE ON dict_types FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_dict_data_updated_time BEFORE UPDATE ON dict_data FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- =============================================
-- 初始化完成
-- =============================================
SELECT 'CloudVPS 系统服务数据库初始化完成!' as message;
