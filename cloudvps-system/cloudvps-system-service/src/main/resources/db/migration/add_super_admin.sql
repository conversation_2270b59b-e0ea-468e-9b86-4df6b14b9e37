-- 添加超级管理员角色和用户
-- 执行时间：2025-07-02

-- 1. 添加超级管理员角色
INSERT INTO roles (role_code, role_name, description, status, created_time, updated_time, created_by, updated_by, is_deleted, version) 
VALUES ('SUPER_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 'ACTIVE', NOW(), NOW(), 1, 1, false, 1)
ON CONFLICT (role_code) DO NOTHING;

-- 2. 添加超级管理员用户（如果不存在）
INSERT INTO users (username, password, email, phone, real_name, status, created_time, updated_time, created_by, updated_by, is_deleted, version)
VALUES ('superadmin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfZEKnE3CgPqRSYLhKSTKbeC', '<EMAIL>', '13800000000', '超级管理员', 'ACTIVE', NOW(), NOW(), 1, 1, false, 1)
ON CONFLICT (username) DO NOTHING;

-- 3. 为超级管理员分配超级管理员角色
INSERT INTO user_roles (user_id, role_id, created_time, created_by)
SELECT 
    u.id as user_id,
    r.id as role_id,
    NOW() as created_time,
    1 as created_by
FROM users u
CROSS JOIN roles r
WHERE u.username = 'superadmin' 
  AND r.role_code = 'SUPER_ADMIN'
  AND NOT EXISTS (
    SELECT 1 FROM user_roles ur 
    WHERE ur.user_id = u.id AND ur.role_id = r.id
  );

-- 4. 为超级管理员角色分配所有权限（可选，因为超级管理员会绕过权限检查）
INSERT INTO role_permissions (role_id, permission_id, created_time, created_by)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_time,
    1 as created_by
FROM roles r
CROSS JOIN permissions p
WHERE r.role_code = 'SUPER_ADMIN'
  AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = r.id AND rp.permission_id = p.id
  );

-- 验证数据
SELECT 
    u.username,
    u.email,
    r.role_code,
    r.role_name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.username = 'superadmin';

-- 显示超级管理员的权限数量
SELECT 
    r.role_code,
    r.role_name,
    COUNT(rp.permission_id) as permission_count
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
WHERE r.role_code = 'SUPER_ADMIN'
GROUP BY r.id, r.role_code, r.role_name;
