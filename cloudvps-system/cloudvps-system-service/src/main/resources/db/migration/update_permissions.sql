-- 更新权限数据，使其与Controller中的@PreAuthorize注解匹配
-- 执行时间：2025-07-02

-- 删除旧的权限数据
DELETE FROM permissions WHERE permission_code IN (
    'system:user:list',
    'system:user:create',
    'system:user:update',
    'system:user:delete',
    'system:role:list',
    'system:role:create',
    'system:role:update',
    'system:role:delete',
    'user:profile:view',
    'user:profile:update',
    'user:password:change'
);

-- 插入新的权限数据，与Controller中的@PreAuthorize注解匹配
INSERT INTO permissions (permission_code, permission_name, resource_type, resource_path, description, created_time, updated_time, created_by, updated_by, is_deleted, version) VALUES
-- 用户管理权限
('USER_VIEW', '查看用户', 'API', '/system/users', '查看用户列表和详情的权限', NOW(), NOW(), 1, 1, false, 1),
('USER_CREATE', '创建用户', 'API', '/system/users', '创建新用户的权限', NOW(), NOW(), 1, 1, false, 1),
('USER_UPDATE', '更新用户', 'API', '/system/users', '更新用户信息的权限', NOW(), NOW(), 1, 1, false, 1),
('USER_DELETE', '删除用户', 'API', '/system/users', '删除用户的权限', NOW(), NOW(), 1, 1, false, 1),

-- 角色管理权限
('ROLE_VIEW', '查看角色', 'API', '/system/roles', '查看角色列表和详情的权限', NOW(), NOW(), 1, 1, false, 1),
('ROLE_CREATE', '创建角色', 'API', '/system/roles', '创建新角色的权限', NOW(), NOW(), 1, 1, false, 1),
('ROLE_UPDATE', '更新角色', 'API', '/system/roles', '更新角色信息的权限', NOW(), NOW(), 1, 1, false, 1),
('ROLE_DELETE', '删除角色', 'API', '/system/roles', '删除角色的权限', NOW(), NOW(), 1, 1, false, 1),

-- 菜单管理权限
('MENU_VIEW', '查看菜单', 'API', '/system/menus', '查看菜单列表和详情的权限', NOW(), NOW(), 1, 1, false, 1),
('MENU_CREATE', '创建菜单', 'API', '/system/menus', '创建新菜单的权限', NOW(), NOW(), 1, 1, false, 1),
('MENU_UPDATE', '更新菜单', 'API', '/system/menus', '更新菜单信息的权限', NOW(), NOW(), 1, 1, false, 1),
('MENU_DELETE', '删除菜单', 'API', '/system/menus', '删除菜单的权限', NOW(), NOW(), 1, 1, false, 1),

-- 权限管理权限
('PERMISSION_VIEW', '查看权限', 'API', '/system/permissions', '查看权限列表和详情的权限', NOW(), NOW(), 1, 1, false, 1),
('PERMISSION_CREATE', '创建权限', 'API', '/system/permissions', '创建新权限的权限', NOW(), NOW(), 1, 1, false, 1),
('PERMISSION_UPDATE', '更新权限', 'API', '/system/permissions', '更新权限信息的权限', NOW(), NOW(), 1, 1, false, 1),
('PERMISSION_DELETE', '删除权限', 'API', '/system/permissions', '删除权限的权限', NOW(), NOW(), 1, 1, false, 1),

-- 用户账户管理权限
('USER_ACCOUNT_VIEW', '查看用户账户', 'API', '/system/user-accounts', '查看用户账户信息的权限', NOW(), NOW(), 1, 1, false, 1),
('USER_ACCOUNT_CREATE', '创建用户账户', 'API', '/system/user-accounts', '创建用户账户的权限', NOW(), NOW(), 1, 1, false, 1),
('USER_ACCOUNT_UPDATE', '更新用户账户', 'API', '/system/user-accounts', '更新用户账户信息的权限', NOW(), NOW(), 1, 1, false, 1),
('USER_ACCOUNT_DELETE', '删除用户账户', 'API', '/system/user-accounts', '删除用户账户的权限', NOW(), NOW(), 1, 1, false, 1),

-- 登录日志管理权限
('LOGIN_LOG_VIEW', '查看登录日志', 'API', '/system/login-logs', '查看用户登录日志的权限', NOW(), NOW(), 1, 1, false, 1),
('LOGIN_LOG_DELETE', '删除登录日志', 'API', '/system/login-logs', '删除登录日志的权限', NOW(), NOW(), 1, 1, false, 1),

-- 字典类型管理权限
('DICT_TYPE_VIEW', '查看字典类型', 'API', '/system/dict-types', '查看字典类型的权限', NOW(), NOW(), 1, 1, false, 1),
('DICT_TYPE_CREATE', '创建字典类型', 'API', '/system/dict-types', '创建字典类型的权限', NOW(), NOW(), 1, 1, false, 1),
('DICT_TYPE_UPDATE', '更新字典类型', 'API', '/system/dict-types', '更新字典类型的权限', NOW(), NOW(), 1, 1, false, 1),
('DICT_TYPE_DELETE', '删除字典类型', 'API', '/system/dict-types', '删除字典类型的权限', NOW(), NOW(), 1, 1, false, 1),

-- 字典数据管理权限
('DICT_DATA_VIEW', '查看字典数据', 'API', '/system/dict-data', '查看字典数据的权限', NOW(), NOW(), 1, 1, false, 1),
('DICT_DATA_CREATE', '创建字典数据', 'API', '/system/dict-data', '创建字典数据的权限', NOW(), NOW(), 1, 1, false, 1),
('DICT_DATA_UPDATE', '更新字典数据', 'API', '/system/dict-data', '更新字典数据的权限', NOW(), NOW(), 1, 1, false, 1),
('DICT_DATA_DELETE', '删除字典数据', 'API', '/system/dict-data', '删除字典数据的权限', NOW(), NOW(), 1, 1, false, 1),

-- 系统配置管理权限
('SYSTEM_CONFIG_VIEW', '查看系统配置', 'API', '/system/configs', '查看系统配置的权限', NOW(), NOW(), 1, 1, false, 1),
('SYSTEM_CONFIG_CREATE', '创建系统配置', 'API', '/system/configs', '创建系统配置的权限', NOW(), NOW(), 1, 1, false, 1),
('SYSTEM_CONFIG_UPDATE', '更新系统配置', 'API', '/system/configs', '更新系统配置的权限', NOW(), NOW(), 1, 1, false, 1),
('SYSTEM_CONFIG_DELETE', '删除系统配置', 'API', '/system/configs', '删除系统配置的权限', NOW(), NOW(), 1, 1, false, 1);

-- 为管理员角色分配所有权限
-- 首先获取管理员角色ID和所有权限ID，然后建立关联关系
INSERT INTO role_permissions (role_id, permission_id, created_time, created_by)
SELECT
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_time,
    1 as created_by
FROM roles r
CROSS JOIN permissions p
WHERE r.role_code = 'ADMIN'
  AND p.permission_code IN (
    'USER_VIEW', 'USER_CREATE', 'USER_UPDATE', 'USER_DELETE',
    'ROLE_VIEW', 'ROLE_CREATE', 'ROLE_UPDATE', 'ROLE_DELETE',
    'MENU_VIEW', 'MENU_CREATE', 'MENU_UPDATE', 'MENU_DELETE',
    'PERMISSION_VIEW', 'PERMISSION_CREATE', 'PERMISSION_UPDATE', 'PERMISSION_DELETE',
    'USER_ACCOUNT_VIEW', 'USER_ACCOUNT_CREATE', 'USER_ACCOUNT_UPDATE', 'USER_ACCOUNT_DELETE',
    'LOGIN_LOG_VIEW', 'LOGIN_LOG_DELETE',
    'DICT_TYPE_VIEW', 'DICT_TYPE_CREATE', 'DICT_TYPE_UPDATE', 'DICT_TYPE_DELETE',
    'DICT_DATA_VIEW', 'DICT_DATA_CREATE', 'DICT_DATA_UPDATE', 'DICT_DATA_DELETE',
    'SYSTEM_CONFIG_VIEW', 'SYSTEM_CONFIG_CREATE', 'SYSTEM_CONFIG_UPDATE', 'SYSTEM_CONFIG_DELETE'
  );

-- 为普通用户角色分配基本查看权限
INSERT INTO role_permissions (role_id, permission_id, created_time, created_by)
SELECT
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_time,
    1 as created_by
FROM roles r
CROSS JOIN permissions p
WHERE r.role_code = 'USER'
  AND p.permission_code IN (
    'USER_VIEW', 'MENU_VIEW', 'DICT_TYPE_VIEW', 'DICT_DATA_VIEW'
  );
