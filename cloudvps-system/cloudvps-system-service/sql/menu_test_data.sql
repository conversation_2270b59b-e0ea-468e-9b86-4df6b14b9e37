-- CloudVPS 系统服务 - 菜单管理测试数据
-- 数据库: PostgreSQL
-- 版本: 1.0.0

-- 清空现有数据（开发环境使用）
-- DELETE FROM sys_role_menu;
-- DELETE FROM sys_menu;
-- ALTER SEQUENCE sys_menu_id_seq RESTART WITH 1;

-- 插入根级菜单（目录）
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(1, 'dashboard', '仪表盘', 'dashboard', '/dashboard', 'Dashboard', 'DIRECTORY', NULL, 1, TRUE, TRUE, 'DASHBOARD_VIEW', '系统仪表盘目录'),
(2, 'system', '系统管理', 'system', '/system', 'Layout', 'DIRECTORY', NULL, 2, TRUE, TRUE, 'SYSTEM_MANAGE', '系统管理目录'),
(3, 'virtualization', '虚拟化管理', 'server', '/virtualization', 'Layout', 'DIRECTORY', NULL, 3, TRUE, TRUE, 'VIRTUALIZATION_MANAGE', '虚拟化管理目录'),
(4, 'order', '订单管理', 'shopping-cart', '/order', 'Layout', 'DIRECTORY', NULL, 4, TRUE, TRUE, 'ORDER_MANAGE', '订单管理目录'),
(5, 'payment', '支付管理', 'money-collect', '/payment', 'Layout', 'DIRECTORY', NULL, 5, TRUE, TRUE, 'PAYMENT_MANAGE', '支付管理目录'),
(6, 'monitor', '系统监控', 'monitor', '/monitor', 'Layout', 'DIRECTORY', NULL, 6, TRUE, TRUE, 'MONITOR_VIEW', '系统监控目录');

-- 插入仪表盘子菜单
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(11, 'dashboard-overview', '总览', 'pie-chart', '/dashboard/overview', 'dashboard/Overview', 'MENU', 1, 1, TRUE, TRUE, 'DASHBOARD_OVERVIEW', '系统总览页面'),
(12, 'dashboard-analysis', '分析页', 'line-chart', '/dashboard/analysis', 'dashboard/Analysis', 'MENU', 1, 2, TRUE, TRUE, 'DASHBOARD_ANALYSIS', '数据分析页面');

-- 插入系统管理子菜单
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(21, 'system-user', '用户管理', 'user', '/system/user', 'system/User', 'MENU', 2, 1, TRUE, TRUE, 'SYSTEM_USER_VIEW', '用户管理页面'),
(22, 'system-role', '角色管理', 'team', '/system/role', 'system/Role', 'MENU', 2, 2, TRUE, TRUE, 'SYSTEM_ROLE_VIEW', '角色管理页面'),
(23, 'system-menu', '菜单管理', 'menu', '/system/menu', 'system/Menu', 'MENU', 2, 3, TRUE, TRUE, 'SYSTEM_MENU_VIEW', '菜单管理页面'),
(24, 'system-dict', '字典管理', 'book', '/system/dict', 'system/Dict', 'MENU', 2, 4, TRUE, TRUE, 'SYSTEM_DICT_VIEW', '字典管理页面'),
(25, 'system-config', '参数配置', 'setting', '/system/config', 'system/Config', 'MENU', 2, 5, TRUE, TRUE, 'SYSTEM_CONFIG_VIEW', '参数配置页面'),
(26, 'system-log', '操作日志', 'file-text', '/system/log', 'system/Log', 'MENU', 2, 6, TRUE, TRUE, 'SYSTEM_LOG_VIEW', '操作日志页面');

-- 插入虚拟化管理子菜单
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(31, 'virtualization-node', '节点管理', 'cluster', '/virtualization/node', 'virtualization/Node', 'MENU', 3, 1, TRUE, TRUE, 'VIRTUALIZATION_NODE_VIEW', 'PVE节点管理页面'),
(32, 'virtualization-vm', '虚拟机管理', 'desktop', '/virtualization/vm', 'virtualization/VirtualMachine', 'MENU', 3, 2, TRUE, TRUE, 'VIRTUALIZATION_VM_VIEW', '虚拟机管理页面'),
(33, 'virtualization-template', '模板管理', 'copy', '/virtualization/template', 'virtualization/Template', 'MENU', 3, 3, TRUE, TRUE, 'VIRTUALIZATION_TEMPLATE_VIEW', '虚拟机模板管理页面'),
(34, 'virtualization-storage', '存储管理', 'hdd', '/virtualization/storage', 'virtualization/Storage', 'MENU', 3, 4, TRUE, TRUE, 'VIRTUALIZATION_STORAGE_VIEW', '存储管理页面');

-- 插入订单管理子菜单
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(41, 'order-list', '订单列表', 'unordered-list', '/order/list', 'order/OrderList', 'MENU', 4, 1, TRUE, TRUE, 'ORDER_VIEW', '订单列表页面'),
(42, 'order-product', '产品管理', 'appstore', '/order/product', 'order/Product', 'MENU', 4, 2, TRUE, TRUE, 'ORDER_PRODUCT_VIEW', '产品管理页面'),
(43, 'order-pricing', '价格配置', 'dollar', '/order/pricing', 'order/Pricing', 'MENU', 4, 3, TRUE, TRUE, 'ORDER_PRICING_VIEW', '价格配置页面');

-- 插入支付管理子菜单
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(51, 'payment-transaction', '交易记录', 'transaction', '/payment/transaction', 'payment/Transaction', 'MENU', 5, 1, TRUE, TRUE, 'PAYMENT_TRANSACTION_VIEW', '交易记录页面'),
(52, 'payment-method', '支付方式', 'credit-card', '/payment/method', 'payment/Method', 'MENU', 5, 2, TRUE, TRUE, 'PAYMENT_METHOD_VIEW', '支付方式管理页面'),
(53, 'payment-refund', '退款管理', 'rollback', '/payment/refund', 'payment/Refund', 'MENU', 5, 3, TRUE, TRUE, 'PAYMENT_REFUND_VIEW', '退款管理页面');

-- 插入系统监控子菜单
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(61, 'monitor-server', '服务监控', 'cloud-server', '/monitor/server', 'monitor/Server', 'MENU', 6, 1, TRUE, TRUE, 'MONITOR_SERVER_VIEW', '服务器监控页面'),
(62, 'monitor-cache', '缓存监控', 'database', '/monitor/cache', 'monitor/Cache', 'MENU', 6, 2, TRUE, TRUE, 'MONITOR_CACHE_VIEW', '缓存监控页面'),
(63, 'monitor-sql', 'SQL监控', 'code', '/monitor/sql', 'monitor/Sql', 'MENU', 6, 3, TRUE, TRUE, 'MONITOR_SQL_VIEW', 'SQL监控页面');

-- 插入按钮权限（以用户管理为例）
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(211, 'system-user-create', '新增用户', NULL, NULL, NULL, 'BUTTON', 21, 1, FALSE, TRUE, 'SYSTEM_USER_CREATE', '新增用户按钮'),
(212, 'system-user-update', '编辑用户', NULL, NULL, NULL, 'BUTTON', 21, 2, FALSE, TRUE, 'SYSTEM_USER_UPDATE', '编辑用户按钮'),
(213, 'system-user-delete', '删除用户', NULL, NULL, NULL, 'BUTTON', 21, 3, FALSE, TRUE, 'SYSTEM_USER_DELETE', '删除用户按钮'),
(214, 'system-user-reset-password', '重置密码', NULL, NULL, NULL, 'BUTTON', 21, 4, FALSE, TRUE, 'SYSTEM_USER_RESET_PASSWORD', '重置用户密码按钮'),
(215, 'system-user-export', '导出用户', NULL, NULL, NULL, 'BUTTON', 21, 5, FALSE, TRUE, 'SYSTEM_USER_EXPORT', '导出用户数据按钮');

-- 插入菜单管理按钮权限
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(231, 'system-menu-create', '新增菜单', NULL, NULL, NULL, 'BUTTON', 23, 1, FALSE, TRUE, 'SYSTEM_MENU_CREATE', '新增菜单按钮'),
(232, 'system-menu-update', '编辑菜单', NULL, NULL, NULL, 'BUTTON', 23, 2, FALSE, TRUE, 'SYSTEM_MENU_UPDATE', '编辑菜单按钮'),
(233, 'system-menu-delete', '删除菜单', NULL, NULL, NULL, 'BUTTON', 23, 3, FALSE, TRUE, 'SYSTEM_MENU_DELETE', '删除菜单按钮'),
(234, 'system-menu-sort', '菜单排序', NULL, NULL, NULL, 'BUTTON', 23, 4, FALSE, TRUE, 'SYSTEM_MENU_SORT', '菜单排序按钮');

-- 插入虚拟机管理按钮权限
INSERT INTO sys_menu (id, name, title, icon, path, component, type, parent_id, sort, visible, enabled, permission, description) VALUES
(321, 'virtualization-vm-create', '创建虚拟机', NULL, NULL, NULL, 'BUTTON', 32, 1, FALSE, TRUE, 'VIRTUALIZATION_VM_CREATE', '创建虚拟机按钮'),
(322, 'virtualization-vm-start', '启动虚拟机', NULL, NULL, NULL, 'BUTTON', 32, 2, FALSE, TRUE, 'VIRTUALIZATION_VM_START', '启动虚拟机按钮'),
(323, 'virtualization-vm-stop', '停止虚拟机', NULL, NULL, NULL, 'BUTTON', 32, 3, FALSE, TRUE, 'VIRTUALIZATION_VM_STOP', '停止虚拟机按钮'),
(324, 'virtualization-vm-restart', '重启虚拟机', NULL, NULL, NULL, 'BUTTON', 32, 4, FALSE, TRUE, 'VIRTUALIZATION_VM_RESTART', '重启虚拟机按钮'),
(325, 'virtualization-vm-delete', '删除虚拟机', NULL, NULL, NULL, 'BUTTON', 32, 5, FALSE, TRUE, 'VIRTUALIZATION_VM_DELETE', '删除虚拟机按钮');

-- 重置序列
SELECT setval('sys_menu_id_seq', (SELECT MAX(id) FROM sys_menu));

-- 插入管理员角色的菜单权限（假设管理员角色ID为1）
-- INSERT INTO sys_role_menu (role_id, menu_id, create_by) 
-- SELECT 1, id, 1 FROM sys_menu WHERE enabled = TRUE;
