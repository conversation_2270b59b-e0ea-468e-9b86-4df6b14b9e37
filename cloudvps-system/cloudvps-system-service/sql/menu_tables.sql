-- CloudVPS 系统服务 - 菜单管理表结构
-- 数据库: PostgreSQL
-- 版本: 1.0.0

-- 删除已存在的表（开发环境使用，生产环境请谨慎）
DROP TABLE IF EXISTS sys_role_menu CASCADE;
DROP TABLE IF EXISTS sys_menu CASCADE;

-- 创建菜单表
CREATE TABLE IF NOT EXISTS sys_menu
(
    id          BIGSERIAL PRIMARY KEY,
    name        VARCHAR(100) NOT NULL,
    title       VARCHAR(100) NOT NULL,
    icon        VARCHAR(100),
    path        VARCHAR(200),
    component   VARCHAR(200),
    type        VARCHAR(20)  NOT NULL DEFAULT 'MENU',
    parent_id   BIGINT,
    sort        INTEGER      NOT NULL DEFAULT 0,
    visible     BOOLEAN      NOT NULL DEFAULT TRUE,
    enabled     BOOLEAN      NOT NULL DEFAULT TRUE,
    permission  VARCHAR(200),
    description TEXT,
    params      TEXT,
    cache       BOOLEAN      NOT NULL DEFAULT FALSE,
    external    BOOLEAN      NOT NULL DEFAULT FALSE,
    create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by   BIGINT,
    update_by   BIGINT,
    deleted     BOOLEAN      NOT NULL DEFAULT FALSE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_menu_parent_id ON sys_menu (parent_id);
CREATE INDEX IF NOT EXISTS idx_sys_menu_type ON sys_menu (type);
CREATE INDEX IF NOT EXISTS idx_sys_menu_enabled ON sys_menu (enabled);
CREATE INDEX IF NOT EXISTS idx_sys_menu_visible ON sys_menu (visible);
CREATE INDEX IF NOT EXISTS idx_sys_menu_sort ON sys_menu (sort);
CREATE INDEX IF NOT EXISTS idx_sys_menu_permission ON sys_menu (permission);
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_menu_name ON sys_menu (name) WHERE deleted = FALSE;

-- 添加外键约束
ALTER TABLE sys_menu
    ADD CONSTRAINT fk_sys_menu_parent
        FOREIGN KEY (parent_id) REFERENCES sys_menu (id) ON DELETE SET NULL;

-- 添加检查约束
ALTER TABLE sys_menu
    ADD CONSTRAINT chk_sys_menu_type
        CHECK (type IN ('DIRECTORY', 'MENU', 'BUTTON'));

-- 添加表注释
COMMENT ON TABLE sys_menu IS '系统菜单表';
COMMENT ON COLUMN sys_menu.id IS '菜单ID';
COMMENT ON COLUMN sys_menu.name IS '菜单名称（唯一标识）';
COMMENT ON COLUMN sys_menu.title IS '菜单标题（显示名称）';
COMMENT ON COLUMN sys_menu.icon IS '菜单图标';
COMMENT ON COLUMN sys_menu.path IS '菜单路径';
COMMENT ON COLUMN sys_menu.component IS '组件路径';
COMMENT ON COLUMN sys_menu.type IS '菜单类型：DIRECTORY-目录，MENU-菜单，BUTTON-按钮';
COMMENT ON COLUMN sys_menu.parent_id IS '父菜单ID';
COMMENT ON COLUMN sys_menu.sort IS '排序号（数字越小越靠前）';
COMMENT ON COLUMN sys_menu.visible IS '是否可见';
COMMENT ON COLUMN sys_menu.enabled IS '是否启用';
COMMENT ON COLUMN sys_menu.permission IS '权限标识';
COMMENT ON COLUMN sys_menu.description IS '菜单描述';
COMMENT ON COLUMN sys_menu.params IS '路由参数（JSON格式）';
COMMENT ON COLUMN sys_menu.cache IS '是否缓存页面';
COMMENT ON COLUMN sys_menu.external IS '是否外链';
COMMENT ON COLUMN sys_menu.create_time IS '创建时间';
COMMENT ON COLUMN sys_menu.update_time IS '更新时间';
COMMENT ON COLUMN sys_menu.create_by IS '创建人ID';
COMMENT ON COLUMN sys_menu.update_by IS '更新人ID';
COMMENT ON COLUMN sys_menu.deleted IS '是否删除（逻辑删除）';

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_sys_menu_update_time()
    RETURNS TRIGGER AS
$$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建更新时间触发器
CREATE TRIGGER trigger_sys_menu_update_time
    BEFORE UPDATE
    ON sys_menu
    FOR EACH ROW
EXECUTE FUNCTION update_sys_menu_update_time();

-- 创建角色菜单关联表
CREATE TABLE IF NOT EXISTS sys_role_menu
(
    id          BIGSERIAL PRIMARY KEY,
    role_id     BIGINT    NOT NULL,
    menu_id     BIGINT    NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by   BIGINT
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_role_menu_role_id ON sys_role_menu (role_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_menu_menu_id ON sys_role_menu (menu_id);
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_role_menu ON sys_role_menu (role_id, menu_id);

-- 添加表注释
COMMENT ON TABLE sys_role_menu IS '角色菜单关联表';
COMMENT ON COLUMN sys_role_menu.id IS '关联ID';
COMMENT ON COLUMN sys_role_menu.role_id IS '角色ID';
COMMENT ON COLUMN sys_role_menu.menu_id IS '菜单ID';
COMMENT ON COLUMN sys_role_menu.create_time IS '创建时间';
COMMENT ON COLUMN sys_role_menu.create_by IS '创建人ID';