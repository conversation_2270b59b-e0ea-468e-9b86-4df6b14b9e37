<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cloudvps</groupId>
        <artifactId>cloudvps-system</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>cloudvps-system-api</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>CloudVPS System API</name>
    <description>CloudVPS系统服务API定义</description>

    <dependencies>
        <!-- Common Core -->
        <dependency>
            <groupId>com.cloudvps</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <!-- Spring Cloud OpenFeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Common Swagger -->
        <dependency>
            <groupId>com.cloudvps</groupId>
            <artifactId>common-swagger</artifactId>
        </dependency>
    </dependencies>

</project>
