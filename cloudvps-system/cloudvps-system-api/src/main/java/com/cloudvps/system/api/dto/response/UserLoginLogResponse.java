package com.cloudvps.system.api.dto.response;

import com.cloudvps.system.api.enums.LoginResult;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户登录日志响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserLoginLogResponse {
    
    /**
     * 日志ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 登录IP地址
     */
    private String loginIp;
    
    /**
     * 登录地点
     */
    private String loginLocation;
    
    /**
     * 用户代理信息
     */
    private String userAgent;
    
    /**
     * 登录时间
     */
    private LocalDateTime loginTime;
    
    /**
     * 登录结果
     */
    private LoginResult loginResult;
    
    /**
     * 失败原因
     */
    private String failureReason;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
