package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.MenuType;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 菜单更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class MenuUpdateRequest {
    
    /**
     * 菜单标题（显示名称）
     */
    @Size(max = 100, message = "菜单标题长度不能超过100个字符")
    private String title;
    
    /**
     * 菜单图标
     */
    @Size(max = 100, message = "菜单图标长度不能超过100个字符")
    private String icon;
    
    /**
     * 菜单路径
     */
    @Size(max = 200, message = "菜单路径长度不能超过200个字符")
    private String path;
    
    /**
     * 组件路径
     */
    @Size(max = 200, message = "组件路径长度不能超过200个字符")
    private String component;
    
    /**
     * 菜单类型
     */
    private MenuType type;
    
    /**
     * 父菜单ID
     */
    private Long parentId;
    
    /**
     * 排序号
     */
    private Integer sort;
    
    /**
     * 是否可见
     */
    private Boolean visible;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 权限标识
     */
    @Size(max = 100, message = "权限标识长度不能超过100个字符")
    private String permission;
    
    /**
     * 菜单描述
     */
    @Size(max = 500, message = "菜单描述长度不能超过500个字符")
    private String description;
    
    /**
     * 路由参数
     */
    @Size(max = 500, message = "路由参数长度不能超过500个字符")
    private String params;
    
    /**
     * 是否缓存
     */
    private Boolean cache;
    
    /**
     * 是否外链
     */
    private Boolean external;
}
