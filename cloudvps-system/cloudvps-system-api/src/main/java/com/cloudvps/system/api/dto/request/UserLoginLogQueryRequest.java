package com.cloudvps.system.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.system.api.enums.LoginResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户登录日志查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserLoginLogQueryRequest extends PageRequest {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 登录IP地址
     */
    private String loginIp;
    
    /**
     * 登录地点
     */
    private String loginLocation;
    
    /**
     * 登录结果
     */
    private LoginResult loginResult;
    
    /**
     * 登录时间开始
     */
    private LocalDateTime loginTimeStart;
    
    /**
     * 登录时间结束
     */
    private LocalDateTime loginTimeEnd;
}
