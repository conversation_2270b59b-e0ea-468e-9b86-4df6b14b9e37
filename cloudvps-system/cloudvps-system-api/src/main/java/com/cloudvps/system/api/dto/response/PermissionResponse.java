package com.cloudvps.system.api.dto.response;

import com.cloudvps.system.api.enums.ResourceType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 权限响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PermissionResponse {
    
    /**
     * 权限ID
     */
    private Long id;
    
    /**
     * 权限编码
     */
    private String permissionCode;
    
    /**
     * 权限名称
     */
    private String permissionName;
    
    /**
     * 资源类型
     */
    private ResourceType resourceType;
    
    /**
     * 资源路径
     */
    private String resourcePath;
    
    /**
     * HTTP方法
     */
    private String httpMethod;
    
    /**
     * 权限描述
     */
    private String description;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
