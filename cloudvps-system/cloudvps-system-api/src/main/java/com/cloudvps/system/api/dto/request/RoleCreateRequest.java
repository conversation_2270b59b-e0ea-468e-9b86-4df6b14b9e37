package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.RoleStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 角色创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class RoleCreateRequest {
    
    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    private String roleCode;
    
    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    private String roleName;
    
    /**
     * 角色描述
     */
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    private String description;
    
    /**
     * 角色状态
     */
    private RoleStatus status = RoleStatus.ACTIVE;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder = 0;
}
