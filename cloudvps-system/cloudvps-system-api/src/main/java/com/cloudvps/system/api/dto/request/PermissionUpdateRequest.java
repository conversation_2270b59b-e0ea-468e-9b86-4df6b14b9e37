package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.ResourceType;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 权限更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PermissionUpdateRequest {
    
    /**
     * 权限名称
     */
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    private String permissionName;
    
    /**
     * 资源类型
     */
    private ResourceType resourceType;
    
    /**
     * 资源路径
     */
    @Size(max = 200, message = "资源路径长度不能超过200个字符")
    private String resourcePath;
    
    /**
     * HTTP方法
     */
    @Size(max = 20, message = "HTTP方法长度不能超过20个字符")
    private String httpMethod;
    
    /**
     * 权限描述
     */
    @Size(max = 500, message = "权限描述长度不能超过500个字符")
    private String description;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
}
