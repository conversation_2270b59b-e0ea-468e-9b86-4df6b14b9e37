package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.ConfigType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 系统配置创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class SystemConfigCreateRequest {
    
    /**
     * 配置键
     */
    @NotBlank(message = "配置键不能为空")
    @Size(max = 100, message = "配置键长度不能超过100个字符")
    private String configKey;
    
    /**
     * 配置值
     */
    @NotBlank(message = "配置值不能为空")
    @Size(max = 2000, message = "配置值长度不能超过2000个字符")
    private String configValue;
    
    /**
     * 配置类型
     */
    @NotNull(message = "配置类型不能为空")
    private ConfigType configType = ConfigType.STRING;
    
    /**
     * 配置描述
     */
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    private String description;
    
    /**
     * 是否加密
     */
    private Boolean isEncrypted = false;
    
    /**
     * 是否系统配置
     */
    private Boolean isSystem = false;
}
