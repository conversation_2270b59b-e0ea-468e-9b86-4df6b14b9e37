package com.cloudvps.system.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.system.api.enums.UserStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户查询请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserQueryRequest extends PageRequest {

    /**
     * 用户状态
     */
    private UserStatus status;

    /**
     * 搜索关键字（用户名、邮箱、真实姓名）
     */
    private String keyword;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 获取清理后的关键字
     */
    public String getCleanKeyword() {
        return keyword != null ? keyword.trim() : null;
    }
    
    /**
     * 获取清理后的用户名
     */
    public String getCleanUsername() {
        return username != null ? username.trim() : null;
    }
    
    /**
     * 获取清理后的邮箱
     */
    public String getCleanEmail() {
        return email != null ? email.trim() : null;
    }
    
    /**
     * 获取清理后的真实姓名
     */
    public String getCleanRealName() {
        return realName != null ? realName.trim() : null;
    }
    
    /**
     * 获取清理后的手机号
     */
    public String getCleanPhone() {
        return phone != null ? phone.trim() : null;
    }
}
