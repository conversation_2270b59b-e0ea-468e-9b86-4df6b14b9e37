package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.DictStatus;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 字典类型更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class DictTypeUpdateRequest {
    
    /**
     * 字典名称
     */
    @Size(max = 100, message = "字典名称长度不能超过100个字符")
    private String dictName;
    
    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
    
    /**
     * 状态
     */
    private DictStatus status;
}
