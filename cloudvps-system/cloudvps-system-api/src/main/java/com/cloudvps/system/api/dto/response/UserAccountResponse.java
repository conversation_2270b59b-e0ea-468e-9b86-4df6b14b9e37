package com.cloudvps.system.api.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户账户响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserAccountResponse {
    
    /**
     * 账户ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 账户余额
     */
    private BigDecimal balance;
    
    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;
    
    /**
     * 可用余额
     */
    private BigDecimal availableBalance;
    
    /**
     * 累计充值金额
     */
    private BigDecimal totalRecharge;
    
    /**
     * 累计消费金额
     */
    private BigDecimal totalConsume;
    
    /**
     * 账户是否锁定
     */
    private Boolean isLocked;
    
    /**
     * 锁定原因
     */
    private String lockReason;
    
    /**
     * 最后操作时间
     */
    private LocalDateTime lastOperationTime;
    
    /**
     * 最后操作描述
     */
    private String lastOperationDescription;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
