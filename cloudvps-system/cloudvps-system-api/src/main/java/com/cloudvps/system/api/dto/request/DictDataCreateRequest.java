package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.DictStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 字典数据创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class DictDataCreateRequest {
    
    /**
     * 字典类型ID
     */
    @NotNull(message = "字典类型ID不能为空")
    private Long dictTypeId;
    
    /**
     * 字典标签
     */
    @NotBlank(message = "字典标签不能为空")
    @Size(max = 100, message = "字典标签长度不能超过100个字符")
    private String dictLabel;
    
    /**
     * 字典值
     */
    @NotBlank(message = "字典值不能为空")
    @Size(max = 100, message = "字典值长度不能超过100个字符")
    private String dictValue;
    
    /**
     * 排序
     */
    private Integer sortOrder = 0;
    
    /**
     * 状态
     */
    private DictStatus status = DictStatus.ACTIVE;
    
    /**
     * 是否默认
     */
    private Boolean isDefault = false;
    
    /**
     * CSS类名
     */
    @Size(max = 100, message = "CSS类名长度不能超过100个字符")
    private String cssClass;
    
    /**
     * 列表类名
     */
    @Size(max = 100, message = "列表类名长度不能超过100个字符")
    private String listClass;
    
    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
