package com.cloudvps.system.api.dto.response;

import com.cloudvps.system.api.enums.MenuType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜单响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class MenuResponse {
    
    /**
     * 菜单ID
     */
    private Long id;
    
    /**
     * 菜单名称
     */
    private String name;
    
    /**
     * 菜单标题（显示名称）
     */
    private String title;
    
    /**
     * 菜单图标
     */
    private String icon;
    
    /**
     * 菜单路径
     */
    private String path;
    
    /**
     * 组件路径
     */
    private String component;
    
    /**
     * 菜单类型
     */
    private MenuType type;
    
    /**
     * 父菜单ID
     */
    private Long parentId;
    
    /**
     * 排序号
     */
    private Integer sort;
    
    /**
     * 是否可见
     */
    private Boolean visible;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 权限标识
     */
    private String permission;
    
    /**
     * 菜单描述
     */
    private String description;
    
    /**
     * 路由参数
     */
    private String params;
    
    /**
     * 是否缓存
     */
    private Boolean cache;
    
    /**
     * 是否外链
     */
    private Boolean external;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 子菜单列表（用于树形结构）
     */
    private List<MenuResponse> children;
}
