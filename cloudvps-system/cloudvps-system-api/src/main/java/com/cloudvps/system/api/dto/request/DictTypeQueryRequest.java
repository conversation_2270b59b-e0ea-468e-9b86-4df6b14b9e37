package com.cloudvps.system.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.system.api.enums.DictStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典类型查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DictTypeQueryRequest extends PageRequest {
    
    /**
     * 字典编码
     */
    private String dictCode;
    
    /**
     * 字典名称
     */
    private String dictName;
    
    /**
     * 状态
     */
    private DictStatus status;
}
