package com.cloudvps.system.api.dto.response;

import com.cloudvps.system.api.enums.ConfigType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统配置响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class SystemConfigResponse {
    
    /**
     * 配置ID
     */
    private Long id;
    
    /**
     * 配置键
     */
    private String configKey;
    
    /**
     * 配置值
     */
    private String configValue;
    
    /**
     * 配置类型
     */
    private ConfigType configType;
    
    /**
     * 配置描述
     */
    private String description;
    
    /**
     * 是否加密
     */
    private Boolean isEncrypted;
    
    /**
     * 是否系统配置
     */
    private Boolean isSystem;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 获取配置值（根据类型转换）
     */
    public Object getTypedValue() {
        if (configValue == null) {
            return null;
        }
        
        return switch (configType) {
            case INTEGER -> Integer.valueOf(configValue);
            case BOOLEAN -> Boolean.valueOf(configValue);
            case JSON -> configValue; // JSON字符串，由调用方解析
            default -> configValue;
        };
    }
}
