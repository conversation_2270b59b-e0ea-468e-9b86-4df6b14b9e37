package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.ConfigType;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 系统配置更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class SystemConfigUpdateRequest {
    
    /**
     * 配置值
     */
    @Size(max = 2000, message = "配置值长度不能超过2000个字符")
    private String configValue;
    
    /**
     * 配置类型
     */
    private ConfigType configType;
    
    /**
     * 配置描述
     */
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    private String description;
    
    /**
     * 是否加密
     */
    private Boolean isEncrypted;
    
    /**
     * 是否系统配置
     */
    private Boolean isSystem;
}
