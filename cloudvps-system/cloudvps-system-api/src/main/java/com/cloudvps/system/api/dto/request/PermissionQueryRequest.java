package com.cloudvps.system.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.system.api.enums.ResourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 权限查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PermissionQueryRequest extends PageRequest {
    
    /**
     * 权限编码
     */
    private String permissionCode;
    
    /**
     * 权限名称
     */
    private String permissionName;
    
    /**
     * 资源类型
     */
    private ResourceType resourceType;
    
    /**
     * 资源路径
     */
    private String resourcePath;
    
    /**
     * HTTP方法
     */
    private String httpMethod;
}
