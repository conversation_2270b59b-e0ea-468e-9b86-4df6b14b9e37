package com.cloudvps.system.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.system.api.enums.MenuType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 菜单查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MenuQueryRequest extends PageRequest {
    
    /**
     * 菜单名称
     */
    private String name;
    
    /**
     * 菜单标题
     */
    private String title;
    
    /**
     * 菜单类型
     */
    private MenuType type;
    
    /**
     * 父菜单ID
     */
    private Long parentId;
    
    /**
     * 是否可见
     */
    private Boolean visible;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
}
