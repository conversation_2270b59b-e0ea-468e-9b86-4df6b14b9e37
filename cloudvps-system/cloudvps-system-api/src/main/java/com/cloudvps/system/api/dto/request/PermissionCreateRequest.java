package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.ResourceType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 权限创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PermissionCreateRequest {
    
    /**
     * 权限编码
     */
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 100, message = "权限编码长度不能超过100个字符")
    private String permissionCode;
    
    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    private String permissionName;
    
    /**
     * 资源类型
     */
    @NotNull(message = "资源类型不能为空")
    private ResourceType resourceType;
    
    /**
     * 资源路径
     */
    @Size(max = 200, message = "资源路径长度不能超过200个字符")
    private String resourcePath;
    
    /**
     * HTTP方法
     */
    @Size(max = 20, message = "HTTP方法长度不能超过20个字符")
    private String httpMethod;
    
    /**
     * 权限描述
     */
    @Size(max = 500, message = "权限描述长度不能超过500个字符")
    private String description;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder = 0;
}
