package com.cloudvps.system.api.dto.response;

import com.cloudvps.system.api.enums.DictStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典类型响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class DictTypeResponse {
    
    /**
     * 字典类型ID
     */
    private Long id;
    
    /**
     * 字典编码
     */
    private String dictCode;
    
    /**
     * 字典名称
     */
    private String dictName;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 状态
     */
    private DictStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
