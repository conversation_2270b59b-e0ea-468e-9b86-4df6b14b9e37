package com.cloudvps.system.api.dto.response;

import com.cloudvps.system.api.enums.RoleStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 角色响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class RoleResponse {
    
    /**
     * 角色ID
     */
    private Long id;
    
    /**
     * 角色编码
     */
    private String roleCode;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 角色描述
     */
    private String description;
    
    /**
     * 角色状态
     */
    private RoleStatus status;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
