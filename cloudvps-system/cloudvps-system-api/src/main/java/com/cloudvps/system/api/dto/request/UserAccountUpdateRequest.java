package com.cloudvps.system.api.dto.request;

import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户账户更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserAccountUpdateRequest {
    
    /**
     * 账户是否锁定
     */
    private Boolean isLocked;
    
    /**
     * 锁定原因
     */
    @Size(max = 500, message = "锁定原因长度不能超过500个字符")
    private String lockReason;
    
    /**
     * 操作描述
     */
    @Size(max = 500, message = "操作描述长度不能超过500个字符")
    private String operationDescription;
}
