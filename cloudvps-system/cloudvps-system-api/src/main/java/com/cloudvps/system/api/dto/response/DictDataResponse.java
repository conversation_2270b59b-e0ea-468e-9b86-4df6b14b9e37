package com.cloudvps.system.api.dto.response;

import com.cloudvps.system.api.enums.DictStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典数据响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class DictDataResponse {
    
    /**
     * 字典数据ID
     */
    private Long id;
    
    /**
     * 字典类型ID
     */
    private Long dictTypeId;
    
    /**
     * 字典标签
     */
    private String dictLabel;
    
    /**
     * 字典值
     */
    private String dictValue;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 状态
     */
    private DictStatus status;
    
    /**
     * 是否默认
     */
    private Boolean isDefault;
    
    /**
     * CSS类名
     */
    private String cssClass;
    
    /**
     * 列表类名
     */
    private String listClass;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
