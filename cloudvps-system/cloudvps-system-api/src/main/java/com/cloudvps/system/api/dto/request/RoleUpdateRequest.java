package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.RoleStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 角色更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class RoleUpdateRequest {
    
    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    private String roleName;
    
    /**
     * 角色描述
     */
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    private String description;
    
    /**
     * 角色状态
     */
    private RoleStatus status;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
}
