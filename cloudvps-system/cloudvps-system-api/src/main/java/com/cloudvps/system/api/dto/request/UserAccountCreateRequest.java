package com.cloudvps.system.api.dto.request;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户账户创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserAccountCreateRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 初始余额
     */
    @DecimalMin(value = "0.00", message = "初始余额不能为负数")
    private BigDecimal balance = BigDecimal.ZERO;
    
    /**
     * 账户是否锁定
     */
    private Boolean isLocked = false;
    
    /**
     * 锁定原因
     */
    @Size(max = 500, message = "锁定原因长度不能超过500个字符")
    private String lockReason;
    
    /**
     * 操作描述
     */
    @Size(max = 500, message = "操作描述长度不能超过500个字符")
    private String operationDescription;
}
