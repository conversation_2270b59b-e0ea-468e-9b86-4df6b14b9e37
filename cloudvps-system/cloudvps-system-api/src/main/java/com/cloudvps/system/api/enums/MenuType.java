package com.cloudvps.system.api.enums;

/**
 * 菜单类型枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum MenuType {

    /**
     * 目录 - 包含子菜单的目录
     */
    DIRECTORY("DIRECTORY", "目录"),

    /**
     * 菜单 - 具体的页面菜单
     */
    MENU("MENU", "菜单"),

    /**
     * 按钮 - 页面内的操作按钮
     */
    BUTTON("BUTTON", "按钮");

    private final String code;
    private final String description;

    MenuType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static MenuType fromCode(String code) {
        for (MenuType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown menu type code: " + code);
    }

    /**
     * 检查是否为目录类型
     */
    public boolean isDirectory() {
        return this == DIRECTORY;
    }

    /**
     * 检查是否为菜单类型
     */
    public boolean isMenu() {
        return this == MENU;
    }

    /**
     * 检查是否为按钮类型
     */
    public boolean isButton() {
        return this == BUTTON;
    }

    @Override
    public String toString() {
        return code + "(" + description + ")";
    }
}
