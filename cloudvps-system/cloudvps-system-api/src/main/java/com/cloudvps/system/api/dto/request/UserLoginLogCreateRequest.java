package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.LoginResult;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户登录日志创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserLoginLogCreateRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 登录IP地址
     */
    @NotBlank(message = "登录IP地址不能为空")
    @Size(max = 50, message = "登录IP地址长度不能超过50个字符")
    private String loginIp;
    
    /**
     * 登录地点
     */
    @Size(max = 200, message = "登录地点长度不能超过200个字符")
    private String loginLocation;
    
    /**
     * 用户代理信息
     */
    @Size(max = 500, message = "用户代理信息长度不能超过500个字符")
    private String userAgent;
    
    /**
     * 登录时间
     */
    private LocalDateTime loginTime = LocalDateTime.now();
    
    /**
     * 登录结果
     */
    @NotNull(message = "登录结果不能为空")
    private LoginResult loginResult = LoginResult.SUCCESS;
    
    /**
     * 失败原因
     */
    @Size(max = 500, message = "失败原因长度不能超过500个字符")
    private String failureReason;
}
