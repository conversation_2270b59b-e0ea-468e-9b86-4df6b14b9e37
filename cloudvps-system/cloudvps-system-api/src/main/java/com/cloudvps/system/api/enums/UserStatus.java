package com.cloudvps.system.api.enums;

import lombok.Getter;

/**
 * 用户状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum UserStatus {
    /**
     * 激活状态
     */
    ACTIVE("激活"),
    
    /**
     * 未激活状态
     */
    INACTIVE("未激活"),
    
    /**
     * 锁定状态
     */
    LOCKED("锁定"),
    
    /**
     * 已删除状态
     */
    DELETED("已删除");
    
    private final String description;
    
    UserStatus(String description) {
        this.description = description;
    }

}
