package com.cloudvps.system.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 用户账户查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserAccountQueryRequest extends PageRequest {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 账户余额最小值
     */
    private BigDecimal minBalance;
    
    /**
     * 账户余额最大值
     */
    private BigDecimal maxBalance;
    
    /**
     * 是否锁定
     */
    private Boolean isLocked;
    
    /**
     * 锁定原因
     */
    private String lockReason;
}
