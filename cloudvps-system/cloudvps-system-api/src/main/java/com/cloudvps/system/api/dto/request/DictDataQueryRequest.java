package com.cloudvps.system.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.system.api.enums.DictStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典数据查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DictDataQueryRequest extends PageRequest {
    
    /**
     * 字典类型ID
     */
    private Long dictTypeId;
    
    /**
     * 字典标签
     */
    private String dictLabel;
    
    /**
     * 字典值
     */
    private String dictValue;
    
    /**
     * 状态
     */
    private DictStatus status;
    
    /**
     * 是否默认
     */
    private Boolean isDefault;
}
