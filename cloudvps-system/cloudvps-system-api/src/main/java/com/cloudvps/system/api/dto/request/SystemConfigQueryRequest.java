package com.cloudvps.system.api.dto.request;

import com.cloudvps.common.core.request.PageRequest;
import com.cloudvps.system.api.enums.ConfigType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统配置查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemConfigQueryRequest extends PageRequest {
    
    /**
     * 配置键
     */
    private String configKey;
    
    /**
     * 配置类型
     */
    private ConfigType configType;
    
    /**
     * 是否加密
     */
    private Boolean isEncrypted;
    
    /**
     * 是否系统配置
     */
    private Boolean isSystem;
}
