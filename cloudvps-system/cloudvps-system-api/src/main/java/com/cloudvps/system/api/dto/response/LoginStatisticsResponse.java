package com.cloudvps.system.api.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 登录统计响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class LoginStatisticsResponse {
    
    /**
     * 统计开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 统计结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 总登录次数
     */
    private Long totalLogins;
    
    /**
     * 成功登录次数
     */
    private Long successfulLogins;
    
    /**
     * 失败登录次数
     */
    private Long failedLogins;
    
    /**
     * 锁定登录次数
     */
    private Long lockedLogins;
    
    /**
     * 独立用户数
     */
    private Long uniqueUsers;
    
    /**
     * 独立IP数
     */
    private Long uniqueIps;
    
    /**
     * 成功率（百分比）
     */
    private Double successRate;
    
    /**
     * 失败率（百分比）
     */
    private Double failureRate;
    
    /**
     * 平均每日登录次数
     */
    private Double averageDailyLogins;
    
    /**
     * 热门登录时段（小时 -> 登录次数）
     */
    private Map<String, Long> popularHours;
    
    /**
     * 热门登录IP（IP -> 登录次数）
     */
    private Map<String, Long> topIps;
    
    /**
     * 登录失败原因统计（原因 -> 次数）
     */
    private Map<String, Long> failureReasons;
    
    /**
     * 每日登录趋势（日期 -> 登录次数）
     */
    private Map<String, Long> dailyTrend;
    
    /**
     * 浏览器统计（浏览器 -> 使用次数）
     */
    private Map<String, Long> browserStats;
    
    /**
     * 操作系统统计（系统 -> 使用次数）
     */
    private Map<String, Long> osStats;
    
    /**
     * 设备类型统计（设备类型 -> 使用次数）
     */
    private Map<String, Long> deviceStats;
}
