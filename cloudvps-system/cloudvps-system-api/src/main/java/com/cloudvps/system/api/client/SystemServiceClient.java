package com.cloudvps.system.api.client;

import com.cloudvps.common.core.response.ApiResponse;
import com.cloudvps.system.api.dto.request.UserLoginRequest;
import com.cloudvps.system.api.dto.response.UserResponse;
import com.cloudvps.system.api.dto.response.LoginResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 系统服务Feign客户端
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@FeignClient(
    name = "system-service",
    url = "${cloudvps.services.system.url:http://localhost:8081}",
    path = "/api/v1"
)
public interface SystemServiceClient {
    
    /**
     * 用户登录
     */
    @PostMapping("/auth/login")
    ApiResponse<LoginResponse> login(@RequestBody UserLoginRequest request);

    /**
     * 验证JWT令牌
     */
    @GetMapping("/auth/verify-token")
    ApiResponse<UserResponse> verifyToken(@RequestHeader("Authorization") String token);

    /**
     * 获取当前用户信息
     */
    @GetMapping("/auth/current-user")
    ApiResponse<UserResponse> getCurrentUser(@RequestHeader("Authorization") String token);

    /**
     * 获取用户信息
     */
    @GetMapping("/users/{userId}")
    ApiResponse<UserResponse> getUserInfo(@PathVariable("userId") Long userId);
    
    /**
     * 获取系统配置值
     */
    @GetMapping("/system/configs/value/{key}")
    ApiResponse<String> getConfigValue(@PathVariable("key") String key);
    
    /**
     * 根据前缀获取配置
     */
    @GetMapping("/system/configs/prefix/{prefix}")
    ApiResponse<Map<String, String>> getConfigsByPrefix(@PathVariable("prefix") String prefix);
    
    /**
     * 获取用户余额
     */
    @GetMapping("/system/accounts/balance/{userId}")
    ApiResponse<Double> getUserBalance(@PathVariable("userId") Long userId);

}
