package com.cloudvps.system.api.dto.request;

import com.cloudvps.system.api.enums.DictStatus;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 字典数据更新请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class DictDataUpdateRequest {
    
    /**
     * 字典标签
     */
    @Size(max = 100, message = "字典标签长度不能超过100个字符")
    private String dictLabel;
    
    /**
     * 字典值
     */
    @Size(max = 100, message = "字典值长度不能超过100个字符")
    private String dictValue;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 状态
     */
    private DictStatus status;
    
    /**
     * 是否默认
     */
    private Boolean isDefault;
    
    /**
     * CSS类名
     */
    @Size(max = 100, message = "CSS类名长度不能超过100个字符")
    private String cssClass;
    
    /**
     * 列表类名
     */
    @Size(max = 100, message = "列表类名长度不能超过100个字符")
    private String listClass;
    
    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
