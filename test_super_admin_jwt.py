#!/usr/bin/env python3
"""
生成超级管理员测试JWT令牌
"""
import jwt
import datetime

# JWT密钥（与应用程序中的密钥保持一致）
SECRET_KEY = "cloudvps-secret-key"

# 超级管理员用户信息
payload = {
    "iss": "cloudvps",    # 发行者
    "sub": "1",           # 用户ID作为subject
    "userId": 1,          # 用户ID
    "username": "superadmin",
    "role": "SUPER_ADMIN",
    "iat": datetime.datetime.utcnow(),
    "exp": datetime.datetime.utcnow() + datetime.timedelta(hours=24)
}

# 生成JWT令牌
token = jwt.encode(payload, SECRET_KEY, algorithm="HS256")

print("超级管理员JWT令牌:")
print(token)
print("\n测试命令:")
print(f'curl -H "Authorization: Bearer {token}" http://localhost:8084/api/payments')
