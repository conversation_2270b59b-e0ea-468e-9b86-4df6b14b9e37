<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cloudvps</groupId>
    <artifactId>cloudvps-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>CloudVPS Parent</name>
    <description>CloudVPS 公有云平台父项目</description>

    <repositories>
        <repository>
            <id>projectlombok.org</id>
            <url>https://projectlombok.org/edge-releases</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>projectlombok.org</id>
            <url>https://projectlombok.org/edge-releases</url>
        </pluginRepository>
    </pluginRepositories>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <!-- Spring Boot -->
        <spring-boot.version>3.5.3</spring-boot.version>
        <spring-cloud.version>2025.0.0</spring-cloud.version>
        <!-- Web 相关 -->
        <springdoc.version>2.8.3</springdoc.version>
        <knife4j.version>4.6.0</knife4j.version>
        <!-- Database -->
        <postgresql.version>42.7.1</postgresql.version>
        <h2.version>2.2.224</h2.version>
<!--        <mybatis-plus.version>3.5.5</mybatis-plus.version>-->

        <!-- Tools -->
        <lombok.version>1.18.38</lombok.version>
        <mapstruct.version>1.6.2</mapstruct.version>
        <hutool.version>5.8.25</hutool.version>

        <!-- Maven 插件版本 -->
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.2.5</maven-surefire-plugin.version>

        <!-- JWT -->
        <jjwt.version>0.12.3</jjwt.version>


        <!-- Testing -->
        <junit.version>5.10.1</junit.version>
        <testcontainers.version>1.19.3</testcontainers.version>

        <!-- PVE Client -->
        <pve-java-client.version>1.0.0</pve-java-client.version>

        <!-- Database -->
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <mybatis-plus-jsqlparser.version>3.5.12</mybatis-plus-jsqlparser.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- CloudVPS Common 模块 -->
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>common-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>common-swagger</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>common-feign</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>common-mybatis</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- CloudVPS API 模块 -->
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>cloudvps-system-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>cloudvps-virtualization-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>cloudvps-order-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cloudvps</groupId>
                <artifactId>cloudvps-payment-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- PVE Java Client -->
            <dependency>
                <groupId>io.github.pve-java-client</groupId>
                <artifactId>pve-java-client</artifactId>
                <version>${pve-java-client.version}</version>
            </dependency>

            <!-- Database -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>

            <!-- MyBatis-Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus-jsqlparser.version}</version>
            </dependency>

            <!-- Tools -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- Swagger/OpenAPI -->
            <dependency>
                <groupId>com.github.xingfudeshi</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 UI：knife4j【网关专属】 -->
                <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
                <version>4.5.0</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>org.springdoc</groupId>-->
<!--                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>-->
<!--                <version>${springdoc.version}</version>-->
<!--            </dependency>-->

            <!-- Testing -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>postgresql</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Maven 编译插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring-boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                        <!-- 编译参数写在 arg 内，解决 Spring Boot 3.2 的 Parameter Name Discovery 问题 -->
                        <debug>false</debug>
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>

                <!-- Maven Surefire 插件 (单元测试) -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <includes>
                            <include>**/*Test.java</include>
                            <include>**/*Tests.java</include>
                        </includes>
                    </configuration>
                </plugin>

                <!-- 统一 revision 版本 -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${flatten-maven-plugin.version}</version>
                    <configuration>
                        <flattenMode>bom</flattenMode>
                        <updatePomFile>true</updatePomFile>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                        </execution>
                        <execution>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                            <id>flatten.clean</id>
                            <phase>clean</phase>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
