# CloudVPS 平台技术架构文档

## 架构概述

CloudVPS 平台采用微服务架构设计，通过 API 网关统一对外提供服务，各微服务独立部署和扩展。平台基于 Spring Cloud 生态系统构建，具备高可用、高并发、易扩展的特性。

## 整体架构设计

### 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (Client Layer)                    │
│  Web 前端 │ 移动端 │ 第三方系统 │ 管理后台                      │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTPS/REST API
┌─────────────────────▼───────────────────────────────────────┐
│                  API 网关层 (Gateway Layer)                  │
│  路由转发 │ 认证授权 │ 限流熔断 │ 监控日志                      │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/内部通信
┌─────────────────────▼───────────────────────────────────────┐
│                  业务服务层 (Service Layer)                  │
│ 系统服务 │ 虚拟化服务 │ 订单服务 │ 支付服务                     │
└─────────────────────┬───────────────────────────────────────┘
                      │ JDBC/Redis Protocol
┌─────────────────────▼───────────────────────────────────────┐
│                   数据存储层 (Data Layer)                    │
│  PostgreSQL │ Redis │ H2 │ 文件存储                         │
└─────────────────────────────────────────────────────────────┘
```

### 微服务拆分原则

1. **业务边界清晰**: 按业务领域拆分服务
2. **数据独立**: 每个服务拥有独立的数据库
3. **接口标准**: 统一的 REST API 设计
4. **无状态设计**: 服务实例可水平扩展
5. **故障隔离**: 服务间故障不相互影响

## 核心组件架构

### 1. API 网关 (cloudvps-gateway)

#### 技术选型
- **框架**: Spring Cloud Gateway
- **认证**: JWT Token 验证
- **限流**: Redis + 滑动窗口算法
- **熔断**: Resilience4j Circuit Breaker
- **负载均衡**: Spring Cloud LoadBalancer

#### 核心功能
```java
// 路由配置示例
@Configuration
public class GatewayConfig {
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            .route("system-service", r -> r
                .path("/system/**")
                .uri("http://localhost:8081"))
            .build();
    }
}
```

#### 过滤器链
1. **JWT 认证过滤器**: 验证用户身份
2. **限流过滤器**: 防止服务过载
3. **熔断过滤器**: 服务降级保护
4. **日志过滤器**: 请求响应日志

### 2. 系统服务 (cloudvps-system)

#### 架构设计
```
Controller Layer (REST API)
    ↓
Service Layer (业务逻辑)
    ↓
Repository Layer (数据访问)
    ↓
Database Layer (PostgreSQL)
```

#### 核心模块
- **用户管理**: 用户注册、登录、权限控制
- **认证授权**: JWT 令牌生成、RBAC 权限模型
- **系统配置**: 动态配置管理
- **审计日志**: 操作日志记录

#### 数据库设计
```sql
-- 核心表结构
users (用户表)
├── user_accounts (用户账户)
├── user_roles (用户角色关联)
└── login_logs (登录日志)

roles (角色表)
└── role_permissions (角色权限关联)

permissions (权限表)
system_configs (系统配置表)
dictionaries (字典表)
```

### 3. 虚拟化服务 (cloudvps-virtualization)

#### PVE 集成架构
```
VirtualMachine Controller
    ↓
VirtualMachine Service
    ↓
PVE Client Wrapper
    ↓
pve-java-client (1.0.0)
    ↓
Proxmox VE API
```

#### 运行模式
- **模拟模式**: 开发测试环境，无需真实 PVE
- **真实模式**: 生产环境，集成真实 PVE 集群

#### 定时任务
```java
@Scheduled(fixedRate = 60000) // 每分钟
public void checkNodeHeartbeat() {
    // 检查 PVE 节点连通性
}

@Scheduled(fixedRate = 300000) // 每5分钟
public void autoStartVms() {
    // 自动启动标记的虚拟机
}
```

### 4. 订单服务 (cloudvps-order)

#### 订单状态机
```
DRAFT → PENDING → VALIDATED → PAID → PROCESSING → COMPLETED
  ↓       ↓         ↓         ↓         ↓
CANCELLED CANCELLED CANCELLED CANCELLED FAILED
```

#### 业务流程
1. **订单创建**: 用户选择产品创建订单
2. **订单验证**: 检查库存、用户权限
3. **费用计算**: 计算订单总金额
4. **支付处理**: 调用支付服务
5. **资源分配**: 调用虚拟化服务创建 VM

### 5. 支付服务 (cloudvps-payment)

#### 支付渠道架构
```
Payment Controller
    ↓
Payment Service
    ↓
Channel Service (策略模式)
    ├── AlipayChannel
    ├── WechatPayChannel
    ├── BankTransferChannel
    └── BalanceChannel
```

#### 支付流程
1. **创建支付**: 生成支付订单
2. **渠道选择**: 用户选择支付方式
3. **网关调用**: 调用第三方支付接口
4. **回调处理**: 处理支付结果通知
5. **状态更新**: 更新订单和支付状态

## 数据架构设计

### 数据库分布策略

#### 系统服务数据库 (cloudvps_system)
- **用户数据**: users, user_accounts, user_roles
- **权限数据**: roles, permissions, role_permissions
- **配置数据**: system_configs, dictionaries
- **日志数据**: login_logs

#### 虚拟化服务数据库 (cloudvps_virtualization)
- **节点数据**: pve_nodes
- **虚拟机数据**: virtual_machines
- **模板数据**: vm_templates
- **监控数据**: resource_metrics

#### 订单服务数据库 (cloudvps_order)
- **订单数据**: orders, order_items
- **产品数据**: products, product_pricing
- **状态数据**: order_status_history

#### 支付服务数据库 (cloudvps_payment)
- **支付数据**: payments, payment_records
- **渠道数据**: payment_channels
- **交易数据**: transactions, refunds

### 缓存架构 (Redis)

#### 缓存策略
```
Database 0: 系统服务缓存 (用户会话、权限)
Database 1: 虚拟化服务缓存 (节点状态、VM 信息)
Database 2: 订单服务缓存 (订单状态、产品信息)
Database 3: 支付服务缓存 (支付状态、交易记录)
Database 4: 网关缓存 (限流计数、熔断状态)
```

#### 缓存模式
- **Cache-Aside**: 应用程序管理缓存
- **TTL 策略**: 不同数据设置不同过期时间
- **缓存预热**: 系统启动时预加载热点数据

## 安全架构设计

### 认证授权流程

```
1. 用户登录 → 系统服务验证 → 生成 JWT Token
2. 客户端请求 → API 网关验证 Token → 提取用户信息
3. 网关转发 → 下游服务获取用户上下文 → 业务处理
```

### JWT Token 设计
```json
{
  "sub": "用户ID",
  "username": "用户名",
  "roles": ["ADMIN", "USER"],
  "permissions": ["USER_VIEW", "VM_CREATE"],
  "iat": "签发时间",
  "exp": "过期时间"
}
```

### 权限控制模型
- **角色**: ADMIN, USER, OPERATOR, GUEST
- **权限**: 资源 + 操作 (如 USER_VIEW, VM_CREATE)
- **控制粒度**: 接口级别权限控制

## 通信架构设计

### 服务间通信

#### 同步通信 (REST API)
```java
@FeignClient(name = "system-service", url = "${system.service.url}")
public interface SystemServiceClient {
    @GetMapping("/system/users/{userId}")
    UserResponse getUser(@PathVariable Long userId);
}
```

#### 异步通信 (消息队列) - 未来扩展
- **事件驱动**: 订单状态变更事件
- **消息队列**: RabbitMQ/Apache Kafka
- **事件溯源**: 业务事件完整记录

### API 设计规范

#### RESTful API 设计
- **资源命名**: 使用名词，复数形式
- **HTTP 方法**: GET(查询), POST(创建), PUT(更新), DELETE(删除)
- **状态码**: 标准 HTTP 状态码
- **响应格式**: 统一 JSON 格式

#### 统一响应格式
```json
{
  "code": 200,
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": 1640995200000
}
```

## 监控架构设计

### 应用监控
- **健康检查**: Spring Boot Actuator
- **指标收集**: Micrometer + Prometheus
- **链路追踪**: Spring Cloud Sleuth (未来)
- **日志聚合**: ELK Stack (未来)

### 业务监控
- **用户行为**: 登录、注册、操作统计
- **系统性能**: 响应时间、吞吐量、错误率
- **资源使用**: CPU、内存、磁盘、网络

### 告警机制
- **阈值告警**: 性能指标超过阈值
- **异常告警**: 系统异常和错误
- **业务告警**: 业务指标异常

## 部署架构设计

### 容器化部署
```dockerfile
# 示例 Dockerfile
FROM openjdk:21-jdk-slim
COPY target/app.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### Kubernetes 部署
```yaml
# 示例 Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloudvps-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cloudvps-gateway
  template:
    spec:
      containers:
      - name: gateway
        image: cloudvps-gateway:latest
        ports:
        - containerPort: 8080
```

### 环境隔离
- **开发环境**: 本地开发，模拟数据
- **测试环境**: 集成测试，真实数据
- **预生产环境**: 性能测试，生产数据副本
- **生产环境**: 正式服务，高可用部署

## 扩展性设计

### 水平扩展
- **无状态服务**: 所有服务设计为无状态
- **负载均衡**: 网关层负载均衡
- **数据库分片**: 大数据量时数据库分片

### 垂直扩展
- **服务拆分**: 按业务边界进一步拆分
- **功能模块**: 新增业务模块独立部署
- **技术升级**: 渐进式技术栈升级

## 性能优化策略

### 缓存优化
- **多级缓存**: 应用缓存 + Redis 缓存
- **缓存预热**: 系统启动预加载
- **缓存更新**: 数据变更时及时更新

### 数据库优化
- **索引优化**: 合理创建数据库索引
- **查询优化**: SQL 查询性能优化
- **连接池**: 数据库连接池配置

### 网络优化
- **压缩传输**: HTTP 响应压缩
- **CDN 加速**: 静态资源 CDN 分发
- **长连接**: WebSocket 长连接支持
