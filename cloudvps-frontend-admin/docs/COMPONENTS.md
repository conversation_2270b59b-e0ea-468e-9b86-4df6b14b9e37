# 组件文档

CloudVPS前端管理系统的组件使用指南和API文档。

## 目录

- [SettingsPanel 设置面板](#settingspanel-设置面板)
- [Icon 图标组件](#icon-图标组件)
- [Logo 标志组件](#logo-标志组件)
- [组件开发规范](#组件开发规范)
- [常见问题](#常见问题)

## SettingsPanel 设置面板

系统设置抽屉组件，提供主题、布局、界面元素等系统设置功能。

### 基本用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <t-button @click="openSettings">打开设置</t-button>
    
    <!-- 设置面板 -->
    <SettingsPanel v-model:visible="showSettings" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SettingsPanel from '@/components/common/SettingsPanel/index.vue'

const showSettings = ref(false)

const openSettings = () => {
  showSettings.value = true
}
</script>
```

### 与Store集成

```vue
<template>
  <SettingsPanel v-model:visible="settingsStore.showSettingsPanel" />
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/modules/settings'
import SettingsPanel from '@/components/common/SettingsPanel/index.vue'

const settingsStore = useSettingsStore()
</script>
```

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | boolean | false | 控制面板显示/隐藏状态 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | (value: boolean) | 面板显示状态变化时触发 |

### 功能特性

- ✅ **主题设置**: 支持浅色、暗色、跟随系统三种主题模式
- ✅ **颜色定制**: 支持主色调选择和自定义颜色
- ✅ **布局配置**: 支持侧边导航、顶部导航、混合布局、分割菜单四种布局模式
- ✅ **界面元素**: 可控制各界面元素的显示/隐藏
- ✅ **设置导入导出**: 支持设置的备份和恢复
- ✅ **ESC键关闭**: 按ESC键可关闭面板
- ✅ **点击空白关闭**: 点击面板外区域可关闭面板
- ✅ **响应式设计**: 适配不同屏幕尺寸

### 技术实现

#### 双向绑定

组件使用Vue 3的`v-model:visible`实现双向绑定：

```typescript
// 组件内部实现
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})
```

#### Store集成

与Pinia store的集成通过computed属性实现：

```typescript
// settings store中的实现
const showSettingsPanel = computed({
  get: () => settings.value.showSettingsPanel,
  set: (value: boolean) => {
    settings.value.showSettingsPanel = value
    saveSettings()
  }
})
```

### 最佳实践

1. **状态管理**: 推荐使用store管理设置面板状态，便于全局控制
2. **事件处理**: 利用TDesign Drawer的内置事件处理ESC键和点击空白关闭
3. **性能优化**: 使用computed属性避免不必要的重新渲染
4. **类型安全**: 使用TypeScript确保类型安全

### 常见问题

#### Q: 设置面板无法通过ESC键关闭？
A: 确保TDesign Drawer组件设置了`close-on-esc-keydown="true"`属性。

#### Q: 点击空白区域无法关闭面板？
A: 确保TDesign Drawer组件设置了`close-on-overlay-click="true"`属性。

#### Q: 出现Vue Proxy错误？
A: 检查store中的computed属性是否正确实现了getter和setter。

## Icon 图标组件

统一的图标组件，支持多种图标源和自定义配置。

### 基本用法

```vue
<template>
  <div>
    <!-- TDesign图标 -->
    <Icon name="setting" />
    
    <!-- 本地SVG图标 -->
    <Icon src="/icons/custom-icon.svg" />
    
    <!-- 远程图标 -->
    <Icon src="https://example.com/icon.svg" />
    
    <!-- 图标字体 -->
    <Icon class="iconfont icon-home" />
  </div>
</template>
```

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| name | string | - | TDesign图标名称 |
| src | string | - | 图标源地址（本地或远程） |
| size | string \| number | '16px' | 图标尺寸 |
| color | string | - | 图标颜色 |
| class | string | - | 自定义CSS类名 |

### 功能特性

- ✅ **多源支持**: TDesign图标、本地SVG、远程URL、图标字体
- ✅ **尺寸控制**: 支持像素值和相对单位
- ✅ **颜色定制**: 支持自定义图标颜色
- ✅ **主题适配**: 自动适配暗色/浅色主题
- ✅ **性能优化**: 按需加载，避免CSP问题

### 使用示例

```vue
<template>
  <div class="icon-examples">
    <!-- 不同尺寸 -->
    <Icon name="home" size="16" />
    <Icon name="home" size="24" />
    <Icon name="home" size="32" />
    
    <!-- 不同颜色 -->
    <Icon name="heart" color="#ff4757" />
    <Icon name="star" color="#ffa502" />
    
    <!-- 自定义SVG -->
    <Icon src="/assets/icons/logo.svg" size="48" />
  </div>
</template>
```

## Logo 标志组件

系统标志组件，支持展开/收起状态和主题适配。

### 基本用法

```vue
<template>
  <Logo :collapsed="false" />
</template>
```

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| collapsed | boolean | false | 是否为收起状态 |
| size | 'small' \| 'medium' \| 'large' | 'medium' | 标志尺寸 |

### 功能特性

- ✅ **状态切换**: 支持展开/收起两种显示状态
- ✅ **主题适配**: 自动适配暗色/浅色主题
- ✅ **响应式**: 适配不同屏幕尺寸
- ✅ **SVG优化**: 使用SVG确保清晰度

## 组件开发规范

### 1. 文件结构

```
components/
├── common/           # 通用组件
│   ├── Icon/
│   ├── Logo/
│   └── SettingsPanel/
├── layout/           # 布局组件
│   ├── Header/
│   ├── Sidebar/
│   └── Footer/
└── business/         # 业务组件
    ├── UserTable/
    └── OrderForm/
```

### 2. 命名规范

- **组件名**: 使用PascalCase，如`SettingsPanel`
- **文件名**: 组件目录使用PascalCase，入口文件使用`index.vue`
- **Props**: 使用camelCase
- **Events**: 使用kebab-case

### 3. TypeScript规范

```typescript
// Props定义
interface Props {
  visible?: boolean
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  size: 'medium',
  disabled: false
})

// Events定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'change', value: any): void
}>()
```

### 4. 样式规范

```vue
<style scoped lang="less">
.component-name {
  // 使用CSS变量
  color: var(--td-text-color-primary);
  background: var(--td-bg-color-container);
  
  // 响应式设计
  @media (max-width: 768px) {
    padding: 12px;
  }
}
</style>
```

### 5. 文档规范

每个组件都应包含：
- 基本用法示例
- Props和Events文档
- 功能特性说明
- 最佳实践建议
- 常见问题解答

## 常见问题

### Q: 如何确保组件的类型安全？
A: 使用TypeScript定义Props和Events接口，启用严格模式检查。

### Q: 如何处理组件的主题适配？
A: 使用CSS变量和TDesign的主题系统，避免硬编码颜色值。

### Q: 如何优化组件性能？
A: 使用computed属性、合理使用v-memo、避免不必要的响应式数据。

### Q: 如何测试组件功能？
A: 编写单元测试，使用Vue Test Utils进行组件测试。

---

更多组件文档正在完善中，如有问题请参考源码或联系开发团队。
