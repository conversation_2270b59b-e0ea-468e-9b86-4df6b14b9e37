# CloudVPS 前端B端管理系统 - 部署指南

## 部署概述

本文档详细介绍了CloudVPS前端B端管理系统的部署流程，包括开发环境、测试环境和生产环境的部署方案。

## 环境要求

### 服务器要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+ 或 CentOS 8+)
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **Node.js**: >= 18.0.0
- **pnpm**: >= 8.0.0
- **Nginx**: >= 1.18.0 (推荐)
- **Git**: >= 2.30.0

## 构建部署

### 1. 代码获取
```bash
# 克隆代码仓库
git clone <repository-url>
cd cloudvps-frontend-admin

# 切换到指定分支
git checkout main
```

### 2. 安装依赖
```bash
# 安装项目依赖
pnpm install

# 验证依赖安装
pnpm list
```

### 3. 环境配置
```bash
# 复制环境变量文件
cp .env.example .env.production

# 编辑生产环境配置
vim .env.production
```

生产环境配置示例：
```bash
# 应用配置
VITE_APP_TITLE=CloudVPS管理后台
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production

# API配置
VITE_API_BASE_URL=https://api.cloudvps.com
VITE_API_TIMEOUT=30000

# 认证配置
VITE_JWT_TOKEN_KEY=cloudvps_admin_token
VITE_JWT_REFRESH_TOKEN_KEY=cloudvps_admin_refresh_token

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_PWA=true
VITE_ENABLE_DEVTOOLS=false

# 上传配置
VITE_UPLOAD_MAX_SIZE=10485760
VITE_UPLOAD_ALLOWED_TYPES=image/*,application/pdf
```

### 4. 构建项目
```bash
# 生产环境构建
pnpm build

# 验证构建结果
ls -la dist/
```

### 5. 部署到服务器

#### 方式一：直接部署
```bash
# 将构建产物上传到服务器
scp -r dist/* user@server:/var/www/cloudvps-admin/

# 或使用rsync
rsync -avz --delete dist/ user@server:/var/www/cloudvps-admin/
```

#### 方式二：使用CI/CD
创建 `.github/workflows/deploy.yml`：
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Build project
      run: pnpm build
      
    - name: Deploy to server
      uses: appleboy/scp-action@v0.1.4
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        source: "dist/*"
        target: "/var/www/cloudvps-admin/"
        strip_components: 1
```

## Nginx 配置

### 1. 安装Nginx
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

### 2. 配置Nginx
创建配置文件 `/etc/nginx/sites-available/cloudvps-admin`：
```nginx
server {
    listen 80;
    server_name admin.cloudvps.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.cloudvps.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/cloudvps.crt;
    ssl_certificate_key /etc/ssl/private/cloudvps.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 网站根目录
    root /var/www/cloudvps-admin;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'self';" always;
    
    # 日志配置
    access_log /var/log/nginx/cloudvps-admin.access.log;
    error_log /var/log/nginx/cloudvps-admin.error.log;
}
```

### 3. 启用配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/cloudvps-admin /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx

# 设置开机自启
sudo systemctl enable nginx
```

## SSL证书配置

### 使用Let's Encrypt免费证书
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d admin.cloudvps.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 监控和日志

### 1. 日志管理
```bash
# 查看访问日志
sudo tail -f /var/log/nginx/cloudvps-admin.access.log

# 查看错误日志
sudo tail -f /var/log/nginx/cloudvps-admin.error.log

# 日志轮转配置
sudo vim /etc/logrotate.d/cloudvps-admin
```

### 2. 性能监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控系统资源
htop

# 监控网络连接
sudo netstat -tulpn | grep :80
sudo netstat -tulpn | grep :443
```

## 备份策略

### 1. 代码备份
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/cloudvps-admin"
SOURCE_DIR="/var/www/cloudvps-admin"

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/cloudvps-admin_$DATE.tar.gz -C $SOURCE_DIR .

# 保留最近7天的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 2. 自动备份
```bash
# 添加到crontab
sudo crontab -e
# 每天凌晨2点备份
0 2 * * * /path/to/backup.sh
```

## 故障排除

### 常见问题

1. **页面无法访问**
   - 检查Nginx状态：`sudo systemctl status nginx`
   - 检查端口占用：`sudo netstat -tulpn | grep :80`
   - 检查防火墙：`sudo ufw status`

2. **API请求失败**
   - 检查后端服务状态
   - 检查Nginx代理配置
   - 查看错误日志

3. **静态资源加载失败**
   - 检查文件权限：`ls -la /var/www/cloudvps-admin/`
   - 检查Nginx配置中的root路径

4. **SSL证书问题**
   - 检查证书有效期：`sudo certbot certificates`
   - 手动续期：`sudo certbot renew`

### 性能优化

1. **启用HTTP/2**
   ```nginx
   listen 443 ssl http2;
   ```

2. **优化缓存策略**
   ```nginx
   location ~* \.(js|css)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

3. **启用Brotli压缩**
   ```bash
   sudo apt install nginx-module-brotli
   ```

## 安全建议

1. **定期更新系统和软件包**
2. **配置防火墙规则**
3. **使用强密码和SSH密钥认证**
4. **定期备份重要数据**
5. **监控系统日志和异常访问**
6. **及时更新SSL证书**

## 版本更新

### 更新流程
1. 备份当前版本
2. 拉取最新代码
3. 安装依赖
4. 构建项目
5. 部署到服务器
6. 验证功能
7. 回滚方案（如有问题）

```bash
#!/bin/bash
# update.sh
set -e

echo "开始更新CloudVPS前端管理系统..."

# 备份当前版本
./backup.sh

# 拉取最新代码
git pull origin main

# 安装依赖
pnpm install

# 构建项目
pnpm build

# 部署
rsync -avz --delete dist/ /var/www/cloudvps-admin/

# 重启Nginx
sudo systemctl reload nginx

echo "更新完成！"
```
