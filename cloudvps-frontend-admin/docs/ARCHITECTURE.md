# CloudVPS 前端B端管理系统 - 架构设计文档

## 项目概述

CloudVPS前端B端管理系统是一个基于Vue 3 + TypeScript + TDesign UI构建的现代化企业级管理后台，专为CloudVPS云服务平台提供完整的业务管理界面。

## 技术架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    CloudVPS Frontend Admin                  │
├─────────────────────────────────────────────────────────────┤
│  Presentation Layer (表现层)                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Pages     │ │ Components  │ │   Layouts   │           │
│  │   页面组件   │ │   业务组件   │ │   布局组件   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer (业务逻辑层)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Composables │ │   Stores    │ │   Hooks     │           │
│  │  组合式函数  │ │   状态管理   │ │   自定义钩子 │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  Data Access Layer (数据访问层)                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │     API     │ │   Request   │ │    Types    │           │
│  │   接口定义   │ │   请求工具   │ │   类型定义   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  Infrastructure Layer (基础设施层)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Router    │ │   Utils     │ │ Directives  │           │
│  │   路由管理   │ │   工具函数   │ │   自定义指令 │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  CloudVPS Backend Services                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Gateway   │ │   System    │ │Virtualization│          │
│  │    :8080    │ │   :8081     │ │    :8082     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐                           │
│  │   Orders    │ │  Payments   │                           │
│  │    :8083    │ │    :8084    │                           │
│  └─────────────┘ └─────────────┘                           │
└─────────────────────────────────────────────────────────────┘
```

### 核心技术栈

#### 前端框架
- **Vue 3.4+**: 渐进式JavaScript框架，使用Composition API
- **TypeScript 5.0+**: 提供类型安全和更好的开发体验
- **Vite 5.0+**: 快速的构建工具和开发服务器

#### UI组件库
- **TDesign Vue Next**: 腾讯企业级设计语言的Vue 3实现
- **TDesign Icons**: 丰富的图标库

#### 状态管理
- **Pinia**: Vue 3官方推荐的状态管理库
- **Pinia Persist**: 状态持久化插件

#### 路由管理
- **Vue Router 4**: Vue 3官方路由管理器
- 支持路由守卫、动态路由、权限控制

#### HTTP客户端
- **Axios**: Promise based HTTP client
- 统一的请求/响应拦截器
- 自动token刷新机制

## 模块设计

### 1. 认证模块 (Auth)
```typescript
// 功能特性
- JWT token认证
- 自动token刷新
- 权限验证
- 登录状态管理

// 主要组件
- Login.vue: 登录页面
- Register.vue: 注册页面
- ForgotPassword.vue: 忘记密码
- useAuthStore: 认证状态管理
```

### 2. 系统管理模块 (System)
```typescript
// 功能特性
- 用户管理 (CRUD)
- 角色管理 (CRUD)
- 权限管理 (CRUD)
- 菜单管理 (CRUD)
- 系统配置管理
- 字典管理
- 操作日志查看

// 权限控制
- USER_VIEW/CREATE/UPDATE/DELETE
- ROLE_VIEW/CREATE/UPDATE/DELETE
- PERMISSION_VIEW/CREATE/UPDATE/DELETE
- MENU_VIEW/CREATE/UPDATE/DELETE
```

### 3. 虚拟化管理模块 (Virtualization)
```typescript
// 功能特性
- PVE节点管理
- 虚拟机生命周期管理
- 资源监控
- 快照管理
- 备份管理
- 模板管理

// 权限控制
- VIRTUALIZATION_NODE_VIEW/CREATE/UPDATE/DELETE
- VIRTUALIZATION_VM_VIEW/CREATE/UPDATE/DELETE
- VIRTUALIZATION_VM_CONSOLE/SNAPSHOT/BACKUP
```

### 4. 订单管理模块 (Orders)
```typescript
// 功能特性
- 订单列表查看
- 订单详情管理
- 产品管理
- 订单统计分析

// 权限控制
- ORDER_VIEW/CREATE/UPDATE/DELETE
- PRODUCT_VIEW/CREATE/UPDATE/DELETE
- ORDER_STATISTICS_VIEW
```

### 5. 支付管理模块 (Payments)
```typescript
// 功能特性
- 交易记录查看
- 支付渠道管理
- 退款管理
- 支付统计分析

// 权限控制
- PAYMENT_TRANSACTION_VIEW
- PAYMENT_CHANNEL_VIEW/CREATE/UPDATE/DELETE
- PAYMENT_REFUND_VIEW
- PAYMENT_STATISTICS_VIEW
```

## 数据流设计

### 1. 认证流程
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Auth API
    participant S as Store
    
    U->>F: 输入用户名密码
    F->>A: POST /auth/login
    A->>F: 返回token和用户信息
    F->>S: 存储认证状态
    S->>F: 更新UI状态
    F->>U: 跳转到首页
```

### 2. API请求流程
```mermaid
sequenceDiagram
    participant C as Component
    participant A as API Module
    participant R as Request Utils
    participant B as Backend
    
    C->>A: 调用API方法
    A->>R: 发送HTTP请求
    R->>R: 添加认证头
    R->>B: 发送请求
    B->>R: 返回响应
    R->>R: 处理响应/错误
    R->>A: 返回处理后的数据
    A->>C: 返回业务数据
```

### 3. 状态管理流程
```mermaid
graph TD
    A[Component] --> B[Action]
    B --> C[API Call]
    C --> D[Update State]
    D --> E[Reactive Update]
    E --> A
    
    F[Persistent Storage] --> D
    D --> F
```

## 权限控制设计

### 1. 权限模型
```typescript
// 用户 -> 角色 -> 权限 -> 资源
User {
  id: number
  roles: Role[]
}

Role {
  id: number
  permissions: Permission[]
}

Permission {
  code: string
  resourceType: 'MENU' | 'BUTTON' | 'API' | 'DATA'
  resourcePath: string
}
```

### 2. 权限验证机制
```typescript
// 路由级权限
router.beforeEach((to, from, next) => {
  const permissions = to.meta.permissions
  if (permissions && !hasPermission(permissions)) {
    next('/403')
  }
})

// 组件级权限
<t-button v-if="hasPermission('USER_CREATE')">
  创建用户
</t-button>

// 指令级权限
<t-button v-permission="'USER_DELETE'">
  删除用户
</t-button>
```

### 3. 超级管理员机制
```typescript
// 超级管理员绕过所有权限检查
const hasPermission = (permission: string | string[]) => {
  if (isSuperAdmin()) return true
  return checkUserPermissions(permission)
}
```

## 响应式设计

### 1. 断点设计
```less
// 移动端
@mobile: ~'(max-width: 768px)';

// 平板端
@tablet: ~'(min-width: 769px) and (max-width: 1024px)';

// 桌面端
@desktop: ~'(min-width: 1025px)';
```

### 2. 布局适配
```typescript
// 自动检测设备类型
const checkDevice = () => {
  const width = window.innerWidth
  if (width < 768) {
    appStore.setDevice('mobile')
  } else if (width < 1024) {
    appStore.setDevice('tablet')
  } else {
    appStore.setDevice('desktop')
  }
}

// 移动端自动收起侧边栏
if (isMobile && sidebarOpened) {
  closeSidebar()
}
```

## 性能优化

### 1. 代码分割
```typescript
// 路由懒加载
const routes = [
  {
    path: '/system/users',
    component: () => import('@/pages/system/users/Index.vue')
  }
]

// 组件懒加载
const AsyncComponent = defineAsyncComponent(
  () => import('@/components/HeavyComponent.vue')
)
```

### 2. 缓存策略
```typescript
// 组件缓存
<keep-alive :include="cachedViews">
  <router-view />
</keep-alive>

// API缓存
const { data, loading } = useRequest(api.getUsers, {
  cacheKey: 'users-list',
  cacheTime: 5 * 60 * 1000 // 5分钟
})
```

### 3. 虚拟滚动
```vue
<!-- 大数据量表格 -->
<t-table
  :data="data"
  :virtual-scroll="{ height: 400 }"
  :row-height="48"
/>
```

## 安全设计

### 1. XSS防护
```typescript
// 输入验证和转义
const sanitizeInput = (input: string) => {
  return DOMPurify.sanitize(input)
}

// CSP策略
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'
```

### 2. CSRF防护
```typescript
// 请求头验证
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest'

// Token验证
const token = getCSRFToken()
axios.defaults.headers.common['X-CSRF-Token'] = token
```

### 3. 敏感信息保护
```typescript
// 敏感数据不存储在localStorage
const sensitiveData = sessionStorage.getItem('sensitive')

// 自动登出机制
const autoLogout = () => {
  if (isTokenExpired()) {
    authStore.logout()
  }
}
```

## 测试策略

### 1. 单元测试
```typescript
// 组件测试
describe('UserList', () => {
  it('should render user list correctly', () => {
    const wrapper = mount(UserList, {
      props: { users: mockUsers }
    })
    expect(wrapper.find('.user-item')).toHaveLength(mockUsers.length)
  })
})

// 工具函数测试
describe('formatDate', () => {
  it('should format date correctly', () => {
    const result = formatDate('2024-01-01', 'YYYY-MM-DD')
    expect(result).toBe('2024-01-01')
  })
})
```

### 2. 集成测试
```typescript
// API集成测试
describe('User API', () => {
  it('should fetch users successfully', async () => {
    const users = await userApi.getUsers({})
    expect(users.data.list).toBeInstanceOf(Array)
  })
})
```

### 3. E2E测试
```typescript
// 端到端测试
test('user login flow', async ({ page }) => {
  await page.goto('/login')
  await page.fill('[data-testid="username"]', 'admin')
  await page.fill('[data-testid="password"]', 'password')
  await page.click('[data-testid="login-button"]')
  await expect(page).toHaveURL('/dashboard')
})
```

## 部署架构

### 1. 开发环境
```
Developer Machine
├── Node.js 18+
├── pnpm 8+
├── VS Code + Extensions
└── Chrome DevTools
```

### 2. 生产环境
```
Production Server
├── Nginx (反向代理 + 静态文件服务)
├── SSL证书 (Let's Encrypt)
├── PM2 (进程管理)
└── 监控和日志系统
```

### 3. CI/CD流程
```
GitHub Actions
├── 代码检查 (ESLint + TypeScript)
├── 单元测试 (Vitest)
├── 构建打包 (Vite)
├── 部署到服务器 (SCP/SFTP)
└── 健康检查
```

## 扩展性设计

### 1. 插件化架构
```typescript
// 插件接口
interface Plugin {
  name: string
  install(app: App): void
}

// 插件注册
const plugins: Plugin[] = [
  ThemePlugin,
  I18nPlugin,
  ChartPlugin
]

plugins.forEach(plugin => {
  app.use(plugin)
})
```

### 2. 微前端支持
```typescript
// 模块联邦配置
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      name: 'cloudvps_admin',
      remotes: {
        monitoring: 'monitoring@http://localhost:3001/remoteEntry.js'
      }
    })
  ]
}
```

### 3. 国际化支持
```typescript
// i18n配置
const i18n = createI18n({
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS
  }
})
```

## 总结

CloudVPS前端B端管理系统采用现代化的技术栈和架构设计，具备以下特点：

1. **技术先进**: Vue 3 + TypeScript + TDesign UI
2. **架构清晰**: 分层架构，职责明确
3. **功能完整**: 覆盖系统管理、虚拟化、订单、支付等核心业务
4. **权限完善**: 细粒度权限控制，支持超级管理员
5. **响应式设计**: 支持多设备访问
6. **性能优化**: 代码分割、缓存策略、虚拟滚动
7. **安全可靠**: XSS/CSRF防护，敏感信息保护
8. **易于维护**: 完善的测试覆盖，清晰的代码结构
9. **部署简单**: 支持CI/CD，一键部署
10. **扩展性强**: 插件化架构，支持微前端

该系统为CloudVPS平台提供了一个功能强大、用户友好的管理界面，能够满足企业级应用的各种需求。
