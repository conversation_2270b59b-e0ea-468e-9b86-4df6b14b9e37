# 路由和权限文档

CloudVPS前端管理系统的路由配置和权限控制实现指南。

## 目录

- [路由架构](#路由架构)
- [路由守卫](#路由守卫)
- [权限控制](#权限控制)
- [认证流程](#认证流程)
- [配置示例](#配置示例)
- [最佳实践](#最佳实践)

## 路由架构

### 路由结构

```
/
├── /login                  # 登录页面
├── /login-simple          # 简化登录页面
├── /register              # 注册页面
├── /dashboard             # 仪表板
├── /system                # 系统管理
│   ├── /users            # 用户管理
│   ├── /roles            # 角色管理
│   ├── /permissions      # 权限管理
│   ├── /dict             # 字典管理
│   └── /config           # 系统配置
├── /virtualization        # 虚拟化管理
│   ├── /nodes            # 节点管理
│   ├── /vms              # 虚拟机管理
│   └── /templates        # 模板管理
├── /order                 # 订单管理
│   ├── /orders           # 订单列表
│   └── /products         # 产品管理
├── /payment               # 支付管理
│   ├── /payments         # 支付记录
│   └── /accounts         # 账户管理
└── /test                  # 测试页面
    ├── /api              # API测试
    ├── /config           # 配置测试
    ├── /icon             # 图标测试
    ├── /auth-guard       # 路由守卫测试
    ├── /settings         # 设置测试
    ├── /api-refactor     # API重构验证
    └── /settings-panel   # 设置面板测试
```

### 路由配置

```typescript
// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/auth/LoginSimple.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: () => import('@/layouts/DefaultLayout.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/pages/dashboard/Index.vue'),
        meta: {
          title: '仪表板',
          permissions: ['DASHBOARD_VIEW']
        }
      },
      {
        path: 'system',
        name: 'System',
        redirect: '/system/users',
        meta: {
          title: '系统管理',
          permissions: ['SYSTEM_VIEW']
        },
        children: [
          {
            path: 'users',
            name: 'SystemUsers',
            component: () => import('@/pages/system/users/Index.vue'),
            meta: {
              title: '用户管理',
              permissions: ['USER_VIEW']
            }
          }
        ]
      }
    ]
  }
]
```

## 路由守卫

### 全局前置守卫

```typescript
// src/router/guards.ts
import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'
import { MessagePlugin } from 'tdesign-vue-next'

export function setupRouterGuards(router: Router) {
  router.beforeEach(async (to, from, next) => {
    // 开始进度条
    NProgress.start()
    
    const authStore = useAuthStore()
    
    // 设置页面标题
    const title = to.meta.title as string
    if (title) {
      document.title = `${title} - ${import.meta.env.VITE_APP_TITLE}`
    }
    
    // 检查白名单
    if (whiteList.includes(to.path)) {
      next()
      return
    }
    
    // 认证检查
    if (to.meta.requiresAuth !== false) {
      if (!authStore.token) {
        MessagePlugin.warning('请先登录')
        next({ path: '/login', query: { redirect: to.fullPath } })
        return
      }
      
      // Token验证
      if (!authStore.currentUser && !authStore.isValidating) {
        try {
          const isValid = await authStore.validateToken()
          if (!isValid) {
            MessagePlugin.error('登录已过期，请重新登录')
            next({ path: '/login', query: { redirect: to.fullPath } })
            return
          }
        } catch (error) {
          MessagePlugin.error('认证验证失败，请重新登录')
          authStore.clearAuthData()
          next({ path: '/login', query: { redirect: to.fullPath } })
          return
        }
      }
      
      // 权限检查
      const permissions = to.meta.permissions as string[]
      if (permissions && permissions.length > 0) {
        const hasPermission = authStore.hasPermission(permissions)
        if (!hasPermission) {
          MessagePlugin.error('没有权限访问该页面')
          next('/403')
          return
        }
      }
      
      // 角色检查
      const roles = to.meta.roles as string[]
      if (roles && roles.length > 0) {
        const userType = authStore.currentUser?.userType
        if (!userType || !roles.includes(userType)) {
          MessagePlugin.error('没有权限访问该页面')
          next('/403')
          return
        }
      }
    }
    
    next()
  })
  
  // 全局后置守卫
  router.afterEach(() => {
    NProgress.done()
  })
}
```

### 白名单配置

```typescript
// 不需要认证的路由
const whiteList = [
  '/login',
  '/login-simple',
  '/register',
  '/forgot-password',
  '/403',
  '/404',
  '/500',
  '/test' // 测试页面
]
```

## 权限控制

### 权限模型

CloudVPS采用基于角色的访问控制(RBAC)模型：

```
用户(User) → 角色(Role) → 权限(Permission) → 资源(Resource)
```

### 权限类型

#### 1. 页面权限
控制用户可以访问哪些页面：

```typescript
// 路由元信息中定义
meta: {
  permissions: ['USER_VIEW', 'USER_CREATE']
}
```

#### 2. 操作权限
控制用户可以执行哪些操作：

```vue
<template>
  <t-button 
    v-if="hasPermission('USER_CREATE')"
    @click="createUser"
  >
    新增用户
  </t-button>
</template>
```

#### 3. 数据权限
控制用户可以查看哪些数据：

```typescript
// 在API请求中添加数据权限过滤
const getUserList = async () => {
  const params = {
    ...queryParams,
    dataScope: authStore.currentUser?.dataScope
  }
  return await userApi.getPage(params)
}
```

### 权限检查方法

#### Store中的权限检查

```typescript
// src/stores/modules/auth.ts
export const useAuthStore = defineStore('auth', () => {
  // 检查单个权限
  const hasPermission = (permission: string): boolean => {
    if (!currentUser.value) return false
    
    // 超级管理员拥有所有权限
    if (currentUser.value.roles?.includes('SUPER_ADMIN')) {
      return true
    }
    
    return currentUser.value.permissions?.includes(permission) || false
  }
  
  // 检查多个权限（AND关系）
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }
  
  // 检查多个权限（OR关系）
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }
  
  return {
    hasPermission,
    hasAllPermissions,
    hasAnyPermission
  }
})
```

#### 组合式函数

```typescript
// src/composables/usePermission.ts
import { useAuthStore } from '@/stores/modules/auth'

export function usePermission() {
  const authStore = useAuthStore()
  
  return {
    hasPermission: authStore.hasPermission,
    hasAllPermissions: authStore.hasAllPermissions,
    hasAnyPermission: authStore.hasAnyPermission
  }
}
```

#### 指令方式

```typescript
// src/directives/permission.ts
import type { Directive } from 'vue'
import { useAuthStore } from '@/stores/modules/auth'

export const vPermission: Directive = {
  mounted(el, binding) {
    const authStore = useAuthStore()
    const { value } = binding
    
    if (value) {
      const hasPermission = Array.isArray(value) 
        ? authStore.hasAnyPermission(value)
        : authStore.hasPermission(value)
        
      if (!hasPermission) {
        el.style.display = 'none'
      }
    }
  }
}
```

使用示例：

```vue
<template>
  <t-button v-permission="'USER_CREATE'">新增用户</t-button>
  <t-button v-permission="['USER_UPDATE', 'USER_DELETE']">批量操作</t-button>
</template>
```

## 认证流程

### 1. 登录流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as 认证服务
    participant S as 后端服务
    
    U->>F: 输入用户名密码
    F->>A: 发送登录请求
    A->>A: 验证用户凭据
    A->>F: 返回JWT Token
    F->>F: 存储Token到localStorage
    F->>S: 使用Token获取用户信息
    S->>F: 返回用户信息和权限
    F->>F: 更新用户状态
    F->>U: 跳转到目标页面
```

### 2. Token验证流程

```mermaid
sequenceDiagram
    participant F as 前端
    participant A as 认证服务
    
    F->>F: 检查本地Token
    alt Token存在
        F->>A: 验证Token有效性
        alt Token有效
            A->>F: 返回用户信息
            F->>F: 更新用户状态
        else Token无效
            A->>F: 返回401错误
            F->>F: 清除本地数据
            F->>F: 跳转到登录页
        end
    else Token不存在
        F->>F: 跳转到登录页
    end
```

### 3. 权限检查流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as 路由守卫
    participant A as Auth Store
    
    U->>F: 访问页面
    F->>G: 触发路由守卫
    G->>G: 检查是否在白名单
    alt 在白名单
        G->>F: 允许访问
    else 需要认证
        G->>A: 检查认证状态
        alt 已认证
            G->>A: 检查页面权限
            alt 有权限
                G->>F: 允许访问
            else 无权限
                G->>F: 跳转到403页面
            end
        else 未认证
            G->>F: 跳转到登录页
        end
    end
```

## 配置示例

### 路由元信息配置

```typescript
// 完整的路由元信息示例
{
  path: '/system/users',
  name: 'SystemUsers',
  component: () => import('@/pages/system/users/Index.vue'),
  meta: {
    title: '用户管理',           // 页面标题
    requiresAuth: true,         // 是否需要认证
    permissions: ['USER_VIEW'], // 需要的权限
    roles: ['ADMIN'],          // 需要的角色
    keepAlive: true,           // 是否缓存组件
    icon: 'user',              // 菜单图标
    hidden: false,             // 是否在菜单中隐藏
    breadcrumb: true,          // 是否显示面包屑
    affix: false              // 是否固定在标签页
  }
}
```

### 权限常量定义

```typescript
// src/constants/permissions.ts
export const PERMISSIONS = {
  // 系统管理
  SYSTEM_VIEW: 'SYSTEM_VIEW',
  
  // 用户管理
  USER_VIEW: 'USER_VIEW',
  USER_CREATE: 'USER_CREATE',
  USER_UPDATE: 'USER_UPDATE',
  USER_DELETE: 'USER_DELETE',
  
  // 角色管理
  ROLE_VIEW: 'ROLE_VIEW',
  ROLE_CREATE: 'ROLE_CREATE',
  ROLE_UPDATE: 'ROLE_UPDATE',
  ROLE_DELETE: 'ROLE_DELETE',
  
  // 虚拟化管理
  VIRTUALIZATION_VIEW: 'VIRTUALIZATION_VIEW',
  NODE_VIEW: 'NODE_VIEW',
  NODE_CREATE: 'NODE_CREATE',
  VM_VIEW: 'VM_VIEW',
  VM_CREATE: 'VM_CREATE',
  VM_OPERATE: 'VM_OPERATE'
} as const
```

## 最佳实践

### 1. 路由设计

```typescript
// ✅ 推荐：清晰的路由层级
{
  path: '/system',
  children: [
    { path: 'users', name: 'SystemUsers' },
    { path: 'roles', name: 'SystemRoles' }
  ]
}

// ❌ 不推荐：扁平化路由
{
  path: '/system-users',
  name: 'SystemUsers'
}
```

### 2. 权限粒度

```typescript
// ✅ 推荐：合理的权限粒度
permissions: ['USER_VIEW']           // 页面级权限
permissions: ['USER_CREATE']         // 操作级权限

// ❌ 不推荐：过于细粒度
permissions: ['USER_VIEW_NAME']      // 字段级权限
```

### 3. 错误处理

```typescript
// ✅ 推荐：友好的错误提示
if (!hasPermission) {
  MessagePlugin.error('没有权限访问该页面')
  next('/403')
}

// ❌ 不推荐：直接抛出错误
if (!hasPermission) {
  throw new Error('Permission denied')
}
```

### 4. 性能优化

```typescript
// ✅ 推荐：避免重复验证
if (!authStore.currentUser && !authStore.isValidating) {
  await authStore.validateToken()
}

// ❌ 不推荐：每次都验证
await authStore.validateToken()
```

### 5. 类型安全

```typescript
// ✅ 推荐：使用类型定义
interface RouteMeta {
  title?: string
  requiresAuth?: boolean
  permissions?: string[]
  roles?: string[]
}

// ❌ 不推荐：使用any
meta: any
```

---

更多路由和权限配置示例请参考项目中的具体实现。
