# CloudVPS 前端B端管理系统 - 开发指南

## 环境要求

### 必需软件
- **Node.js**: >= 18.0.0 (推荐使用 LTS 版本)
- **pnpm**: >= 8.0.0 (推荐包管理器)
- **Git**: >= 2.30.0

### 推荐开发工具
- **VS Code** + Vue Language Features (Volar) 插件
- **Chrome DevTools** + Vue DevTools 扩展

## 项目初始化

### 1. 克隆项目
```bash
git clone <repository-url>
cd cloudvps-frontend-admin
```

### 2. 安装依赖
```bash
# 使用 pnpm (推荐)
pnpm install

# 或使用 npm
npm install

# 或使用 yarn
yarn install
```

### 3. 环境配置
```bash
# 复制环境变量文件
cp .env.example .env.development
cp .env.example .env.production

# 编辑开发环境配置
vim .env.development
```

### 4. 启动开发服务器
```bash
pnpm dev
```

访问 http://localhost:3000 查看应用

## 环境变量配置

### .env.development
```bash
# 应用配置
VITE_APP_TITLE=CloudVPS管理后台
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:8080
VITE_API_TIMEOUT=10000

# 认证配置
VITE_JWT_TOKEN_KEY=cloudvps_admin_token
VITE_JWT_REFRESH_TOKEN_KEY=cloudvps_admin_refresh_token

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_PWA=false
VITE_ENABLE_DEVTOOLS=true

# 上传配置
VITE_UPLOAD_MAX_SIZE=10485760
VITE_UPLOAD_ALLOWED_TYPES=image/*,application/pdf
```

### .env.production
```bash
# 应用配置
VITE_APP_TITLE=CloudVPS管理后台
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production

# API配置
VITE_API_BASE_URL=https://api.cloudvps.com
VITE_API_TIMEOUT=30000

# 认证配置
VITE_JWT_TOKEN_KEY=cloudvps_admin_token
VITE_JWT_REFRESH_TOKEN_KEY=cloudvps_admin_refresh_token

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_PWA=true
VITE_ENABLE_DEVTOOLS=false

# 上传配置
VITE_UPLOAD_MAX_SIZE=10485760
VITE_UPLOAD_ALLOWED_TYPES=image/*,application/pdf
```

## 开发规范

### 1. 代码组织规范

#### 文件命名
- **组件文件**: PascalCase (如: `UserList.vue`)
- **工具文件**: camelCase (如: `formatDate.ts`)
- **常量文件**: UPPER_SNAKE_CASE (如: `API_ENDPOINTS.ts`)
- **类型文件**: camelCase (如: `userTypes.ts`)

#### 目录结构
- 按功能模块组织代码
- 公共代码放在对应的公共目录
- 业务代码按页面/模块分组

### 2. Vue 组件开发规范

#### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from './types'

// 2. 定义Props
interface Props {
  title: string
  data?: any[]
}
const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

// 3. 定义Emits
interface Emits {
  (e: 'update', value: any): void
  (e: 'delete', id: number): void
}
const emit = defineEmits<Emits>()

// 4. 响应式数据
const loading = ref(false)
const list = ref<any[]>([])

// 5. 计算属性
const filteredList = computed(() => {
  return list.value.filter(item => item.active)
})

// 6. 方法定义
const handleUpdate = (value: any) => {
  emit('update', value)
}

// 7. 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 8. 暴露给父组件的方法
defineExpose({
  refresh: () => {
    // 刷新逻辑
  }
})
</script>

<style scoped lang="less">
/* 组件样式 */
</style>
```

#### 组件命名规范
- 组件名使用 PascalCase
- 文件名与组件名保持一致
- 避免与HTML标签冲突

### 3. TypeScript 开发规范

#### 类型定义
```typescript
// 基础类型定义
export interface User {
  id: number
  username: string
  email: string
  status: UserStatus
  createdAt: string
  updatedAt: string
}

// 枚举定义
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED'
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

// 分页类型
export interface PageResponse<T = any> {
  list: T[]
  total: number
  page: number
  size: number
}

// 请求参数类型
export interface UserQueryParams {
  page?: number
  size?: number
  username?: string
  status?: UserStatus
  startDate?: string
  endDate?: string
}
```

#### 类型导入导出
```typescript
// 类型导出
export type { User, UserStatus, UserQueryParams }

// 类型导入
import type { User, UserStatus } from '@/types/user'
```

### 4. API 开发规范

#### API 模块结构
```typescript
// src/api/modules/user.ts
import { request } from '@/utils/request'
import type { User, UserQueryParams, PageResponse } from '@/types/user'

export const userApi = {
  // 获取用户列表
  getUsers: (params: UserQueryParams) => 
    request.get<PageResponse<User>>('/system/users', { params }),
  
  // 获取用户详情
  getUserById: (id: number) => 
    request.get<User>(`/system/users/${id}`),
  
  // 创建用户
  createUser: (data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) => 
    request.post<User>('/system/users', data),
  
  // 更新用户
  updateUser: (id: number, data: Partial<User>) => 
    request.put<User>(`/system/users/${id}`, data),
  
  // 删除用户
  deleteUser: (id: number) => 
    request.delete(`/system/users/${id}`),
  
  // 批量删除用户
  batchDeleteUsers: (ids: number[]) => 
    request.delete('/system/users/batch', { data: { ids } })
}
```

### 5. 状态管理规范

#### Pinia Store 结构
```typescript
// src/stores/modules/user.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApi } from '@/api/modules/user'
import type { User } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const userList = ref<User[]>([])
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!currentUser.value)
  const userCount = computed(() => userList.value.length)

  // 方法
  const setCurrentUser = (user: User | null) => {
    currentUser.value = user
  }

  const fetchUserList = async () => {
    loading.value = true
    try {
      const response = await userApi.getUsers({})
      userList.value = response.data.list
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    currentUser.value = null
    // 清理其他状态
  }

  return {
    // 状态
    currentUser,
    userList,
    loading,
    // 计算属性
    isLoggedIn,
    userCount,
    // 方法
    setCurrentUser,
    fetchUserList,
    logout
  }
}, {
  persist: {
    key: 'user-store',
    paths: ['currentUser']
  }
})
```

### 6. 路由开发规范

#### 路由定义
```typescript
// src/router/routes/system.ts
import type { RouteRecordRaw } from 'vue-router'

export const systemRoutes: RouteRecordRaw[] = [
  {
    path: '/system',
    name: 'System',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: {
      title: '系统管理',
      icon: 'setting',
      requiresAuth: true,
      permissions: ['SYSTEM_VIEW']
    },
    children: [
      {
        path: 'users',
        name: 'SystemUsers',
        component: () => import('@/pages/system/users/Index.vue'),
        meta: {
          title: '用户管理',
          permissions: ['USER_VIEW']
        }
      },
      {
        path: 'roles',
        name: 'SystemRoles',
        component: () => import('@/pages/system/roles/Index.vue'),
        meta: {
          title: '角色管理',
          permissions: ['ROLE_VIEW']
        }
      }
    ]
  }
]
```

#### 路由守卫
```typescript
// src/router/guards.ts
import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'
import { usePermissionStore } from '@/stores/modules/permission'

export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    const permissionStore = usePermissionStore()

    // 检查认证
    if (to.meta.requiresAuth && !authStore.isLoggedIn) {
      next('/login')
      return
    }

    // 检查权限
    if (to.meta.permissions) {
      const hasPermission = permissionStore.hasPermissions(to.meta.permissions)
      if (!hasPermission) {
        next('/403')
        return
      }
    }

    next()
  })

  // 全局后置守卫
  router.afterEach((to) => {
    // 设置页面标题
    document.title = to.meta.title ? `${to.meta.title} - CloudVPS` : 'CloudVPS'
  })
}
```

## 组件开发指南

### 1. 公共组件开发

#### 数据表格组件
```vue
<!-- src/components/common/DataTable/index.vue -->
<template>
  <div class="data-table">
    <t-table
      :data="data"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      row-key="id"
      @page-change="handlePageChange"
      @select-change="handleSelectChange"
    >
      <template #operation="{ row }">
        <t-space>
          <t-button
            v-if="showEdit"
            theme="primary"
            variant="text"
            @click="handleEdit(row)"
          >
            编辑
          </t-button>
          <t-button
            v-if="showDelete"
            theme="danger"
            variant="text"
            @click="handleDelete(row)"
          >
            删除
          </t-button>
        </t-space>
      </template>
    </t-table>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TableProps, PaginationProps } from 'tdesign-vue-next'

interface Props {
  data: any[]
  columns: TableProps['columns']
  loading?: boolean
  showEdit?: boolean
  showDelete?: boolean
  total?: number
  page?: number
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showEdit: true,
  showDelete: true,
  total: 0,
  page: 1,
  pageSize: 10
})

interface Emits {
  (e: 'edit', row: any): void
  (e: 'delete', row: any): void
  (e: 'page-change', page: number, pageSize: number): void
  (e: 'select-change', selectedRows: any[]): void
}

const emit = defineEmits<Emits>()

const pagination = computed<PaginationProps>(() => ({
  current: props.page,
  pageSize: props.pageSize,
  total: props.total,
  showJumper: true,
  showSizer: true
}))

const handleEdit = (row: any) => {
  emit('edit', row)
}

const handleDelete = (row: any) => {
  emit('delete', row)
}

const handlePageChange = (pageInfo: any) => {
  emit('page-change', pageInfo.current, pageInfo.pageSize)
}

const handleSelectChange = (selectedRows: any[]) => {
  emit('select-change', selectedRows)
}
</script>
```

### 2. 业务组件开发

#### 用户选择器组件
```vue
<!-- src/components/business/UserSelector/index.vue -->
<template>
  <t-select
    v-model="selectedValue"
    :options="userOptions"
    :loading="loading"
    filterable
    remote
    :remote-method="handleSearch"
    placeholder="请选择用户"
    clearable
  />
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { userApi } from '@/api/modules/user'
import type { User } from '@/types/user'

interface Props {
  modelValue?: number | number[]
  multiple?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false
})

interface Emits {
  (e: 'update:modelValue', value: number | number[]): void
}

const emit = defineEmits<Emits>()

const selectedValue = ref(props.modelValue)
const userOptions = ref<Array<{ label: string; value: number }>>([])
const loading = ref(false)

watch(selectedValue, (newValue) => {
  emit('update:modelValue', newValue)
})

watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue
})

const handleSearch = async (keyword: string) => {
  if (!keyword) return
  
  loading.value = true
  try {
    const response = await userApi.getUsers({ username: keyword })
    userOptions.value = response.data.list.map((user: User) => ({
      label: `${user.username} (${user.email})`,
      value: user.id
    }))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 初始化加载用户列表
  handleSearch('')
})
</script>
```

## 工具函数开发

### 1. 请求工具
```typescript
// src/utils/request.ts
import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { MessagePlugin } from 'tdesign-vue-next'
import { useAuthStore } from '@/stores/modules/auth'
import router from '@/router'

// 创建axios实例
const instance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: Number(import.meta.env.VITE_API_TIMEOUT) || 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    const token = authStore.token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, message, data } = response.data
    
    if (code === 200) {
      return { ...response, data }
    } else {
      MessagePlugin.error(message || '请求失败')
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    const { response } = error
    
    if (response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      router.push('/login')
      MessagePlugin.error('登录已过期，请重新登录')
    } else if (response?.status === 403) {
      MessagePlugin.error('没有权限访问该资源')
    } else if (response?.status >= 500) {
      MessagePlugin.error('服务器错误，请稍后重试')
    } else {
      MessagePlugin.error(response?.data?.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 封装请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) =>
    instance.get<T>(url, config),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    instance.post<T>(url, data, config),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    instance.put<T>(url, data, config),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig) =>
    instance.delete<T>(url, config)
}

export default instance
```

### 2. 权限工具
```typescript
// src/utils/permission.ts
import { usePermissionStore } from '@/stores/modules/permission'

/**
 * 检查是否有指定权限
 */
export function hasPermission(permission: string | string[]): boolean {
  const permissionStore = usePermissionStore()
  return permissionStore.hasPermissions(permission)
}

/**
 * 检查是否有任一权限
 */
export function hasAnyPermission(permissions: string[]): boolean {
  const permissionStore = usePermissionStore()
  return permissions.some(permission => permissionStore.hasPermissions(permission))
}

/**
 * 检查是否为管理员
 */
export function isAdmin(): boolean {
  const permissionStore = usePermissionStore()
  return permissionStore.isAdmin
}
```

### 3. 格式化工具
```typescript
// src/utils/format.ts
import dayjs from 'dayjs'

/**
 * 格式化日期
 */
export function formatDate(date: string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
  return dayjs(date).format(format)
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化金额
 */
export function formatMoney(amount: number, currency = '¥'): string {
  return `${currency}${amount.toFixed(2)}`
}
```

## 组合式函数开发

### 1. 表格操作Hook
```typescript
// src/composables/useTable.ts
import { ref, reactive, computed } from 'vue'
import type { Ref } from 'vue'

interface TableOptions<T = any> {
  api: (params: any) => Promise<{ data: { list: T[]; total: number } }>
  defaultParams?: Record<string, any>
  immediate?: boolean
}

export function useTable<T = any>(options: TableOptions<T>) {
  const { api, defaultParams = {}, immediate = true } = options

  const loading = ref(false)
  const data = ref<T[]>([]) as Ref<T[]>
  const total = ref(0)
  const selectedRows = ref<T[]>([])

  const pagination = reactive({
    current: 1,
    pageSize: 10
  })

  const queryParams = reactive({
    ...defaultParams
  })

  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        page: pagination.current,
        size: pagination.pageSize,
        ...queryParams
      }

      const response = await api(params)
      data.value = response.data.list
      total.value = response.data.total
    } finally {
      loading.value = false
    }
  }

  const handlePageChange = (current: number, pageSize: number) => {
    pagination.current = current
    pagination.pageSize = pageSize
    fetchData()
  }

  const handleSearch = (params: Record<string, any>) => {
    Object.assign(queryParams, params)
    pagination.current = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(queryParams, defaultParams)
    pagination.current = 1
    fetchData()
  }

  const handleSelectChange = (rows: T[]) => {
    selectedRows.value = rows
  }

  const refresh = () => {
    fetchData()
  }

  if (immediate) {
    fetchData()
  }

  return {
    loading: readonly(loading),
    data: readonly(data),
    total: readonly(total),
    selectedRows: readonly(selectedRows),
    pagination: readonly(pagination),
    queryParams,
    fetchData,
    handlePageChange,
    handleSearch,
    handleReset,
    handleSelectChange,
    refresh
  }
}
```

### 2. 表单操作Hook
```typescript
// src/composables/useForm.ts
import { ref, reactive } from 'vue'
import type { FormInstanceFunctions } from 'tdesign-vue-next'

interface FormOptions<T = any> {
  initialValues?: Partial<T>
  onSubmit?: (values: T) => Promise<void>
  onReset?: () => void
}

export function useForm<T = Record<string, any>>(options: FormOptions<T> = {}) {
  const { initialValues = {}, onSubmit, onReset } = options

  const formRef = ref<FormInstanceFunctions>()
  const loading = ref(false)
  const formData = reactive({ ...initialValues }) as T

  const handleSubmit = async () => {
    if (!formRef.value) return

    const validateResult = await formRef.value.validate()
    if (validateResult !== true) return

    if (onSubmit) {
      loading.value = true
      try {
        await onSubmit(formData)
      } finally {
        loading.value = false
      }
    }
  }

  const handleReset = () => {
    if (formRef.value) {
      formRef.value.reset()
    }
    Object.assign(formData, initialValues)
    onReset?.()
  }

  const setFieldValue = (field: keyof T, value: any) => {
    ;(formData as any)[field] = value
  }

  const setFieldsValue = (values: Partial<T>) => {
    Object.assign(formData, values)
  }

  const validateField = async (field: keyof T) => {
    if (!formRef.value) return
    return formRef.value.validate({ fields: [field as string] })
  }

  return {
    formRef,
    formData,
    loading: readonly(loading),
    handleSubmit,
    handleReset,
    setFieldValue,
    setFieldsValue,
    validateField
  }
}
```

## 测试指南

### 1. 单元测试
```typescript
// tests/unit/utils/format.test.ts
import { describe, it, expect } from 'vitest'
import { formatDate, formatFileSize, formatMoney } from '@/utils/format'

describe('format utils', () => {
  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = '2024-01-01T12:00:00Z'
      const result = formatDate(date, 'YYYY-MM-DD')
      expect(result).toBe('2024-01-01')
    })
  })

  describe('formatFileSize', () => {
    it('should format file size correctly', () => {
      expect(formatFileSize(0)).toBe('0 B')
      expect(formatFileSize(1024)).toBe('1 KB')
      expect(formatFileSize(1048576)).toBe('1 MB')
    })
  })

  describe('formatMoney', () => {
    it('should format money correctly', () => {
      expect(formatMoney(100)).toBe('¥100.00')
      expect(formatMoney(100, '$')).toBe('$100.00')
    })
  })
})
```

### 2. 组件测试
```typescript
// tests/unit/components/DataTable.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import DataTable from '@/components/common/DataTable/index.vue'

describe('DataTable', () => {
  const mockData = [
    { id: 1, name: 'Test 1' },
    { id: 2, name: 'Test 2' }
  ]

  const mockColumns = [
    { colKey: 'id', title: 'ID' },
    { colKey: 'name', title: 'Name' }
  ]

  it('should render correctly', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns
      }
    })

    expect(wrapper.exists()).toBe(true)
  })

  it('should emit edit event', async () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        showEdit: true
      }
    })

    const editButton = wrapper.find('[data-testid="edit-button"]')
    await editButton.trigger('click')

    expect(wrapper.emitted('edit')).toBeTruthy()
  })
})
```

## 构建和部署

### 1. 构建命令
```bash
# 开发环境构建
pnpm build:dev

# 生产环境构建
pnpm build

# 预览构建结果
pnpm preview
```

### 2. 代码检查
```bash
# ESLint检查
pnpm lint

# ESLint修复
pnpm lint:fix

# 类型检查
pnpm type-check
```

### 3. 测试命令
```bash
# 运行单元测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行E2E测试
pnpm test:e2e
```

## 常见问题

### 1. 开发环境问题

**Q: 启动项目时出现端口占用错误**
A: 修改 `vite.config.ts` 中的端口配置，或使用 `pnpm dev --port 3001` 指定端口

**Q: 热更新不生效**
A: 检查文件路径是否正确，确保没有使用绝对路径导入

### 2. 构建问题

**Q: 构建时出现内存不足错误**
A: 增加 Node.js 内存限制：`NODE_OPTIONS="--max-old-space-size=4096" pnpm build`

**Q: 构建后静态资源路径错误**
A: 检查 `vite.config.ts` 中的 `base` 配置

### 3. 类型问题

**Q: TypeScript 类型错误**
A: 确保安装了正确的类型定义包，检查 `tsconfig.json` 配置

**Q: 第三方库类型缺失**
A: 安装对应的 `@types/` 包或创建类型声明文件

## 最佳实践

### 1. 性能优化
- 使用路由懒加载
- 合理使用 `v-memo` 和 `v-once`
- 避免在模板中使用复杂计算
- 使用虚拟滚动处理大量数据

### 2. 代码质量
- 遵循 ESLint 规则
- 编写单元测试
- 使用 TypeScript 严格模式
- 定期进行代码审查

### 3. 用户体验
- 添加加载状态
- 提供错误处理
- 支持键盘导航
- 确保无障碍访问

### 4. 安全性
- 验证用户输入
- 使用 HTTPS
- 实施 CSP 策略
- 定期更新依赖

## 组件开发规范

### 1. 组件命名

```typescript
// ✅ 推荐：使用PascalCase
export default defineComponent({
  name: 'UserTable'
})

// ❌ 不推荐：使用其他命名方式
export default defineComponent({
  name: 'userTable'
})
```

### 2. Props定义

```typescript
// ✅ 推荐：使用TypeScript接口
interface Props {
  visible?: boolean
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  size: 'medium',
  disabled: false
})

// ❌ 不推荐：使用运行时声明
const props = defineProps({
  visible: Boolean,
  size: String,
  disabled: Boolean
})
```

### 3. 事件定义

```typescript
// ✅ 推荐：明确的事件类型
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'change', value: any): void
  (e: 'submit', data: FormData): void
}>()

// ❌ 不推荐：无类型定义
const emit = defineEmits(['update:visible', 'change', 'submit'])
```

## 状态管理指南 (Pinia)

### Store结构

```typescript
// src/stores/modules/example.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useExampleStore = defineStore('example', () => {
  // 1. 状态定义
  const data = ref<any[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 2. 计算属性
  const filteredData = computed(() => {
    return data.value.filter(item => item.active)
  })

  // 3. 方法
  const fetchData = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await api.getData()
      data.value = response.data
    } catch (err: any) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  // 4. 重置方法
  const $reset = () => {
    data.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),

    // 计算属性
    filteredData,

    // 方法
    fetchData,
    $reset
  }
})
```

### Store使用最佳实践

```vue
<script setup lang="ts">
import { useExampleStore } from '@/stores/modules/example'

// ✅ 推荐：直接使用store
const exampleStore = useExampleStore()

// ✅ 推荐：解构响应式状态
const { data, loading } = storeToRefs(exampleStore)

// ✅ 推荐：直接调用方法
const handleRefresh = () => {
  exampleStore.fetchData()
}

// ❌ 不推荐：解构方法（会失去响应性）
const { fetchData } = exampleStore
</script>
```

## 常见错误排查

### 1. Vue Proxy错误

**错误信息**: `TypeError: 'set' on proxy: trap returned falsish`

**原因**: 尝试设置只读的computed属性

**解决方案**:
```typescript
// ❌ 错误：只读computed
const value = computed(() => store.value)

// ✅ 正确：支持setter的computed
const value = computed({
  get: () => store.value,
  set: (newValue) => store.setValue(newValue)
})
```

### 2. 图标导入错误

**错误信息**: `The requested module does not provide an export named 'SunIcon'`

**原因**: 图标名称错误

**解决方案**:
```typescript
// ❌ 错误：图标名称不存在
import { SunIcon } from 'tdesign-icons-vue-next'

// ✅ 正确：使用正确的图标名称
import { SunnyIcon } from 'tdesign-icons-vue-next'
```

### 3. API路径错误

**错误信息**: `404 Not Found`

**原因**: 前端API路径与后端不匹配

**解决方案**:
```typescript
// ❌ 错误：路径不匹配
getCurrentUser: () => request.get('/system/auth/me')

// ✅ 正确：使用正确的后端路径
getCurrentUser: () => request.get('/system/auth/user')
```

### 4. Token验证重复调用

**问题**: 页面刷新时多次调用验证API

**解决方案**:
```typescript
// 添加防重复调用机制
const isValidating = ref(false)

const validateToken = async () => {
  if (isValidating.value) {
    return true // 跳过重复调用
  }

  isValidating.value = true
  try {
    // 验证逻辑
  } finally {
    isValidating.value = false
  }
}
```

### 5. 路由守卫死循环

**问题**: 路由守卫导致无限重定向

**解决方案**:
```typescript
// ✅ 正确：检查目标路由
router.beforeEach((to, from, next) => {
  if (to.path === '/login') {
    next()
    return
  }

  // 其他逻辑
})
```

### 6. 设置面板无法关闭

**问题**: ESC键和点击空白区域无法关闭抽屉

**解决方案**:
```vue
<t-drawer
  v-model:visible="visible"
  :close-on-esc-keydown="true"
  :close-on-overlay-click="true"
>
  <!-- 内容 -->
</t-drawer>
```

## 项目总结

CloudVPS 前端B端管理系统采用现代化的技术栈和开发流程，通过规范化的开发指南确保代码质量和团队协作效率。

### 核心优势
- **类型安全**: 完整的 TypeScript 支持
- **组件化**: 基于 Vue 3 Composition API
- **模块化**: 清晰的项目结构和代码组织
- **标准化**: 统一的开发规范和工具链
- **高效率**: 完善的开发工具和自动化流程

### 持续改进
- 定期更新依赖版本
- 优化构建性能
- 完善测试覆盖率
- 改进开发体验
- 扩展功能特性

遵循本开发指南，可以确保项目的可维护性、可扩展性和团队开发的一致性。
