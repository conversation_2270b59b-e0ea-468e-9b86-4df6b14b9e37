# API架构文档

CloudVPS前端管理系统的API组织结构和使用规范。

## 目录

- [API架构概述](#api架构概述)
- [微服务模块划分](#微服务模块划分)
- [API组织结构](#api组织结构)
- [类型定义规范](#类型定义规范)
- [使用示例](#使用示例)
- [最佳实践](#最佳实践)

## API架构概述

CloudVPS采用微服务架构，前端API按照后端微服务模块进行组织，确保清晰的模块边界和高效的开发体验。

### 设计原则

- **模块化**: 按微服务划分API模块
- **类型安全**: 完整的TypeScript类型定义
- **统一规范**: 一致的命名和组织方式
- **易于维护**: 清晰的文件结构和引用关系

### 技术栈

- **HTTP客户端**: Axios
- **类型系统**: TypeScript
- **状态管理**: Pinia
- **构建工具**: Vite

## 微服务模块划分

### 1. 系统服务 (System Service)
**端口**: 8081  
**职责**: 用户管理、权限控制、系统配置

**API模块**:
- `auth.ts` - 认证相关API
- `user.ts` - 用户管理API
- `role.ts` - 角色管理API
- `permission.ts` - 权限管理API
- `dict.ts` - 字典管理API
- `config.ts` - 系统配置API

### 2. 虚拟化服务 (Virtualization Service)
**端口**: 8082  
**职责**: 虚拟机管理、节点管理、资源监控

**API模块**:
- `node.ts` - 节点管理API
- `vm.ts` - 虚拟机管理API
- `template.ts` - 模板管理API

### 3. 订单服务 (Order Service)
**端口**: 8083  
**职责**: 订单管理、产品管理、计费

**API模块**:
- `order.ts` - 订单管理API
- `product.ts` - 产品管理API

### 4. 支付服务 (Payment Service)
**端口**: 8084  
**职责**: 支付处理、账户管理、退款

**API模块**:
- `payment.ts` - 支付管理API
- `account.ts` - 账户管理API

## API组织结构

```
src/api/
├── system/                 # 系统服务
│   ├── types/
│   │   ├── common.ts      # 系统模块特有类型
│   │   ├── auth.ts        # 认证相关类型
│   │   ├── user.ts        # 用户相关类型
│   │   ├── role.ts        # 角色相关类型
│   │   ├── permission.ts  # 权限相关类型
│   │   ├── dict.ts        # 字典相关类型
│   │   └── config.ts      # 配置相关类型
│   ├── auth.ts            # 认证API
│   ├── user.ts            # 用户API
│   ├── role.ts            # 角色API
│   ├── permission.ts      # 权限API
│   ├── dict.ts            # 字典API
│   ├── config.ts          # 配置API
│   └── index.ts           # 统一导出
├── virtualization/         # 虚拟化服务
│   ├── types/
│   │   ├── common.ts      # 虚拟化模块特有类型
│   │   ├── node.ts        # 节点相关类型
│   │   └── vm.ts          # 虚拟机相关类型
│   ├── node.ts            # 节点API
│   ├── vm.ts              # 虚拟机API
│   └── index.ts           # 统一导出
├── order/                  # 订单服务
│   ├── types/
│   │   ├── common.ts      # 订单模块特有类型
│   │   ├── order.ts       # 订单相关类型
│   │   └── product.ts     # 产品相关类型
│   ├── order.ts           # 订单API
│   ├── product.ts         # 产品API
│   └── index.ts           # 统一导出
├── payment/                # 支付服务
│   ├── types/
│   │   ├── common.ts      # 支付模块特有类型
│   │   ├── payment.ts     # 支付相关类型
│   │   └── account.ts     # 账户相关类型
│   ├── payment.ts         # 支付API
│   ├── account.ts         # 账户API
│   └── index.ts           # 统一导出
└── index.ts               # 全局统一导出
```

## 类型定义规范

### 公共类型 (src/types/api.ts)

所有模块共享的基础类型定义：

```typescript
// 基础分页请求
export interface BasePageRequest {
  current?: number
  pageSize?: number
  sortField?: string
  sortOrder?: string
}

// 基础实体时间字段
export interface BaseTimeFields {
  createdTime?: string
  updatedTime?: string
}

// 基础响应实体
export interface BaseEntity extends BaseTimeFields {
  id: number
}

// 批量操作请求
export interface BatchRequest {
  ids: number[]
}

// API响应包装
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

// 分页响应
export interface PageResponse<T = any> {
  size: number
  pages: number
  total: number
  current: number
  records: T[]
}
```

### 模块特有类型

每个模块的`types/common.ts`文件定义该模块特有的枚举和类型：

```typescript
// 系统模块特有类型 (src/api/system/types/common.ts)
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  LOCKED = 'LOCKED'
}

export enum RoleStatus {
  ENABLED = 'ENABLED',
  DISABLED = 'DISABLED'
}

// 虚拟化模块特有类型 (src/api/virtualization/types/common.ts)
export enum NodeStatus {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
  MAINTENANCE = 'MAINTENANCE'
}

export interface ResourceUsage {
  used: number
  total: number
  percentage: number
}
```

### 命名规范

#### 接口命名
- **请求类型**: `{Entity}QueryRequest`, `{Entity}CreateRequest`, `{Entity}UpdateRequest`
- **响应类型**: `{Entity}Response`
- **枚举类型**: `{Entity}Status`, `{Entity}Type`

#### 示例
```typescript
// 用户相关类型
export interface UserQueryRequest extends BasePageRequest {
  username?: string
  email?: string
  status?: UserStatus
}

export interface UserCreateRequest {
  username: string
  email: string
  password: string
  realName?: string
}

export interface UserUpdateRequest {
  email?: string
  realName?: string
  status?: UserStatus
}

export interface UserResponse extends BaseEntity {
  username: string
  email: string
  realName?: string
  status: UserStatus
  lastLoginTime?: string
}
```

## 使用示例

### 统一导入方式

```typescript
// 从统一入口导入所有API
import { 
  authApi, 
  userApi, 
  nodeApi, 
  vmApi, 
  orderApi, 
  paymentApi 
} from '@/api'

// 或者从具体模块导入
import { authApi } from '@/api/system/auth'
import { nodeApi } from '@/api/virtualization/node'
```

### 类型定义使用

```typescript
// 公共类型从 @/types/api 导入
import type { BasePageRequest, ApiResponse } from '@/types/api'

// 模块特有类型从对应模块导入
import type { UserStatus } from '@/api/system/types/common'
import type { UserResponse, UserQueryRequest } from '@/api/system/types/user'

// 使用示例
const queryUsers = async (params: UserQueryRequest): Promise<UserResponse[]> => {
  const response = await userApi.getPage(params)
  return response.data.records
}
```

### API调用示例

```typescript
// 用户管理示例
import { userApi } from '@/api'
import type { UserQueryRequest, UserCreateRequest } from '@/api/system/types/user'

// 查询用户列表
const getUserList = async () => {
  const params: UserQueryRequest = {
    current: 1,
    pageSize: 10,
    username: 'admin'
  }
  
  const response = await userApi.getPage(params)
  return response.data
}

// 创建用户
const createUser = async () => {
  const userData: UserCreateRequest = {
    username: 'newuser',
    email: '<EMAIL>',
    password: 'password123',
    realName: '新用户'
  }
  
  await userApi.create(userData)
}
```

## 最佳实践

### 1. 类型安全

```typescript
// ✅ 推荐：使用类型定义
const params: UserQueryRequest = {
  current: 1,
  pageSize: 10
}

// ❌ 不推荐：使用any或无类型
const params = {
  current: 1,
  pageSize: 10
}
```

### 2. 错误处理

```typescript
// ✅ 推荐：统一错误处理
try {
  const response = await userApi.getPage(params)
  return response.data
} catch (error: any) {
  console.error('获取用户列表失败:', error)
  throw error
}
```

### 3. 响应式数据

```typescript
// ✅ 推荐：使用响应式数据
const users = ref<UserResponse[]>([])
const loading = ref(false)

const loadUsers = async () => {
  loading.value = true
  try {
    const response = await userApi.getPage(params)
    users.value = response.data.records
  } finally {
    loading.value = false
  }
}
```

### 4. 模块化导入

```typescript
// ✅ 推荐：按需导入
import { userApi, roleApi } from '@/api'

// ❌ 不推荐：导入整个API对象
import * as api from '@/api'
```

### 5. 类型复用

```typescript
// ✅ 推荐：复用公共类型
interface CustomQueryRequest extends BasePageRequest {
  customField?: string
}

// ❌ 不推荐：重复定义相同结构
interface CustomQueryRequest {
  current?: number
  pageSize?: number
  customField?: string
}
```

---

更多API文档和使用示例请参考各模块的具体实现和测试用例。
