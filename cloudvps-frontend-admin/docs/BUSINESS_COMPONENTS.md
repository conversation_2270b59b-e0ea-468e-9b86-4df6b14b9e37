# 业务组件和工具使用指南

本文档介绍CloudVPS前端管理平台的业务组件和工具的使用方法。

## 目录结构

```
src/
├── composables/           # 组合式函数
│   ├── usePagination.ts  # 分页钩子
│   ├── useTable.ts       # 表格钩子
│   ├── useForm.ts        # 表单钩子
│   ├── useCrud.ts        # CRUD钩子
│   └── index.ts          # 导出文件
├── components/business/   # 业务组件
│   ├── DictTag/          # 字典标签组件
│   ├── DictSelect/       # 字典选择组件
│   ├── IconSelect/       # 图标选择组件
│   └── index.ts          # 导出文件
├── stores/modules/
│   └── dict.ts           # 字典存储
└── types/
    └── business.ts       # 业务类型定义
```

## 1. 字典存储 (useDictStore)

### 基本用法

```typescript
import { useDictStore } from '@/stores/modules/dict'

const dictStore = useDictStore()

// 获取字典数据
const userStatusOptions = dictStore.getDictData('user_status')

// 获取字典标签
const statusLabel = dictStore.getDictLabel('user_status', 'ACTIVE')

// 获取字典颜色
const statusColor = dictStore.getDictColor('user_status', 'ACTIVE')

// 手动获取字典数据
await dictStore.fetchDictData('user_status')

// 预加载常用字典
await dictStore.preloadCommonDicts()
```

### 特性

- 自动缓存机制（5分钟过期）
- 避免重复请求
- 支持批量获取
- 自动颜色映射
- 错误处理

## 2. 业务组件

### 2.1 DictTag - 字典标签组件

用于显示字典值对应的标签，支持自动颜色映射。

```vue
<template>
  <!-- 基本用法 -->
  <DictTag dict-type="user_status" :value="user.status" />
  
  <!-- 自定义主题 -->
  <DictTag 
    dict-type="user_status" 
    :value="user.status"
    theme="primary"
    variant="outline"
    size="large"
  />
  
  <!-- 可关闭标签 -->
  <DictTag 
    dict-type="user_status" 
    :value="user.status"
    closable
    @close="handleTagClose"
  />
</template>

<script setup lang="ts">
import { DictTag } from '@/components/business'

const handleTagClose = (value: string | number) => {
  console.log('标签关闭:', value)
}
</script>
```

### 2.2 DictSelect - 字典选择组件

基于字典数据的下拉选择组件。

```vue
<template>
  <!-- 单选 -->
  <DictSelect 
    v-model="form.status"
    dict-type="user_status"
    placeholder="请选择状态"
  />
  
  <!-- 多选 -->
  <DictSelect 
    v-model="form.roles"
    dict-type="user_role"
    multiple
    placeholder="请选择角色"
  />
  
  <!-- 包含无效选项 -->
  <DictSelect 
    v-model="form.status"
    dict-type="user_status"
    include-inactive
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DictSelect } from '@/components/business'

const form = ref({
  status: '',
  roles: []
})
</script>
```

### 2.3 IconSelect - 图标选择组件

支持多种图标类型的选择组件。

```vue
<template>
  <IconSelect 
    v-model="form.icon"
    v-model:icon-type="form.iconType"
    placeholder="请选择图标"
    upload-action="/api/upload/icon"
    @change="handleIconChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { IconSelect } from '@/components/business'
import type { IconType } from '@/types/business'

const form = ref({
  icon: '',
  iconType: 'tdesign' as IconType
})

const handleIconChange = (value: string | undefined, type: IconType) => {
  console.log('图标变化:', value, type)
}
</script>
```

## 3. CRUD钩子

### 3.1 useCrud - 完整CRUD解决方案

```vue
<template>
  <div class="user-management">
    <!-- 搜索表单 -->
    <t-card class="search-card">
      <t-form 
        :data="queryParams"
        layout="inline"
        @submit="handleSearch"
        @reset="handleReset"
      >
        <t-form-item label="用户名" name="username">
          <t-input v-model="queryParams.username" placeholder="请输入用户名" />
        </t-form-item>
        <t-form-item label="状态" name="status">
          <DictSelect v-model="queryParams.status" dict-type="user_status" />
        </t-form-item>
        <t-form-item>
          <t-button theme="primary" type="submit">搜索</t-button>
          <t-button variant="outline" type="reset">重置</t-button>
        </t-form-item>
      </t-form>
    </t-card>

    <!-- 工具栏 -->
    <t-card class="table-card">
      <div class="table-toolbar">
        <div class="toolbar-left">
          <t-button 
            v-if="hasPermission('create')"
            theme="primary" 
            @click="handleCreate"
          >
            <t-icon name="add" />
            新增用户
          </t-button>
          <t-button 
            v-if="hasPermission('delete')"
            theme="danger"
            variant="outline"
            :disabled="!hasSelection"
            @click="handleBatchDelete"
          >
            <t-icon name="delete" />
            批量删除
          </t-button>
        </div>
        <div class="toolbar-right">
          <t-button variant="outline" @click="handleRefresh">
            <t-icon name="refresh" />
            刷新
          </t-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <t-table
        :data="tableData"
        :columns="columns"
        :loading="loading.list"
        :pagination="paginationConfig"
        :selected-row-keys="selectedRowKeys"
        row-key="id"
        @select-change="handleSelectionChange"
        @page-change="handlePageChange"
      />
    </t-card>

    <!-- 表单弹窗 -->
    <t-dialog
      v-model:visible="formVisible"
      :header="formTitle"
      width="600px"
      @close="closeForm"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="formRules"
        layout="vertical"
      >
        <t-form-item label="用户名" name="username">
          <t-input v-model="formData.username" placeholder="请输入用户名" />
        </t-form-item>
        <t-form-item label="邮箱" name="email">
          <t-input v-model="formData.email" placeholder="请输入邮箱" />
        </t-form-item>
        <t-form-item label="状态" name="status">
          <DictSelect v-model="formData.status" dict-type="user_status" />
        </t-form-item>
      </t-form>
      
      <template #footer>
        <t-button variant="outline" @click="closeForm">取消</t-button>
        <t-button 
          theme="primary" 
          :loading="isSubmitting"
          @click="handleSubmit"
        >
          {{ submitButtonText }}
        </t-button>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useCrud } from '@/composables'
import { DictSelect } from '@/components/business'
import { userApi } from '@/api/system/user'
import type { UserResponse } from '@/api/system/types/user'

// 表格列配置
const columns = [
  { colKey: 'id', title: 'ID', width: 80 },
  { colKey: 'username', title: '用户名', width: 120 },
  { colKey: 'email', title: '邮箱', width: 200 },
  { 
    colKey: 'status', 
    title: '状态', 
    width: 100,
    cell: (h, { row }) => h(DictTag, { 
      dictType: 'user_status', 
      value: row.status 
    })
  },
  { colKey: 'createdTime', title: '创建时间', width: 180 },
  {
    colKey: 'operation',
    title: '操作',
    width: 150,
    cell: (h, { row }) => [
      h(t-button, {
        size: 'small',
        variant: 'text',
        onClick: () => handleUpdate(row)
      }, '编辑'),
      h(t-button, {
        size: 'small',
        variant: 'text',
        theme: 'danger',
        onClick: () => handleDelete(row)
      }, '删除')
    ]
  }
]

// 表单验证规则
const formRules = {
  username: [{ required: true, message: '请输入用户名' }],
  email: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式' }
  ],
  status: [{ required: true, message: '请选择状态' }]
}

// 使用CRUD钩子
const {
  // 状态
  tableData,
  loading,
  selectedRowKeys,
  hasSelection,
  formVisible,
  formData,
  formRef,
  formTitle,
  submitButtonText,
  isSubmitting,
  queryParams,
  paginationConfig,
  
  // 方法
  handleCreate,
  handleUpdate,
  handleDelete,
  handleBatchDelete,
  handleSubmit,
  handleSearch,
  handleReset,
  handleRefresh,
  handleSelectionChange,
  handlePageChange,
  closeForm,
  hasPermission
} = useCrud<UserResponse>({
  title: '用户管理',
  api: {
    list: userApi.getPage,
    create: userApi.create,
    update: userApi.update,
    delete: userApi.delete,
    batchDelete: userApi.batchDelete
  },
  table: {
    columns,
    rowKey: 'id'
  },
  permissions: {
    view: 'USER_VIEW',
    create: 'USER_CREATE',
    update: 'USER_UPDATE',
    delete: 'USER_DELETE'
  }
})
</script>
```

### 3.2 单独使用钩子

```typescript
// 只使用表格钩子
import { useTable } from '@/composables'

const {
  tableData,
  loading,
  selectedRowKeys,
  fetchTableData,
  handleSearch,
  handleDelete
} = useTable({
  api: {
    list: userApi.getPage,
    delete: userApi.delete
  },
  columns: tableColumns
})

// 只使用表单钩子
import { useForm } from '@/composables'

const {
  formVisible,
  formData,
  formMode,
  openCreateForm,
  openUpdateForm,
  submitForm
} = useForm({
  api: {
    create: userApi.create,
    update: userApi.update
  },
  defaultValues: {
    status: 'ACTIVE'
  }
})

// 只使用分页钩子
import { usePagination } from '@/composables'

const {
  pagination,
  paginationConfig,
  handlePageChange,
  resetPagination
} = usePagination({
  defaultPageSize: 20
})
```

## 4. 类型定义

所有相关的TypeScript类型定义都在 `@/types/business.ts` 中，包括：

- `CrudPageConfig` - CRUD页面配置
- `TableConfig` - 表格配置
- `FormConfig` - 表单配置
- `DictItem` - 字典项
- `IconConfig` - 图标配置
- 等等...

## 5. 最佳实践

### 5.1 字典数据管理

1. 在应用启动时预加载常用字典：

```typescript
// main.ts
import { useDictStore } from '@/stores/modules/dict'

const app = createApp(App)

// 预加载字典
const dictStore = useDictStore()
dictStore.preloadCommonDicts()

app.mount('#app')
```

2. 在组件中按需加载字典：

```typescript
// 组件中
onMounted(() => {
  dictStore.fetchDictData('custom_dict_type')
})
```

### 5.2 CRUD页面开发

1. 使用 `useCrud` 钩子可以快速构建标准的CRUD页面
2. 通过配置对象定义页面行为，减少重复代码
3. 支持权限控制和自定义操作

### 5.3 组件复用

1. 业务组件都支持完整的TypeScript类型定义
2. 组件API设计遵循TDesign规范
3. 支持主题定制和样式覆盖

## 6. 注意事项

1. 确保后端API返回的数据格式符合预期
2. 字典数据的缓存机制需要考虑数据一致性
3. 权限检查需要集成实际的权限系统
4. 图标上传功能需要配置正确的上传接口

## 7. 扩展开发

如需扩展功能，可以：

1. 在现有钩子基础上添加新的方法
2. 创建新的业务组件
3. 扩展字典存储的功能
4. 添加新的类型定义

这些基础工具为CloudVPS平台的业务页面开发提供了强大的支持，可以显著提高开发效率和代码质量。
