{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "tests/**/*"], "exclude": ["src/**/__tests__/*", "dist", "node_modules"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["src/*"], "~/*": ["src/*"], "@components/*": ["src/components/*"], "@pages/*": ["src/pages/*"], "@utils/*": ["src/utils/*"], "@api/*": ["src/api/*"], "@stores/*": ["src/stores/*"], "@types/*": ["src/types/*"], "@assets/*": ["src/assets/*"]}, "types": ["vite/client", "node", "vue", "unplugin-vue-components/client", "unplugin-auto-import/client"]}}