{"name": "cloudvps-frontend-admin", "version": "1.0.0", "description": "CloudVPS前端B端管理系统", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:dev": "vue-tsc && vite build --mode development", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "test": "vitest", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "prepare": "husky install"}, "dependencies": {"axios": "^1.6.0", "axios-retry": "^4.0.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.11.2", "tdesign-icons-vue-next": "^0.2.0", "tdesign-vue-next": "^1.9.0", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.10", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "husky": "^8.0.3", "jsdom": "^23.0.0", "less": "^4.2.0", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "typescript": "~5.3.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.0", "vitest": "^1.0.0", "vue-tsc": "^1.8.0"}, "lint-staged": {"*.{vue,js,jsx,cjs,mjs,ts,tsx,cts,mts}": ["eslint --fix", "prettier --write"], "*.{css,less,scss,html,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}