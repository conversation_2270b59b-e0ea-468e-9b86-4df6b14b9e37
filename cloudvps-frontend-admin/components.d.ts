/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Breadcrumb: typeof import('./src/components/layout/Breadcrumb/index.vue')['default']
    CardGridLayout: typeof import('./src/components/layout/CardGridLayout/index.vue')['default']
    ContentComponent: typeof import('./src/components/layout/components/ContentComponent.vue')['default']
    DataTable: typeof import('./src/components/business/DataTable/index.vue')['default']
    DetailPageLayout: typeof import('./src/components/layout/DetailPageLayout/index.vue')['default']
    DictSelect: typeof import('./src/components/business/DictSelect/index.vue')['default']
    DictTag: typeof import('./src/components/business/DictTag/index.vue')['default']
    Footer: typeof import('./src/components/layout/Footer/index.vue')['default']
    FormTableLayout: typeof import('./src/components/layout/FormTableLayout/index.vue')['default']
    Header: typeof import('./src/components/layout/Header/index.vue')['default']
    HeaderComponent: typeof import('./src/components/layout/components/HeaderComponent.vue')['default']
    HeaderMenuComponent: typeof import('./src/components/layout/components/HeaderMenuComponent.vue')['default']
    Icon: typeof import('./src/components/common/Icon/index.vue')['default']
    IconSelect: typeof import('./src/components/business/IconSelect/index.vue')['default']
    LayoutPreview: typeof import('./src/components/common/SettingsPanel/components/LayoutPreview.vue')['default']
    ListItem: typeof import('./src/components/business/ListItem/index.vue')['default']
    Logo: typeof import('./src/components/common/Logo/index.vue')['default']
    MasterDetailLayout: typeof import('./src/components/layout/MasterDetailLayout/index.vue')['default']
    Navigation: typeof import('./src/components/layout/Navigation/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchBox: typeof import('./src/components/layout/components/SearchBox.vue')['default']
    SearchForm: typeof import('./src/components/business/SearchForm/index.vue')['default']
    Settings: typeof import('./src/components/layout/Settings/index.vue')['default']
    SettingsButton: typeof import('./src/components/layout/components/SettingsButton.vue')['default']
    SettingsPanel: typeof import('./src/components/common/SettingsPanel/index.vue')['default']
    Sidebar: typeof import('./src/components/layout/Sidebar/index.vue')['default']
    SidebarComponent: typeof import('./src/components/layout/components/SidebarComponent.vue')['default']
    SidebarFirst: typeof import('./src/components/layout/Sidebar/SidebarFirst.vue')['default']
    SidebarSecond: typeof import('./src/components/layout/Sidebar/SidebarSecond.vue')['default']
    SideMenuComponent: typeof import('./src/components/layout/components/SideMenuComponent.vue')['default']
    SimpleFormTableLayout: typeof import('./src/components/layout/SimpleFormTableLayout.vue')['default']
    SplitLayout: typeof import('./src/components/business/SplitLayout/index.vue')['default']
    TAlert: typeof import('tdesign-vue-next')['Alert']
    TAside: typeof import('tdesign-vue-next')['Aside']
    TAvatar: typeof import('tdesign-vue-next')['Avatar']
    TBadge: typeof import('tdesign-vue-next')['Badge']
    TBreadcrumb: typeof import('tdesign-vue-next')['Breadcrumb']
    TBreadcrumbItem: typeof import('tdesign-vue-next')['BreadcrumbItem']
    TButton: typeof import('tdesign-vue-next')['Button']
    TCard: typeof import('tdesign-vue-next')['Card']
    TCheckbox: typeof import('tdesign-vue-next')['Checkbox']
    TCheckboxGroup: typeof import('tdesign-vue-next')['CheckboxGroup']
    TCol: typeof import('tdesign-vue-next')['Col']
    TCollapseTransition: typeof import('tdesign-vue-next')['CollapseTransition']
    TColorPicker: typeof import('tdesign-vue-next')['ColorPicker']
    TContent: typeof import('tdesign-vue-next')['Content']
    TDatePicker: typeof import('tdesign-vue-next')['DatePicker']
    TDateRangePicker: typeof import('tdesign-vue-next')['DateRangePicker']
    TDescriptions: typeof import('tdesign-vue-next')['Descriptions']
    TDescriptionsItem: typeof import('tdesign-vue-next')['DescriptionsItem']
    TDialog: typeof import('tdesign-vue-next')['Dialog']
    TDivider: typeof import('tdesign-vue-next')['Divider']
    TDrawer: typeof import('tdesign-vue-next')['Drawer']
    TDropdown: typeof import('tdesign-vue-next')['Dropdown']
    TEmpty: typeof import('tdesign-vue-next')['Empty']
    TFooter: typeof import('tdesign-vue-next')['Footer']
    TForm: typeof import('tdesign-vue-next')['Form']
    TFormItem: typeof import('tdesign-vue-next')['FormItem']
    THeader: typeof import('tdesign-vue-next')['Header']
    TIcon: typeof import('tdesign-vue-next')['Icon']
    TInput: typeof import('tdesign-vue-next')['Input']
    TInputNumber: typeof import('tdesign-vue-next')['InputNumber']
    TLayout: typeof import('tdesign-vue-next')['Layout']
    TLink: typeof import('tdesign-vue-next')['Link']
    TList: typeof import('tdesign-vue-next')['List']
    TListItem: typeof import('tdesign-vue-next')['ListItem']
    TListItemMeta: typeof import('tdesign-vue-next')['ListItemMeta']
    TLoading: typeof import('tdesign-vue-next')['Loading']
    TMenu: typeof import('tdesign-vue-next')['Menu']
    TMenuItem: typeof import('tdesign-vue-next')['MenuItem']
    TOption: typeof import('tdesign-vue-next')['Option']
    TPagination: typeof import('tdesign-vue-next')['Pagination']
    TRadio: typeof import('tdesign-vue-next')['Radio']
    TRadioButton: typeof import('tdesign-vue-next')['RadioButton']
    TRadioGroup: typeof import('tdesign-vue-next')['RadioGroup']
    TRow: typeof import('tdesign-vue-next')['Row']
    TSelect: typeof import('tdesign-vue-next')['Select']
    TSpace: typeof import('tdesign-vue-next')['Space']
    TSubmenu: typeof import('tdesign-vue-next')['Submenu']
    TSwitch: typeof import('tdesign-vue-next')['Switch']
    TTable: typeof import('tdesign-vue-next')['Table']
    TTabPanel: typeof import('tdesign-vue-next')['TabPanel']
    TTabs: typeof import('tdesign-vue-next')['Tabs']
    TTag: typeof import('tdesign-vue-next')['Tag']
    TTextarea: typeof import('tdesign-vue-next')['Textarea']
    TTimeline: typeof import('tdesign-vue-next')['Timeline']
    TTimelineItem: typeof import('tdesign-vue-next')['TimelineItem']
    TTree: typeof import('tdesign-vue-next')['Tree']
    TTreeSelect: typeof import('tdesign-vue-next')['TreeSelect']
    TTypographyTitle: typeof import('tdesign-vue-next')['TypographyTitle']
    TUpload: typeof import('tdesign-vue-next')['Upload']
    UserDropdown: typeof import('./src/components/layout/components/UserDropdown.vue')['default']
  }
}
