import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { TDesignResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [
      vue(),
      
      // 自动导入Vue API
      AutoImport({
        imports: [
          'vue',
          'vue-router',
          'pinia',
          {
            'tdesign-vue-next': [
              'MessagePlugin',
              'DialogPlugin',
              'NotifyPlugin',
              'LoadingPlugin'
            ]
          }
        ],
        dts: true,
        eslintrc: {
          enabled: true
        }
      }),
      
      // 自动导入组件
      Components({
        resolvers: [
          TDesignResolver({
            library: 'vue-next'
          })
        ],
        dts: true
      }),
      
      // PWA支持 (暂时禁用)
      // VitePWA({
      //   registerType: 'autoUpdate',
      //   workbox: {
      //     globPatterns: ['**/*.{js,css,html,ico,png,svg}']
      //   }
      // })
    ],
    
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '~': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@pages': resolve(__dirname, 'src/pages'),
        '@utils': resolve(__dirname, 'src/utils'),
        '@api': resolve(__dirname, 'src/api'),
        '@stores': resolve(__dirname, 'src/stores'),
        '@types': resolve(__dirname, 'src/types'),
        '@assets': resolve(__dirname, 'src/assets')
      }
    },
    
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            // TDesign主题变量自定义
            '@brand-color': '#0052d9',
            '@brand-color-light': '#266fe8',
            '@brand-color-focus': '#0034b5',
            '@brand-color-disabled': '#b3c7f7',
            '@error-color': '#e34d59',
            '@warning-color': '#ed7b2f',
            '@success-color': '#00a870'
          },
          javascriptEnabled: true
        }
      }
    },
    
    server: {
      host: '0.0.0.0',
      port: 5173,
      open: true,
      cors: true,
      headers: {
        // 配置CSP策略，允许TDesign图标资源
        'Content-Security-Policy': [
          "default-src 'self'",
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://tdesign.gtimg.com",
          "style-src 'self' 'unsafe-inline' https://tdesign.gtimg.com",
          "font-src 'self' data: https://tdesign.gtimg.com",
          "img-src 'self' data: blob: https:",
          "connect-src 'self' ws: wss: http: https:"
        ].join('; ')
      },
      proxy: {
        // 统一API网关代理 - 所有API请求都通过网关
        '/api': {
          target: env.VITE_API_BASE_URL || 'http://localhost:8080',
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(/^\/api/, ''), // 移除/api前缀
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('🔴 API Gateway Proxy Error:', err.message);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('🚀 API Request via Gateway:', req.method, req.url, '-> Gateway:8080');
              console.log('📝 Rewritten Path:', req.url.replace(/^\/api/, ''));
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log('✅ Gateway Response:', proxyRes.statusCode, req.url);
            });
          }
        }
      }
    },
    
    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: mode === 'development',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'production',
          drop_debugger: mode === 'production'
        }
      },
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          manualChunks: {
            vue: ['vue', 'vue-router', 'pinia'],
            tdesign: ['tdesign-vue-next', 'tdesign-icons-vue-next'],
            utils: ['axios', 'dayjs', 'lodash-es']
          }
        }
      },
      chunkSizeWarningLimit: 1000
    },
    
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'axios',
        'dayjs',
        'lodash-es',
        'tdesign-vue-next',
        'tdesign-icons-vue-next'
      ]
    },
    
    define: {
      __VUE_OPTIONS_API__: false,
      __VUE_PROD_DEVTOOLS__: false
    }
  }
})
