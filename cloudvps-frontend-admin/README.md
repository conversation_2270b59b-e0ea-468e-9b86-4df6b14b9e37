# CloudVPS 前端B端管理系统

## 项目概述

CloudVPS前端B端管理系统是基于Vue 3 + TypeScript + TDesign UI构建的现代化企业级管理后台，为CloudVPS云服务平台提供完整的业务管理界面。

## 技术栈

### 核心框架
- **Vue 3.4+** - 渐进式JavaScript框架，使用Composition API
- **TypeScript 5.0+** - 类型安全的JavaScript超集
- **Vite 5.0+** - 下一代前端构建工具

### UI组件库
- **TDesign Vue Next** - 腾讯企业级设计语言TDesign的Vue 3实现
- **TDesign Icons Vue Next** - TDesign图标库

### 状态管理
- **Pinia** - Vue 3官方推荐的状态管理库
- **Pinia Persist** - Pinia持久化插件

### 路由管理
- **Vue Router 4** - Vue 3官方路由管理器

### HTTP客户端
- **Axios** - Promise based HTTP client
- **Axios Retry** - 请求重试插件

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Husky** - Git hooks工具
- **Lint-staged** - 暂存文件检查

### 构建优化
- **Unplugin Vue Components** - 组件自动导入
- **Unplugin Auto Import** - API自动导入
- **Vite PWA** - PWA支持

## 项目结构

```
cloudvps-frontend-admin/
├── public/                     # 静态资源
│   ├── favicon.ico
│   └── logo.png
├── src/                        # 源代码
│   ├── api/                    # API接口定义
│   │   ├── modules/            # 按业务模块分组
│   │   │   ├── auth.ts         # 认证相关API
│   │   │   ├── system.ts       # 系统管理API
│   │   │   ├── virtualization.ts # 虚拟化API
│   │   │   ├── order.ts        # 订单管理API
│   │   │   └── payment.ts      # 支付管理API
│   │   ├── types/              # API类型定义
│   │   └── index.ts            # API统一导出
│   ├── assets/                 # 静态资源
│   │   ├── images/
│   │   ├── icons/
│   │   └── styles/
│   │       ├── index.less      # 全局样式
│   │       ├── variables.less  # 样式变量
│   │       └── mixins.less     # 样式混入
│   ├── components/             # 公共组件
│   │   ├── common/             # 通用组件
│   │   │   ├── Icon/           # 图标组件
│   │   │   ├── Logo/           # Logo组件
│   │   │   └── SettingsPanel/  # 设置面板
│   │   ├── business/           # 业务组件
│   │   │   ├── DictTag/        # 字典标签组件
│   │   │   ├── DictSelect/     # 字典选择组件
│   │   │   └── IconSelect/     # 图标选择组件
│   │   └── layout/             # 布局组件
│   │       ├── Breadcrumb/     # 面包屑
│   │       ├── Footer/         # 页脚
│   │       ├── Header/         # 顶部导航
│   │       ├── Navigation/     # 导航菜单
│   │       ├── Settings/       # 设置
│   │       └── Sidebar/        # 侧边栏
│   ├── composables/            # 组合式函数
│   │   ├── useCrud.ts          # CRUD操作钩子
│   │   ├── useForm.ts          # 表单操作钩子
│   │   ├── usePagination.ts    # 分页钩子
│   │   └── useTable.ts         # 表格操作钩子
│   ├── constants/              # 常量定义
│   │   ├── api.ts              # API常量
│   │   ├── enums.ts            # 枚举值
│   │   └── permissions.ts      # 权限常量
│   ├── directives/             # 自定义指令
│   │   ├── permission.ts       # 权限指令
│   │   └── loading.ts          # 加载指令
│   ├── hooks/                  # 自定义Hooks
│   │   ├── useRequest.ts       # 请求Hook
│   │   └── useDialog.ts        # 对话框Hook
│   ├── layouts/                # 页面布局
│   │   ├── DefaultLayout.vue   # 默认布局
│   │   └── BlankLayout.vue     # 空白布局
│   ├── pages/                  # 页面组件
│   │   ├── auth/               # 认证页面
│   │   │   ├── Login.vue       # 登录页
│   │   │   └── Register.vue    # 注册页
│   │   ├── dashboard/          # 仪表板
│   │   │   └── Index.vue       # 首页
│   │   ├── system/             # 系统管理
│   │   │   ├── users/          # 用户管理
│   │   │   ├── roles/          # 角色管理
│   │   │   ├── permissions/    # 权限管理
│   │   │   ├── menus/          # 菜单管理
│   │   │   └── configs/        # 系统配置
│   │   ├── virtualization/     # 虚拟化管理
│   │   │   ├── nodes/          # 节点管理
│   │   │   ├── vms/            # 虚拟机管理
│   │   │   └── templates/      # 模板管理
│   │   ├── orders/             # 订单管理
│   │   │   ├── list/           # 订单列表
│   │   │   ├── detail/         # 订单详情
│   │   │   └── products/       # 产品管理
│   │   └── payments/           # 支付管理
│   │       ├── transactions/   # 交易记录
│   │       ├── channels/       # 支付渠道
│   │       └── refunds/        # 退款管理
│   ├── router/                 # 路由配置
│   │   ├── index.ts            # 路由主文件
│   │   ├── routes/             # 路由定义
│   │   │   ├── auth.ts         # 认证路由
│   │   │   ├── system.ts       # 系统路由
│   │   │   ├── virtualization.ts # 虚拟化路由
│   │   │   ├── orders.ts       # 订单路由
│   │   │   └── payments.ts     # 支付路由
│   │   └── guards.ts           # 路由守卫
│   ├── stores/                 # Pinia状态管理
│   │   ├── modules/            # 状态模块
│   │   │   ├── auth.ts         # 认证状态
│   │   │   ├── user.ts         # 用户状态
│   │   │   ├── permission.ts   # 权限状态
│   │   │   ├── app.ts          # 应用状态
│   │   │   └── websocket.ts    # WebSocket状态
│   │   └── index.ts            # Store入口
│   ├── types/                  # TypeScript类型定义
│   │   ├── api.ts              # API类型
│   │   ├── auth.ts             # 认证类型
│   │   ├── common.ts           # 通用类型
│   │   └── business.ts         # 业务类型
│   ├── utils/                  # 工具函数
│   │   ├── auth.ts             # 认证工具
│   │   ├── request.ts          # 请求工具
│   │   ├── storage.ts          # 存储工具
│   │   ├── format.ts           # 格式化工具
│   │   ├── validation.ts       # 验证工具
│   │   └── permission.ts       # 权限工具
│   ├── App.vue                 # 根组件
│   ├── main.ts                 # 应用入口
│   └── env.d.ts                # 环境类型声明
├── tests/                      # 测试文件
│   ├── unit/                   # 单元测试
│   └── e2e/                    # 端到端测试
├── docs/                       # 项目文档
│   ├── DEVELOPMENT.md          # 开发指南
│   ├── DEPLOYMENT.md           # 部署指南
│   └── API.md                  # API文档
├── .env                        # 环境变量
├── .env.development            # 开发环境变量
├── .env.production             # 生产环境变量
├── .eslintrc.js                # ESLint配置
├── .prettierrc                 # Prettier配置
├── index.html                  # HTML模板
├── package.json                # 项目配置
├── tsconfig.json               # TypeScript配置
├── vite.config.ts              # Vite配置
└── README.md                   # 项目说明
```

## 核心特性

### 1. 现代化技术栈
- Vue 3 Composition API + TypeScript 严格模式
- TDesign UI 企业级组件库
- Vite 快速构建和热更新
- Pinia 轻量级状态管理

### 2. 完整的权限系统
- 基于JWT的身份认证
- 细粒度权限控制
- 动态路由和菜单
- 权限指令和组合式函数

### 3. 响应式设计
- 移动端适配
- 多主题支持
- 国际化支持
- 无障碍访问

### 4. 开发体验
- TypeScript 类型安全
- 组件和API自动导入
- 热模块替换
- 代码规范检查

### 5. 性能优化
- 路由懒加载
- 组件按需加载
- 图片懒加载
- 缓存策略

## 业务组件库

本项目提供了一套完整的业务组件库和工具函数，用于快速开发业务页面：

### 核心组件
- **DictTag** - 字典标签组件，自动显示字典值对应的标签
- **DictSelect** - 字典选择组件，基于字典数据的下拉选择
- **IconSelect** - 图标选择组件，支持多种图标类型选择

### CRUD钩子
- **useCrud** - 完整的CRUD操作解决方案
- **useTable** - 表格数据管理和操作
- **useForm** - 表单状态管理和操作
- **usePagination** - 分页功能

### 字典管理
- **useDictStore** - 字典数据缓存和管理
- 自动缓存机制，避免重复请求
- 支持批量获取和预加载

### 使用示例

```vue
<template>
  <!-- 字典标签 -->
  <DictTag dict-type="user_status" :value="user.status" />

  <!-- 字典选择 -->
  <DictSelect v-model="form.status" dict-type="user_status" />

  <!-- 图标选择 -->
  <IconSelect v-model="form.icon" />
</template>

<script setup lang="ts">
import { DictTag, DictSelect, IconSelect } from '@/components/business'
import { useCrud } from '@/composables'

// 使用CRUD钩子快速构建页面
const {
  tableData,
  loading,
  handleCreate,
  handleUpdate,
  handleDelete
} = useCrud({
  title: '用户管理',
  api: {
    list: userApi.getPage,
    create: userApi.create,
    update: userApi.update,
    delete: userApi.delete
  },
  // ... 其他配置
})
</script>
```

详细使用指南请参考：
- [业务组件使用指南](./docs/BUSINESS_COMPONENTS.md)
- [组件库文档](./src/components/business/README.md)

## 标准化布局组件库

本项目提供了一套完整的标准化业务页面布局组件，确保界面一致性和开发效率：

### 核心布局组件
- **FormTableLayout** - 上下分割布局（搜索表单 + 数据表格）
- **MasterDetailLayout** - 左右分栏布局（主从详情）
- **CardGridLayout** - 卡片网格布局（卡片展示）
- **DetailPageLayout** - 详情页布局（详细信息展示）

### 设计规范
- 统一的设计Token（间距、颜色、圆角、阴影）
- 完整的响应式适配规则
- 符合TDesign设计语言
- 支持暗色主题切换

### 使用场景
```vue
<!-- 用户管理页面 - 使用FormTableLayout -->
<FormTableLayout
  title="用户管理"
  search-form-title="搜索条件"
  @search="handleSearch"
>
  <template #searchForm>
    <!-- 搜索表单 -->
  </template>
  <template #table>
    <!-- 用户列表表格 -->
  </template>
</FormTableLayout>

<!-- 角色权限管理 - 使用MasterDetailLayout -->
<MasterDetailLayout
  title="角色权限管理"
  master-title="角色列表"
  detail-title="权限配置"
>
  <template #master>
    <!-- 角色列表 -->
  </template>
  <template #detail>
    <!-- 权限配置 -->
  </template>
</MasterDetailLayout>

<!-- 虚拟机管理 - 使用CardGridLayout -->
<CardGridLayout
  title="虚拟机管理"
  :show-view-toggle="true"
>
  <template #gridItems>
    <!-- VM状态卡片 -->
  </template>
</CardGridLayout>

<!-- 用户详情 - 使用DetailPageLayout -->
<DetailPageLayout
  title="用户详情"
  :show-tabs="true"
  :tabs="userTabs"
>
  <template #content>
    <!-- 详细信息 -->
  </template>
</DetailPageLayout>
```

### 布局选择指南
- **数据列表** → FormTableLayout 或 CardGridLayout
- **主从关系** → MasterDetailLayout
- **详情展示** → DetailPageLayout
- **复杂筛选** → FormTableLayout
- **图片展示** → CardGridLayout

详细文档请参考：
- [布局设计规范](./docs/LAYOUT_GUIDELINES.md)
- [布局选择决策指南](./docs/LAYOUT_DECISION_GUIDE.md)
- [布局组件使用指南](./docs/LAYOUT_COMPONENTS_GUIDE.md)

## 快速开始

详细的开发指南请参考 [DEVELOPMENT.md](./docs/DEVELOPMENT.md)
