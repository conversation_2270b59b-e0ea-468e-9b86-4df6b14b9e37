<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CloudVPS 管理后台</title>
    <meta name="description" content="CloudVPS云服务平台管理后台" />
    <meta name="keywords" content="CloudVPS,云服务,管理后台,虚拟化,VPS" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- PWA相关 -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#0052d9" />
    
    <!-- iOS Safari -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="CloudVPS" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    
    <!-- 安全策略 -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'self';" />
  </head>
  <body>
    <div id="app">
      <!-- 加载中的占位内容 -->
      <div id="initial-loader" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f8fafc;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      ">
        <div style="
          width: 64px;
          height: 64px;
          margin-bottom: 16px;
          background: url('/logo.png') no-repeat center;
          background-size: contain;
        "></div>
        <div style="
          font-size: 24px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 8px;
        ">CloudVPS</div>
        <div style="
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 24px;
        ">云服务平台管理后台</div>
        <div style="
          width: 32px;
          height: 32px;
          border: 3px solid #e5e7eb;
          border-top: 3px solid #0052d9;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        "></div>
      </div>
    </div>
    
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 隐藏加载器的样式 */
      .app-loaded #initial-loader {
        display: none;
      }
    </style>
    
    <script type="module" src="/src/main.ts"></script>
    
    <!-- 监控脚本 (仅在生产环境) -->
    <script>
      // 全局错误捕获
      window.addEventListener('error', function(event) {
        console.error('Global Error:', event.error);
      });
      
      window.addEventListener('unhandledrejection', function(event) {
        console.error('Unhandled Promise Rejection:', event.reason);
      });
      
      // 应用加载完成后隐藏加载器
      window.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          const loader = document.getElementById('initial-loader');
          if (loader) {
            loader.style.opacity = '0';
            loader.style.transition = 'opacity 0.3s ease';
            setTimeout(function() {
              loader.style.display = 'none';
              document.body.classList.add('app-loaded');
            }, 300);
          }
        }, 1000);
      });
      
      // 设置全局变量兼容性
      window.global = window;
    </script>
  </body>
</html>
