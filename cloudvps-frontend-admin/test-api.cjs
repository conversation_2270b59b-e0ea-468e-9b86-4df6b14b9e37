#!/usr/bin/env node

/**
 * API连接测试脚本
 * 用于验证前端是否能正确连接到后端服务
 */

const http = require('http');

// 微服务架构测试配置
const services = [
  // 直接测试网关
  { name: 'API网关', host: 'localhost', port: 8080, path: '/health', type: 'gateway' },

  // 通过网关测试各个微服务
  { name: '系统服务(通过网关)', host: 'localhost', port: 8080, path: '/api/system/health', type: 'via-gateway' },
  { name: '虚拟化服务(通过网关)', host: 'localhost', port: 8080, path: '/api/virtualization/health', type: 'via-gateway' },
  { name: '订单服务(通过网关)', host: 'localhost', port: 8080, path: '/api/order/health', type: 'via-gateway' },
  { name: '支付服务(通过网关)', host: 'localhost', port: 8080, path: '/api/payment/health', type: 'via-gateway' }
];

// 测试单个服务
function testService(service) {
  return new Promise((resolve) => {
    const options = {
      hostname: service.host,
      port: service.port,
      path: service.path,
      method: 'GET',
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'CloudVPS-Frontend-Test'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          service: service.name,
          port: service.port,
          status: 'online',
          statusCode: res.statusCode,
          message: `HTTP ${res.statusCode}`,
          response: data.substring(0, 100) // 只显示前100个字符
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        service: service.name,
        port: service.port,
        status: 'offline',
        statusCode: null,
        message: error.message,
        error: error.code
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        service: service.name,
        port: service.port,
        status: 'timeout',
        statusCode: null,
        message: '连接超时',
        error: 'TIMEOUT'
      });
    });

    req.end();
  });
}

// 主测试函数
async function testAllServices() {
  console.log('🚀 开始测试CloudVPS微服务架构连接...\n');
  console.log('📋 测试策略: 所有API请求通过网关(8080)统一路由\n');
  
  const results = [];
  
  for (const service of services) {
    console.log(`⏳ 测试 ${service.name} (${service.host}:${service.port})...`);
    const result = await testService(service);
    results.push(result);
    
    const statusIcon = result.status === 'online' ? '✅' : '❌';
    console.log(`${statusIcon} ${result.service}: ${result.message}`);
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log('='.repeat(60));
  
  const onlineServices = results.filter(r => r.status === 'online');
  const offlineServices = results.filter(r => r.status !== 'online');
  
  console.log(`✅ 在线服务: ${onlineServices.length}/${results.length}`);
  console.log(`❌ 离线服务: ${offlineServices.length}/${results.length}`);
  
  if (onlineServices.length > 0) {
    console.log('\n🟢 在线服务列表:');
    onlineServices.forEach(service => {
      console.log(`  - ${service.service} (${service.port}): ${service.message}`);
    });
  }
  
  if (offlineServices.length > 0) {
    console.log('\n🔴 离线服务列表:');
    offlineServices.forEach(service => {
      console.log(`  - ${service.service} (${service.port}): ${service.message}`);
    });
  }
  
  console.log('\n💡 解决方案建议:');
  if (offlineServices.length === results.length) {
    console.log('  - 所有后端服务都未启动，请先启动CloudVPS后端服务');
    console.log('  - 检查服务端口是否被占用');
    console.log('  - 确认服务配置是否正确');
  } else if (offlineServices.length > 0) {
    console.log('  - 部分服务未启动，请检查对应的服务状态');
    console.log('  - 确认服务依赖关系是否正确');
  } else {
    console.log('  - 所有服务运行正常！');
    console.log('  - 可以继续测试前端API调用');
  }
  
  console.log('\n🌐 前端访问地址:');
  console.log('  - 开发服务器: http://localhost:3001');
  console.log('  - API测试页面: http://localhost:3001/test');
  console.log('  - 用户管理页面: http://localhost:3001/system/users');
  
  return results;
}

// 运行测试
if (require.main === module) {
  testAllServices().catch(console.error);
}

module.exports = { testAllServices, testService };
