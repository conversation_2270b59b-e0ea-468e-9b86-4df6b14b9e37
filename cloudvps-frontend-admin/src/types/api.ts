/**
 * API相关类型定义
 */

// 基础API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

// 分页响应类型
export interface PageResponse<T = any> {
  records: T[]  // 修改为records字段
  total: number
  current: number  // 修改为current字段
  size: number
  pages: number
}

// 基础分页请求（统一各模块使用）
export interface BasePageRequest {
  current?: number
  pageSize?: number
  sortField?: string
  sortOrder?: string
}

// 分页请求参数（保持向后兼容）
export interface PageRequest {
  page?: number
  size?: number
  sort?: string
  order?: 'asc' | 'desc'
}

// 基础查询参数
export interface BaseQueryParams extends PageRequest {
  keyword?: string
  startDate?: string
  endDate?: string
}

// 基础实体时间字段
export interface BaseTimeFields {
  createdTime?: string
  updatedTime?: string
}

// 基础响应实体
export interface BaseEntity extends BaseTimeFields {
  id: number
}

// 批量操作请求（统一各模块使用）
export interface BatchRequest {
  ids: number[]
}

// 批量操作参数（保持向后兼容）
export interface BatchOperationParams {
  ids: number[]
  action?: string
}

// 状态更新请求
export interface StatusUpdateRequest {
  status: string
}

// 排序更新请求
export interface SortUpdateRequest {
  sortOrder: number
}

// 文件上传响应
export interface UploadResponse {
  url: string
  filename: string
  size: number
  type: string
}

// 错误响应
export interface ErrorResponse {
  code: number
  message: string
  details?: string
  timestamp: string
  path?: string
}

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置
export interface RequestConfig {
  url: string
  method: HttpMethod
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
}

// API端点配置
export interface ApiEndpoint {
  url: string
  method: HttpMethod
  description?: string
}

// API模块配置
export interface ApiModule {
  baseUrl: string
  endpoints: Record<string, ApiEndpoint>
}

// 导出状态
export enum ExportStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

// 导出任务
export interface ExportTask {
  id: string
  name: string
  status: ExportStatus
  progress: number
  downloadUrl?: string
  createdAt: string
  completedAt?: string
  errorMessage?: string
}
