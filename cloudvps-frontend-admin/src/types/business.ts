/**
 * 业务相关类型定义
 */

import type { BasePageRequest } from './api'

// CRUD操作类型
export type CrudAction = 'create' | 'read' | 'update' | 'delete' | 'batch'

// 表格列配置
export interface TableColumn {
  key: string
  title: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  fixed?: 'left' | 'right'
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: any, index: number) => any
}

// 表格配置
export interface TableConfig {
  columns: TableColumn[]
  rowKey?: string
  bordered?: boolean
  striped?: boolean
  hover?: boolean
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  pagination?: boolean
  selection?: boolean
  expandable?: boolean
}

// 表单字段配置
export interface FormField {
  key: string
  label: string
  type: 'input' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'datetime' | 'number' | 'password' | 'dict-select'
  required?: boolean
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  dictType?: string
  rules?: any[]
  span?: number
  disabled?: boolean
  readonly?: boolean
}

// 表单配置
export interface FormConfig {
  fields: FormField[]
  layout?: 'horizontal' | 'vertical' | 'inline'
  labelWidth?: string
  colon?: boolean
  requiredMark?: boolean
}

// 查询表单配置
export interface SearchFormConfig extends FormConfig {
  showReset?: boolean
  showExpand?: boolean
  defaultExpanded?: boolean
}

// 操作按钮配置
export interface ActionButton {
  key: string
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  permission?: string
  disabled?: boolean
  loading?: boolean
  onClick?: (record?: any) => void
}

// CRUD页面配置
export interface CrudPageConfig {
  title: string
  api: {
    list: Function
    create?: Function
    update?: Function
    delete?: Function
    batchDelete?: Function
  }
  table: TableConfig
  searchForm?: SearchFormConfig
  createForm?: FormConfig
  updateForm?: FormConfig
  actions?: {
    toolbar?: ActionButton[]
    row?: ActionButton[]
  }
  permissions?: {
    view?: string
    create?: string
    update?: string
    delete?: string
  }
}

// 字典项
export interface DictItem {
  label: string
  value: string | number
  color?: string
  status?: string
  description?: string
}

// 字典数据
export interface DictData {
  [key: string]: DictItem[]
}

// 图标类型
export type IconType = 'tdesign' | 'svg' | 'url' | 'font'

// 图标配置
export interface IconConfig {
  type: IconType
  value: string
  size?: number | string
  color?: string
}

// 分页配置
export interface PaginationConfig {
  current: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
  pageSizeOptions?: number[]
}

// 加载状态
export interface LoadingState {
  list?: boolean
  create?: boolean
  update?: boolean
  delete?: boolean
  export?: boolean
}

// 错误状态
export interface ErrorState {
  message?: string
  details?: any
}

// CRUD状态
export interface CrudState<T = any> {
  // 数据
  list: T[]
  current: T | null
  
  // 分页
  pagination: PaginationConfig
  
  // 加载状态
  loading: LoadingState
  
  // 错误状态
  error: ErrorState
  
  // 选中项
  selectedRowKeys: (string | number)[]
  selectedRows: T[]
  
  // 表单状态
  formVisible: boolean
  formMode: 'create' | 'update'
  formData: Partial<T>
  
  // 查询参数
  queryParams: BasePageRequest & Record<string, any>
}

// 表格操作选项
export interface TableActionOptions {
  selection?: boolean
  refresh?: boolean
  density?: boolean
  fullscreen?: boolean
  setting?: boolean
}

// 导出配置
export interface ExportConfig {
  filename?: string
  format?: 'xlsx' | 'csv' | 'pdf'
  columns?: string[]
  params?: Record<string, any>
}
