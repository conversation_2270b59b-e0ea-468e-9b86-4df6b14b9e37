/**
 * 菜单数据类型定义
 */

/**
 * 菜单项基础接口
 */
export interface MenuItem {
  /** 菜单唯一标识 */
  value: string
  /** 菜单标题 */
  title: string
  /** 菜单图标 */
  icon?: string
  /** 路由路径 */
  path?: string
  /** 子菜单 */
  children?: MenuItem[]
  /** 是否禁用 */
  disabled?: boolean
  /** 是否隐藏 */
  hidden?: boolean
  /** 权限标识 */
  permissions?: string[]
  /** 外部链接 */
  href?: string
  /** 链接打开方式 */
  target?: '_blank' | '_self' | '_parent' | '_top'
  /** 菜单描述 */
  description?: string
  /** 排序权重 */
  sort?: number
}

/**
 * 菜单配置接口
 */
export interface MenuConfig {
  /** 菜单列表 */
  items: MenuItem[]
  /** 默认展开的菜单 */
  defaultExpanded?: string[]
  /** 默认激活的菜单 */
  defaultValue?: string
}

/**
 * 菜单事件上下文
 */
export interface MenuEventContext {
  /** 菜单项 */
  item: MenuItem
  /** 事件对象 */
  e: MouseEvent
}

/**
 * 菜单类型枚举
 */
export enum MenuType {
  /** 目录 */
  DIRECTORY = 'directory',
  /** 菜单 */
  MENU = 'menu',
  /** 按钮 */
  BUTTON = 'button'
}
