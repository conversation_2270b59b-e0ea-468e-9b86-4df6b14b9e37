/**
 * 系统设置相关类型定义
 */

// 主题模式
export type ThemeMode = 'light' | 'dark' | 'auto'

// 布局模式
export type LayoutMode = 'sidebar' | 'header' | 'mixed' | 'split'

// 主题颜色预设
export interface ThemeColor {
  name: string
  value: string
  description?: string
}

// 布局配置
export interface LayoutConfig {
  mode: LayoutMode
  name: string
  description: string
  preview?: string
}

// 界面元素显示配置
export interface UIElementsConfig {
  /** 面包屑导航 */
  breadcrumb: boolean
  /** Tab多标签页 */
  tabs: boolean
  /** 侧边栏折叠按钮 */
  sidebarCollapseButton: boolean
  /** 页脚 */
  footer: boolean
  /** 顶部导航 */
  header: boolean
  /** 侧边栏 */
  sidebar: boolean
  /** Logo */
  logo: boolean
  /** 设置按钮 */
  settingsButton: boolean
}

// 系统设置状态
export interface SettingsState {
  /** 主题模式 */
  themeMode: ThemeMode
  /** 主色调 */
  primaryColor: string
  /** 布局模式 */
  layoutMode: LayoutMode
  /** 界面元素配置 */
  uiElements: UIElementsConfig
  /** 是否显示设置面板 */
  showSettingsPanel: boolean
  /** 自定义颜色列表 */
  customColors: string[]
  /** 最近使用的颜色 */
  recentColors: string[]
}

// 设置项配置
export interface SettingItem {
  key: string
  label: string
  description?: string
  type: 'switch' | 'radio' | 'select' | 'color' | 'slider'
  options?: Array<{ label: string; value: any; description?: string }>
  defaultValue: any
}

// 设置分组
export interface SettingGroup {
  key: string
  title: string
  description?: string
  icon?: string
  items: SettingItem[]
}

// 预设主题颜色
export const PRESET_THEME_COLORS: ThemeColor[] = [
  { name: '默认蓝', value: '#0052d9', description: 'TDesign默认主色' },
  { name: '科技蓝', value: '#1890ff', description: '科技感蓝色' },
  { name: '成功绿', value: '#00a870', description: '成功状态绿色' },
  { name: '警告橙', value: '#ed7b2f', description: '警告状态橙色' },
  { name: '错误红', value: '#d54941', description: '错误状态红色' },
  { name: '紫罗兰', value: '#722ed1', description: '优雅紫色' },
  { name: '青色', value: '#13c2c2', description: '清新青色' },
  { name: '粉色', value: '#eb2f96', description: '活力粉色' },
  { name: '金色', value: '#fa8c16', description: '温暖金色' },
  { name: '石墨', value: '#434343', description: '沉稳石墨色' }
]

// 布局模式配置
export const LAYOUT_CONFIGS: LayoutConfig[] = [
  {
    mode: 'sidebar',
    name: '侧边导航',
    description: '经典的侧边栏导航布局，适合功能较多的管理系统'
  },
  {
    mode: 'header',
    name: '顶部导航',
    description: '顶部水平导航布局，适合功能相对简单的系统'
  },
  {
    mode: 'mixed',
    name: '混合布局',
    description: '顶部显示一级菜单，侧边栏显示当前一级菜单的子菜单'
  },
  {
    mode: 'split',
    name: '分割菜单',
    description: '左侧显示一级菜单图标，右侧动态显示子菜单'
  }
]

// 默认设置
export const DEFAULT_SETTINGS: SettingsState = {
  themeMode: 'auto',
  primaryColor: '#0052d9',
  layoutMode: 'sidebar',
  uiElements: {
    breadcrumb: true,
    tabs: true,
    sidebarCollapseButton: true,
    footer: true,
    header: true,
    sidebar: true,
    logo: true,
    settingsButton: true
  },
  showSettingsPanel: false,
  customColors: [],
  recentColors: []
}

// 设置存储键名
export const SETTINGS_STORAGE_KEY = 'cloudvps_settings'

// CSS变量映射
export const CSS_VARIABLES = {
  // 主题色相关
  primaryColor: '--td-brand-color',
  primaryColorHover: '--td-brand-color-hover',
  primaryColorFocus: '--td-brand-color-focus',
  primaryColorActive: '--td-brand-color-active',
  primaryColorDisabled: '--td-brand-color-disabled',
  
  // 背景色
  backgroundColor: '--td-bg-color-page',
  containerBackground: '--td-bg-color-container',
  
  // 文字颜色
  textColorPrimary: '--td-text-color-primary',
  textColorSecondary: '--td-text-color-secondary',
  textColorPlaceholder: '--td-text-color-placeholder',
  textColorDisabled: '--td-text-color-disabled',
  
  // 边框颜色
  borderColor: '--td-border-level-1-color',
  borderColorHover: '--td-border-level-2-color',
  
  // 阴影
  shadowBase: '--td-shadow-base',
  shadowHover: '--td-shadow-hover'
}

// 主题模式对应的CSS类名（根据TDesign官方规范）
export const THEME_CLASS_MAP = {
  light: 't-theme--light',
  dark: 't-theme--dark',
  auto: 't-theme--auto'
}

// 布局模式对应的CSS类名
export const LAYOUT_CLASS_MAP = {
  sidebar: 'layout-sidebar',
  header: 'layout-header',
  mixed: 'layout-mixed',
  split: 'layout-split'
}

// 颜色工具函数类型
export interface ColorUtils {
  /** 十六进制转RGB */
  hexToRgb: (hex: string) => { r: number; g: number; b: number } | null
  /** RGB转十六进制 */
  rgbToHex: (r: number, g: number, b: number) => string
  /** 生成颜色变体 */
  generateColorVariants: (color: string) => {
    hover: string
    focus: string
    active: string
    disabled: string
  }
  /** 判断颜色亮度 */
  isLightColor: (color: string) => boolean
  /** 获取对比色 */
  getContrastColor: (color: string) => string
}

// 设置面板事件
export interface SettingsPanelEvents {
  /** 主题模式变更 */
  'theme-change': (mode: ThemeMode) => void
  /** 主色调变更 */
  'primary-color-change': (color: string) => void
  /** 布局模式变更 */
  'layout-change': (mode: LayoutMode) => void
  /** UI元素显示变更 */
  'ui-element-change': (key: keyof UIElementsConfig, value: boolean) => void
  /** 设置重置 */
  'settings-reset': () => void
  /** 设置导出 */
  'settings-export': (settings: SettingsState) => void
  /** 设置导入 */
  'settings-import': (settings: SettingsState) => void
}

// 设置验证规则
export interface SettingsValidation {
  /** 验证主题模式 */
  validateThemeMode: (mode: string) => boolean
  /** 验证颜色值 */
  validateColor: (color: string) => boolean
  /** 验证布局模式 */
  validateLayoutMode: (mode: string) => boolean
  /** 验证设置对象 */
  validateSettings: (settings: Partial<SettingsState>) => {
    valid: boolean
    errors: string[]
  }
}
