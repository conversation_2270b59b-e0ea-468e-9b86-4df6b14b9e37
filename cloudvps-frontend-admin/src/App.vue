<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAppStore } from '@/stores/modules/app'
import { useAuthStore } from '@/stores/modules/auth'

const appStore = useAppStore()
const authStore = useAuthStore()

onMounted(async () => {
  // 初始化主题
  appStore.initTheme()
  
  // 检测设备类型
  const checkDevice = () => {
    const width = window.innerWidth
    if (width < 768) {
      appStore.setDevice('mobile')
    } else if (width < 1024) {
      appStore.setDevice('tablet')
    } else {
      appStore.setDevice('desktop')
    }
  }
  
  // 初始检测
  checkDevice()
  
  // 监听窗口大小变化
  window.addEventListener('resize', checkDevice)
  
  // 认证状态已在main.ts中初始化，这里不需要重复检查
})
</script>

<style lang="less">
// 全局样式重置
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  width: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 暗色主题下的滚动条
.dark {
  ::-webkit-scrollbar-track {
    background: #2c2c2c;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #6c6c6c;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #8c8c8c;
  }
}

// 响应式断点
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none !important;
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
</style>
