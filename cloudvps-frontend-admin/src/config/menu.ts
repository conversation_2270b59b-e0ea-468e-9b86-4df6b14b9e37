/**
 * 菜单配置数据
 * 将硬编码的菜单项提取为JSON格式数据
 */

import type { MenuConfig } from '@/types/menu'

/**
 * 主菜单配置
 */
export const menuConfig: MenuConfig = {
  items: [
    {
      value: 'dashboard',
      title: '仪表板',
      icon: 'dashboard',
      path: '/dashboard',
      sort: 1
    },
    {
      value: 'system',
      title: '系统管理',
      icon: 'setting',
      sort: 2,
      children: [
        {
          value: 'system-overview',
          title: '系统概览',
          path: '/system',
          sort: 1
        },
        {
          value: 'system-users',
          title: '用户管理',
          path: '/system/users',
          permissions: ['USER_VIEW'],
          sort: 2
        },
        {
          value: 'system-dict',
          title: '字典管理',
          path: '/system/dict',
          permissions: ['DICT_VIEW'],
          sort: 3
        },
        {
          value: 'system-menus',
          title: '菜单管理',
          path: '/system/menus',
          permissions: ['SYSTEM_MENU_VIEW'],
          sort: 4
        },
        {
          value: 'system-roles',
          title: '角色管理',
          path: '/system/roles',
          permissions: ['SYSTEM_ROLE_VIEW'],
          sort: 4
        }
      ]
    },
    {
      value: 'virtualization',
      title: '虚拟化管理',
      icon: 'server',
      sort: 3,
      children: [
        {
          value: 'virtualization-nodes',
          title: 'PVE节点',
          path: '/virtualization/nodes',
          permissions: ['VIRTUALIZATION_VIEW'],
          sort: 1
        },
        {
          value: 'virtualization-vms',
          title: '虚拟机管理',
          path: '/virtualization/vms',
          permissions: ['VIRTUALIZATION_VIEW'],
          sort: 2
        }
      ]
    },
    {
      value: 'orders',
      title: '订单管理',
      icon: 'order',
      sort: 4,
      children: [
        {
          value: 'orders-list',
          title: '订单列表',
          path: '/orders/list',
          permissions: ['ORDER_VIEW'],
          sort: 1
        },
        {
          value: 'orders-products',
          title: '产品管理',
          path: '/orders/products',
          permissions: ['ORDER_VIEW'],
          sort: 2
        }
      ]
    },
    {
      value: 'payments',
      title: '支付管理',
      icon: 'money',
      sort: 5,
      children: [
        {
          value: 'payments-transactions',
          title: '交易记录',
          path: '/payments/transactions',
          permissions: ['PAYMENT_VIEW'],
          sort: 1
        },
        {
          value: 'payments-channels',
          title: '支付渠道',
          path: '/payments/channels',
          permissions: ['PAYMENT_VIEW'],
          sort: 2
        }
      ]
    },
    {
      value: 'test',
      title: 'API测试',
      icon: 'tools',
      path: '/test',
      sort: 6
    },
    {
      value: 'test-auto-route',
      title: '自动路由测试',
      icon: 'tools',
      path: '/test-auto-route',
      sort: 7
    }
  ],
  defaultExpanded: ['system', 'virtualization'],
  defaultValue: 'dashboard'
}

/**
 * 根据权限过滤菜单项
 * @param items 菜单项列表
 * @param permissions 用户权限列表
 * @param isSuperAdmin 是否为超级管理员
 * @returns 过滤后的菜单项列表
 */
export function filterMenuByPermissions(
  items: MenuConfig['items'],
  permissions: string[] = [],
  isSuperAdmin: boolean = false
): MenuConfig['items'] {
  return items.filter(item => {
    // 超级管理员拥有所有权限
    if (isSuperAdmin) {
      return !item.hidden
    }

    // 检查当前菜单项权限
    if (item.permissions && item.permissions.length > 0) {
      const hasPermission = item.permissions.some(permission => 
        permissions.includes(permission)
      )
      if (!hasPermission) {
        return false
      }
    }

    // 递归过滤子菜单
    if (item.children && item.children.length > 0) {
      item.children = filterMenuByPermissions(item.children, permissions, isSuperAdmin)
      // 如果子菜单全部被过滤掉，则隐藏父菜单
      if (item.children.length === 0 && item.permissions && item.permissions.length > 0) {
        return false
      }
    }

    return !item.hidden
  })
}

/**
 * 根据路径查找菜单项
 * @param items 菜单项列表
 * @param path 路径
 * @returns 匹配的菜单项
 */
export function findMenuByPath(items: MenuConfig['items'], path: string): MenuConfig['items'][0] | null {
  for (const item of items) {
    if (item.path === path) {
      return item
    }
    if (item.children && item.children.length > 0) {
      const found = findMenuByPath(item.children, path)
      if (found) {
        return found
      }
    }
  }
  return null
}

/**
 * 获取当前激活的菜单值
 * @param items 菜单项列表
 * @param currentPath 当前路径
 * @returns 激活的菜单值
 */
export function getActiveMenuValue(items: MenuConfig['items'], currentPath: string): string {
  // 精确匹配
  const exactMatch = findMenuByPath(items, currentPath)
  if (exactMatch) {
    return exactMatch.value
  }

  // 模糊匹配（找最长匹配的路径）
  let bestMatch: MenuConfig['items'][0] | null = null
  let maxLength = 0

  const findBestMatch = (menuItems: MenuConfig['items']) => {
    for (const item of menuItems) {
      if (item.path && currentPath.startsWith(item.path) && item.path.length > maxLength) {
        bestMatch = item
        maxLength = item.path.length
      }
      if (item.children && item.children.length > 0) {
        findBestMatch(item.children)
      }
    }
  }

  findBestMatch(items)
  return bestMatch?.value || menuConfig.defaultValue || ''
}
