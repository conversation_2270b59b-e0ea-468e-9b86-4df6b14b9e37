<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 云朵图标 -->
  <g id="cloud-icon">
    <!-- 主云朵 -->
    <path d="M8 24C8 20.6863 10.6863 18 14 18C14.3516 18 14.6953 18.0313 15.0273 18.0898C15.5742 15.7031 17.6016 14 20 14C22.7617 14 25 16.2383 25 19C25 19.1758 24.9922 19.3477 24.9766 19.5156C26.1875 20.4375 27 21.8906 27 23.5C27 26.2617 24.7617 28.5 22 28.5H10C8.89844 28.5 8 27.6016 8 26.5V24Z" fill="url(#gradient1)"/>
    
    <!-- 小云朵装饰 -->
    <circle cx="30" cy="16" r="3" fill="url(#gradient2)" opacity="0.7"/>
    <circle cx="34" cy="20" r="2" fill="url(#gradient2)" opacity="0.5"/>
  </g>
  
  <!-- 文字部分 -->
  <g id="text-logo">
    <!-- Cloud -->
    <text x="42" y="20" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#1f2937">Cloud</text>
    <!-- VPS -->
    <text x="42" y="32" font-family="Arial, sans-serif" font-size="12" font-weight="500" fill="#0052d9">VPS</text>
  </g>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0052d9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0084ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0084ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00a6fb;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
