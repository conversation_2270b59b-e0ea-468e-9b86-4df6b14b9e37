/**
 * 主题样式文件
 * 基于TDesign Vue Next的主题系统
 */

// ==================== 浅色主题 ====================
:root,
.t-theme--light {
  // TDesign品牌色
  --td-brand-color: #0052d9;
  --td-brand-color-1: #f2f6ff;
  --td-brand-color-2: #d9e7ff;
  --td-brand-color-3: #b5c7ff;
  --td-brand-color-4: #8eabff;
  --td-brand-color-5: #618dff;
  --td-brand-color-6: #366ef4;
  --td-brand-color-7: #0052d9;
  --td-brand-color-8: #003cab;
  --td-brand-color-9: #002a7c;
  --td-brand-color-10: #001a57;
  
  // 品牌色状态
  --td-brand-color-hover: #266fe8;
  --td-brand-color-focus: #0052d9;
  --td-brand-color-active: #0034b5;
  --td-brand-color-disabled: #b3c7f7;
  
  // 背景色
  --td-bg-color-page: #f5f7fa;
  --td-bg-color-container: #ffffff;
  --td-bg-color-container-hover: #f9fafb;
  --td-bg-color-container-active: #f3f4f6;
  --td-bg-color-container-select: #e5f3ff;
  --td-bg-color-disabled: #f3f4f6;
  
  // 文字颜色
  --td-text-color-primary: #1f2937;
  --td-text-color-secondary: #6b7280;
  --td-text-color-placeholder: #9ca3af;
  --td-text-color-disabled: #d1d5db;
  --td-text-color-anti: #ffffff;
  --td-text-color-brand: #0052d9;
  
  // 边框颜色
  --td-border-level-1-color: #e5e7eb;
  --td-border-level-2-color: #d1d5db;
  --td-border-level-3-color: #9ca3af;
  
  // 功能色
  --td-success-color: #00a870;
  --td-success-color-hover: #059465;
  --td-success-color-active: #00875a;
  --td-success-color-disabled: #b3e5d1;
  
  --td-warning-color: #ed7b2f;
  --td-warning-color-hover: #d35a21;
  --td-warning-color-active: #ba431a;
  --td-warning-color-disabled: #f6d7c2;
  
  --td-error-color: #e34d59;
  --td-error-color-hover: #c9353f;
  --td-error-color-active: #b11e26;
  --td-error-color-disabled: #f1c2c6;
  
  // 阴影
  --td-shadow-1: 0 1px 10px rgba(0, 0, 0, 0.05), 0 4px 5px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.12);
  --td-shadow-2: 0 3px 14px 2px rgba(0, 0, 0, 0.05), 0 8px 10px 1px rgba(0, 0, 0, 0.06), 0 5px 5px -3px rgba(0, 0, 0, 0.1);
  --td-shadow-3: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

// ==================== 暗色主题 ====================
.t-theme--dark {
  // TDesign品牌色（暗色主题下稍微调亮）
  --td-brand-color: #4582e6;
  --td-brand-color-1: #0a0e1a;
  --td-brand-color-2: #0f1419;
  --td-brand-color-3: #1c2128;
  --td-brand-color-4: #262c36;
  --td-brand-color-5: #373e47;
  --td-brand-color-6: #4d5566;
  --td-brand-color-7: #4582e6;
  --td-brand-color-8: #58a6ff;
  --td-brand-color-9: #79c0ff;
  --td-brand-color-10: #a5d6ff;
  
  // 品牌色状态
  --td-brand-color-hover: #58a6ff;
  --td-brand-color-focus: #4582e6;
  --td-brand-color-active: #316dca;
  --td-brand-color-disabled: #3d5a80;
  
  // 背景色
  --td-bg-color-page: #0d1117;
  --td-bg-color-container: #161b22;
  --td-bg-color-container-hover: #21262d;
  --td-bg-color-container-active: #30363d;
  --td-bg-color-container-select: #1c2128;
  --td-bg-color-disabled: #21262d;
  
  // 文字颜色
  --td-text-color-primary: #f0f6fc;
  --td-text-color-secondary: #8b949e;
  --td-text-color-placeholder: #6e7681;
  --td-text-color-disabled: #484f58;
  --td-text-color-anti: #0d1117;
  --td-text-color-brand: #4582e6;
  
  // 边框颜色
  --td-border-level-1-color: #30363d;
  --td-border-level-2-color: #21262d;
  --td-border-level-3-color: #484f58;
  
  // 功能色
  --td-success-color: #3fb950;
  --td-success-color-hover: #56d364;
  --td-success-color-active: #2ea043;
  --td-success-color-disabled: #2d4a3a;
  
  --td-warning-color: #d29922;
  --td-warning-color-hover: #e2a336;
  --td-warning-color-active: #bb8009;
  --td-warning-color-disabled: #4a3d1a;
  
  --td-error-color: #f85149;
  --td-error-color-hover: #ff6b6b;
  --td-error-color-active: #da3633;
  --td-error-color-disabled: #4a2c2a;
  
  // 阴影
  --td-shadow-1: 0 1px 10px rgba(0, 0, 0, 0.2), 0 4px 5px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.4);
  --td-shadow-2: 0 3px 14px 2px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.25), 0 5px 5px -3px rgba(0, 0, 0, 0.35);
  --td-shadow-3: 0 6px 16px 0 rgba(0, 0, 0, 0.3), 0 3px 6px -4px rgba(0, 0, 0, 0.4), 0 9px 28px 8px rgba(0, 0, 0, 0.25);
}

// ==================== 布局相关样式 ====================
// 侧边导航布局
body[data-layout="sidebar"] {
  .layout-container {
    display: flex;
    
    .layout-sidebar {
      width: var(--sidebar-width, 240px);
      transition: width 0.3s ease;
      
      &.collapsed {
        width: var(--sidebar-collapsed-width, 64px);
      }
    }
    
    .layout-main {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

// 顶部导航布局
body[data-layout="header"] {
  .layout-container {
    display: flex;
    flex-direction: column;
    
    .layout-header {
      height: var(--header-height, 64px);
    }
    
    .layout-main {
      flex: 1;
    }
  }
}

// 混合布局
body[data-layout="mix"] {
  .layout-container {
    display: flex;
    flex-direction: column;
    
    .layout-header {
      height: var(--header-height, 64px);
    }
    
    .layout-content {
      flex: 1;
      display: flex;
      
      .layout-sidebar {
        width: var(--sidebar-width, 240px);
      }
      
      .layout-main {
        flex: 1;
      }
    }
  }
}

// 分割菜单布局
body[data-layout="split"] {
  .layout-container {
    display: flex;
    
    .layout-sidebar-first {
      width: var(--sidebar-first-width, 80px);
    }
    
    .layout-sidebar-second {
      width: var(--sidebar-second-width, 200px);
    }
    
    .layout-main {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

// ==================== UI元素控制 ====================
// 标签页控制
body[data-show-tabs="false"] .layout-tabs {
  display: none !important;
}

// 面包屑控制
body[data-show-breadcrumb="false"] .layout-breadcrumb {
  display: none !important;
}

// 页脚控制
body[data-show-footer="false"] .layout-footer {
  display: none !important;
}

// 侧边栏控制
body[data-show-sidebar="false"] .layout-sidebar {
  display: none !important;
}

// 头部控制
body[data-show-header="false"] .layout-header {
  display: none !important;
}

// ==================== 主题切换动画 ====================
* {
  transition: background-color 0.3s ease, 
              border-color 0.3s ease, 
              color 0.3s ease, 
              box-shadow 0.3s ease;
}

// 禁用某些元素的过渡动画
.no-transition,
.no-transition * {
  transition: none !important;
}

// ==================== 响应式适配 ====================
@media (max-width: 768px) {
  body[data-layout="sidebar"] .layout-sidebar,
  body[data-layout="mix"] .layout-sidebar,
  body[data-layout="split"] .layout-sidebar-first,
  body[data-layout="split"] .layout-sidebar-second {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &.mobile-open {
      transform: translateX(0);
    }
  }
  
  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    
    &.active {
      opacity: 1;
      visibility: visible;
    }
  }
}
