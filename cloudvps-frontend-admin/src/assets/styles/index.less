/**
 * 全局样式文件
 */

// 导入变量和混入
@import './variables.less';
@import './mixins.less';
@import './theme.less';

// 全局重置样式
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: @text-color-primary;
  background-color: @bg-color-page;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
}

#app {
  height: 100%;
  width: 100%;
}

// 链接样式
a {
  color: @brand-color;
  text-decoration: none;
  transition: color 0.2s ease;
  
  &:hover {
    color: @brand-color-hover;
  }
  
  &:active {
    color: @brand-color-active;
  }
}

// 按钮基础样式重置
button {
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

// 输入框基础样式
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  outline: none;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 表格样式
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// 列表样式
ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

h1 { font-size: 32px; }
h2 { font-size: 24px; }
h3 { font-size: 20px; }
h4 { font-size: 16px; }
h5 { font-size: 14px; }
h6 { font-size: 12px; }

// 段落样式
p {
  margin: 0 0 16px 0;
  line-height: 1.6;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: @scrollbar-track-color;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: @scrollbar-thumb-color;
  border-radius: 3px;
  
  &:hover {
    background: @scrollbar-thumb-hover-color;
  }
}

// 选择文本样式
::selection {
  background: @brand-color-light;
  color: #fff;
}

::-moz-selection {
  background: @brand-color-light;
  color: #fff;
}

// 工具类
.clearfix {
  .clearfix();
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-ellipsis {
  .text-ellipsis();
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

// 间距工具类
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }
.m-6 { margin: 24px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.mt-5 { margin-top: 20px; }
.mt-6 { margin-top: 24px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-5 { margin-bottom: 20px; }
.mb-6 { margin-bottom: 24px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 12px; }
.ml-4 { margin-left: 16px; }
.ml-5 { margin-left: 20px; }
.ml-6 { margin-left: 24px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 12px; }
.mr-4 { margin-right: 16px; }
.mr-5 { margin-right: 20px; }
.mr-6 { margin-right: 24px; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }
.p-6 { padding: 24px; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 4px; }
.pt-2 { padding-top: 8px; }
.pt-3 { padding-top: 12px; }
.pt-4 { padding-top: 16px; }
.pt-5 { padding-top: 20px; }
.pt-6 { padding-top: 24px; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 4px; }
.pb-2 { padding-bottom: 8px; }
.pb-3 { padding-bottom: 12px; }
.pb-4 { padding-bottom: 16px; }
.pb-5 { padding-bottom: 20px; }
.pb-6 { padding-bottom: 24px; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 4px; }
.pl-2 { padding-left: 8px; }
.pl-3 { padding-left: 12px; }
.pl-4 { padding-left: 16px; }
.pl-5 { padding-left: 20px; }
.pl-6 { padding-left: 24px; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 4px; }
.pr-2 { padding-right: 8px; }
.pr-3 { padding-right: 12px; }
.pr-4 { padding-right: 16px; }
.pr-5 { padding-right: 20px; }
.pr-6 { padding-right: 24px; }

// 响应式工具类
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-block {
    display: block !important;
  }
  
  .mobile-flex {
    display: flex !important;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none !important;
  }
  
  .desktop-block {
    display: block !important;
  }
  
  .desktop-flex {
    display: flex !important;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

.zoom-enter-active,
.zoom-leave-active {
  transition: transform 0.3s ease;
}

.zoom-enter-from,
.zoom-leave-to {
  transform: scale(0.8);
}

// 页面布局样式
.page-container {
  min-height: 100vh;
  background-color: var(--td-bg-color-page);
}

.page-header {
  margin-bottom: 24px;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin-bottom: 8px;
  }

  p {
    color: var(--td-text-color-secondary);
    margin: 0;
  }
}

// 卡片间距统一配置
.t-card + .t-card {
  margin-top: 16px;
}

// 表单和表格布局
.search-form {
  .t-form-item {
    margin-bottom: 16px;
  }

  .search-actions {
    text-align: right;
    margin-top: 16px;
  }
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .selection-info {
    color: var(--td-text-color-secondary);
    font-size: 14px;
  }
}

// 表格样式增强
.t-table {
  .user-info {
    .username {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }

    .real-name {
      font-size: 12px;
      color: var(--td-text-color-secondary);
      margin-top: 2px;
    }

    .email {
      font-size: 12px;
      color: var(--td-text-color-placeholder);
      margin-top: 2px;
    }
  }

  .text-placeholder {
    color: var(--td-text-color-placeholder);
  }
}

// 表格顶部内容样式 - 使用TDesign Grid布局
.table-top-content {
  margin-bottom: var(--td-comp-margin-l);

  .selection-info {
    color: var(--td-text-color-secondary);
    font-size: var(--td-font-size-body-small);
    white-space: nowrap;
  }
}




// 暗色主题适配
.dark {
  color: @text-color-primary-dark;
  background-color: @bg-color-page-dark;

  ::-webkit-scrollbar-track {
    background: @scrollbar-track-color-dark;
  }

  ::-webkit-scrollbar-thumb {
    background: @scrollbar-thumb-color-dark;

    &:hover {
      background: @scrollbar-thumb-hover-color-dark;
    }
  }
}

// 按钮组间距 - 使用TDesign Design Token
.t-button + .t-button {
  margin-left: var(--td-comp-margin-s);
}

// Space组件内的按钮间距（保持兼容）
.t-space .t-button + .t-button {
  margin-left: 0; // Space组件自己管理间距
}
