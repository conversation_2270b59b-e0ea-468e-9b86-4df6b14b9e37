/**
 * 全局样式变量
 */

// ==================== 品牌色彩 ====================
@brand-color: #0052d9;
@brand-color-light: #266fe8;
@brand-color-hover: #266fe8;
@brand-color-active: #0034b5;
@brand-color-disabled: #b3c7f7;
@brand-color-focus: #0034b5;

// ==================== 功能色彩 ====================
@success-color: #00a870;
@success-color-light: #e8f8f2;
@success-color-hover: #059465;
@success-color-active: #00875a;

@warning-color: #ed7b2f;
@warning-color-light: #fef3e6;
@warning-color-hover: #d35a21;
@warning-color-active: #ba431a;

@error-color: #e34d59;
@error-color-light: #fdedef;
@error-color-hover: #c9353f;
@error-color-active: #b11e26;

@info-color: #0052d9;
@info-color-light: #e6f2ff;
@info-color-hover: #266fe8;
@info-color-active: #0034b5;

// ==================== 中性色彩 ====================
// 文字颜色
@text-color-primary: #1f2937;
@text-color-secondary: #6b7280;
@text-color-placeholder: #9ca3af;
@text-color-disabled: #d1d5db;
@text-color-white: #ffffff;
@text-color-anti: #ffffff;

// 背景颜色
@bg-color-page: #f8fafc;
@bg-color-container: #ffffff;
@bg-color-container-hover: #f9fafb;
@bg-color-container-active: #f3f4f6;
@bg-color-container-select: #e5f3ff;
@bg-color-disabled: #f3f4f6;

// 边框颜色
@border-color: #e5e7eb;
@border-color-hover: #d1d5db;
@border-color-focus: @brand-color;
@border-color-disabled: #f3f4f6;

// 分割线颜色
@divider-color: #e5e7eb;

// 阴影颜色
@shadow-color: rgba(0, 0, 0, 0.1);
@shadow-color-light: rgba(0, 0, 0, 0.05);
@shadow-color-heavy: rgba(0, 0, 0, 0.15);

// ==================== 暗色主题色彩 ====================
@text-color-primary-dark: #f9fafb;
@text-color-secondary-dark: #d1d5db;
@text-color-placeholder-dark: #9ca3af;
@text-color-disabled-dark: #6b7280;

@bg-color-page-dark: #111827;
@bg-color-container-dark: #1f2937;
@bg-color-container-hover-dark: #374151;
@bg-color-container-active-dark: #4b5563;
@bg-color-container-select-dark: #1e3a8a;
@bg-color-disabled-dark: #374151;

@border-color-dark: #374151;
@border-color-hover-dark: #4b5563;
@border-color-disabled-dark: #374151;

@divider-color-dark: #374151;

@shadow-color-dark: rgba(0, 0, 0, 0.3);
@shadow-color-light-dark: rgba(0, 0, 0, 0.2);
@shadow-color-heavy-dark: rgba(0, 0, 0, 0.4);

// ==================== 滚动条颜色 ====================
@scrollbar-track-color: #f1f1f1;
@scrollbar-thumb-color: #c1c1c1;
@scrollbar-thumb-hover-color: #a8a8a8;

@scrollbar-track-color-dark: #2c2c2c;
@scrollbar-thumb-color-dark: #6c6c6c;
@scrollbar-thumb-hover-color-dark: #8c8c8c;

// ==================== 尺寸变量 ====================
// 边框圆角
@border-radius-small: 3px;
@border-radius-default: 6px;
@border-radius-large: 9px;
@border-radius-round: 50%;

// 边框宽度
@border-width: 1px;
@border-width-focus: 2px;

// 间距
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 12px;
@spacing-lg: 16px;
@spacing-xl: 20px;
@spacing-xxl: 24px;
@spacing-xxxl: 32px;

// 字体大小
@font-size-xs: 12px;
@font-size-sm: 14px;
@font-size-md: 16px;
@font-size-lg: 18px;
@font-size-xl: 20px;
@font-size-xxl: 24px;
@font-size-xxxl: 32px;

// 行高
@line-height-xs: 1.2;
@line-height-sm: 1.4;
@line-height-md: 1.5;
@line-height-lg: 1.6;
@line-height-xl: 1.8;

// 字体粗细
@font-weight-normal: 400;
@font-weight-medium: 500;
@font-weight-semibold: 600;
@font-weight-bold: 700;

// ==================== 组件尺寸 ====================
// 按钮高度
@btn-height-small: 28px;
@btn-height-medium: 32px;
@btn-height-large: 40px;

// 输入框高度
@input-height-small: 28px;
@input-height-medium: 32px;
@input-height-large: 40px;

// 表格行高
@table-row-height: 48px;
@table-header-height: 48px;

// 导航栏高度
@header-height: 64px;
@sidebar-width: 240px;
@sidebar-collapsed-width: 64px;

// ==================== 层级变量 ====================
@z-index-dropdown: 1000;
@z-index-sticky: 1020;
@z-index-fixed: 1030;
@z-index-modal-backdrop: 1040;
@z-index-modal: 1050;
@z-index-popover: 1060;
@z-index-tooltip: 1070;
@z-index-toast: 1080;

// ==================== 动画变量 ====================
@animation-duration-slow: 0.3s;
@animation-duration-base: 0.2s;
@animation-duration-fast: 0.1s;

@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);
@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);
@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);

// ==================== 响应式断点 ====================
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// ==================== 阴影变量 ====================
@box-shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
@box-shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
@box-shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
@box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// 暗色主题阴影
@box-shadow-light-dark: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
@box-shadow-medium-dark: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
@box-shadow-large-dark: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
@box-shadow-xl-dark: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);

// ==================== 业务页面布局设计Token ====================

// 页面容器间距
@page-padding: @spacing-xxl;
@page-padding-sm: @spacing-lg;
@page-padding-lg: @spacing-xxxl;

// 页面内容区域
@page-content-max-width: 1400px;
@page-content-min-height: calc(100vh - @header-height - 48px);

// 卡片间距
@card-margin-bottom: @spacing-lg;
@card-padding: @spacing-xxl;
@card-padding-sm: @spacing-lg;

// 搜索表单区域
@search-form-padding: 20px @spacing-xxl;
@search-form-margin-bottom: @spacing-lg;
@search-form-border-radius: @border-radius-default;
@search-form-bg: @bg-color-container;

// 工具栏区域
@toolbar-height: 56px;
@toolbar-padding: 12px @spacing-xxl;
@toolbar-margin-bottom: @spacing-lg;
@toolbar-border-radius: @border-radius-default;
@toolbar-bg: @bg-color-container;

// 表格区域
@table-card-padding: 0;
@table-border-radius: @border-radius-default;
@table-header-bg: @bg-color-container;

// 左右分栏布局 (Master-Detail)
@master-detail-gap: @spacing-lg;
@master-panel-min-width: 280px;
@master-panel-max-width: 400px;
@master-panel-default-width: 320px;
@detail-panel-min-width: 600px;
@master-panel-bg: @bg-color-container;
@detail-panel-bg: @bg-color-container;

// 卡片网格布局
@card-grid-gap: @spacing-lg;
@card-grid-item-min-width: 280px;
@card-grid-item-max-width: 400px;
@card-grid-item-padding: @spacing-lg;

// 详情页布局
@detail-page-max-width: 1200px;
@detail-page-section-margin: @spacing-xxl;
@detail-page-header-height: 80px;

// 表单布局
@form-item-margin-bottom: @spacing-lg;
@form-label-width: 120px;
@form-label-width-lg: 150px;
@form-section-margin: @spacing-xxxl;

// 操作按钮区域
@action-bar-padding: @spacing-lg @spacing-xxl;
@action-bar-margin-top: @spacing-xxl;
@action-bar-border-top: 1px solid @border-color;
@action-button-gap: @spacing-sm;

// 状态指示器
@status-indicator-size: 8px;
@status-indicator-margin: 0 @spacing-sm 0 0;

// 分割线
@section-divider-margin: @spacing-xxl 0;
@content-divider-margin: @spacing-lg 0;
