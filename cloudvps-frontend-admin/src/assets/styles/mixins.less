/**
 * 全局样式混入
 */

// ==================== 清除浮动 ====================
.clearfix() {
  &::before,
  &::after {
    content: '';
    display: table;
  }
  
  &::after {
    clear: both;
  }
}

// ==================== 文本省略 ====================
.text-ellipsis() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-ellipsis-multiline(@lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// ==================== 居中对齐 ====================
.center-absolute() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.center-flex() {
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-vertical() {
  display: flex;
  align-items: center;
}

.center-horizontal() {
  display: flex;
  justify-content: center;
}

// ==================== 响应式设计 ====================
.respond-to(@media) when (@media = xs) {
  @media (max-width: @screen-xs - 1) { @content(); }
}

.respond-to(@media) when (@media = sm) {
  @media (min-width: @screen-sm) { @content(); }
}

.respond-to(@media) when (@media = md) {
  @media (min-width: @screen-md) { @content(); }
}

.respond-to(@media) when (@media = lg) {
  @media (min-width: @screen-lg) { @content(); }
}

.respond-to(@media) when (@media = xl) {
  @media (min-width: @screen-xl) { @content(); }
}

.respond-to(@media) when (@media = xxl) {
  @media (min-width: @screen-xxl) { @content(); }
}

// ==================== 按钮样式 ====================
.button-base() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: @border-width solid transparent;
  border-radius: @border-radius-default;
  font-size: @font-size-sm;
  font-weight: @font-weight-medium;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all @animation-duration-base @ease-in-out;
  user-select: none;
  white-space: nowrap;
  
  &:focus {
    outline: none;
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.button-size(@height, @padding-horizontal, @font-size) {
  height: @height;
  padding: 0 @padding-horizontal;
  font-size: @font-size;
}

.button-variant(@color, @background, @border) {
  color: @color;
  background-color: @background;
  border-color: @border;
  
  &:hover:not(:disabled) {
    color: @color;
    background-color: lighten(@background, 5%);
    border-color: lighten(@border, 5%);
  }
  
  &:active:not(:disabled) {
    color: @color;
    background-color: darken(@background, 5%);
    border-color: darken(@border, 5%);
  }
}

// ==================== 输入框样式 ====================
.input-base() {
  display: inline-block;
  width: 100%;
  padding: 0 @spacing-md;
  border: @border-width solid @border-color;
  border-radius: @border-radius-default;
  font-size: @font-size-sm;
  line-height: 1.5;
  color: @text-color-primary;
  background-color: @bg-color-container;
  transition: all @animation-duration-base @ease-in-out;
  
  &::placeholder {
    color: @text-color-placeholder;
  }
  
  &:hover {
    border-color: @border-color-hover;
  }
  
  &:focus {
    border-color: @border-color-focus;
    box-shadow: 0 0 0 2px fade(@brand-color, 20%);
  }
  
  &:disabled {
    color: @text-color-disabled;
    background-color: @bg-color-disabled;
    border-color: @border-color-disabled;
    cursor: not-allowed;
  }
}

.input-size(@height, @padding-horizontal, @font-size) {
  height: @height;
  padding-left: @padding-horizontal;
  padding-right: @padding-horizontal;
  font-size: @font-size;
}

// ==================== 卡片样式 ====================
.card-base() {
  background-color: @bg-color-container;
  border: @border-width solid @border-color;
  border-radius: @border-radius-default;
  box-shadow: @box-shadow-light;
  transition: all @animation-duration-base @ease-in-out;
}

.card-hover() {
  &:hover {
    box-shadow: @box-shadow-medium;
    transform: translateY(-2px);
  }
}

// ==================== 阴影效果 ====================
.shadow(@level: 1) when (@level = 1) {
  box-shadow: @box-shadow-light;
}

.shadow(@level: 2) when (@level = 2) {
  box-shadow: @box-shadow-medium;
}

.shadow(@level: 3) when (@level = 3) {
  box-shadow: @box-shadow-large;
}

.shadow(@level: 4) when (@level = 4) {
  box-shadow: @box-shadow-xl;
}

// ==================== 动画效果 ====================
.fade-in(@duration: @animation-duration-base) {
  animation: fadeIn @duration @ease-out;
}

.fade-out(@duration: @animation-duration-base) {
  animation: fadeOut @duration @ease-in;
}

.slide-in-up(@duration: @animation-duration-base) {
  animation: slideInUp @duration @ease-out;
}

.slide-in-down(@duration: @animation-duration-base) {
  animation: slideInDown @duration @ease-out;
}

.slide-in-left(@duration: @animation-duration-base) {
  animation: slideInLeft @duration @ease-out;
}

.slide-in-right(@duration: @animation-duration-base) {
  animation: slideInRight @duration @ease-out;
}

.zoom-in(@duration: @animation-duration-base) {
  animation: zoomIn @duration @ease-out;
}

.zoom-out(@duration: @animation-duration-base) {
  animation: zoomOut @duration @ease-in;
}

// ==================== 加载动画 ====================
.loading-spin() {
  animation: spin 1s linear infinite;
}

.loading-pulse() {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loading-bounce() {
  animation: bounce 1s infinite;
}

// ==================== 工具混入 ====================
.size(@width, @height: @width) {
  width: @width;
  height: @height;
}

.square(@size) {
  .size(@size);
}

.circle(@size) {
  .size(@size);
  border-radius: 50%;
}

.triangle(@size, @color, @direction: up) when (@direction = up) {
  width: 0;
  height: 0;
  border-left: @size solid transparent;
  border-right: @size solid transparent;
  border-bottom: @size solid @color;
}

.triangle(@size, @color, @direction: down) when (@direction = down) {
  width: 0;
  height: 0;
  border-left: @size solid transparent;
  border-right: @size solid transparent;
  border-top: @size solid @color;
}

.triangle(@size, @color, @direction: left) when (@direction = left) {
  width: 0;
  height: 0;
  border-top: @size solid transparent;
  border-bottom: @size solid transparent;
  border-right: @size solid @color;
}

.triangle(@size, @color, @direction: right) when (@direction = right) {
  width: 0;
  height: 0;
  border-top: @size solid transparent;
  border-bottom: @size solid transparent;
  border-left: @size solid @color;
}

// ==================== 滚动条样式 ====================
.scrollbar(@width: 6px, @track-color: @scrollbar-track-color, @thumb-color: @scrollbar-thumb-color) {
  &::-webkit-scrollbar {
    width: @width;
    height: @width;
  }
  
  &::-webkit-scrollbar-track {
    background: @track-color;
    border-radius: @width / 2;
  }
  
  &::-webkit-scrollbar-thumb {
    background: @thumb-color;
    border-radius: @width / 2;
    
    &:hover {
      background: darken(@thumb-color, 10%);
    }
  }
}

// ==================== 渐变背景 ====================
.gradient-linear(@start-color, @end-color, @direction: to right) {
  background: linear-gradient(@direction, @start-color, @end-color);
}

.gradient-radial(@start-color, @end-color, @position: center) {
  background: radial-gradient(@position, @start-color, @end-color);
}

// ==================== 边框样式 ====================
.border-dashed(@color: @border-color, @width: @border-width) {
  border: @width dashed @color;
}

.border-dotted(@color: @border-color, @width: @border-width) {
  border: @width dotted @color;
}

.border-top(@color: @border-color, @width: @border-width) {
  border-top: @width solid @color;
}

.border-bottom(@color: @border-color, @width: @border-width) {
  border-bottom: @width solid @color;
}

.border-left(@color: @border-color, @width: @border-width) {
  border-left: @width solid @color;
}

.border-right(@color: @border-color, @width: @border-width) {
  border-right: @width solid @color;
}

// ==================== 业务页面布局Mixins ====================

// 页面容器
.page-container() {
  padding: @page-padding;
  max-width: @page-content-max-width;
  margin: 0 auto;
  min-height: @page-content-min-height;

  .respond-to(md) {
    padding: @page-padding-sm;
  }
}

// 卡片容器
.card-container() {
  background: @bg-color-container;
  border-radius: @border-radius-default;
  box-shadow: @box-shadow-light;
  margin-bottom: @card-margin-bottom;

  [theme-mode="dark"] & {
    background: @bg-color-container-dark;
    box-shadow: @box-shadow-light-dark;
  }
}

// 搜索表单区域
.search-form-container() {
  .card-container();
  padding: @search-form-padding;
  margin-bottom: @search-form-margin-bottom;
}

// 工具栏区域
.toolbar-container() {
  .card-container();
  padding: @toolbar-padding;
  margin-bottom: @toolbar-margin-bottom;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: @toolbar-height;

  .respond-to(md) {
    flex-direction: column;
    align-items: stretch;
    gap: @spacing-md;
  }
}

// 表格容器
.table-container() {
  .card-container();
  padding: @table-card-padding;

  .t-table {
    border-radius: @table-border-radius;
  }
}

// 左右分栏布局
.master-detail-layout() {
  display: flex;
  gap: @master-detail-gap;
  height: 100%;

  .respond-to(lg) {
    flex-direction: column;
    gap: @spacing-lg;
  }
}

.master-panel() {
  .card-container();
  flex: 0 0 @master-panel-default-width;
  min-width: @master-panel-min-width;
  max-width: @master-panel-max-width;
  padding: @card-padding;

  .respond-to(lg) {
    flex: none;
    max-width: none;
  }
}

.detail-panel() {
  .card-container();
  flex: 1;
  min-width: @detail-panel-min-width;
  padding: @card-padding;

  .respond-to(lg) {
    min-width: auto;
  }
}

// 卡片网格布局
.card-grid-layout() {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(@card-grid-item-min-width, 1fr));
  gap: @card-grid-gap;

  .respond-to(md) {
    grid-template-columns: 1fr;
  }
}

.card-grid-item() {
  .card-container();
  padding: @card-grid-item-padding;
  transition: all @animation-duration-base @ease-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: @box-shadow-medium;

    [theme-mode="dark"] & {
      box-shadow: @box-shadow-medium-dark;
    }
  }
}

// 详情页布局
.detail-page-layout() {
  .page-container();
  max-width: @detail-page-max-width;
}

.detail-page-header() {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: @detail-page-header-height;
  margin-bottom: @detail-page-section-margin;
  padding-bottom: @spacing-lg;
  border-bottom: 1px solid @border-color;

  [theme-mode="dark"] & {
    border-bottom-color: @border-color-dark;
  }

  .respond-to(md) {
    flex-direction: column;
    align-items: stretch;
    gap: @spacing-md;
  }
}

.detail-page-section() {
  .card-container();
  padding: @card-padding;
  margin-bottom: @detail-page-section-margin;
}

// 表单布局
.form-section() {
  margin-bottom: @form-section-margin;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-section-title() {
  font-size: @font-size-lg;
  font-weight: @font-weight-semibold;
  color: @text-color-primary;
  margin-bottom: @spacing-lg;
  padding-bottom: @spacing-sm;
  border-bottom: 1px solid @border-color;

  [theme-mode="dark"] & {
    color: @text-color-primary-dark;
    border-bottom-color: @border-color-dark;
  }
}

// 操作按钮区域
.action-bar() {
  display: flex;
  align-items: center;
  gap: @action-button-gap;
  padding: @action-bar-padding;
  margin-top: @action-bar-margin-top;
  border-top: @action-bar-border-top;
  background: @bg-color-container;

  [theme-mode="dark"] & {
    background: @bg-color-container-dark;
    border-top-color: @border-color-dark;
  }

  &.sticky {
    position: sticky;
    bottom: 0;
    z-index: @z-index-sticky;
  }

  .respond-to(md) {
    flex-direction: column;
    align-items: stretch;
  }
}

// 状态指示器
.status-indicator(@color: @text-color-placeholder) {
  display: inline-flex;
  align-items: center;

  &::before {
    content: '';
    width: @status-indicator-size;
    height: @status-indicator-size;
    border-radius: @border-radius-round;
    background-color: @color;
    margin-right: @status-indicator-margin;
  }
}

// 分割线
.section-divider() {
  margin: @section-divider-margin;
  border: none;
  border-top: 1px solid @border-color;

  [theme-mode="dark"] & {
    border-top-color: @border-color-dark;
  }
}

.content-divider() {
  margin: @content-divider-margin;
  border: none;
  border-top: 1px solid @border-color;

  [theme-mode="dark"] & {
    border-top-color: @border-color-dark;
  }
}
