/**
 * 认证工具函数
 */

// Token存储键名
const TOKEN_KEY = import.meta.env.VITE_JWT_TOKEN_KEY || 'cloudvps_admin_token'
const REFRESH_TOKEN_KEY = import.meta.env.VITE_JWT_REFRESH_TOKEN_KEY || 'cloudvps_admin_refresh_token'

/**
 * 获取Token
 */
export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 设置Token
 */
export function setToken(token: string): void {
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除Token
 */
export function removeToken(): void {
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(REFRESH_TOKEN_KEY)
}

/**
 * 获取刷新Token
 */
export function getRefreshToken(): string | null {
  return localStorage.getItem(REFRESH_TOKEN_KEY)
}

/**
 * 设置刷新Token
 */
export function setRefreshToken(refreshToken: string): void {
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
}

/**
 * 检查Token是否存在
 */
export function hasToken(): boolean {
  return !!getToken()
}

/**
 * 解析JWT Token
 */
export function parseJWT(token: string): any {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('解析JWT失败:', error)
    return null
  }
}

/**
 * 检查Token是否过期
 */
export function isTokenExpired(token: string): boolean {
  try {
    const payload = parseJWT(token)
    if (!payload || !payload.exp) {
      return true
    }
    
    const currentTime = Math.floor(Date.now() / 1000)
    return payload.exp < currentTime
  } catch (error) {
    return true
  }
}

/**
 * 获取Token过期时间
 */
export function getTokenExpiration(token: string): Date | null {
  try {
    const payload = parseJWT(token)
    if (!payload || !payload.exp) {
      return null
    }
    
    return new Date(payload.exp * 1000)
  } catch (error) {
    return null
  }
}

/**
 * 获取Token剩余有效时间（秒）
 */
export function getTokenRemainingTime(token: string): number {
  try {
    const payload = parseJWT(token)
    if (!payload || !payload.exp) {
      return 0
    }
    
    const currentTime = Math.floor(Date.now() / 1000)
    const remainingTime = payload.exp - currentTime
    return Math.max(0, remainingTime)
  } catch (error) {
    return 0
  }
}

/**
 * 检查Token是否即将过期（默认5分钟内）
 */
export function isTokenExpiringSoon(token: string, threshold = 300): boolean {
  const remainingTime = getTokenRemainingTime(token)
  return remainingTime > 0 && remainingTime <= threshold
}

/**
 * 从Token中获取用户信息
 */
export function getUserFromToken(token: string): any {
  try {
    const payload = parseJWT(token)
    if (!payload) {
      return null
    }
    
    return {
      id: payload.sub,
      username: payload.username,
      role: payload.role,
      status: payload.status
    }
  } catch (error) {
    return null
  }
}

/**
 * 验证Token格式
 */
export function isValidTokenFormat(token: string): boolean {
  if (!token || typeof token !== 'string') {
    return false
  }
  
  // JWT应该有三个部分，用.分隔
  const parts = token.split('.')
  return parts.length === 3
}

/**
 * 清除所有认证相关数据
 */
export function clearAuthData(): void {
  removeToken()
  // 清除其他可能的认证相关数据
  localStorage.removeItem('user_info')
  localStorage.removeItem('permissions')
  localStorage.removeItem('menus')
}

/**
 * 生成随机字符串（用于状态参数等）
 */
export function generateRandomString(length = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 创建安全的重定向URL
 */
export function createSafeRedirectUrl(url: string, baseUrl?: string): string {
  try {
    const redirectUrl = new URL(url, baseUrl || window.location.origin)
    
    // 只允许同源重定向
    if (redirectUrl.origin !== window.location.origin) {
      return '/'
    }
    
    return redirectUrl.pathname + redirectUrl.search + redirectUrl.hash
  } catch (error) {
    return '/'
  }
}

/**
 * 检查是否为安全的重定向URL
 */
export function isSafeRedirectUrl(url: string): boolean {
  try {
    const redirectUrl = new URL(url, window.location.origin)
    return redirectUrl.origin === window.location.origin
  } catch (error) {
    return false
  }
}
