/**
 * 格式化工具函数
 */

import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 格式化日期时间
 */
export function formatDate(
  date: string | number | Date,
  format = 'YYYY-MM-DD HH:mm:ss'
): string {
  if (!date) return '-'
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 */
export function formatRelativeTime(date: string | number | Date): string {
  if (!date) return '-'
  return dayjs(date).fromNow()
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 */
export function formatNumber(
  num: number,
  options: {
    precision?: number
    separator?: string
    prefix?: string
    suffix?: string
  } = {}
): string {
  const {
    precision = 0,
    separator = ',',
    prefix = '',
    suffix = ''
  } = options
  
  if (isNaN(num)) return '-'
  
  const fixed = num.toFixed(precision)
  const parts = fixed.split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  
  return prefix + parts.join('.') + suffix
}

/**
 * 格式化货币
 */
export function formatCurrency(
  amount: number,
  currency = '¥',
  precision = 2
): string {
  return formatNumber(amount, {
    precision,
    separator: ',',
    prefix: currency
  })
}

/**
 * 格式化百分比
 */
export function formatPercentage(
  value: number,
  precision = 1
): string {
  if (isNaN(value)) return '-'
  return (value * 100).toFixed(precision) + '%'
}

/**
 * 格式化手机号
 */
export function formatPhone(phone: string): string {
  if (!phone) return '-'
  
  // 中国大陆手机号格式化
  if (/^1[3-9]\d{9}$/.test(phone)) {
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
  
  return phone
}

/**
 * 格式化身份证号
 */
export function formatIdCard(idCard: string): string {
  if (!idCard) return '-'
  
  if (idCard.length === 18) {
    return idCard.replace(/(\d{6})(\d{8})(\d{4})/, '$1 $2 $3')
  }
  
  return idCard
}

/**
 * 格式化银行卡号
 */
export function formatBankCard(cardNo: string): string {
  if (!cardNo) return '-'
  
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1 ')
}

/**
 * 脱敏处理
 */
export const desensitize = {
  // 手机号脱敏
  phone: (phone: string): string => {
    if (!phone || phone.length < 11) return phone
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  },
  
  // 邮箱脱敏
  email: (email: string): string => {
    if (!email || !email.includes('@')) return email
    const [username, domain] = email.split('@')
    if (username.length <= 2) return email
    return username.substring(0, 2) + '***@' + domain
  },
  
  // 身份证脱敏
  idCard: (idCard: string): string => {
    if (!idCard || idCard.length < 8) return idCard
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  },
  
  // 银行卡脱敏
  bankCard: (cardNo: string): string => {
    if (!cardNo || cardNo.length < 8) return cardNo
    return cardNo.replace(/(\d{4})\d+(\d{4})/, '$1****$2')
  },
  
  // 姓名脱敏
  name: (name: string): string => {
    if (!name || name.length < 2) return name
    if (name.length === 2) return name.charAt(0) + '*'
    return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
  }
}

/**
 * 格式化地址
 */
export function formatAddress(
  province?: string,
  city?: string,
  district?: string,
  detail?: string
): string {
  const parts = [province, city, district, detail].filter(Boolean)
  return parts.join(' ') || '-'
}

/**
 * 格式化时长
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`
  } else if (seconds < 86400) {
    const hours = Math.floor(seconds / 3600)
    const remainingMinutes = Math.floor((seconds % 3600) / 60)
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  } else {
    const days = Math.floor(seconds / 86400)
    const remainingHours = Math.floor((seconds % 86400) / 3600)
    return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`
  }
}

/**
 * 格式化IP地址
 */
export function formatIpAddress(ip: string): string {
  if (!ip) return '-'
  
  // IPv4格式验证
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
  if (ipv4Regex.test(ip)) {
    return ip
  }
  
  // IPv6格式处理
  if (ip.includes(':')) {
    return ip.toLowerCase()
  }
  
  return ip
}

/**
 * 格式化版本号
 */
export function formatVersion(version: string): string {
  if (!version) return '-'
  
  // 移除前缀v
  if (version.startsWith('v')) {
    version = version.substring(1)
  }
  
  return `v${version}`
}

/**
 * 格式化状态文本
 */
export function formatStatus(
  status: string,
  statusMap: Record<string, string>
): string {
  return statusMap[status] || status
}

/**
 * 格式化枚举值
 */
export function formatEnum<T extends Record<string, string>>(
  value: keyof T,
  enumMap: T
): string {
  return enumMap[value] || String(value)
}

/**
 * 截断文本
 */
export function truncateText(
  text: string,
  maxLength: number,
  suffix = '...'
): string {
  if (!text || text.length <= maxLength) return text
  return text.substring(0, maxLength) + suffix
}

/**
 * 首字母大写
 */
export function capitalize(str: string): string {
  if (!str) return str
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 驼峰转下划线
 */
export function camelToSnake(str: string): string {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

/**
 * 下划线转驼峰
 */
export function snakeToCamel(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 格式化JSON
 */
export function formatJson(obj: any, indent = 2): string {
  try {
    return JSON.stringify(obj, null, indent)
  } catch (error) {
    return String(obj)
  }
}

/**
 * 安全的JSON解析
 */
export function safeJsonParse<T = any>(str: string, defaultValue: T): T {
  try {
    return JSON.parse(str)
  } catch (error) {
    return defaultValue
  }
}
