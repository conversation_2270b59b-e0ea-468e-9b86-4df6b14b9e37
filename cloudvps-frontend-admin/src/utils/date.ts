/**
 * 日期时间工具函数
 */

import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 格式化日期时间
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的字符串
 */
export function formatDateTime(date: string | Date | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) return '-'
  return dayjs(date).format(format)
}

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的字符串
 */
export function formatDate(date: string | Date | number, format = 'YYYY-MM-DD'): string {
  if (!date) return '-'
  return dayjs(date).format(format)
}

/**
 * 格式化时间
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的字符串
 */
export function formatTime(date: string | Date | number, format = 'HH:mm:ss'): string {
  if (!date) return '-'
  return dayjs(date).format(format)
}

/**
 * 相对时间
 * @param date 日期
 * @returns 相对时间字符串
 */
export function fromNow(date: string | Date | number): string {
  if (!date) return '-'
  return dayjs(date).fromNow()
}

/**
 * 获取今天的开始时间
 * @returns 今天开始时间
 */
export function getStartOfToday(): Date {
  return dayjs().startOf('day').toDate()
}

/**
 * 获取今天的结束时间
 * @returns 今天结束时间
 */
export function getEndOfToday(): Date {
  return dayjs().endOf('day').toDate()
}

/**
 * 获取本周的开始时间
 * @returns 本周开始时间
 */
export function getStartOfWeek(): Date {
  return dayjs().startOf('week').toDate()
}

/**
 * 获取本周的结束时间
 * @returns 本周结束时间
 */
export function getEndOfWeek(): Date {
  return dayjs().endOf('week').toDate()
}

/**
 * 获取本月的开始时间
 * @returns 本月开始时间
 */
export function getStartOfMonth(): Date {
  return dayjs().startOf('month').toDate()
}

/**
 * 获取本月的结束时间
 * @returns 本月结束时间
 */
export function getEndOfMonth(): Date {
  return dayjs().endOf('month').toDate()
}

/**
 * 判断是否为今天
 * @param date 日期
 * @returns 是否为今天
 */
export function isToday(date: string | Date | number): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为本周
 * @param date 日期
 * @returns 是否为本周
 */
export function isThisWeek(date: string | Date | number): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 判断是否为本月
 * @param date 日期
 * @returns 是否为本月
 */
export function isThisMonth(date: string | Date | number): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 计算两个日期之间的天数差
 * @param date1 日期1
 * @param date2 日期2
 * @returns 天数差
 */
export function diffInDays(date1: string | Date | number, date2: string | Date | number): number {
  return dayjs(date1).diff(dayjs(date2), 'day')
}

/**
 * 计算两个日期之间的小时差
 * @param date1 日期1
 * @param date2 日期2
 * @returns 小时差
 */
export function diffInHours(date1: string | Date | number, date2: string | Date | number): number {
  return dayjs(date1).diff(dayjs(date2), 'hour')
}

/**
 * 计算两个日期之间的分钟差
 * @param date1 日期1
 * @param date2 日期2
 * @returns 分钟差
 */
export function diffInMinutes(date1: string | Date | number, date2: string | Date | number): number {
  return dayjs(date1).diff(dayjs(date2), 'minute')
}

/**
 * 添加天数
 * @param date 日期
 * @param days 天数
 * @returns 新日期
 */
export function addDays(date: string | Date | number, days: number): Date {
  return dayjs(date).add(days, 'day').toDate()
}

/**
 * 减去天数
 * @param date 日期
 * @param days 天数
 * @returns 新日期
 */
export function subtractDays(date: string | Date | number, days: number): Date {
  return dayjs(date).subtract(days, 'day').toDate()
}

/**
 * 获取日期范围的快捷选项
 * @returns 日期范围选项
 */
export function getDateRangePresets() {
  const today = dayjs()
  
  return [
    {
      label: '今天',
      value: [today.startOf('day').toDate(), today.endOf('day').toDate()]
    },
    {
      label: '昨天',
      value: [
        today.subtract(1, 'day').startOf('day').toDate(),
        today.subtract(1, 'day').endOf('day').toDate()
      ]
    },
    {
      label: '最近7天',
      value: [today.subtract(6, 'day').startOf('day').toDate(), today.endOf('day').toDate()]
    },
    {
      label: '最近30天',
      value: [today.subtract(29, 'day').startOf('day').toDate(), today.endOf('day').toDate()]
    },
    {
      label: '本周',
      value: [today.startOf('week').toDate(), today.endOf('week').toDate()]
    },
    {
      label: '本月',
      value: [today.startOf('month').toDate(), today.endOf('month').toDate()]
    },
    {
      label: '上月',
      value: [
        today.subtract(1, 'month').startOf('month').toDate(),
        today.subtract(1, 'month').endOf('month').toDate()
      ]
    }
  ]
}

/**
 * 验证日期格式
 * @param date 日期字符串
 * @param format 格式
 * @returns 是否有效
 */
export function isValidDate(date: string, format?: string): boolean {
  return dayjs(date, format).isValid()
}

/**
 * 获取时间戳
 * @param date 日期
 * @returns 时间戳
 */
export function getTimestamp(date?: string | Date | number): number {
  return dayjs(date).valueOf()
}

/**
 * 从时间戳创建日期
 * @param timestamp 时间戳
 * @returns 日期对象
 */
export function fromTimestamp(timestamp: number): Date {
  return dayjs(timestamp).toDate()
}
