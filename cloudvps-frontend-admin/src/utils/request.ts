/**
 * HTTP请求工具
 */

import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { MessagePlugin, LoadingPlugin } from 'tdesign-vue-next'
import { useAuthStore } from '@/stores/modules/auth'
import router from '@/router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'


// 配置NProgress
NProgress.configure({
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

// 请求配置
const config = {
  baseURL: '/api', // 不在这里设置前缀，由API路径构建器处理
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest'
  },
  withCredentials: import.meta.env.VITE_CORS_CREDENTIALS === 'true'
}

// 创建axios实例
const instance: AxiosInstance = axios.create(config)

// 调试模式
const isDebug = import.meta.env.VITE_DEBUG_API === 'true'

// 打印API配置信息（调试用）
if (isDebug) {
  console.log('🔧 API Configuration:', {
    baseURL: config.baseURL,
    timeout: config.timeout
  })
}

// 请求计数器
let requestCount = 0
let loadingInstance: any = null

// 显示加载
const showLoading = () => {
  if (requestCount === 0) {
    NProgress.start()
    loadingInstance = LoadingPlugin({
      text: '加载中...',
      delay: 300
    })
  }
  requestCount++
}

// 隐藏加载
const hideLoading = () => {
  requestCount--
  if (requestCount <= 0) {
    requestCount = 0
    NProgress.done()
    if (loadingInstance) {
      loadingInstance.hide()
      loadingInstance = null
    }
  }
}

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 显示加载状态
    showLoading()
    
    // 添加认证token
    const authStore = useAuthStore()
    const token = authStore.token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 调试信息
    if (isDebug) {
      console.log('🚀 API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        baseURL: config.baseURL,
        fullURL: `${config.baseURL}${config.url}`,
        headers: config.headers,
        data: config.data,
        params: config.params
      })
    }

    return config
  },
  (error) => {
    hideLoading()
    if (isDebug) {
      console.error('🔴 Request Config Error:', error)
    }
    MessagePlugin.error('请求配置错误')
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  (response: AxiosResponse) => {
    hideLoading()

    // 调试信息
    if (isDebug) {
      console.log('✅ API Response:', {
        status: response.status,
        statusText: response.statusText,
        url: response.config.url,
        data: response.data
      })
    }

    // 检查是否是标准的API响应格式
    if (response.data && typeof response.data === 'object' && 'code' in response.data) {
      const { code, message, data } = response.data

      // 成功响应
      if (code === 200) {
        return { ...response, data }
      }

      // 业务错误
      MessagePlugin.error(message || '请求失败')
      return Promise.reject(new Error(message || '请求失败'))
    }

    // 非标准响应格式，直接返回
    return response
  },
  async (error) => {
    hideLoading()

    const { response, config } = error
    const authStore = useAuthStore()

    if (response) {
      const { status, data } = response

      switch (status) {
        case 401:
          // 未授权，尝试刷新token
          if (authStore.refreshToken && !config._retry) {
            config._retry = true
            try {
              console.log('🔄 尝试刷新Token...')
              await authStore.refreshTokenAction()
              console.log('✅ Token刷新成功，重试原请求')
              // 重新发送原请求
              config.headers.Authorization = `Bearer ${authStore.token}`
              return instance.request(config)
            } catch (refreshError) {
              console.error('❌ Token刷新失败:', refreshError)
              // 刷新失败，清除认证信息并跳转到登录页
              authStore.clearAuthData()
              // 避免在登录页面重复跳转
              if (router.currentRoute.value.path !== '/login') {
                router.push({
                  path: '/login',
                  query: { redirect: router.currentRoute.value.fullPath }
                })
              }
              MessagePlugin.error('登录已过期，请重新登录')
              return Promise.reject(refreshError)
            }
          } else {
            console.log('❌ 没有refresh token或已经重试过')
            // 没有refresh token或已经重试过，直接跳转登录页
            authStore.clearAuthData()
            // 避免在登录页面重复跳转
            if (router.currentRoute.value.path !== '/login') {
              router.push({
                path: '/login',
                query: { redirect: router.currentRoute.value.fullPath }
              })
            }
            MessagePlugin.error('登录已过期，请重新登录')
          }
          break
          
        case 403:
          // 禁止访问
          MessagePlugin.error('没有权限访问该资源')
          break
          
        case 404:
          // 资源不存在
          MessagePlugin.error('请求的资源不存在')
          break
          
        case 422:
          // 参数验证错误
          const errors = data?.errors
          if (errors && typeof errors === 'object') {
            const errorMessages = Object.values(errors).flat()
            MessagePlugin.error(errorMessages.join(', '))
          } else {
            MessagePlugin.error(data?.message || '参数验证失败')
          }
          break
          
        case 429:
          // 请求过于频繁
          MessagePlugin.error('请求过于频繁，请稍后重试')
          break
          
        case 500:
          // 服务器内部错误
          MessagePlugin.error('服务器内部错误，请稍后重试')
          break
          
        case 502:
        case 503:
        case 504:
          // 服务不可用
          MessagePlugin.error('服务暂时不可用，请稍后重试')
          break
          
        default:
          MessagePlugin.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      // 请求超时
      if (isDebug) {
        console.error('🔴 Request Timeout:', error)
      }
      MessagePlugin.error('请求超时，请检查网络连接')
    } else if (error.message === 'Network Error') {
      // 网络错误
      if (isDebug) {
        console.error('🔴 Network Error:', error)
      }
      MessagePlugin.error('网络连接失败，请检查网络设置')
    } else if (error.message?.includes('CORS')) {
      // CORS跨域错误
      if (isDebug) {
        console.error('🔴 CORS Error:', error)
      }
      MessagePlugin.error('跨域请求被阻止，请检查服务器配置')
    } else if (error.code === 'ERR_NETWORK') {
      // 网络连接错误
      if (isDebug) {
        console.error('🔴 Network Connection Error:', error)
      }
      MessagePlugin.error('无法连接到服务器，请检查网络或服务器状态')
    } else {
      // 其他错误
      if (isDebug) {
        console.error('🔴 Unknown Error:', error)
      }
      MessagePlugin.error(error.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 封装请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) =>
    instance.get<T>(url, config),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    instance.post<T>(url, data, config),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    instance.put<T>(url, data, config),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig) =>
    instance.delete<T>(url, config),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    instance.patch<T>(url, data, config),
  
  upload: <T = any>(url: string, formData: FormData, config?: AxiosRequestConfig) =>
    instance.post<T>(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      }
    }),
  
  download: (url: string, filename?: string, config?: AxiosRequestConfig) =>
    instance.get(url, {
      ...config,
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
}

// 取消请求
export const CancelToken = axios.CancelToken
export const isCancel = axios.isCancel

// 创建取消令牌
export const createCancelToken = () => CancelToken.source()

export default instance
