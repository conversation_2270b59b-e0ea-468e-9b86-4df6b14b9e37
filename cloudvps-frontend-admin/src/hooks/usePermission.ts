/**
 * 权限检查Hook
 */

import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

/**
 * 权限检查Hook
 */
export function usePermission() {
  const userStore = useUserStore()

  // 当前用户权限列表
  const permissions = computed(() => userStore.permissions || [])

  // 检查是否有指定权限
  const hasPermission = (permission: string | string[]): boolean => {
    if (!permission) return true
    
    // 超级管理员拥有所有权限
    if (userStore.isSuperAdmin) return true
    
    const userPermissions = permissions.value
    
    if (Array.isArray(permission)) {
      // 数组权限：需要拥有其中任意一个权限
      return permission.some(p => userPermissions.includes(p))
    } else {
      // 单个权限
      return userPermissions.includes(permission)
    }
  }

  // 检查是否拥有所有指定权限
  const hasAllPermissions = (permissionList: string[]): boolean => {
    if (!permissionList || permissionList.length === 0) return true
    
    // 超级管理员拥有所有权限
    if (userStore.isSuperAdmin) return true
    
    const userPermissions = permissions.value
    return permissionList.every(permission => userPermissions.includes(permission))
  }

  // 检查是否拥有任意一个权限
  const hasAnyPermission = (permissionList: string[]): boolean => {
    if (!permissionList || permissionList.length === 0) return true
    
    // 超级管理员拥有所有权限
    if (userStore.isSuperAdmin) return true
    
    const userPermissions = permissions.value
    return permissionList.some(permission => userPermissions.includes(permission))
  }

  // 检查是否有指定角色
  const hasRole = (role: string | string[]): boolean => {
    if (!role) return true
    
    const userRoles = userStore.roles || []
    
    if (Array.isArray(role)) {
      // 数组角色：需要拥有其中任意一个角色
      return role.some(r => userRoles.includes(r))
    } else {
      // 单个角色
      return userRoles.includes(role)
    }
  }

  // 检查是否拥有所有指定角色
  const hasAllRoles = (roleList: string[]): boolean => {
    if (!roleList || roleList.length === 0) return true
    
    const userRoles = userStore.roles || []
    return roleList.every(role => userRoles.includes(role))
  }

  // 检查是否拥有任意一个角色
  const hasAnyRole = (roleList: string[]): boolean => {
    if (!roleList || roleList.length === 0) return true
    
    const userRoles = userStore.roles || []
    return roleList.some(role => userRoles.includes(role))
  }

  // 检查是否是超级管理员
  const isSuperAdmin = computed(() => userStore.isSuperAdmin)

  // 检查是否是管理员
  const isAdmin = computed(() => {
    return userStore.isSuperAdmin || hasRole(['ADMIN', 'SYSTEM_ADMIN'])
  })

  return {
    permissions,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasRole,
    hasAllRoles,
    hasAnyRole,
    isSuperAdmin,
    isAdmin
  }
}
