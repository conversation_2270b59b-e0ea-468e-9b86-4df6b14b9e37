<template>
  <div class="layout-wrapper" :class="layoutWrapperClass">
    <!-- 侧边导航布局（唯一支持的布局模式） -->
    <t-layout class="layout-container">
      <!-- 侧边栏 -->
      <t-aside
        v-if="!isMobile && showSidebar"
        :width="sidebarWidth"
        class="layout-sidebar"
      >
        <AppSidebar />
      </t-aside>

      <!-- 移动端侧边栏抽屉 -->
      <t-drawer
        v-if="isMobile"
        v-model:visible="sidebarVisible"
        placement="left"
        :width="240"
        :show-overlay="true"
        class="mobile-sidebar"
      >
        <AppSidebar @menu-click="handleMobileMenuClick" />
      </t-drawer>

      <t-layout class="layout-main">
        <!-- 顶部导航 -->
        <t-header v-if="showHeader" class="layout-header">
          <AppHeader @toggle-sidebar="handleToggleSidebar" />
        </t-header>

        <!-- 面包屑导航 -->
        <div v-if="showBreadcrumb" class="layout-breadcrumb">
          <AppBreadcrumb />
        </div>

        <!-- 主要内容区域 -->
        <t-content class="layout-content">
          <router-view v-slot="{ Component, route }">
            <transition name="fade" mode="out-in">
              <keep-alive :include="cachedViews">
                <component :is="Component" :key="route.fullPath" />
              </keep-alive>
            </transition>
          </router-view>
        </t-content>

        <!-- 底部 -->
        <t-footer v-if="showFooter" class="layout-footer">
          <AppFooter />
        </t-footer>
      </t-layout>
    </t-layout>




    <!-- 设置面板 -->
    <AppSettings v-if="showSettings" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useAppStore } from '@/stores/modules/app'
import { useSettingsStore } from '@/stores/modules/settings'
import AppSidebar from '@/components/layout/Sidebar/index.vue'
import AppHeader from '@/components/layout/Header/index.vue'
import AppBreadcrumb from '@/components/layout/Breadcrumb/index.vue'
import AppFooter from '@/components/layout/Footer/index.vue'
import AppSettings from '@/components/layout/Settings/index.vue'

const appStore = useAppStore()
const settingsStore = useSettingsStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)
const sidebarOpened = computed(() => appStore.sidebarOpened)

// UI元素显示控制
const showBreadcrumb = computed(() => settingsStore.uiElements.breadcrumb)
const showSettings = computed(() => settingsStore.showSettingsPanel)
const showSidebar = computed(() => settingsStore.uiElements.sidebar)
const showHeader = computed(() => settingsStore.uiElements.header)
const showFooter = computed(() => settingsStore.uiElements.footer)

// 布局包装器类名（简化为只支持侧边栏布局）
const layoutWrapperClass = computed(() => {
  return [
    'layout-sidebar',
    {
      'layout-mobile': isMobile.value,
      'sidebar-collapsed': !sidebarOpened.value
    }
  ]
})

// 侧边栏宽度
const sidebarWidth = computed(() => {
  return sidebarOpened.value ? '240px' : '64px'
})

// 移动端侧边栏显示状态
const sidebarVisible = computed({
  get: () => sidebarOpened.value && isMobile.value,
  set: (value: boolean) => {
    if (value) {
      appStore.openSidebar()
    } else {
      appStore.closeSidebar()
    }
  }
})

// 缓存的视图
const cachedViews = ref<string[]>([])

// 处理侧边栏切换
const handleToggleSidebar = () => {
  appStore.toggleSidebar()
}

// 处理移动端菜单点击
const handleMobileMenuClick = () => {
  if (isMobile.value) {
    appStore.closeSidebar()
  }
}
</script>

<style scoped lang="less">
// ==================== 基础布局样式 ====================
.layout-wrapper {
  height: 100vh;
  overflow: hidden;
  background: var(--td-bg-color-page);
  color: var(--td-text-color-primary);
  transition: all 0.3s ease;
}

.layout-container {
  height: 100vh;
  overflow: hidden;
}

.layout-main {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.layout-content-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// ==================== 侧边栏样式 ====================
.layout-sidebar {
  background: var(--td-bg-color-container);
  border-right: 1px solid var(--td-border-level-1-color);
  transition: width 0.3s ease;
  overflow: hidden;
  z-index: 20;
}

.layout-sidebar-first {
  background: var(--td-bg-color-container);
  border-right: 1px solid var(--td-border-level-1-color);
  overflow: hidden;
  z-index: 21;
}

.layout-sidebar-second {
  background: var(--td-bg-color-container);
  border-right: 1px solid var(--td-border-level-1-color);
  overflow: hidden;
  z-index: 20;
}

// ==================== 头部样式 ====================
.layout-header {
  background: var(--td-bg-color-container);
  border-bottom: 1px solid var(--td-border-level-1-color);
  padding: 0;
  height: 64px;
  display: flex;
  align-items: center;
  z-index: 10;
  transition: all 0.3s ease;
}

.layout-header-with-nav {
  height: auto;
  min-height: 64px;
  flex-direction: column;
}

.layout-header-mix {
  position: relative;
  z-index: 30;
}

// ==================== 面包屑样式 ====================
.layout-breadcrumb {
  background: var(--td-bg-color-page);
  border-bottom: 1px solid var(--td-border-level-1-color);
  padding: 12px 24px;
  min-height: 48px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

// ==================== 内容区域样式 ====================
.layout-content {
  flex: 1;
  padding: 12px;
  overflow: auto;
  background: var(--td-bg-color-page);
  transition: all 0.3s ease;
}

// ==================== 底部样式 ====================
.layout-footer {
  background: var(--td-bg-color-container);
  border-top: 1px solid var(--td-border-level-1-color);
  padding: 16px 24px;
  text-align: center;
  height: auto;
  transition: all 0.3s ease;
}

// ==================== 侧边栏布局样式 ====================
.layout-sidebar {
  .layout-container {
    display: flex;
  }
}

// ==================== 移动端样式 ====================
.mobile-sidebar {
  :deep(.t-drawer__body) {
    padding: 0;
    background: var(--td-bg-color-container);
  }
}

.layout-mobile {
  .layout-sidebar {
    display: none;
  }
}

// ==================== 侧边栏折叠状态 ====================
.sidebar-collapsed {
  .layout-sidebar {
    width: 64px !important;
  }
}

// ==================== 过渡动画 ====================
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .layout-content {
    padding: 16px;
  }
  
  .layout-breadcrumb {
    padding: 8px 16px;
  }
  
  .layout-footer {
    padding: 12px 16px;
  }
}

// ==================== UI元素控制 ====================
// 通过data属性控制UI元素显示/隐藏
:global(body[data-show-breadcrumb="false"]) .layout-breadcrumb {
  display: none !important;
}

:global(body[data-show-footer="false"]) .layout-footer {
  display: none !important;
}

:global(body[data-show-sidebar="false"]) .layout-sidebar,
:global(body[data-show-sidebar="false"]) .layout-sidebar-first,
:global(body[data-show-sidebar="false"]) .layout-sidebar-second {
  display: none !important;
}

:global(body[data-show-header="false"]) .layout-header {
  display: none !important;
}

// 固定头部样式
.fixed-header {
  .layout-header {
    position: fixed;
    top: 0;
    right: 0;
    left: 240px;
    z-index: 100;
  }
  
  .layout-breadcrumb {
    margin-top: 64px;
  }
  
  &.sidebar-collapsed {
    .layout-header {
      left: 64px;
    }
  }
  
  &.mobile {
    .layout-header {
      left: 0;
    }
  }
}
</style>
