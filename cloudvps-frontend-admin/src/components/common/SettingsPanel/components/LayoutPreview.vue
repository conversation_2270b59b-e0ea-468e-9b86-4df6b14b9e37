<template>
  <div class="layout-preview" :class="`layout-${mode}`">
    <!-- 侧边导航布局 -->
    <div v-if="mode === 'sidebar'" class="preview-sidebar-layout">
      <div class="preview-sidebar">
        <div class="sidebar-logo"></div>
        <div class="sidebar-menu">
          <div class="menu-item active"></div>
          <div class="menu-item"></div>
          <div class="menu-item"></div>
          <div class="menu-item"></div>
        </div>
      </div>
      <div class="preview-main">
        <div class="main-header">
          <div class="header-breadcrumb"></div>
          <div class="header-actions"></div>
        </div>
        <div class="main-content">
          <div class="content-block"></div>
          <div class="content-block"></div>
        </div>
      </div>
    </div>

    <!-- 顶部导航布局 -->
    <div v-else-if="mode === 'header'" class="preview-header-layout">
      <div class="preview-header">
        <div class="header-logo"></div>
        <div class="header-nav">
          <div class="nav-item active"></div>
          <div class="nav-item"></div>
          <div class="nav-item"></div>
        </div>
        <div class="header-actions"></div>
      </div>
      <div class="preview-content">
        <div class="content-block"></div>
        <div class="content-block"></div>
        <div class="content-block"></div>
      </div>
    </div>

    <!-- 混合布局 -->
    <div v-else-if="mode === 'mixed'" class="preview-mixed-layout">
      <div class="preview-header">
        <div class="header-logo"></div>
        <div class="header-nav">
          <div class="nav-item active"></div>
          <div class="nav-item"></div>
          <div class="nav-item"></div>
        </div>
        <div class="header-actions"></div>
      </div>
      <div class="preview-body">
        <div class="preview-sidebar">
          <div class="sidebar-menu">
            <div class="menu-item active"></div>
            <div class="menu-item"></div>
            <div class="menu-item"></div>
          </div>
        </div>
        <div class="preview-main">
          <div class="main-content">
            <div class="content-block"></div>
            <div class="content-block"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分割菜单布局 -->
    <div v-else-if="mode === 'split'" class="preview-split-layout">
      <div class="preview-sidebar">
        <div class="sidebar-icons">
          <div class="icon-item active"></div>
          <div class="icon-item"></div>
          <div class="icon-item"></div>
          <div class="icon-item"></div>
        </div>
        <div class="sidebar-submenu">
          <div class="submenu-header"></div>
          <div class="submenu-item active"></div>
          <div class="submenu-item"></div>
          <div class="submenu-item"></div>
        </div>
      </div>
      <div class="preview-main">
        <div class="main-header">
          <div class="header-breadcrumb"></div>
          <div class="header-actions"></div>
        </div>
        <div class="main-content">
          <div class="content-block"></div>
          <div class="content-block"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { LayoutMode } from '@/types/settings'

interface Props {
  mode: LayoutMode
}

defineProps<Props>()
</script>

<style scoped lang="less">
.layout-preview {
  width: 100%;
  height: 80px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 4px;
  overflow: hidden;
  background: var(--td-bg-color-page);
}

// 侧边导航布局
.preview-sidebar-layout {
  display: flex;
  height: 100%;
  
  .preview-sidebar {
    width: 24px;
    background: var(--td-bg-color-container);
    border-right: 1px solid var(--td-border-level-1-color);
    padding: 4px 2px;
    
    .sidebar-logo {
      width: 16px;
      height: 8px;
      background: var(--td-brand-color);
      border-radius: 1px;
      margin: 0 auto 4px;
    }
    
    .sidebar-menu {
      display: flex;
      flex-direction: column;
      gap: 2px;
      
      .menu-item {
        height: 6px;
        background: var(--td-bg-color-page);
        border-radius: 1px;
        
        &.active {
          background: var(--td-brand-color);
        }
      }
    }
  }
  
  .preview-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .main-header {
      height: 16px;
      background: var(--td-bg-color-container);
      border-bottom: 1px solid var(--td-border-level-1-color);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 4px;
      
      .header-breadcrumb {
        width: 32px;
        height: 4px;
        background: var(--td-text-color-placeholder);
        border-radius: 1px;
      }
      
      .header-actions {
        width: 12px;
        height: 4px;
        background: var(--td-text-color-placeholder);
        border-radius: 1px;
      }
    }
    
    .main-content {
      flex: 1;
      padding: 4px;
      display: flex;
      flex-direction: column;
      gap: 2px;
      
      .content-block {
        height: 12px;
        background: var(--td-bg-color-container);
        border-radius: 2px;
        border: 1px solid var(--td-border-level-1-color);
      }
    }
  }
}

// 顶部导航布局
.preview-header-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .preview-header {
    height: 20px;
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-1-color);
    display: flex;
    align-items: center;
    padding: 0 4px;
    gap: 8px;
    
    .header-logo {
      width: 12px;
      height: 8px;
      background: var(--td-brand-color);
      border-radius: 1px;
    }
    
    .header-nav {
      display: flex;
      gap: 4px;
      flex: 1;
      
      .nav-item {
        width: 16px;
        height: 4px;
        background: var(--td-text-color-placeholder);
        border-radius: 1px;
        
        &.active {
          background: var(--td-brand-color);
        }
      }
    }
    
    .header-actions {
      width: 12px;
      height: 4px;
      background: var(--td-text-color-placeholder);
      border-radius: 1px;
    }
  }
  
  .preview-content {
    flex: 1;
    padding: 4px;
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    .content-block {
      height: 12px;
      background: var(--td-bg-color-container);
      border-radius: 2px;
      border: 1px solid var(--td-border-level-1-color);
    }
  }
}

// 混合布局
.preview-mixed-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .preview-header {
    height: 16px;
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-1-color);
    display: flex;
    align-items: center;
    padding: 0 4px;
    gap: 6px;
    
    .header-logo {
      width: 10px;
      height: 6px;
      background: var(--td-brand-color);
      border-radius: 1px;
    }
    
    .header-nav {
      display: flex;
      gap: 3px;
      flex: 1;
      
      .nav-item {
        width: 12px;
        height: 3px;
        background: var(--td-text-color-placeholder);
        border-radius: 1px;
        
        &.active {
          background: var(--td-brand-color);
        }
      }
    }
    
    .header-actions {
      width: 8px;
      height: 3px;
      background: var(--td-text-color-placeholder);
      border-radius: 1px;
    }
  }
  
  .preview-body {
    flex: 1;
    display: flex;
    
    .preview-sidebar {
      width: 20px;
      background: var(--td-bg-color-container);
      border-right: 1px solid var(--td-border-level-1-color);
      padding: 2px;
      
      .sidebar-menu {
        display: flex;
        flex-direction: column;
        gap: 1px;
        
        .menu-item {
          height: 4px;
          background: var(--td-bg-color-page);
          border-radius: 1px;
          
          &.active {
            background: var(--td-brand-color);
          }
        }
      }
    }
    
    .preview-main {
      flex: 1;
      padding: 2px;
      
      .main-content {
        display: flex;
        flex-direction: column;
        gap: 1px;
        height: 100%;
        
        .content-block {
          height: 10px;
          background: var(--td-bg-color-container);
          border-radius: 1px;
          border: 1px solid var(--td-border-level-1-color);
        }
      }
    }
  }
}

// 分割菜单布局
.preview-split-layout {
  display: flex;
  height: 100%;
  
  .preview-sidebar {
    width: 32px;
    background: var(--td-bg-color-container);
    border-right: 1px solid var(--td-border-level-1-color);
    display: flex;
    
    .sidebar-icons {
      width: 12px;
      background: var(--td-bg-color-page);
      border-right: 1px solid var(--td-border-level-1-color);
      padding: 2px 1px;
      display: flex;
      flex-direction: column;
      gap: 2px;
      
      .icon-item {
        width: 8px;
        height: 6px;
        background: var(--td-text-color-placeholder);
        border-radius: 1px;
        
        &.active {
          background: var(--td-brand-color);
        }
      }
    }
    
    .sidebar-submenu {
      flex: 1;
      padding: 2px;
      
      .submenu-header {
        height: 4px;
        background: var(--td-text-color-placeholder);
        border-radius: 1px;
        margin-bottom: 2px;
      }
      
      .submenu-item {
        height: 3px;
        background: var(--td-bg-color-page);
        border-radius: 1px;
        margin-bottom: 1px;
        
        &.active {
          background: var(--td-brand-color);
        }
      }
    }
  }
  
  .preview-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .main-header {
      height: 12px;
      background: var(--td-bg-color-container);
      border-bottom: 1px solid var(--td-border-level-1-color);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 3px;
      
      .header-breadcrumb {
        width: 24px;
        height: 3px;
        background: var(--td-text-color-placeholder);
        border-radius: 1px;
      }
      
      .header-actions {
        width: 8px;
        height: 3px;
        background: var(--td-text-color-placeholder);
        border-radius: 1px;
      }
    }
    
    .main-content {
      flex: 1;
      padding: 2px;
      display: flex;
      flex-direction: column;
      gap: 1px;
      
      .content-block {
        height: 10px;
        background: var(--td-bg-color-container);
        border-radius: 1px;
        border: 1px solid var(--td-border-level-1-color);
      }
    }
  }
}
</style>
