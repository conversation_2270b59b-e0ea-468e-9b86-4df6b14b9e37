<template>
  <t-drawer
    v-model:visible="visible"
    title="系统设置"
    placement="right"
    size="400px"
    :footer="false"
    :close-on-esc-keydown="true"
    :close-on-overlay-click="true"
    class="settings-panel"
  >
    <div class="settings-content">
      <!-- 主题设置 -->
      <div class="setting-section">
        <div class="section-header">
          <h3 class="section-title">
            <SettingIcon class="section-icon" />
            主题设置
          </h3>
          <p class="section-description">选择您喜欢的主题模式</p>
        </div>
        
        <div class="setting-item">
          <label class="setting-label">主题模式</label>
          <t-radio-group 
            v-model="currentThemeMode" 
            variant="default-filled"
            @change="handleThemeModeChange"
          >
            <t-radio-button value="light">
              <SunnyIcon class="theme-icon" />
              浅色
            </t-radio-button>
            <t-radio-button value="dark">
              <MoonIcon class="theme-icon" />
              暗色
            </t-radio-button>
            <t-radio-button value="auto">
              <LaptopIcon class="theme-icon" />
              跟随系统
            </t-radio-button>
          </t-radio-group>
        </div>
        
        <div class="setting-item">
          <div class="theme-preview">
            <div class="preview-card" :class="{ 'preview-dark': settingsStore.isDarkTheme }">
              <div class="preview-header">
                <div class="preview-logo"></div>
                <div class="preview-nav">
                  <div class="nav-item active"></div>
                  <div class="nav-item"></div>
                  <div class="nav-item"></div>
                </div>
              </div>
              <div class="preview-content">
                <div class="content-sidebar">
                  <div class="sidebar-item active"></div>
                  <div class="sidebar-item"></div>
                  <div class="sidebar-item"></div>
                </div>
                <div class="content-main">
                  <div class="main-header"></div>
                  <div class="main-body">
                    <div class="body-item"></div>
                    <div class="body-item"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="preview-label">{{ getThemeModeLabel(settingsStore.actualThemeMode) }}</div>
          </div>
        </div>
      </div>

      <!-- 主颜色设置 -->
      <div class="setting-section">
        <div class="section-header">
          <h3 class="section-title">
            <PaletteIcon class="section-icon" />
            主颜色设置
          </h3>
          <p class="section-description">自定义系统主色调</p>
        </div>
        
        <div class="setting-item">
          <label class="setting-label">预设颜色</label>
          <div class="color-presets">
            <div 
              v-for="color in PRESET_THEME_COLORS" 
              :key="color.value"
              class="color-preset"
              :class="{ active: currentPrimaryColor === color.value }"
              :style="{ backgroundColor: color.value }"
              :title="color.description"
              @click="handleColorChange(color.value)"
            >
              <CheckIcon v-if="currentPrimaryColor === color.value" class="color-check" />
            </div>
          </div>
        </div>
        
        <div class="setting-item">
          <label class="setting-label">自定义颜色</label>
          <div class="custom-color-section">
            <t-color-picker
              v-model="currentPrimaryColor"
              :color-modes="['monochrome']"
              :enable-alpha="false"
              :swatch-colors="settingsStore.allColors"
              :recent-colors="settingsStore.recentColors"
              @change="handleColorPickerChange"
              class="color-picker"
            />
            <div class="color-info">
              <div class="color-value">{{ currentPrimaryColor }}</div>
              <t-button 
                size="small" 
                variant="text"
                @click="copyColorValue"
              >
                <CopyIcon />
                复制
              </t-button>
            </div>
          </div>
        </div>
        
        <div class="setting-item" v-if="settingsStore.recentColors.length > 0">
          <label class="setting-label">最近使用</label>
          <div class="recent-colors">
            <div 
              v-for="color in settingsStore.recentColors.slice(0, 8)" 
              :key="color"
              class="recent-color"
              :class="{ active: currentPrimaryColor === color }"
              :style="{ backgroundColor: color }"
              :title="color"
              @click="handleColorChange(color)"
            >
              <CheckIcon v-if="currentPrimaryColor === color" class="color-check" />
            </div>
          </div>
        </div>
      </div>

      <!-- 布局设置 -->
      <div class="setting-section">
        <div class="section-header">
          <h3 class="section-title">
            <ViewModuleIcon class="section-icon" />
            布局设置
          </h3>
          <p class="section-description">选择适合的布局模式</p>
        </div>
        
        <div class="setting-item">
          <div class="layout-options">
            <div 
              v-for="layout in LAYOUT_CONFIGS" 
              :key="layout.mode"
              class="layout-option"
              :class="{ active: currentLayoutMode === layout.mode }"
              @click="handleLayoutChange(layout.mode)"
            >
              <div class="layout-preview">
                <LayoutPreview :mode="layout.mode" />
              </div>
              <div class="layout-info">
                <div class="layout-name">{{ layout.name }}</div>
                <div class="layout-description">{{ layout.description }}</div>
              </div>
              <CheckIcon v-if="currentLayoutMode === layout.mode" class="layout-check" />
            </div>
          </div>
        </div>
      </div>

      <!-- 界面元素设置 -->
      <div class="setting-section">
        <div class="section-header">
          <h3 class="section-title">
            <ViewListIcon class="section-icon" />
            界面元素
          </h3>
          <p class="section-description">控制界面元素的显示</p>
        </div>
        
        <div class="ui-elements">
          <div 
            v-for="(value, key) in settingsStore.uiElements" 
            :key="key"
            class="ui-element-item"
          >
            <div class="element-info">
              <span class="element-name">{{ getUIElementName(key) }}</span>
              <span class="element-description">{{ getUIElementDescription(key) }}</span>
            </div>
            <t-switch 
              :value="value"
              @change="(val) => handleUIElementChange(key, val)"
            />
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="setting-actions">
        <t-space direction="vertical" size="small">
          <t-button theme="primary" block @click="applySettings">
            <CheckIcon />
            应用设置
          </t-button>
          <t-button variant="outline" block @click="resetSettings">
            <RefreshIcon />
            重置设置
          </t-button>
          <t-space>
            <t-button size="small" @click="exportSettings">
              <DownloadIcon />
              导出
            </t-button>
            <t-button size="small" @click="showImportDialog = true">
              <UploadIcon />
              导入
            </t-button>
          </t-space>
        </t-space>
      </div>
    </div>

    <!-- 导入设置对话框 -->
    <t-dialog
      v-model:visible="showImportDialog"
      title="导入设置"
      width="500px"
      @confirm="handleImportSettings"
    >
      <t-textarea
        v-model="importSettingsText"
        placeholder="请粘贴设置JSON数据"
        :rows="10"
        class="import-textarea"
      />
    </t-dialog>
  </t-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { useSettingsStore } from '@/stores/modules/settings'
import { PRESET_THEME_COLORS, LAYOUT_CONFIGS } from '@/types/settings'
import type { ThemeMode, LayoutMode, UIElementsConfig } from '@/types/settings'
import LayoutPreview from './components/LayoutPreview.vue'

// 导入图标
import {
  SettingIcon,
  SunnyIcon,
  MoonIcon,
  LaptopIcon,
  PaletteIcon,
  CheckIcon,
  CopyIcon,
  ViewModuleIcon,
  ViewListIcon,
  RefreshIcon,
  DownloadIcon,
  UploadIcon
} from 'tdesign-icons-vue-next'

// Props
interface Props {
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

// Store
const settingsStore = useSettingsStore()

// 响应式数据
const showImportDialog = ref(false)
const importSettingsText = ref('')

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const currentThemeMode = computed({
  get: () => settingsStore.themeMode,
  set: (value: ThemeMode) => settingsStore.setThemeMode(value)
})

const currentPrimaryColor = computed({
  get: () => settingsStore.primaryColor,
  set: (value: string) => settingsStore.setPrimaryColor(value)
})

const currentLayoutMode = computed({
  get: () => settingsStore.layoutMode,
  set: (value: LayoutMode) => settingsStore.setLayoutMode(value)
})

// 方法
const getThemeModeLabel = (mode: ThemeMode) => {
  const labels = {
    light: '浅色主题',
    dark: '暗色主题',
    auto: '跟随系统'
  }
  return labels[mode]
}

const getUIElementName = (key: string) => {
  const names: Record<string, string> = {
    breadcrumb: '面包屑导航',
    tabs: 'Tab标签页',
    sidebarCollapseButton: '侧边栏折叠按钮',
    footer: '页脚',
    header: '顶部导航',
    sidebar: '侧边栏',
    logo: 'Logo',
    settingsButton: '设置按钮'
  }
  return names[key] || key
}

const getUIElementDescription = (key: string) => {
  const descriptions: Record<string, string> = {
    breadcrumb: '显示当前页面路径',
    tabs: '显示多标签页导航',
    sidebarCollapseButton: '显示侧边栏折叠控制按钮',
    footer: '显示页面底部信息',
    header: '显示顶部导航栏',
    sidebar: '显示侧边导航栏',
    logo: '显示系统Logo',
    settingsButton: '显示设置按钮'
  }
  return descriptions[key] || ''
}

const handleThemeModeChange = (mode: ThemeMode) => {
  settingsStore.setThemeMode(mode)
  MessagePlugin.success(`已切换到${getThemeModeLabel(mode)}`)
}

const handleColorChange = (color: string) => {
  settingsStore.setPrimaryColor(color)
  MessagePlugin.success('主色调已更新')
}

const handleColorPickerChange = (value: string) => {
  settingsStore.setPrimaryColor(value)
}

const handleLayoutChange = (mode: LayoutMode) => {
  // 只允许侧边栏布局，其他布局显示提示
  if (mode !== 'sidebar') {
    const layout = LAYOUT_CONFIGS.find(l => l.mode === mode)
    MessagePlugin.warning(`${layout?.name}暂未开发，敬请期待`)
    return
  }

  settingsStore.setLayoutMode(mode)
  const layout = LAYOUT_CONFIGS.find(l => l.mode === mode)
  MessagePlugin.success(`已切换到${layout?.name}`)
}

const handleUIElementChange = (key: keyof UIElementsConfig, value: boolean) => {
  settingsStore.setUIElement(key, value)
}

const copyColorValue = async () => {
  try {
    await navigator.clipboard.writeText(currentPrimaryColor.value)
    MessagePlugin.success('颜色值已复制到剪贴板')
  } catch (error) {
    MessagePlugin.error('复制失败')
  }
}

const applySettings = () => {
  settingsStore.applyAllSettings()
  MessagePlugin.success('设置已应用')
}

const resetSettings = () => {
  settingsStore.resetSettings()
  MessagePlugin.success('设置已重置')
}

const exportSettings = () => {
  const settings = settingsStore.exportSettings()
  const blob = new Blob([settings], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'cloudvps-settings.json'
  a.click()
  URL.revokeObjectURL(url)
  MessagePlugin.success('设置已导出')
}

const handleImportSettings = () => {
  if (!importSettingsText.value.trim()) {
    MessagePlugin.error('请输入设置数据')
    return
  }
  
  const success = settingsStore.importSettings(importSettingsText.value)
  if (success) {
    MessagePlugin.success('设置导入成功')
    showImportDialog.value = false
    importSettingsText.value = ''
  } else {
    MessagePlugin.error('设置数据格式错误')
  }
}

// 注意：不再需要watch监听，因为store的showSettingsPanel已支持双向绑定
</script>

<style scoped lang="less">
.settings-panel {
  :deep(.t-drawer__content-wrapper) {
    background: var(--td-bg-color-page);
  }
}

.settings-content {
  padding: 0;
  height: 100%;
  overflow-y: auto;
}

.setting-section {
  padding: 24px;
  border-bottom: 1px solid var(--td-border-level-1-color);
  
  &:last-child {
    border-bottom: none;
  }
}

.section-header {
  margin-bottom: 20px;
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin: 0 0 8px 0;
    
    .section-icon {
      color: var(--td-brand-color);
    }
  }
  
  .section-description {
    font-size: 12px;
    color: var(--td-text-color-secondary);
    margin: 0;
  }
}

.setting-item {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .setting-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--td-text-color-primary);
    margin-bottom: 12px;
  }
}

// 主题预览
.theme-preview {
  margin-top: 16px;
  
  .preview-card {
    width: 100%;
    height: 120px;
    border: 2px solid var(--td-border-level-1-color);
    border-radius: 8px;
    overflow: hidden;
    background: #ffffff;
    transition: all 0.3s ease;
    
    &.preview-dark {
      background: #1a1a1a;
      
      .preview-header {
        background: #2a2a2a;
        border-bottom-color: #3a3a3a;
      }
      
      .content-sidebar {
        background: #2a2a2a;
        
        .sidebar-item {
          background: #3a3a3a;
          
          &.active {
            background: var(--td-brand-color);
          }
        }
      }
      
      .content-main {
        background: #1a1a1a;
        
        .main-header {
          background: #3a3a3a;
        }
        
        .body-item {
          background: #2a2a2a;
        }
      }
    }
  }
  
  .preview-header {
    height: 32px;
    background: #f5f5f5;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    padding: 0 12px;
    gap: 12px;
    
    .preview-logo {
      width: 16px;
      height: 16px;
      background: var(--td-brand-color);
      border-radius: 2px;
    }
    
    .preview-nav {
      display: flex;
      gap: 8px;
      
      .nav-item {
        width: 24px;
        height: 4px;
        background: #d0d0d0;
        border-radius: 2px;
        
        &.active {
          background: var(--td-brand-color);
        }
      }
    }
  }
  
  .preview-content {
    display: flex;
    height: calc(100% - 32px);
    
    .content-sidebar {
      width: 60px;
      background: #f0f0f0;
      padding: 8px;
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .sidebar-item {
        height: 8px;
        background: #d0d0d0;
        border-radius: 2px;
        
        &.active {
          background: var(--td-brand-color);
        }
      }
    }
    
    .content-main {
      flex: 1;
      background: #ffffff;
      padding: 8px;
      
      .main-header {
        height: 12px;
        background: #f0f0f0;
        border-radius: 2px;
        margin-bottom: 8px;
      }
      
      .main-body {
        display: flex;
        flex-direction: column;
        gap: 4px;
        
        .body-item {
          height: 8px;
          background: #f5f5f5;
          border-radius: 2px;
        }
      }
    }
  }
  
  .preview-label {
    text-align: center;
    font-size: 12px;
    color: var(--td-text-color-secondary);
    margin-top: 8px;
  }
}

// 颜色设置
.color-presets {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  
  .color-preset {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      transform: scale(1.1);
    }
    
    &.active {
      border-color: var(--td-text-color-primary);
    }
    
    .color-check {
      color: white;
      font-size: 16px;
      text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
    }
  }
}

.custom-color-section {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .color-picker {
    flex-shrink: 0;
  }
  
  .color-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    
    .color-value {
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 12px;
      color: var(--td-text-color-secondary);
      background: var(--td-bg-color-container);
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid var(--td-border-level-1-color);
    }
  }
}

.recent-colors {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  
  .recent-color {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    border: 1px solid var(--td-border-level-1-color);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      transform: scale(1.1);
    }
    
    &.active {
      border-color: var(--td-text-color-primary);
      border-width: 2px;
    }
    
    .color-check {
      color: white;
      font-size: 12px;
      text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
    }
  }
}

// 布局设置
.layout-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .layout-option {
    border: 2px solid var(--td-border-level-1-color);
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    
    &:hover {
      border-color: var(--td-brand-color);
    }
    
    &.active {
      border-color: var(--td-brand-color);
      background: var(--td-brand-color-light);
    }
    
    .layout-preview {
      margin-bottom: 12px;
    }
    
    .layout-info {
      .layout-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--td-text-color-primary);
        margin-bottom: 4px;
      }
      
      .layout-description {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        line-height: 1.4;
      }
    }
    
    .layout-check {
      position: absolute;
      top: 12px;
      right: 12px;
      color: var(--td-brand-color);
      font-size: 16px;
    }
  }
}

// UI元素设置
.ui-elements {
  .ui-element-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--td-border-level-1-color);
    
    &:last-child {
      border-bottom: none;
    }
    
    .element-info {
      flex: 1;
      
      .element-name {
        display: block;
        font-size: 14px;
        color: var(--td-text-color-primary);
        margin-bottom: 2px;
      }
      
      .element-description {
        font-size: 12px;
        color: var(--td-text-color-secondary);
      }
    }
  }
}

// 操作按钮
.setting-actions {
  padding: 24px;
  border-top: 1px solid var(--td-border-level-1-color);
  background: var(--td-bg-color-container);
}

.import-textarea {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

// 主题图标
.theme-icon {
  margin-right: 4px;
}

// 响应式设计
@media (max-width: 768px) {
  .settings-panel {
    :deep(.t-drawer) {
      width: 100% !important;
    }
  }
  
  .color-presets {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .custom-color-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
