<template>
  <div 
    class="cloudvps-logo" 
    :class="[
      `logo-${size}`,
      { 'logo-collapsed': collapsed },
      `logo-theme-${theme}`
    ]"
    @click="handleClick"
  >
    <!-- 图标部分 -->
    <div class="logo-icon">
      <svg
        :width="iconSize"
        :height="iconSize"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <!-- 渐变定义 -->
        <defs>
          <linearGradient :id="`gradient-${theme}-${Math.random()}`" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" :style="`stop-color:${primaryColor};stop-opacity:1`" />
            <stop offset="100%" :style="`stop-color:${secondaryColor};stop-opacity:1`" />
          </linearGradient>
        </defs>

        <!-- 云朵图标 -->
        <g class="cloud-icon">
          <!-- 主云朵 -->
          <path
            d="M8 24C8 20.6863 10.6863 18 14 18C14.3516 18 14.6953 18.0313 15.0273 18.0898C15.5742 15.7031 17.6016 14 20 14C22.7617 14 25 16.2383 25 19C25 19.1758 24.9922 19.3477 24.9766 19.5156C26.1875 20.4375 27 21.8906 27 23.5C27 26.2617 24.7617 28.5 22 28.5H10C8.89844 28.5 8 27.6016 8 26.5V24Z"
            :fill="`url(#gradient-${theme}-${Math.random()})`"
          />

          <!-- 小云朵装饰 -->
          <circle cx="30" cy="16" r="3" :fill="accentColor" opacity="0.7"/>
          <circle cx="34" cy="20" r="2" :fill="accentColor" opacity="0.5"/>
        </g>
      </svg>
    </div>
    
    <!-- 文字部分 -->
    <transition name="logo-text">
      <div v-if="!collapsed" class="logo-text">
        <div class="logo-title">{{ title }}</div>
        <div v-if="subtitle" class="logo-subtitle">{{ subtitle }}</div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props定义
interface Props {
  /** 是否折叠显示（仅显示图标） */
  collapsed?: boolean
  /** 尺寸大小 */
  size?: 'small' | 'medium' | 'large'
  /** 主题 */
  theme?: 'light' | 'dark' | 'auto'
  /** 主标题 */
  title?: string
  /** 副标题 */
  subtitle?: string
  /** 是否可点击 */
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
  size: 'medium',
  theme: 'auto',
  title: 'CloudVPS',
  subtitle: '云服务平台',
  clickable: false
})

// Emits定义
const emit = defineEmits<{
  (e: 'click'): void
}>()

// 计算属性
const iconSize = computed(() => {
  const sizes = {
    small: 24,
    medium: 32,
    large: 40
  }
  return sizes[props.size]
})

const primaryColor = computed(() => {
  if (props.theme === 'dark') {
    return '#4dabf7'
  } else if (props.theme === 'light') {
    return '#0052d9'
  } else {
    // auto theme - 使用CSS变量
    return '#0052d9' // 默认值，实际会被CSS变量覆盖
  }
})

const secondaryColor = computed(() => {
  if (props.theme === 'dark') {
    return '#74c0fc'
  } else if (props.theme === 'light') {
    return '#0084ff'
  } else {
    return '#0084ff'
  }
})

const accentColor = computed(() => {
  if (props.theme === 'dark') {
    return '#4dabf7'
  } else {
    return '#0084ff'
  }
})

// 方法
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>

<style scoped lang="less">
.cloudvps-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: default;
  transition: all 0.3s ease;
  
  &.logo-clickable {
    cursor: pointer;
    
    &:hover {
      opacity: 0.8;
    }
  }
  
  // 尺寸变体
  &.logo-small {
    gap: 8px;
    
    .logo-text {
      .logo-title {
        font-size: 14px;
      }
      
      .logo-subtitle {
        font-size: 10px;
      }
    }
  }
  
  &.logo-medium {
    gap: 12px;
    
    .logo-text {
      .logo-title {
        font-size: 16px;
      }
      
      .logo-subtitle {
        font-size: 12px;
      }
    }
  }
  
  &.logo-large {
    gap: 16px;
    
    .logo-text {
      .logo-title {
        font-size: 20px;
      }
      
      .logo-subtitle {
        font-size: 14px;
      }
    }
  }
  
  // 主题变体
  &.logo-theme-light {
    .logo-text {
      .logo-title {
        color: #1f2937;
      }
      
      .logo-subtitle {
        color: #0052d9;
      }
    }
  }
  
  &.logo-theme-dark {
    .logo-text {
      .logo-title {
        color: #ffffff;
      }
      
      .logo-subtitle {
        color: #4dabf7;
      }
    }
  }
  
  &.logo-theme-auto {
    .logo-text {
      .logo-title {
        color: var(--td-text-color-primary);
      }
      
      .logo-subtitle {
        color: var(--td-brand-color);
      }
    }
  }
}

.logo-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    transition: transform 0.3s ease;
    
    .cloud-icon {
      transition: all 0.3s ease;
    }
  }
  
  &:hover svg {
    transform: scale(1.05);
  }
}

.logo-text {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
  
  .logo-title {
    font-weight: 600;
    margin: 0;
    transition: color 0.3s ease;
  }
  
  .logo-subtitle {
    font-weight: 500;
    margin: 0;
    opacity: 0.8;
    transition: color 0.3s ease;
  }
}

// 折叠状态
.logo-collapsed {
  .logo-icon {
    margin: 0;
  }
}

// 文字动画
.logo-text-enter-active,
.logo-text-leave-active {
  transition: all 0.3s ease;
}

.logo-text-enter-from,
.logo-text-leave-to {
  opacity: 0;
  transform: translateX(-10px);
  width: 0;
}

.logo-text-enter-to,
.logo-text-leave-from {
  opacity: 1;
  transform: translateX(0);
  width: auto;
}

// 响应式设计
@media (max-width: 768px) {
  .cloudvps-logo {
    &.logo-large {
      gap: 12px;
      
      .logo-text {
        .logo-title {
          font-size: 18px;
        }
        
        .logo-subtitle {
          font-size: 12px;
        }
      }
    }
  }
}

// SVG渐变定义（通过CSS变量支持主题切换）
:deep(svg) {
  defs {
    linearGradient {
      &#gradient-light {
        stop:first-child {
          stop-color: #0052d9;
        }
        stop:last-child {
          stop-color: #0084ff;
        }
      }
      
      &#gradient-dark {
        stop:first-child {
          stop-color: #4dabf7;
        }
        stop:last-child {
          stop-color: #74c0fc;
        }
      }
      
      &#gradient-auto {
        stop:first-child {
          stop-color: var(--td-brand-color);
        }
        stop:last-child {
          stop-color: var(--td-brand-color-light);
        }
      }
    }
  }
}
</style>
