/**
 * Logo组件类型定义
 */

export type LogoSize = 'small' | 'medium' | 'large'

export type LogoTheme = 'light' | 'dark' | 'auto'

export interface LogoProps {
  /** 是否折叠显示（仅显示图标） */
  collapsed?: boolean
  /** 尺寸大小 */
  size?: LogoSize
  /** 主题 */
  theme?: LogoTheme
  /** 主标题 */
  title?: string
  /** 副标题 */
  subtitle?: string
  /** 是否可点击 */
  clickable?: boolean
}

export interface LogoEmits {
  (e: 'click'): void
}

export interface LogoSlots {
  /** 自定义图标插槽 */
  icon?: () => any
  /** 自定义文字插槽 */
  text?: () => any
}

/**
 * Logo配置选项
 */
export interface LogoConfig {
  /** 默认尺寸 */
  defaultSize: LogoSize
  /** 默认主题 */
  defaultTheme: LogoTheme
  /** 默认标题 */
  defaultTitle: string
  /** 默认副标题 */
  defaultSubtitle: string
  /** 是否启用动画 */
  enableAnimation: boolean
  /** 是否启用悬停效果 */
  enableHover: boolean
}

/**
 * Logo尺寸映射
 */
export const LOGO_SIZE_MAP: Record<LogoSize, {
  iconSize: number
  fontSize: {
    title: number
    subtitle: number
  }
  gap: number
}> = {
  small: {
    iconSize: 24,
    fontSize: {
      title: 14,
      subtitle: 10
    },
    gap: 8
  },
  medium: {
    iconSize: 32,
    fontSize: {
      title: 16,
      subtitle: 12
    },
    gap: 12
  },
  large: {
    iconSize: 40,
    fontSize: {
      title: 20,
      subtitle: 14
    },
    gap: 16
  }
}

/**
 * Logo主题颜色映射
 */
export const LOGO_THEME_MAP: Record<LogoTheme, {
  iconColor: string
  accentColor: string
  titleColor: string
  subtitleColor: string
}> = {
  light: {
    iconColor: '#0052d9',
    accentColor: '#0084ff',
    titleColor: '#1f2937',
    subtitleColor: '#0052d9'
  },
  dark: {
    iconColor: '#4dabf7',
    accentColor: '#74c0fc',
    titleColor: '#ffffff',
    subtitleColor: '#4dabf7'
  },
  auto: {
    iconColor: 'var(--td-brand-color)',
    accentColor: 'var(--td-brand-color-light)',
    titleColor: 'var(--td-text-color-primary)',
    subtitleColor: 'var(--td-brand-color)'
  }
}
