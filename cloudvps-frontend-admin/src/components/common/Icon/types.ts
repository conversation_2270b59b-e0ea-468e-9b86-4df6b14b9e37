/**
 * Icon组件类型定义
 */

export type IconType = 'tdesign' | 'svg' | 'url' | 'iconfont' | 'custom'

export type IconSize = string | number

export type IconTheme = 'primary' | 'success' | 'warning' | 'danger' | 'default'

export interface IconProps {
  /** 图标名称或内容 */
  name?: string
  /** 图标类型 */
  type?: IconType
  /** 图标大小 */
  size?: IconSize
  /** 图标颜色 */
  color?: string
  /** 自定义SVG内容 */
  svg?: string
  /** 远程图标URL */
  url?: string
  /** 图标字体类名前缀 */
  prefix?: string
  /** 是否可点击 */
  clickable?: boolean
  /** 自定义类名 */
  className?: string
  /** 旋转角度 */
  rotate?: number
  /** 是否旋转动画 */
  spin?: boolean
  /** 主题色 */
  theme?: IconTheme
}

export interface IconEmits {
  (e: 'click', event: MouseEvent): void
}

/**
 * 常用TDesign图标名称
 */
export const TDESIGN_ICONS = {
  // 基础图标
  add: 'add',
  close: 'close',
  check: 'check',
  arrow: 'arrow-right',
  arrowLeft: 'arrow-left',
  arrowUp: 'arrow-up',
  arrowDown: 'arrow-down',
  
  // 用户相关
  user: 'user',
  userCircle: 'user-circle',
  users: 'user-group',
  
  // 系统图标
  setting: 'setting',
  home: 'home',
  dashboard: 'dashboard',
  menu: 'menu',
  search: 'search',
  filter: 'filter',
  refresh: 'refresh',
  
  // 操作图标
  edit: 'edit',
  delete: 'delete',
  view: 'view',
  download: 'download',
  upload: 'upload',
  copy: 'copy',
  
  // 状态图标
  loading: 'loading',
  success: 'check-circle',
  warning: 'error-circle',
  error: 'close-circle',
  info: 'info-circle',
  
  // 安全图标
  lock: 'lock',
  unlock: 'lock-off',
  security: 'secured',
  key: 'key',
  
  // 文件图标
  file: 'file',
  folder: 'folder',
  image: 'image',
  video: 'video',
  
  // 通信图标
  mail: 'mail',
  phone: 'call',
  message: 'chat',
  notification: 'notification',
  
  // 时间图标
  time: 'time',
  calendar: 'calendar',
  
  // 其他
  heart: 'heart',
  star: 'star',
  flag: 'flag',
  tag: 'discount'
} as const

export type TDesignIconName = keyof typeof TDESIGN_ICONS

/**
 * 图标配置选项
 */
export interface IconConfig {
  /** 默认图标类型 */
  defaultType: IconType
  /** 默认图标大小 */
  defaultSize: IconSize
  /** 默认主题 */
  defaultTheme: IconTheme
  /** 图标字体前缀 */
  iconFontPrefix: string
  /** 是否启用远程图标缓存 */
  enableRemoteCache: boolean
  /** 远程图标缓存时间（毫秒） */
  remoteCacheTime: number
  /** 是否启用错误回退 */
  enableFallback: boolean
}

/**
 * 图标使用示例
 */
export const ICON_EXAMPLES = {
  tdesign: {
    basic: '<Icon name="user" type="tdesign" />',
    withSize: '<Icon name="user" type="tdesign" size="24px" />',
    withColor: '<Icon name="user" type="tdesign" color="#0052d9" />',
    withTheme: '<Icon name="user" type="tdesign" theme="primary" />',
    clickable: '<Icon name="user" type="tdesign" :clickable="true" @click="handleClick" />'
  },
  svg: {
    inline: '<Icon type="svg" :svg="svgContent" />',
    withSize: '<Icon type="svg" :svg="svgContent" size="32px" />'
  },
  url: {
    remote: '<Icon type="url" url="https://example.com/icon.svg" />',
    withFallback: '<Icon type="url" url="https://example.com/icon.svg" name="fallback" />'
  },
  iconfont: {
    basic: '<Icon name="home" type="iconfont" prefix="iconfont" />',
    custom: '<Icon name="custom-icon" type="iconfont" prefix="my-icons" />'
  }
}

/**
 * 图标工具函数类型
 */
export interface IconUtils {
  /** 验证图标名称是否有效 */
  validateIconName: (name: string, type: IconType) => boolean
  /** 获取图标组件名称 */
  getIconComponentName: (name: string) => string
  /** 格式化图标大小 */
  formatIconSize: (size: IconSize) => string
  /** 生成图标类名 */
  generateIconClass: (props: IconProps) => string[]
}

/**
 * 图标加载状态
 */
export interface IconLoadState {
  loading: boolean
  error: boolean
  loaded: boolean
  errorMessage?: string
}

/**
 * 远程图标缓存项
 */
export interface RemoteIconCacheItem {
  url: string
  content: string
  timestamp: number
  size: number
}

/**
 * 图标注册信息
 */
export interface IconRegistration {
  name: string
  type: IconType
  component?: any
  svg?: string
  url?: string
  metadata?: {
    author?: string
    license?: string
    version?: string
    description?: string
  }
}
