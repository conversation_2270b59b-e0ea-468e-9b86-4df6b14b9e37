<template>
  <component
    :is="iconComponent"
    v-if="iconComponent"
    :class="iconClasses"
    :style="iconStyles"
    v-bind="iconProps"
    @click="handleClick"
  />
  <span
    v-else-if="isCustomSvg || isRemoteUrl"
    :class="iconClasses"
    :style="iconStyles"
    v-html="svgContent"
    @click="handleClick"
  />
  <i
    v-else-if="isIconFont"
    :class="iconFontClasses"
    :style="iconStyles"
    @click="handleClick"
  />
  <span
    v-else
    :class="iconClasses"
    :style="iconStyles"
    @click="handleClick"
  >
    {{ fallbackText }}
  </span>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue'
import * as TDesignIcons from 'tdesign-icons-vue-next'

// Props定义
interface Props {
  /** 图标名称或内容 */
  name?: string
  /** 图标类型 */
  type?: 'tdesign' | 'svg' | 'url' | 'iconfont' | 'custom'
  /** 图标大小 */
  size?: string | number
  /** 图标颜色 */
  color?: string
  /** 自定义SVG内容 */
  svg?: string
  /** 远程图标URL */
  url?: string
  /** 图标字体类名前缀 */
  prefix?: string
  /** 是否可点击 */
  clickable?: boolean
  /** 自定义类名 */
  className?: string
  /** 旋转角度 */
  rotate?: number
  /** 是否旋转动画 */
  spin?: boolean
  /** 主题色 */
  theme?: 'primary' | 'success' | 'warning' | 'danger' | 'default'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'tdesign',
  size: '16px',
  color: '',
  prefix: 'icon',
  clickable: false,
  className: '',
  rotate: 0,
  spin: false,
  theme: 'default'
})

// Emits定义
const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>()

// 响应式数据
const svgContent = ref('')
const loading = ref(false)
const error = ref(false)

// 计算属性
const iconComponent = computed(() => {
  if (props.type === 'tdesign' && props.name) {
    // 转换图标名称为组件名称
    const componentName = props.name
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('') + 'Icon'
    
    return TDesignIcons[componentName as keyof typeof TDesignIcons] || null
  }
  return null
})

const isCustomSvg = computed(() => props.type === 'svg' && props.svg)
const isRemoteUrl = computed(() => props.type === 'url' && props.url)
const isIconFont = computed(() => props.type === 'iconfont' && props.name)

const iconClasses = computed(() => {
  const classes = ['cloudvps-icon']
  
  if (props.className) {
    classes.push(props.className)
  }
  
  if (props.clickable) {
    classes.push('cloudvps-icon--clickable')
  }
  
  if (props.spin) {
    classes.push('cloudvps-icon--spin')
  }
  
  if (props.theme !== 'default') {
    classes.push(`cloudvps-icon--${props.theme}`)
  }
  
  if (loading.value) {
    classes.push('cloudvps-icon--loading')
  }
  
  if (error.value) {
    classes.push('cloudvps-icon--error')
  }
  
  return classes
})

const iconFontClasses = computed(() => {
  const classes = [...iconClasses.value]
  
  if (props.prefix && props.name) {
    classes.push(`${props.prefix}-${props.name}`)
  }
  
  return classes
})

const iconStyles = computed(() => {
  const styles: Record<string, string> = {}
  
  // 尺寸
  if (props.size) {
    const size = typeof props.size === 'number' ? `${props.size}px` : props.size
    styles.fontSize = size
    styles.width = size
    styles.height = size
  }
  
  // 颜色
  if (props.color) {
    styles.color = props.color
  }
  
  // 旋转
  if (props.rotate) {
    styles.transform = `rotate(${props.rotate}deg)`
  }
  
  return styles
})

const iconProps = computed(() => {
  const baseProps: Record<string, any> = {}
  
  // 传递给TDesign图标的属性
  if (props.type === 'tdesign') {
    if (props.size) {
      baseProps.size = props.size
    }
  }
  
  return baseProps
})

const fallbackText = computed(() => {
  if (error.value) {
    return '❌'
  }
  if (loading.value) {
    return '⏳'
  }
  return props.name ? props.name.charAt(0).toUpperCase() : '?'
})

// 方法
const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event)
  }
}

const loadRemoteSvg = async (url: string) => {
  if (!url) return
  
  loading.value = true
  error.value = false
  
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }
    
    const text = await response.text()
    
    // 验证是否为有效的SVG
    if (text.includes('<svg')) {
      svgContent.value = text
    } else {
      throw new Error('Invalid SVG content')
    }
  } catch (err) {
    console.error('Failed to load remote SVG:', err)
    error.value = true
    svgContent.value = ''
  } finally {
    loading.value = false
  }
}

// 监听器
watch(
  () => props.url,
  (newUrl) => {
    if (props.type === 'url' && newUrl) {
      loadRemoteSvg(newUrl)
    }
  },
  { immediate: true }
)

watch(
  () => props.svg,
  (newSvg) => {
    if (props.type === 'svg' && newSvg) {
      svgContent.value = newSvg
    }
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  if (props.type === 'url' && props.url) {
    loadRemoteSvg(props.url)
  } else if (props.type === 'svg' && props.svg) {
    svgContent.value = props.svg
  }
})
</script>

<style scoped lang="less">
.cloudvps-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  line-height: 1;
  transition: all 0.2s ease;
  
  // 可点击状态
  &--clickable {
    cursor: pointer;
    
    &:hover {
      opacity: 0.8;
      transform: scale(1.1);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  // 旋转动画
  &--spin {
    animation: cloudvps-icon-spin 1s linear infinite;
  }
  
  // 主题色
  &--primary {
    color: var(--td-brand-color, #0052d9);
  }
  
  &--success {
    color: var(--td-success-color, #00a870);
  }
  
  &--warning {
    color: var(--td-warning-color, #ed7b2f);
  }
  
  &--danger {
    color: var(--td-error-color, #d54941);
  }
  
  // 状态
  &--loading {
    opacity: 0.6;
    animation: cloudvps-icon-pulse 1.5s ease-in-out infinite;
  }
  
  &--error {
    color: var(--td-error-color, #d54941);
    opacity: 0.6;
  }
  
  // SVG内容样式
  :deep(svg) {
    width: 1em;
    height: 1em;
    fill: currentColor;
    vertical-align: middle;
  }
}

// 动画定义
@keyframes cloudvps-icon-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes cloudvps-icon-pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .cloudvps-icon {
    &--clickable:hover {
      transform: none; // 移动端禁用hover效果
    }
  }
}
</style>
