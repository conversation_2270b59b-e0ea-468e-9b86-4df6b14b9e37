<template>
  <t-menu
    :value="activeMenuValue"
    :collapsed="collapsed"
    :expanded="expandedMenus"
    theme="light"
    @change="handleMenuChange"
    @expand="handleMenuExpand"
  >
    <template #logo>
      <Logo
        :collapsed="collapsed"
        size="medium"
        theme="auto"
        title="CloudVPS"
        subtitle="管理后台"
        :clickable="true"
        @click="handleLogoClick"
      />
    </template>

    <!-- 动态渲染菜单项 -->
    <template v-for="menuItem in filteredMenuItems" :key="menuItem.value">
      <!-- 有子菜单的情况 -->
      <t-submenu
        v-if="menuItem.children && menuItem.children.length > 0"
        :value="menuItem.value"
        :title="menuItem.title"
        :disabled="menuItem.disabled"
      >
        <template #icon>
          <Icon :name="menuItem.icon || 'file'" />
        </template>

        <template v-for="childItem in menuItem.children" :key="childItem.value">
          <t-menu-item
            :value="childItem.value"
            :to="childItem.path"
            :disabled="childItem.disabled"
            :href="childItem.href"
            :target="childItem.target"
          >
            <template v-if="childItem.icon" #icon>
              <Icon :name="childItem.icon" />
            </template>
            {{ childItem.title }}
          </t-menu-item>
        </template>
      </t-submenu>

      <!-- 没有子菜单的情况 -->
      <t-menu-item
        v-else
        :value="menuItem.value"
        :to="menuItem.path"
        :disabled="menuItem.disabled"
        :href="menuItem.href"
        :target="menuItem.target"
      >
        <template #icon>
          <Icon :name="menuItem.icon || 'file'" />
        </template>
        {{ menuItem.title }}
      </t-menu-item>
    </template>
  </t-menu>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useAppStore } from "@/stores/modules/app";
import { useUserStore } from "@/stores/modules/user";
import { menuConfig, filterMenuByPermissions, getActiveMenuValue } from "@/config/menu";
import Logo from "@/components/common/Logo/index.vue";
import Icon from "@/components/common/Icon/index.vue";
import type { MenuItem } from "@/types/menu";

// 路由和状态管理
const route = useRoute();
const router = useRouter();
const appStore = useAppStore();
const userStore = useUserStore();

// 响应式数据
const expandedMenus = ref<string[]>(menuConfig.defaultExpanded || []);

// 计算属性
const collapsed = computed(() => !appStore.sidebarOpened);

// 根据权限过滤菜单项
const filteredMenuItems = computed(() => {
  return filterMenuByPermissions(
    menuConfig.items,
    userStore.permissions || [],
    userStore.isSuperAdmin || false
  );
});

// 获取当前激活的菜单值
const activeMenuValue = computed(() => {
  return getActiveMenuValue(filteredMenuItems.value, route.path);
});

// 事件处理
const handleMenuChange = (value: string) => {
  console.log("📋 菜单切换:", value);
  emit("menu-click");
};

const handleMenuExpand = (value: string[]) => {
  expandedMenus.value = value;
  console.log("📂 菜单展开状态:", value);
};

const handleLogoClick = () => {
  router.push("/dashboard");
};

// 初始化
onMounted(() => {
  console.log("🎯 菜单组件初始化完成");
  console.log("📋 菜单配置:", menuConfig);
  console.log("🔐 用户权限:", userStore.permissions);
  console.log("👑 超级管理员:", userStore.isSuperAdmin);
  console.log("📂 过滤后菜单:", filteredMenuItems.value);
});

// 事件定义
const emit = defineEmits<{
  (e: "menu-click"): void;
}>();
</script>

<style scoped lang="less">
.app-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;

  .logo {
    display: flex;
    align-items: center;
    gap: 12px;

    img {
      width: 32px;
      height: 32px;
    }

    .logo-text {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}
</style>
