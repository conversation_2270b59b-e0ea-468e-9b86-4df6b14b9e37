/**
 * 布局组件导出
 */

// 基础布局组件
import Breadcrumb from './Breadcrumb/index.vue'
import Footer from './Footer/index.vue'
import Header from './Header/index.vue'
import Sidebar from './Sidebar/index.vue'
import Settings from './Settings/index.vue'



// 导出基础布局组件
export {
  Breadcrumb,
  Footer,
  Header,
  Sidebar,
  Settings
}



// 默认导出
export default {
  // 基础布局组件
  Breadcrumb,
  Footer,
  Header,
  Sidebar,
  Settings,

}

// 类型定义（从Vue组件中提取）
export interface FormTableLayoutProps {
  title?: string
  description?: string
  showHeader?: boolean
  showSearchForm?: boolean
  searchFormTitle?: string
  collapsible?: boolean
  defaultCollapsed?: boolean
  showDefaultActions?: boolean
  showToolbar?: boolean
  showDefaultToolbar?: boolean
  showRefreshButton?: boolean
  showExportButton?: boolean
  showSettingsButton?: boolean
}

export interface MasterDetailLayoutProps {
  title?: string
  description?: string
  showHeader?: boolean
  masterTitle?: string
  masterDescription?: string
  showMasterHeader?: boolean
  showMasterSearch?: boolean
  showDefaultSearch?: boolean
  searchPlaceholder?: string
  showDefaultMasterActions?: boolean
  showAddButton?: boolean
  addButtonText?: string
  showRefreshButton?: boolean
  collapsible?: boolean
  defaultCollapsed?: boolean
  detailTitle?: string
  detailDescription?: string
  showDetailHeader?: boolean
  showEmptyState?: boolean
  emptyImage?: string
  emptyDescription?: string
}

export interface CardGridLayoutProps {
  title?: string
  description?: string
  showHeader?: boolean
  showFilters?: boolean
  filtersCollapsible?: boolean
  defaultFiltersCollapsed?: boolean
  showToolbar?: boolean
  showDefaultToolbar?: boolean
  showViewToggle?: boolean
  defaultView?: 'grid' | 'list'
  showAddButton?: boolean
  addButtonText?: string
  showRefreshButton?: boolean
  showMoreActions?: boolean
  showStats?: boolean
  gridColumns?: number
  gridGap?: 'small' | 'medium' | 'large'
  showEmptyState?: boolean
  emptyImage?: string
  emptyDescription?: string
  showPagination?: boolean
}

export interface TabItem {
  label: string
  value: string
  disabled?: boolean
}

export interface DetailPageLayoutProps {
  title?: string
  subtitle?: string
  description?: string
  status?: string
  statusVariant?: 'dark' | 'light' | 'outline' | 'light-outline'
  showBackButton?: boolean
  backButtonText?: string
  showTabs?: boolean
  tabs?: TabItem[]
  defaultActiveTab?: string
  tabsTheme?: 'normal' | 'card'
  showSidebar?: boolean
  showActionBar?: boolean
  stickyActionBar?: boolean
  showDefaultActions?: boolean
  showCancelButton?: boolean
  cancelButtonText?: string
  showSaveButton?: boolean
  saveButtonText?: string
  saveLoading?: boolean
  statusMap?: Record<string, { label: string; theme: string }>
}
