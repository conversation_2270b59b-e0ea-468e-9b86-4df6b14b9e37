<template>
  <t-breadcrumb>
    <t-breadcrumb-item
      v-for="(item, index) in breadcrumbItems"
      :key="index"
      :to="item.path"
    >
      {{ item.title }}
    </t-breadcrumb-item>
  </t-breadcrumb>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const breadcrumbItems = computed(() => {
  const items: Array<{ title: string; path?: string }> = []
  
  // 根据路由生成面包屑
  if (route.matched && route.matched.length > 0) {
    route.matched.forEach((match, index) => {
      if (match.meta && match.meta.title) {
        items.push({
          title: match.meta.title as string,
          path: index === route.matched.length - 1 ? undefined : match.path
        })
      }
    })
  }
  
  return items
})
</script>

<style scoped lang="less">
// 面包屑样式可以在这里自定义
</style>
