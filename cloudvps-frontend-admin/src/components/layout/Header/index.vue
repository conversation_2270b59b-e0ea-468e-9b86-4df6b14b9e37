<template>
  <t-row justify="space-between" align="middle" class="app-header">
    <!-- 左侧：侧边栏切换按钮 -->
    <t-col flex="auto">
      <t-space align="center">
        <t-button
          variant="text"
          shape="square"
          size="medium"
          @click="handleToggleSidebar"
        >
          <Icon name="menu" />
        </t-button>

        <!-- 页面标题 -->
        <div class="page-title">
          {{ pageTitle }}
        </div>
      </t-space>
    </t-col>

    <!-- 右侧：功能按钮和用户菜单 -->
    <t-col flex="none">
      <t-space size="small" align="center">
        <!-- 全屏切换 -->
        <t-button
          variant="text"
          shape="square"
          size="medium"
          @click="handleFullscreen"
        >
          <Icon name="fullscreen" />
        </t-button>

        <!-- 主题切换 -->
        <t-button
          variant="text"
          shape="square"
          size="medium"
          @click="handleThemeToggle"
        >
          <Icon :name="isDarkMode ? 'sunny' : 'moon'" />
        </t-button>

        <!-- 设置按钮 -->
        <t-button
          variant="text"
          shape="square"
          size="medium"
          @click="handleSettingsToggle"
        >
          <Icon name="setting" />
        </t-button>

        <!-- 用户菜单 -->
        <t-dropdown
          :options="userMenuOptions"
          trigger="click"
          placement="bottom-right"
          @click="handleUserMenuClick"
        >
          <t-button variant="text" size="medium">
            <t-space size="small" align="center">
              <t-avatar
                size="small"
                shape="circle"
                :image="currentUser?.avatar"
                :hide-on-load-failed="false"
              >
                {{ userInitial }}
              </t-avatar>
              <span class="username">{{ currentUser?.username }}</span>
              <Icon name="chevron-down" />
            </t-space>
          </t-button>
        </t-dropdown>
      </t-space>
    </t-col>
  </t-row>
</template>

<script setup lang="tsx">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'
import { useSettingsStore } from '@/stores/modules/settings'
import Icon from '@/components/common/Icon/index.vue'
import type { DropdownOption } from 'tdesign-vue-next'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const settingsStore = useSettingsStore()

// 计算属性
const isDarkMode = computed(() => settingsStore.actualThemeMode === 'dark')
const currentUser = computed(() => authStore.currentUser)

// 页面标题
const pageTitle = computed(() => {
  return route.meta?.title || '仪表板'
})

// 用户头像首字母
const userInitial = computed(() => {
  const username = currentUser.value?.username
  return username ? username.charAt(0).toUpperCase() : 'U'
})

// 用户菜单选项 - 根据TDesign官方文档优化
const userMenuOptions: DropdownOption[] = [
  {
    content: '个人中心',
    value: 'profile',
    prefixIcon: () => <Icon name="user" />
  },
  {
    content: '账户设置',
    value: 'settings',
    prefixIcon: () => <Icon name="setting" />
  },
  {
    content: '修改密码',
    value: 'change-password',
    prefixIcon: () => <Icon name="lock" />
  },
  {
    divider: true
  },
  {
    content: '退出登录',
    value: 'logout',
    theme: 'error',
    prefixIcon: () => <Icon name="logout" />
  }
]

// 事件定义
const emit = defineEmits<{
  (e: 'toggle-sidebar'): void
}>()

// 事件处理方法
const handleToggleSidebar = () => {
  emit('toggle-sidebar')
}

const handleFullscreen = () => {
  try {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      document.documentElement.requestFullscreen()
    }
  } catch (error) {
    console.warn('全屏操作失败:', error)
  }
}

const handleThemeToggle = () => {
  const currentMode = settingsStore.themeMode
  const nextMode = currentMode === 'light' ? 'dark' : 'light'
  settingsStore.setThemeMode(nextMode)
}

const handleSettingsToggle = () => {
  settingsStore.toggleSettingsPanel()
}

const handleUserMenuClick = (data: { value: string }) => {
  const { value } = data

  switch (value) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'change-password':
      router.push('/change-password')
      break
    case 'logout':
      authStore.logout().then(() => {
        router.push('/login')
      }).catch((error) => {
        console.error('退出登录失败:', error)
      })
      break
  }
}
</script>

<style scoped lang="less">
// 使用TDesign Grid栅格布局和CSS变量
.app-header {
  padding: 0 var(--td-comp-paddingLR-xl);
  height: 100%;
  background: var(--td-bg-color-container);
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.page-title {
  font-size: var(--td-font-size-title-medium);
  font-weight: var(--td-font-weight-semi-bold);
  color: var(--td-text-color-primary);
  line-height: var(--td-line-height-title-medium);
}

.username {
  font-size: var(--td-font-size-body-medium);
  color: var(--td-text-color-primary);
  font-weight: var(--td-font-weight-medium);
}
</style>
