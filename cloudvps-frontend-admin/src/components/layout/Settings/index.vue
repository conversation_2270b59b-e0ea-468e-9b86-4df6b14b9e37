<template>
  <div class="settings-trigger">
    <t-button
      theme="default"
      variant="text"
      shape="circle"
      @click="toggleSettingsPanel"
      class="settings-button"
      :title="settingsStore.showSettingsPanel ? '关闭设置' : '打开设置'"
    >
      <SettingIcon :class="{ 'rotating': settingsStore.showSettingsPanel }" />
    </t-button>

    <!-- 设置面板 -->
    <SettingsPanel
      v-model:visible="settingsStore.showSettingsPanel"
    />
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/modules/settings'
import SettingsPanel from '@/components/common/SettingsPanel/index.vue'
import { SettingIcon } from 'tdesign-icons-vue-next'

const settingsStore = useSettingsStore()

const toggleSettingsPanel = () => {
  settingsStore.toggleSettingsPanel()
}
</script>

<style scoped lang="less">
.settings-trigger {
  position: relative;
}

.settings-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background: var(--td-bg-color-container-hover);

    :deep(.t-icon) {
      transform: rotate(90deg);
    }
  }

  :deep(.t-icon) {
    transition: transform 0.3s ease;

    &.rotating {
      transform: rotate(180deg);
    }
  }
}

// 固定设置按钮（可选）
.settings-fixed {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  z-index: 1000;

  .settings-button {
    width: 48px;
    height: 48px;
    border-radius: 24px 0 0 24px;
    background: var(--td-brand-color);
    color: white;
    box-shadow: var(--td-shadow-base);

    &:hover {
      background: var(--td-brand-color-hover);
    }
  }
}
</style>
