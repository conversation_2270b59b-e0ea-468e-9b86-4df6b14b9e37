<template>
  <div class="icon-select">
    <t-input
      v-model="displayValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :size="size"
      @click="handleInputClick"
    >
      <template #prefix-icon>
        <Icon
          v-if="modelValue"
          :type="iconType"
          :value="modelValue"
          :size="16"
        />
      </template>
      <template #suffix-icon>
        <t-icon name="chevron-down" />
      </template>
    </t-input>

    <!-- 图标选择弹窗 -->
    <t-dialog
      v-model:visible="dialogVisible"
      header="选择图标"
      :width="800"
      :footer="false"
      placement="center"
      @close="handleDialogClose"
    >
      <div class="icon-select-dialog">
        <!-- 搜索框 -->
        <div class="icon-search">
          <t-input
            v-model="searchKeyword"
            placeholder="搜索图标..."
            clearable
          >
            <template #prefix-icon>
              <t-icon name="search" />
            </template>
          </t-input>
        </div>

        <!-- 图标类型切换 -->
        <div class="icon-type-tabs">
          <t-tabs v-model="activeTab" @change="handleTabChange">
            <t-tab-panel value="tdesign" label="TDesign图标">
              <div class="icon-grid">
                <div
                  v-for="icon in filteredTDesignIcons"
                  :key="icon"
                  class="icon-item"
                  :class="{ active: selectedIcon === icon }"
                  @click="handleIconSelect('tdesign', icon)"
                >
                  <t-icon :name="icon" size="24" />
                  <span class="icon-name">{{ icon }}</span>
                </div>
              </div>
            </t-tab-panel>

            <t-tab-panel value="svg" label="SVG图标">
              <div class="svg-upload">
                <t-upload
                  :action="uploadAction"
                  accept=".svg"
                  :size-limit="{ size: 100, unit: 'KB' }"
                  @success="handleSvgUpload"
                  @fail="handleUploadFail"
                >
                  <t-button variant="outline">
                    <t-icon name="upload" />
                    上传SVG文件
                  </t-button>
                </t-upload>
                <p class="upload-tip">支持上传SVG格式图标，文件大小不超过100KB</p>
              </div>
            </t-tab-panel>

            <t-tab-panel value="url" label="图片URL">
              <div class="url-input">
                <t-input
                  v-model="urlInput"
                  placeholder="请输入图片URL地址"
                  @enter="handleUrlSubmit"
                >
                  <template #suffix>
                    <t-button size="small" @click="handleUrlSubmit">
                      确定
                    </t-button>
                  </template>
                </t-input>
                <div v-if="urlPreview" class="url-preview">
                  <img :src="urlInput" alt="预览" @error="handleImageError" />
                </div>
              </div>
            </t-tab-panel>

            <t-tab-panel value="font" label="字体图标">
              <div class="font-input">
                <t-input
                  v-model="fontInput"
                  placeholder="请输入字体图标类名，如：fa fa-home"
                  @enter="handleFontSubmit"
                >
                  <template #suffix>
                    <t-button size="small" @click="handleFontSubmit">
                      确定
                    </t-button>
                  </template>
                </t-input>
                <div v-if="fontPreview" class="font-preview">
                  <i :class="fontInput"></i>
                  <span>{{ fontInput }}</span>
                </div>
              </div>
            </t-tab-panel>
          </t-tabs>
        </div>

        <!-- 操作按钮 -->
        <div class="dialog-actions">
          <t-button variant="outline" @click="handleClear">
            清空
          </t-button>
          <t-button theme="primary" @click="handleConfirm">
            确定
          </t-button>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import Icon from '@/components/common/Icon/index.vue'
import type { IconType } from '@/types/business'

export interface IconSelectProps {
  /** 选中的图标值 */
  modelValue?: string
  /** 图标类型 */
  iconType?: IconType
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 上传接口地址 */
  uploadAction?: string
}

const props = withDefaults(defineProps<IconSelectProps>(), {
  iconType: 'tdesign',
  placeholder: '请选择图标',
  disabled: false,
  readonly: false,
  size: 'medium',
  uploadAction: '/api/upload/icon'
})

const emit = defineEmits<{
  'update:modelValue': [value: string | undefined]
  'update:iconType': [type: IconType]
  change: [value: string | undefined, type: IconType]
}>()

// 弹窗状态
const dialogVisible = ref(false)
const activeTab = ref<IconType>(props.iconType)
const searchKeyword = ref('')
const selectedIcon = ref('')

// 输入状态
const urlInput = ref('')
const fontInput = ref('')
const urlPreview = ref(false)
const fontPreview = ref(false)

// 显示值
const displayValue = computed(() => {
  if (!props.modelValue) return ''
  
  switch (props.iconType) {
    case 'tdesign':
      return `TDesign: ${props.modelValue}`
    case 'svg':
      return `SVG: ${props.modelValue}`
    case 'url':
      return `URL: ${props.modelValue}`
    case 'font':
      return `Font: ${props.modelValue}`
    default:
      return props.modelValue
  }
})

// TDesign图标列表（这里只列出部分，实际应该从TDesign获取完整列表）
const tdesignIcons = [
  'home', 'user', 'setting', 'search', 'add', 'delete', 'edit', 'view',
  'refresh', 'download', 'upload', 'save', 'close', 'check', 'clear',
  'arrow-up', 'arrow-down', 'arrow-left', 'arrow-right', 'chevron-up',
  'chevron-down', 'chevron-left', 'chevron-right', 'more', 'menu',
  'notification', 'mail', 'phone', 'location', 'time', 'calendar',
  'star', 'heart', 'like', 'share', 'copy', 'link', 'lock', 'unlock',
  'eye', 'eye-off', 'image', 'file', 'folder', 'dashboard', 'chart',
  'table', 'list', 'grid', 'filter', 'sort', 'export', 'import'
]

// 过滤后的TDesign图标
const filteredTDesignIcons = computed(() => {
  if (!searchKeyword.value) return tdesignIcons
  return tdesignIcons.filter(icon => 
    icon.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

/**
 * 输入框点击处理
 */
const handleInputClick = () => {
  if (!props.disabled && !props.readonly) {
    dialogVisible.value = true
    selectedIcon.value = props.modelValue || ''
  }
}

/**
 * 弹窗关闭处理
 */
const handleDialogClose = () => {
  dialogVisible.value = false
  searchKeyword.value = ''
  urlInput.value = ''
  fontInput.value = ''
  urlPreview.value = false
  fontPreview.value = false
}

/**
 * 标签页切换处理
 */
const handleTabChange = (value: string) => {
  activeTab.value = value as IconType
}

/**
 * 图标选择处理
 */
const handleIconSelect = (type: IconType, value: string) => {
  selectedIcon.value = value
  activeTab.value = type
}

/**
 * SVG上传成功处理
 */
const handleSvgUpload = (context: any) => {
  const { response } = context
  if (response?.data?.url) {
    selectedIcon.value = response.data.url
    activeTab.value = 'svg'
    MessagePlugin.success('SVG图标上传成功')
  }
}

/**
 * 上传失败处理
 */
const handleUploadFail = (context: any) => {
  console.error('上传失败:', context)
  MessagePlugin.error('图标上传失败')
}

/**
 * URL提交处理
 */
const handleUrlSubmit = () => {
  if (!urlInput.value.trim()) {
    MessagePlugin.warning('请输入图片URL')
    return
  }
  
  // 简单的URL格式验证
  const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|svg|webp)$/i
  if (!urlPattern.test(urlInput.value)) {
    MessagePlugin.warning('请输入有效的图片URL')
    return
  }
  
  selectedIcon.value = urlInput.value
  activeTab.value = 'url'
  urlPreview.value = true
}

/**
 * 字体图标提交处理
 */
const handleFontSubmit = () => {
  if (!fontInput.value.trim()) {
    MessagePlugin.warning('请输入字体图标类名')
    return
  }
  
  selectedIcon.value = fontInput.value
  activeTab.value = 'font'
  fontPreview.value = true
}

/**
 * 图片加载错误处理
 */
const handleImageError = () => {
  urlPreview.value = false
  MessagePlugin.error('图片加载失败，请检查URL是否正确')
}

/**
 * 清空处理
 */
const handleClear = () => {
  selectedIcon.value = ''
  emit('update:modelValue', undefined)
  emit('update:iconType', 'tdesign')
  emit('change', undefined, 'tdesign')
  handleDialogClose()
}

/**
 * 确认处理
 */
const handleConfirm = () => {
  emit('update:modelValue', selectedIcon.value || undefined)
  emit('update:iconType', activeTab.value)
  emit('change', selectedIcon.value || undefined, activeTab.value)
  handleDialogClose()
}

/**
 * 监听外部值变化
 */
watch(
  () => props.modelValue,
  (newValue) => {
    selectedIcon.value = newValue || ''
  },
  { immediate: true }
)

watch(
  () => props.iconType,
  (newType) => {
    activeTab.value = newType
  },
  { immediate: true }
)
</script>

<style scoped>
.icon-select {
  width: 100%;
}

.icon-select-dialog {
  padding: 16px 0;
}

.icon-search {
  margin-bottom: 16px;
}

.icon-type-tabs {
  margin-bottom: 24px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 16px 0;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  cursor: pointer;
  transition: all 0.2s;
}

.icon-item:hover {
  border-color: var(--td-brand-color);
  background-color: var(--td-brand-color-light);
}

.icon-item.active {
  border-color: var(--td-brand-color);
  background-color: var(--td-brand-color-focus);
  color: var(--td-brand-color);
}

.icon-name {
  margin-top: 4px;
  font-size: 12px;
  text-align: center;
  word-break: break-all;
}

.svg-upload,
.url-input,
.font-input {
  padding: 16px 0;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: var(--td-text-color-placeholder);
}

.url-preview,
.font-preview {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  text-align: center;
}

.url-preview img {
  max-width: 100px;
  max-height: 100px;
}

.font-preview i {
  font-size: 24px;
  margin-right: 8px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid var(--td-border-level-1-color);
}
</style>
