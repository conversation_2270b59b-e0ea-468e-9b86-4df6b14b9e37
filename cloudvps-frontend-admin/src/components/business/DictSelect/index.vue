<template>
  <t-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    :filterable="filterable"
    :loading="loading"
    :multiple="multiple"
    :size="size"
    :popup-props="popupProps"
    @change="handleChange"
    @clear="handleClear"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <t-option
      v-for="item in options"
      :key="item.value"
      :value="item.value"
      :label="item.label"
      :disabled="item.status === 'INACTIVE'"
    >
      <div class="dict-option">
        <span class="dict-option-label">{{ item.label }}</span>
        <t-tag
          v-if="item.color"
          :theme="item.color"
          variant="light"
          size="small"
          class="dict-option-tag"
        >
          {{ item.value }}
        </t-tag>
      </div>
    </t-option>
    
    <!-- 空状态 -->
    <template #empty>
      <div class="dict-select-empty">
        <t-icon name="inbox" size="24" />
        <p>暂无数据</p>
      </div>
    </template>
  </t-select>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useDictStore } from '@/stores/modules/dict'
import type { DictItem } from '@/types/business'

export interface DictSelectProps {
  /** 字典类型代码 */
  dictType: string
  /** 选中值 */
  modelValue?: string | number | (string | number)[]
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否可清空 */
  clearable?: boolean
  /** 是否可搜索 */
  filterable?: boolean
  /** 是否多选 */
  multiple?: boolean
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 是否自动获取字典数据 */
  autoFetch?: boolean
  /** 是否包含无效状态的选项 */
  includeInactive?: boolean
  /** 弹窗属性 */
  popupProps?: Record<string, any>
}

const props = withDefaults(defineProps<DictSelectProps>(), {
  placeholder: '请选择',
  disabled: false,
  clearable: true,
  filterable: true,
  multiple: false,
  size: 'medium',
  autoFetch: true,
  includeInactive: false,
  popupProps: () => ({})
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number | (string | number)[] | undefined]
  change: [value: string | number | (string | number)[] | undefined, option: DictItem | DictItem[] | undefined]
  clear: []
  focus: [context: { e: FocusEvent }]
  blur: [context: { e: FocusEvent }]
}>()

const dictStore = useDictStore()

// 内部值
const selectedValue = ref(props.modelValue)

// 加载状态
const loading = computed(() => {
  return dictStore.isDictLoading(props.dictType)
})

/**
 * 选项列表
 */
const options = computed(() => {
  const dictItems = dictStore.getDictData(props.dictType)
  
  if (!props.includeInactive) {
    return dictItems.filter(item => item.status !== 'INACTIVE')
  }
  
  return dictItems
})

/**
 * 监听外部值变化
 */
watch(
  () => props.modelValue,
  (newValue) => {
    selectedValue.value = newValue
  },
  { immediate: true }
)

/**
 * 监听内部值变化
 */
watch(
  selectedValue,
  (newValue) => {
    emit('update:modelValue', newValue)
  }
)

/**
 * 值变化处理
 */
const handleChange = (value: string | number | (string | number)[] | undefined) => {
  selectedValue.value = value
  
  // 查找对应的选项
  let option: DictItem | DictItem[] | undefined
  
  if (props.multiple && Array.isArray(value)) {
    option = options.value.filter(item => value.includes(item.value))
  } else if (!props.multiple && value !== undefined) {
    option = options.value.find(item => item.value === value)
  }
  
  emit('change', value, option)
}

/**
 * 清空处理
 */
const handleClear = () => {
  emit('clear')
}

/**
 * 获得焦点处理
 */
const handleFocus = (context: { e: FocusEvent }) => {
  emit('focus', context)
}

/**
 * 失去焦点处理
 */
const handleBlur = (context: { e: FocusEvent }) => {
  emit('blur', context)
}

/**
 * 获取选中项的标签
 */
const getSelectedLabel = (value: string | number): string => {
  const item = options.value.find(item => item.value === value)
  return item?.label || String(value)
}

/**
 * 获取选中项的颜色
 */
const getSelectedColor = (value: string | number): string => {
  const item = options.value.find(item => item.value === value)
  return item?.color || ''
}

/**
 * 初始化
 */
onMounted(() => {
  if (props.autoFetch) {
    dictStore.fetchDictData(props.dictType)
  }
})

// 暴露方法
defineExpose({
  getSelectedLabel,
  getSelectedColor,
  options
})
</script>

<style scoped>
.dict-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.dict-option-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dict-option-tag {
  margin-left: 8px;
  flex-shrink: 0;
}

.dict-select-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: var(--td-text-color-placeholder);
}

.dict-select-empty p {
  margin: 8px 0 0 0;
  font-size: 14px;
}
</style>
