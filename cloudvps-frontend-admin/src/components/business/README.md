# 业务组件库

CloudVPS前端管理平台的业务组件库，提供了一套完整的业务逻辑封装和可复用组件。

## 组件列表

### 1. DictTag - 字典标签组件

用于显示字典值对应的标签，支持自动颜色映射。

**特性：**
- 自动从字典存储获取标签文本
- 支持自动颜色映射
- 完整的TDesign Tag组件属性支持
- TypeScript类型安全

**使用示例：**
```vue
<DictTag dict-type="user_status" :value="user.status" />
```

### 2. DictSelect - 字典选择组件

基于字典数据的下拉选择组件。

**特性：**
- 自动从字典存储获取选项数据
- 支持单选和多选
- 支持搜索过滤
- 支持包含/排除无效状态选项
- 完整的TDesign Select组件属性支持

**使用示例：**
```vue
<DictSelect 
  v-model="form.status"
  dict-type="user_status"
  placeholder="请选择状态"
/>
```

### 3. IconSelect - 图标选择组件

支持多种图标类型的选择组件。

**特性：**
- 支持TDesign图标、SVG文件、图片URL、字体图标
- 可视化图标选择界面
- 支持图标搜索
- 支持文件上传
- 实时预览功能

**使用示例：**
```vue
<IconSelect 
  v-model="form.icon"
  v-model:icon-type="form.iconType"
  placeholder="请选择图标"
/>
```

## 组合式函数

### 1. usePagination - 分页钩子

提供完整的分页功能。

**特性：**
- 分页状态管理
- 页码和页面大小变化处理
- 与TDesign Pagination组件完美集成
- 数据范围计算

### 2. useTable - 表格钩子

提供表格数据管理和操作功能。

**特性：**
- 表格数据获取和刷新
- 分页集成
- 行选择管理
- 搜索和过滤
- 删除操作

### 3. useForm - 表单钩子

提供表单状态管理和操作功能。

**特性：**
- 表单显示/隐藏控制
- 创建/编辑模式切换
- 表单验证
- 提交处理
- 数据重置

### 4. useCrud - CRUD钩子

完整的CRUD操作解决方案，整合了表格和表单功能。

**特性：**
- 完整的CRUD操作流程
- 权限控制
- 批量操作
- 搜索和过滤
- 自动化的UI交互

## 字典存储

### useDictStore - 字典数据管理

提供字典数据的缓存和管理功能。

**特性：**
- 自动缓存机制（5分钟过期）
- 避免重复请求
- 批量数据获取
- 预加载常用字典
- 自动颜色映射

## 快速开始

### 1. 安装依赖

确保项目已安装必要的依赖：
- Vue 3
- TDesign Vue Next
- Pinia
- TypeScript

### 2. 导入组件

```typescript
import { DictTag, DictSelect, IconSelect } from '@/components/business'
import { useCrud, useTable, useForm, usePagination } from '@/composables'
import { useDictStore } from '@/stores/modules/dict'
```

### 3. 使用组件

参考 `/src/pages/test/BusinessComponents.vue` 中的完整示例。

## 开发指南

### 扩展字典类型

1. 在后端添加新的字典类型
2. 在 `useDictStore` 的 `preloadCommonDicts` 方法中添加新类型
3. 在组件中使用新的字典类型

### 自定义业务组件

1. 在 `/src/components/business/` 下创建新组件目录
2. 实现组件逻辑，遵循TDesign设计规范
3. 在 `index.ts` 中导出新组件
4. 添加TypeScript类型定义

### 扩展CRUD功能

1. 在相应的钩子中添加新方法
2. 更新类型定义
3. 在组件中使用新功能

## 注意事项

1. 确保后端API返回的数据格式符合预期
2. 字典数据的缓存机制需要考虑数据一致性
3. 权限检查需要集成实际的权限系统
4. 图标上传功能需要配置正确的上传接口

## 测试

访问 `/system/test/business-components` 页面查看所有组件的演示和测试。

## 贡献

欢迎提交Issue和Pull Request来改进这个组件库。
