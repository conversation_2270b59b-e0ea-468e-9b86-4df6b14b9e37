<template>
  <t-tag
    :theme="tagTheme"
    :variant="variant"
    :size="size"
    :closable="closable"
    :disabled="disabled"
    :max-width="maxWidth"
    @close="handleClose"
  >
    {{ displayLabel }}
  </t-tag>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useDictStore } from '@/stores/modules/dict'

export interface DictTagProps {
  /** 字典类型代码 */
  dictType: string
  /** 字典值 */
  value: string | number
  /** 标签主题 */
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger'
  /** 标签变体 */
  variant?: 'dark' | 'light' | 'outline' | 'light-outline'
  /** 标签尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 是否可关闭 */
  closable?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 最大宽度 */
  maxWidth?: string | number
  /** 是否自动获取字典数据 */
  autoFetch?: boolean
}

const props = withDefaults(defineProps<DictTagProps>(), {
  theme: 'default',
  variant: 'light',
  size: 'medium',
  closable: false,
  disabled: false,
  autoFetch: true
})

const emit = defineEmits<{
  close: [value: string | number]
}>()

const dictStore = useDictStore()

/**
 * 显示标签
 */
const displayLabel = computed(() => {
  return dictStore.getDictLabel(props.dictType, props.value)
})

/**
 * 标签主题（优先使用字典配置的颜色）
 */
const tagTheme = computed(() => {
  const dictColor = dictStore.getDictColor(props.dictType, props.value)

  // 验证dictColor是否为有效的TDesign Tag theme值
  const validThemes = ['default', 'primary', 'success', 'warning', 'danger']

  if (dictColor && validThemes.includes(dictColor)) {
    return dictColor as 'default' | 'primary' | 'success' | 'warning' | 'danger'
  }

  return props.theme
})

/**
 * 关闭处理
 */
const handleClose = () => {
  emit('close', props.value)
}

/**
 * 初始化
 */
onMounted(() => {
  if (props.autoFetch) {
    dictStore.fetchDictData(props.dictType)
  }
})
</script>

<style scoped>
/* 自定义样式可以在这里添加 */
</style>
