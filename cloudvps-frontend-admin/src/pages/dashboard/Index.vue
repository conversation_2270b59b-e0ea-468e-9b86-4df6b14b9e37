<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表板</h1>
      <p>欢迎使用 CloudVPS 管理后台</p>
    </div>
    
    <div class="dashboard-content">
      <t-row :gutter="24">
        <t-col :span="6">
          <t-card title="用户总数" hover>
            <div class="stat-number">1,234</div>
            <div class="stat-label">活跃用户</div>
          </t-card>
        </t-col>
        
        <t-col :span="6">
          <t-card title="虚拟机数量" hover>
            <div class="stat-number">567</div>
            <div class="stat-label">运行中</div>
          </t-card>
        </t-col>
        
        <t-col :span="6">
          <t-card title="订单总数" hover>
            <div class="stat-number">890</div>
            <div class="stat-label">本月新增</div>
          </t-card>
        </t-col>
        
        <t-col :span="6">
          <t-card title="收入统计" hover>
            <div class="stat-number">¥12,345</div>
            <div class="stat-label">本月收入</div>
          </t-card>
        </t-col>
      </t-row>
      
      <t-row :gutter="24" class="mt-6">
        <t-col :span="12">
          <t-card title="系统状态">
            <div class="system-status">
              <div class="status-item">
                <span class="status-label">系统运行时间</span>
                <span class="status-value">15天 8小时</span>
              </div>
              <div class="status-item">
                <span class="status-label">CPU使用率</span>
                <span class="status-value">45%</span>
              </div>
              <div class="status-item">
                <span class="status-label">内存使用率</span>
                <span class="status-value">62%</span>
              </div>
              <div class="status-item">
                <span class="status-label">磁盘使用率</span>
                <span class="status-value">38%</span>
              </div>
            </div>
          </t-card>
        </t-col>
        
        <t-col :span="12">
          <t-card title="快速操作">
            <t-space direction="vertical" size="large">
              <t-button theme="primary" block>
                <t-icon name="add" />
                创建虚拟机
              </t-button>
              <t-button theme="default" block>
                <t-icon name="user-add" />
                添加用户
              </t-button>
              <t-button theme="default" block>
                <t-icon name="setting" />
                系统设置
              </t-button>
            </t-space>
          </t-card>
        </t-col>
      </t-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('Dashboard mounted')
})
</script>

<style scoped lang="less">
.dashboard {
  padding: 24px;
}

.dashboard-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.dashboard-content {
  .stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #0052d9;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #6b7280;
  }
  
  .system-status {
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f3f4f6;
      
      &:last-child {
        border-bottom: none;
      }
      
      .status-label {
        color: #6b7280;
      }
      
      .status-value {
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}
</style>
