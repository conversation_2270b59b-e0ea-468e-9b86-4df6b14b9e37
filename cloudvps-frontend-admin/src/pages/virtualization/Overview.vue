<template>
  <div class="virtualization-overview">
    <div class="page-header">
      <h1>虚拟化管理</h1>
      <p>管理PVE节点、虚拟机和资源监控</p>
    </div>
    
    <t-row :gutter="24">
      <t-col :span="6">
        <t-card title="PVE节点" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="server" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">8</div>
              <div class="card-label">在线节点</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              管理节点
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="虚拟机" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="desktop" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">156</div>
              <div class="card-label">运行中</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              管理虚拟机
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="CPU使用率" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="chart-line" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">45%</div>
              <div class="card-label">平均使用率</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              查看监控
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="内存使用率" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="chart-bar" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">62%</div>
              <div class="card-label">平均使用率</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              查看详情
            </t-button>
          </div>
        </t-card>
      </t-col>
    </t-row>
    
    <t-row :gutter="24" class="mt-6">
      <t-col :span="12">
        <t-card title="节点状态">
          <div class="node-list">
            <div class="node-item">
              <div class="node-info">
                <div class="node-name">pve-node-01</div>
                <div class="node-ip">*************</div>
              </div>
              <div class="node-status">
                <t-tag theme="success">在线</t-tag>
              </div>
            </div>
            <div class="node-item">
              <div class="node-info">
                <div class="node-name">pve-node-02</div>
                <div class="node-ip">*************</div>
              </div>
              <div class="node-status">
                <t-tag theme="success">在线</t-tag>
              </div>
            </div>
            <div class="node-item">
              <div class="node-info">
                <div class="node-name">pve-node-03</div>
                <div class="node-ip">*************</div>
              </div>
              <div class="node-status">
                <t-tag theme="warning">维护中</t-tag>
              </div>
            </div>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="12">
        <t-card title="资源统计">
          <div class="resource-stats">
            <div class="stat-item">
              <span class="stat-label">总CPU核心</span>
              <span class="stat-value">256 核</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总内存</span>
              <span class="stat-value">2048 GB</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总存储</span>
              <span class="stat-value">50 TB</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">虚拟机模板</span>
              <span class="stat-value">12 个</span>
            </div>
          </div>
        </t-card>
      </t-col>
    </t-row>
  </div>
</template>

<script setup lang="ts">
// 虚拟化概览页面逻辑
</script>

<style scoped lang="less">
.virtualization-overview {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.overview-card {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  
  .card-icon {
    color: #0052d9;
  }
  
  .card-content {
    flex: 1;
    
    .card-number {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 4px;
    }
    
    .card-label {
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.card-actions {
  border-top: 1px solid #f3f4f6;
  padding-top: 16px;
  margin-top: 16px;
}

.node-list {
  .node-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .node-info {
      .node-name {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
      }
      
      .node-ip {
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
}

.resource-stats {
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .stat-label {
      color: #6b7280;
    }
    
    .stat-value {
      font-weight: 600;
      color: #1f2937;
    }
  }
}
</style>
