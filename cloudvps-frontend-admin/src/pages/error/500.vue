<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-code">500</div>
      <div class="error-title">服务器错误</div>
      <div class="error-description">
        抱歉，服务器出现了一些问题。请稍后重试，或者联系技术支持。
      </div>
      <div class="error-actions">
        <t-button theme="primary" @click="refresh">
          刷新页面
        </t-button>
        <t-button theme="default" @click="goHome">
          回到首页
        </t-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const refresh = () => {
  window.location.reload()
}

const goHome = () => {
  router.push('/dashboard')
}
</script>

<style scoped lang="less">
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.error-content {
  text-align: center;
  
  .error-code {
    font-size: 120px;
    font-weight: 700;
    color: #e34d59;
    line-height: 1;
    margin-bottom: 24px;
  }
  
  .error-title {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
  }
  
  .error-description {
    font-size: 16px;
    color: #6b7280;
    margin-bottom: 32px;
    max-width: 400px;
  }
  
  .error-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
  }
}
</style>
