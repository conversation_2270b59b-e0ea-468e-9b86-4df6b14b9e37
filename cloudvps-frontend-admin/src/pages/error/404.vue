<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-code">404</div>
      <div class="error-title">页面不存在</div>
      <div class="error-description">
        抱歉，您访问的页面不存在。请检查URL是否正确，或者返回首页。
      </div>
      <div class="error-actions">
        <t-button theme="primary" @click="goBack">
          返回上一页
        </t-button>
        <t-button theme="default" @click="goHome">
          回到首页
        </t-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/dashboard')
}
</script>

<style scoped lang="less">
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.error-content {
  text-align: center;
  
  .error-code {
    font-size: 120px;
    font-weight: 700;
    color: #ed7b2f;
    line-height: 1;
    margin-bottom: 24px;
  }
  
  .error-title {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
  }
  
  .error-description {
    font-size: 16px;
    color: #6b7280;
    margin-bottom: 32px;
    max-width: 400px;
  }
  
  .error-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
  }
}
</style>
