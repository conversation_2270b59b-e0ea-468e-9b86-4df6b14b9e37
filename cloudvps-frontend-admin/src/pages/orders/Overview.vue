<template>
  <div class="orders-overview">
    <div class="page-header">
      <h1>订单管理</h1>
      <p>管理订单、产品和销售统计</p>
    </div>
    
    <t-row :gutter="24">
      <t-col :span="6">
        <t-card title="今日订单" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="order" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">45</div>
              <div class="card-label">新增订单</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              查看订单
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="待处理" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="time" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">12</div>
              <div class="card-label">待处理订单</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              立即处理
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="今日收入" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="money" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">¥8,456</div>
              <div class="card-label">销售收入</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              查看详情
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="产品数量" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="shop" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">28</div>
              <div class="card-label">在售产品</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              管理产品
            </t-button>
          </div>
        </t-card>
      </t-col>
    </t-row>
    
    <t-row :gutter="24" class="mt-6">
      <t-col :span="12">
        <t-card title="最新订单">
          <div class="order-list">
            <div class="order-item">
              <div class="order-info">
                <div class="order-id">#ORD-2024-001</div>
                <div class="order-product">VPS-2C4G</div>
              </div>
              <div class="order-status">
                <t-tag theme="success">已完成</t-tag>
              </div>
              <div class="order-amount">¥299</div>
            </div>
            <div class="order-item">
              <div class="order-info">
                <div class="order-id">#ORD-2024-002</div>
                <div class="order-product">VPS-4C8G</div>
              </div>
              <div class="order-status">
                <t-tag theme="warning">处理中</t-tag>
              </div>
              <div class="order-amount">¥599</div>
            </div>
            <div class="order-item">
              <div class="order-info">
                <div class="order-id">#ORD-2024-003</div>
                <div class="order-product">VPS-8C16G</div>
              </div>
              <div class="order-status">
                <t-tag theme="default">待支付</t-tag>
              </div>
              <div class="order-amount">¥1,199</div>
            </div>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="12">
        <t-card title="销售统计">
          <div class="sales-stats">
            <div class="stat-item">
              <span class="stat-label">本月订单</span>
              <span class="stat-value">1,234 单</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">本月收入</span>
              <span class="stat-value">¥156,789</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">平均客单价</span>
              <span class="stat-value">¥456</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">热销产品</span>
              <span class="stat-value">VPS-2C4G</span>
            </div>
          </div>
        </t-card>
      </t-col>
    </t-row>
  </div>
</template>

<script setup lang="ts">
// 订单概览页面逻辑
</script>

<style scoped lang="less">
.orders-overview {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.overview-card {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  
  .card-icon {
    color: #0052d9;
  }
  
  .card-content {
    flex: 1;
    
    .card-number {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 4px;
    }
    
    .card-label {
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.card-actions {
  border-top: 1px solid #f3f4f6;
  padding-top: 16px;
  margin-top: 16px;
}

.order-list {
  .order-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .order-info {
      flex: 1;
      
      .order-id {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
      }
      
      .order-product {
        font-size: 12px;
        color: #6b7280;
      }
    }
    
    .order-amount {
      font-weight: 600;
      color: #0052d9;
    }
  }
}

.sales-stats {
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .stat-label {
      color: #6b7280;
    }
    
    .stat-value {
      font-weight: 600;
      color: #1f2937;
    }
  }
}
</style>
