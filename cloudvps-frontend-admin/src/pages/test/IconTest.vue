<template>
  <div class="icon-test">
    <div class="page-header">
      <h1>Icon组件测试</h1>
      <p>测试TDesign图标修复和自定义Icon组件功能</p>
    </div>

    <!-- TDesign按需导入图标测试 -->
    <t-card title="TDesign按需导入图标测试" class="test-card">
      <div class="icon-grid">
        <div v-for="icon in tdesignIconComponents" :key="icon.name" class="icon-item">
          <component :is="icon.component" />
          <span class="icon-name">{{ icon.name }}</span>
        </div>
      </div>
    </t-card>

    <!-- 自定义Icon组件测试 -->
    <t-card title="自定义Icon组件测试" class="test-card">
      <div class="test-sections">
        <!-- TDesign图标 -->
        <div class="test-section">
          <h4>TDesign图标</h4>
          <div class="icon-examples">
            <div class="example-item">
              <Icon name="user" type="tdesign" />
              <code>name="user"</code>
            </div>
            <div class="example-item">
              <Icon name="user-circle" type="tdesign" size="24px" />
              <code>name="user-circle" size="24px"</code>
            </div>
            <div class="example-item">
              <Icon name="setting" type="tdesign" color="#0052d9" />
              <code>color="#0052d9"</code>
            </div>
            <div class="example-item">
              <Icon name="loading" type="tdesign" :spin="true" />
              <code>:spin="true"</code>
            </div>
            <div class="example-item">
              <Icon name="heart" type="tdesign" theme="danger" />
              <code>theme="danger"</code>
            </div>
          </div>
        </div>

        <!-- SVG图标 -->
        <div class="test-section">
          <h4>SVG图标</h4>
          <div class="icon-examples">
            <div class="example-item">
              <Icon type="svg" :svg="customSvg" />
              <code>自定义SVG</code>
            </div>
            <div class="example-item">
              <Icon type="svg" :svg="customSvg" size="32px" color="#00a870" />
              <code>size="32px" color="#00a870"</code>
            </div>
          </div>
        </div>

        <!-- 远程URL图标 -->
        <div class="test-section">
          <h4>远程URL图标</h4>
          <div class="icon-examples">
            <div class="example-item">
              <Icon type="url" :url="remoteIconUrl" />
              <code>远程SVG图标</code>
            </div>
          </div>
        </div>

        <!-- 图标字体 -->
        <div class="test-section">
          <h4>图标字体</h4>
          <div class="icon-examples">
            <div class="example-item">
              <Icon name="home" type="iconfont" prefix="fa fa" />
              <code>FontAwesome风格</code>
            </div>
            <div class="example-item">
              <Icon name="star" type="iconfont" prefix="iconfont icon" />
              <code>Iconfont风格</code>
            </div>
          </div>
        </div>

        <!-- 交互测试 -->
        <div class="test-section">
          <h4>交互测试</h4>
          <div class="icon-examples">
            <div class="example-item">
              <Icon 
                name="thumb-up" 
                type="tdesign" 
                :clickable="true" 
                theme="primary"
                @click="handleIconClick"
              />
              <code>可点击图标</code>
            </div>
            <div class="example-item">
              <Icon 
                name="refresh" 
                type="tdesign" 
                :clickable="true"
                :spin="isRefreshing"
                @click="handleRefresh"
              />
              <code>刷新动画</code>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 尺寸和主题测试 -->
    <t-card title="尺寸和主题测试" class="test-card">
      <div class="size-theme-test">
        <div class="size-test">
          <h4>尺寸测试</h4>
          <div class="size-examples">
            <Icon name="star" type="tdesign" size="12px" />
            <Icon name="star" type="tdesign" size="16px" />
            <Icon name="star" type="tdesign" size="20px" />
            <Icon name="star" type="tdesign" size="24px" />
            <Icon name="star" type="tdesign" size="32px" />
            <Icon name="star" type="tdesign" size="48px" />
          </div>
        </div>
        
        <div class="theme-test">
          <h4>主题测试</h4>
          <div class="theme-examples">
            <Icon name="check-circle" type="tdesign" theme="default" />
            <Icon name="check-circle" type="tdesign" theme="primary" />
            <Icon name="check-circle" type="tdesign" theme="success" />
            <Icon name="check-circle" type="tdesign" theme="warning" />
            <Icon name="check-circle" type="tdesign" theme="danger" />
          </div>
        </div>
      </div>
    </t-card>

    <!-- 使用文档 -->
    <t-card title="使用文档" class="test-card">
      <div class="documentation">
        <h4>基本用法</h4>
        <pre class="code-block">{{ basicUsage }}</pre>
        
        <h4>高级用法</h4>
        <pre class="code-block">{{ advancedUsage }}</pre>
        
        <h4>Props说明</h4>
        <div class="props-table">
          <table>
            <thead>
              <tr>
                <th>属性</th>
                <th>类型</th>
                <th>默认值</th>
                <th>说明</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="prop in propsDoc" :key="prop.name">
                <td><code>{{ prop.name }}</code></td>
                <td>{{ prop.type }}</td>
                <td>{{ prop.default }}</td>
                <td>{{ prop.description }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import Icon from '@/components/common/Icon/index.vue'
import {
  UserIcon,
  UserCircleIcon,
  SettingIcon,
  HomeIcon,
  SearchIcon,
  EditIcon,
  DeleteIcon,
  AddIcon,
  CloseIcon,
  CheckIcon,
  ChevronRightIcon,
  LoadingIcon,
  RefreshIcon,
  HeartIcon,
  StarIcon,
  LockOnIcon
} from 'tdesign-icons-vue-next'

// 响应式数据
const isRefreshing = ref(false)

// TDesign图标组件测试列表
const tdesignIconComponents = [
  { name: 'UserIcon', component: UserIcon },
  { name: 'UserCircleIcon', component: UserCircleIcon },
  { name: 'SettingIcon', component: SettingIcon },
  { name: 'HomeIcon', component: HomeIcon },
  { name: 'SearchIcon', component: SearchIcon },
  { name: 'EditIcon', component: EditIcon },
  { name: 'DeleteIcon', component: DeleteIcon },
  { name: 'AddIcon', component: AddIcon },
  { name: 'CloseIcon', component: CloseIcon },
  { name: 'CheckIcon', component: CheckIcon },
  { name: 'ChevronRightIcon', component: ChevronRightIcon },
  { name: 'LoadingIcon', component: LoadingIcon },
  { name: 'RefreshIcon', component: RefreshIcon },
  { name: 'HeartIcon', component: HeartIcon },
  { name: 'StarIcon', component: StarIcon },
  { name: 'LockOnIcon', component: LockOnIcon }
]

// 自定义SVG
const customSvg = `<svg viewBox="0 0 24 24" fill="currentColor">
  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
</svg>`

// 远程图标URL
const remoteIconUrl = 'https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons/brand-github.svg'

// 计算属性
const basicUsage = computed(() => `<!-- TDesign图标 -->
<Icon name="user" type="tdesign" />
<Icon name="setting" type="tdesign" size="24px" />
<Icon name="heart" type="tdesign" theme="danger" />

<!-- 自定义SVG -->
<Icon type="svg" :svg="svgContent" />

<!-- 远程图标 -->
<Icon type="url" url="https://example.com/icon.svg" />

<!-- 图标字体 -->
<Icon name="home" type="iconfont" prefix="fa fa" />`)

const advancedUsage = computed(() => `<!-- 可点击图标 -->
<Icon 
  name="thumb-up" 
  type="tdesign" 
  :clickable="true"
  @click="handleClick"
/>

<!-- 旋转动画 -->
<Icon 
  name="loading" 
  type="tdesign" 
  :spin="true"
/>

<!-- 自定义样式 -->
<Icon 
  name="star" 
  type="tdesign"
  size="32px"
  color="#FFD700"
  :rotate="45"
  className="custom-icon"
/>`)

const propsDoc = [
  { name: 'name', type: 'string', default: '-', description: '图标名称' },
  { name: 'type', type: 'IconType', default: 'tdesign', description: '图标类型' },
  { name: 'size', type: 'string | number', default: '16px', description: '图标大小' },
  { name: 'color', type: 'string', default: '-', description: '图标颜色' },
  { name: 'theme', type: 'IconTheme', default: 'default', description: '主题色' },
  { name: 'clickable', type: 'boolean', default: 'false', description: '是否可点击' },
  { name: 'spin', type: 'boolean', default: 'false', description: '是否旋转动画' },
  { name: 'rotate', type: 'number', default: '0', description: '旋转角度' }
]

// 方法
const handleIconClick = () => {
  MessagePlugin.success('图标被点击了！')
}

const handleRefresh = () => {
  isRefreshing.value = true
  setTimeout(() => {
    isRefreshing.value = false
    MessagePlugin.success('刷新完成！')
  }, 2000)
}
</script>

<style scoped lang="less">
.icon-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.test-card {
  margin-bottom: 24px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  
  .icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #0052d9;
      background: #f8fafc;
    }
    
    .icon-name {
      font-size: 12px;
      color: #6b7280;
      text-align: center;
    }
  }
}

.test-sections {
  .test-section {
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #374151;
      margin: 0 0 16px 0;
    }
  }
}

.icon-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  
  .example-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    min-width: 120px;
    
    code {
      font-size: 12px;
      color: #6b7280;
      text-align: center;
      background: #f3f4f6;
      padding: 4px 8px;
      border-radius: 4px;
    }
  }
}

.size-theme-test {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  
  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 16px 0;
  }
  
  .size-examples {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .theme-examples {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

.documentation {
  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 24px 0 12px 0;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  .code-block {
    background: #1f2937;
    color: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 12px;
    line-height: 1.5;
    overflow-x: auto;
    margin-bottom: 16px;
  }
  
  .props-table {
    overflow-x: auto;
    
    table {
      width: 100%;
      border-collapse: collapse;
      
      th, td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
      }
      
      th {
        background: #f9fafb;
        font-weight: 600;
        color: #374151;
      }
      
      td {
        color: #4b5563;
        
        code {
          background: #f3f4f6;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .icon-test {
    padding: 16px;
  }
  
  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .size-theme-test {
    grid-template-columns: 1fr;
  }
  
  .icon-examples {
    justify-content: center;
  }
}
</style>
