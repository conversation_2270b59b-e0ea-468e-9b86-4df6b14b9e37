<template>
  <div class="demo-container">
    <!-- 页面头部 -->
    <div class="demo-header">
      <t-card :bordered="false">
        <div class="header-content">
          <div class="header-left">
            <h1 class="demo-title">菜单管理系统演示</h1>
            <p class="demo-subtitle">
              基于 TDesign Vue Next 的现代化菜单管理界面
            </p>
          </div>
          <div class="header-right">
            <t-space>
              <t-tag theme="success" variant="light">
                <template #icon>
                  <check-circle-icon />
                </template>
                功能完成
              </t-tag>
              <t-tag theme="primary" variant="light">
                Vue 3 + TypeScript
              </t-tag>
              <t-tag theme="warning" variant="light">
                TDesign Vue Next
              </t-tag>
            </t-space>
          </div>
        </div>
      </t-card>
    </div>

    <!-- 功能特性展示 -->
    <div class="features-section">
      <t-row :gutter="16">
        <t-col :span="8">
          <t-card 
            title="🎯 设计特性"
            :bordered="false"
            hover-shadow
          >
            <div class="feature-list">
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>现代化左右分栏布局</span>
                </t-space>
              </div>
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>响应式设计适配</span>
                </t-space>
              </div>
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>优雅的交互动画</span>
                </t-space>
              </div>
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>统一的视觉风格</span>
                </t-space>
              </div>
            </div>
          </t-card>
        </t-col>

        <t-col :span="8">
          <t-card 
            title="⚡ 功能特性"
            :bordered="false"
            hover-shadow
          >
            <div class="feature-list">
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>树形结构展示</span>
                </t-space>
              </div>
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>拖拽排序支持</span>
                </t-space>
              </div>
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>权限控制管理</span>
                </t-space>
              </div>
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>图标选择器</span>
                </t-space>
              </div>
            </div>
          </t-card>
        </t-col>

        <t-col :span="8">
          <t-card 
            title="🔧 技术特性"
            :bordered="false"
            hover-shadow
          >
            <div class="feature-list">
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>TypeScript 类型安全</span>
                </t-space>
              </div>
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>组件化架构设计</span>
                </t-space>
              </div>
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>API 标准化设计</span>
                </t-space>
              </div>
              <div class="feature-item">
                <t-space align="center">
                  <check-icon class="feature-icon success" />
                  <span>完整的错误处理</span>
                </t-space>
              </div>
            </div>
          </t-card>
        </t-col>
      </t-row>
    </div>

    <!-- 组件架构展示 -->
    <div class="architecture-section">
      <t-card 
        title="📦 组件架构"
        :bordered="false"
      >
        <div class="architecture-diagram">
          <t-row :gutter="24">
            <t-col :span="6">
              <div class="component-box main">
                <div class="component-header">
                  <setting-icon class="component-icon" />
                  <span class="component-name">Index.vue</span>
                </div>
                <div class="component-desc">主容器组件</div>
              </div>
            </t-col>
            
            <t-col :span="6">
              <div class="component-box">
                <div class="component-header">
                  <list-icon class="component-icon" />
                  <span class="component-name">MenuTree.vue</span>
                </div>
                <div class="component-desc">树形菜单组件</div>
              </div>
            </t-col>
            
            <t-col :span="6">
              <div class="component-box">
                <div class="component-header">
                  <edit-icon class="component-icon" />
                  <span class="component-name">MenuDetail.vue</span>
                </div>
                <div class="component-desc">详情编辑组件</div>
              </div>
            </t-col>
            
            <t-col :span="6">
              <div class="component-box">
                <div class="component-header">
                  <user-icon class="component-icon" />
                  <span class="component-name">RoleAssign.vue</span>
                </div>
                <div class="component-desc">角色分配组件</div>
              </div>
            </t-col>
          </t-row>
          
          <t-row :gutter="24" style="margin-top: 16px;">
            <t-col :span="12">
              <div class="component-box sub">
                <div class="component-header">
                  <view-module-icon class="component-icon" />
                  <span class="component-name">IconSelectDialog.vue</span>
                </div>
                <div class="component-desc">图标选择对话框</div>
              </div>
            </t-col>
            
            <t-col :span="12">
              <div class="component-box sub">
                <div class="component-header">
                  <dashboard-icon class="component-icon" />
                  <span class="component-name">IconGrid.vue</span>
                </div>
                <div class="component-desc">图标网格展示</div>
              </div>
            </t-col>
          </t-row>
        </div>
      </t-card>
    </div>

    <!-- 快速访问 -->
    <div class="quick-access-section">
      <t-card 
        title="🚀 快速访问"
        :bordered="false"
      >
        <t-space size="large">
          <t-button 
            theme="primary" 
            size="large"
            @click="navigateToMenuManagement"
          >
            <template #icon>
              <menu-icon />
            </template>
            菜单管理页面
          </t-button>
          
          <t-button 
            variant="outline" 
            size="large"
            @click="openApiDocs"
          >
            <template #icon>
              <file-icon />
            </template>
            API 文档
          </t-button>
          
          <t-button 
            variant="outline" 
            size="large"
            @click="viewSourceCode"
          >
            <template #icon>
              <code-icon />
            </template>
            查看源码
          </t-button>
        </t-space>
      </t-card>
    </div>

    <!-- 技术栈信息 -->
    <div class="tech-stack-section">
      <t-card 
        title="💻 技术栈"
        :bordered="false"
      >
        <t-row :gutter="16">
          <t-col :span="6">
            <div class="tech-item">
              <div class="tech-icon vue">V</div>
              <div class="tech-info">
                <div class="tech-name">Vue 3</div>
                <div class="tech-desc">渐进式 JavaScript 框架</div>
              </div>
            </div>
          </t-col>
          
          <t-col :span="6">
            <div class="tech-item">
              <div class="tech-icon typescript">TS</div>
              <div class="tech-info">
                <div class="tech-name">TypeScript</div>
                <div class="tech-desc">JavaScript 的超集</div>
              </div>
            </div>
          </t-col>
          
          <t-col :span="6">
            <div class="tech-item">
              <div class="tech-icon tdesign">TD</div>
              <div class="tech-info">
                <div class="tech-name">TDesign</div>
                <div class="tech-desc">腾讯设计体系组件库</div>
              </div>
            </div>
          </t-col>
          
          <t-col :span="6">
            <div class="tech-item">
              <div class="tech-icon vite">V</div>
              <div class="tech-info">
                <div class="tech-name">Vite</div>
                <div class="tech-desc">下一代前端构建工具</div>
              </div>
            </div>
          </t-col>
        </t-row>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import {
  CheckCircleIcon,
  CheckIcon,
  SettingIcon,
  ListIcon,
  EditIcon,
  UserIcon,
  ViewModuleIcon,
  DashboardIcon,
  MenuIcon,
  FileIcon,
  CodeIcon,
} from 'tdesign-icons-vue-next'

const router = useRouter()

// 导航到菜单管理页面
const navigateToMenuManagement = () => {
  router.push('/system/menus')
}

// 打开API文档
const openApiDocs = () => {
  // 这里可以链接到实际的API文档
  MessagePlugin.info('API文档功能待实现')
}

// 查看源码
const viewSourceCode = () => {
  // 这里可以链接到GitHub等代码仓库
  MessagePlugin.info('源码查看功能待实现')
}
</script>

<style scoped>
.demo-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: calc(100vh - 64px);
}

/* 页面头部 */
.demo-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.demo-title {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--td-brand-color), var(--td-brand-color-7));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.demo-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}

.header-right {
  flex-shrink: 0;
}

/* 功能特性 */
.features-section {
  margin-bottom: 24px;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  padding: 8px 0;
}

.feature-icon {
  font-size: 16px;
}

.feature-icon.success {
  color: var(--td-success-color);
}

/* 组件架构 */
.architecture-section {
  margin-bottom: 24px;
}

.architecture-diagram {
  padding: 20px 0;
}

.component-box {
  padding: 20px;
  border: 2px solid var(--td-border-level-1-color);
  border-radius: 8px;
  background: var(--td-bg-color-container);
  text-align: center;
  transition: all 0.3s ease;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.component-box:hover {
  border-color: var(--td-brand-color);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.component-box.main {
  border-color: var(--td-brand-color);
  background: var(--td-brand-color-1);
}

.component-box.sub {
  border-style: dashed;
  opacity: 0.8;
}

.component-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.component-icon {
  font-size: 20px;
  color: var(--td-brand-color);
}

.component-name {
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.component-desc {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

/* 快速访问 */
.quick-access-section {
  margin-bottom: 24px;
}

.quick-access-section :deep(.t-card__body) {
  text-align: center;
  padding: 32px;
}

/* 技术栈 */
.tech-stack-section {
  margin-bottom: 24px;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  transition: all 0.3s ease;
}

.tech-item:hover {
  border-color: var(--td-brand-color-light);
  background: var(--td-brand-color-1);
}

.tech-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 18px;
}

.tech-icon.vue {
  background: #4FC08D;
}

.tech-icon.typescript {
  background: #3178C6;
}

.tech-icon.tdesign {
  background: #0052D9;
}

.tech-icon.vite {
  background: #646CFF;
}

.tech-info {
  flex: 1;
}

.tech-name {
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 4px;
}

.tech-desc {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .demo-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .demo-container {
    padding: 16px;
  }
  
  .demo-title {
    font-size: 24px;
  }
  
  .component-box {
    height: 80px;
    padding: 16px;
  }
  
  .quick-access-section :deep(.t-card__body) {
    padding: 20px;
  }
  
  .tech-item {
    padding: 12px;
  }
  
  .tech-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}
</style> 