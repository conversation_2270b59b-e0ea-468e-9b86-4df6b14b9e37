<template>
  <div class="settings-test">
    <div class="page-header">
      <h1>系统设置测试</h1>
      <p>测试系统设置面板的各项功能</p>
    </div>

    <!-- 当前设置状态 -->
    <t-card title="当前设置状态" class="test-card">
      <div class="settings-status">
        <div class="status-grid">
          <div class="status-item">
            <label>主题模式:</label>
            <t-tag :theme="getThemeTagTheme(settingsStore.themeMode)">
              {{ getThemeModeLabel(settingsStore.themeMode) }}
            </t-tag>
          </div>
          
          <div class="status-item">
            <label>实际主题:</label>
            <t-tag :theme="settingsStore.isDarkTheme ? 'warning' : 'primary'">
              {{ settingsStore.actualThemeMode === 'dark' ? '暗色' : '浅色' }}
            </t-tag>
          </div>
          
          <div class="status-item">
            <label>主色调:</label>
            <div class="color-display">
              <div 
                class="color-swatch" 
                :style="{ backgroundColor: settingsStore.primaryColor }"
              ></div>
              <code>{{ settingsStore.primaryColor }}</code>
            </div>
          </div>
          
          <div class="status-item">
            <label>布局模式:</label>
            <t-tag theme="success">
              {{ getLayoutModeLabel(settingsStore.layoutMode) }}
            </t-tag>
          </div>
          
          <div class="status-item">
            <label>设置面板:</label>
            <t-tag :theme="settingsStore.showSettingsPanel ? 'success' : 'default'">
              {{ settingsStore.showSettingsPanel ? '已打开' : '已关闭' }}
            </t-tag>
          </div>
          
          <div class="status-item">
            <label>自定义颜色:</label>
            <span>{{ settingsStore.customColors.length }} 个</span>
          </div>
          
          <div class="status-item">
            <label>最近颜色:</label>
            <span>{{ settingsStore.recentColors.length }} 个</span>
          </div>
          
          <div class="status-item">
            <label>CSS类名:</label>
            <div class="class-names">
              <code>{{ settingsStore.themeClassName }}</code>
              <code>{{ settingsStore.layoutClassName }}</code>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- UI元素状态 -->
    <t-card title="UI元素显示状态" class="test-card">
      <div class="ui-elements-status">
        <div class="elements-grid">
          <div 
            v-for="(value, key) in settingsStore.uiElements" 
            :key="key"
            class="element-status"
          >
            <span class="element-name">{{ getUIElementName(key) }}</span>
            <t-tag :theme="value ? 'success' : 'danger'">
              {{ value ? '显示' : '隐藏' }}
            </t-tag>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 快速操作 -->
    <t-card title="快速操作" class="test-card">
      <div class="quick-actions">
        <t-space wrap>
          <t-button 
            theme="primary" 
            @click="settingsStore.toggleSettingsPanel()"
          >
            {{ settingsStore.showSettingsPanel ? '关闭' : '打开' }}设置面板
          </t-button>
          
          <t-button 
            @click="cycleThemeMode"
          >
            切换主题模式
          </t-button>
          
          <t-button 
            @click="randomColor"
          >
            随机主色调
          </t-button>
          
          <t-button 
            @click="cycleLayoutMode"
          >
            切换布局模式
          </t-button>
          
          <t-button 
            variant="outline"
            @click="settingsStore.resetSettings"
          >
            重置所有设置
          </t-button>
        </t-space>
      </div>
    </t-card>

    <!-- 主题预览 -->
    <t-card title="主题预览" class="test-card">
      <div class="theme-preview">
        <div class="preview-sections">
          <div class="preview-section">
            <h4>按钮组件</h4>
            <t-space>
              <t-button theme="primary">主要按钮</t-button>
              <t-button theme="default">默认按钮</t-button>
              <t-button theme="success">成功按钮</t-button>
              <t-button theme="warning">警告按钮</t-button>
              <t-button theme="danger">危险按钮</t-button>
            </t-space>
          </div>
          
          <div class="preview-section">
            <h4>表单组件</h4>
            <t-space direction="vertical">
              <t-input placeholder="输入框示例" />
              <t-select placeholder="选择器示例" />
              <t-switch v-model="testSwitch" />
            </t-space>
          </div>
          
          <div class="preview-section">
            <h4>反馈组件</h4>
            <t-space>
              <t-tag theme="primary">主要标签</t-tag>
              <t-tag theme="success">成功标签</t-tag>
              <t-tag theme="warning">警告标签</t-tag>
              <t-tag theme="danger">危险标签</t-tag>
            </t-space>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 颜色测试 -->
    <t-card title="颜色测试" class="test-card">
      <div class="color-test">
        <div class="color-variables">
          <h4>CSS变量测试</h4>
          <div class="variable-list">
            <div class="variable-item">
              <span class="variable-name">--td-brand-color</span>
              <div class="variable-value" style="background: var(--td-brand-color)"></div>
            </div>
            <div class="variable-item">
              <span class="variable-name">--td-brand-color-hover</span>
              <div class="variable-value" style="background: var(--td-brand-color-hover)"></div>
            </div>
            <div class="variable-item">
              <span class="variable-name">--td-brand-color-focus</span>
              <div class="variable-value" style="background: var(--td-brand-color-focus)"></div>
            </div>
            <div class="variable-item">
              <span class="variable-name">--td-brand-color-active</span>
              <div class="variable-value" style="background: var(--td-brand-color-active)"></div>
            </div>
            <div class="variable-item">
              <span class="variable-name">--td-brand-color-disabled</span>
              <div class="variable-value" style="background: var(--td-brand-color-disabled)"></div>
            </div>
          </div>
        </div>
        
        <div class="recent-colors">
          <h4>最近使用的颜色</h4>
          <div class="color-list">
            <div 
              v-for="color in settingsStore.recentColors" 
              :key="color"
              class="color-item"
              :style="{ backgroundColor: color }"
              :title="color"
              @click="settingsStore.setPrimaryColor(color)"
            >
              {{ color }}
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 设置数据 -->
    <t-card title="设置数据" class="test-card">
      <div class="settings-data">
        <t-space direction="vertical" size="large">
          <div>
            <h4>当前设置JSON</h4>
            <pre class="settings-json">{{ JSON.stringify(settingsStore.settings, null, 2) }}</pre>
          </div>
          
          <t-space>
            <t-button @click="exportSettings">导出设置</t-button>
            <t-button @click="showImportDialog = true">导入设置</t-button>
            <t-button @click="validateCurrentSettings">验证设置</t-button>
          </t-space>
        </t-space>
      </div>
    </t-card>

    <!-- 导入设置对话框 -->
    <t-dialog
      v-model:visible="showImportDialog"
      title="导入设置"
      width="600px"
      @confirm="handleImportSettings"
    >
      <t-textarea
        v-model="importSettingsText"
        placeholder="请粘贴设置JSON数据"
        :rows="15"
        class="import-textarea"
      />
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { useSettingsStore } from '@/stores/modules/settings'
import { PRESET_THEME_COLORS, LAYOUT_CONFIGS } from '@/types/settings'
import type { ThemeMode, LayoutMode } from '@/types/settings'

// Store
const settingsStore = useSettingsStore()

// 响应式数据
const testSwitch = ref(true)
const showImportDialog = ref(false)
const importSettingsText = ref('')

// 方法
const getThemeModeLabel = (mode: ThemeMode) => {
  const labels = {
    light: '浅色主题',
    dark: '暗色主题',
    auto: '跟随系统'
  }
  return labels[mode]
}

const getThemeTagTheme = (mode: ThemeMode) => {
  const themes = {
    light: 'primary',
    dark: 'warning',
    auto: 'success'
  }
  return themes[mode]
}

const getLayoutModeLabel = (mode: LayoutMode) => {
  const layout = LAYOUT_CONFIGS.find(l => l.mode === mode)
  return layout?.name || mode
}

const getUIElementName = (key: string) => {
  const names: Record<string, string> = {
    breadcrumb: '面包屑导航',
    tabs: 'Tab标签页',
    sidebarCollapseButton: '侧边栏折叠按钮',
    footer: '页脚',
    header: '顶部导航',
    sidebar: '侧边栏',
    logo: 'Logo',
    settingsButton: '设置按钮'
  }
  return names[key] || key
}

const cycleThemeMode = () => {
  const modes: ThemeMode[] = ['light', 'dark', 'auto']
  const currentIndex = modes.indexOf(settingsStore.themeMode)
  const nextIndex = (currentIndex + 1) % modes.length
  settingsStore.setThemeMode(modes[nextIndex])
  MessagePlugin.success(`已切换到${getThemeModeLabel(modes[nextIndex])}`)
}

const randomColor = () => {
  const colors = PRESET_THEME_COLORS
  const randomIndex = Math.floor(Math.random() * colors.length)
  const color = colors[randomIndex]
  settingsStore.setPrimaryColor(color.value)
  MessagePlugin.success(`已设置为${color.name}`)
}

const cycleLayoutMode = () => {
  const modes: LayoutMode[] = ['sidebar', 'header', 'mixed', 'split']
  const currentIndex = modes.indexOf(settingsStore.layoutMode)
  const nextIndex = (currentIndex + 1) % modes.length
  settingsStore.setLayoutMode(modes[nextIndex])
  MessagePlugin.success(`已切换到${getLayoutModeLabel(modes[nextIndex])}`)
}

const exportSettings = () => {
  const settings = settingsStore.exportSettings()
  const blob = new Blob([settings], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `cloudvps-settings-${new Date().toISOString().slice(0, 10)}.json`
  a.click()
  URL.revokeObjectURL(url)
  MessagePlugin.success('设置已导出')
}

const handleImportSettings = () => {
  if (!importSettingsText.value.trim()) {
    MessagePlugin.error('请输入设置数据')
    return
  }
  
  const success = settingsStore.importSettings(importSettingsText.value)
  if (success) {
    MessagePlugin.success('设置导入成功')
    showImportDialog.value = false
    importSettingsText.value = ''
  } else {
    MessagePlugin.error('设置数据格式错误')
  }
}

const validateCurrentSettings = () => {
  try {
    const settings = JSON.parse(settingsStore.exportSettings())
    MessagePlugin.success('当前设置格式正确')
    console.log('设置验证通过:', settings)
  } catch (error) {
    MessagePlugin.error('设置格式错误')
    console.error('设置验证失败:', error)
  }
}
</script>

<style scoped lang="less">
.settings-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--td-text-color-secondary);
    margin: 0;
  }
}

.test-card {
  margin-bottom: 24px;
}

.settings-status {
  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    
    .status-item {
      display: flex;
      align-items: center;
      gap: 12px;
      
      label {
        font-weight: 500;
        color: var(--td-text-color-primary);
        min-width: 80px;
      }
      
      .color-display {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .color-swatch {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          border: 1px solid var(--td-border-level-1-color);
        }
        
        code {
          font-size: 12px;
          background: var(--td-bg-color-container);
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
      
      .class-names {
        display: flex;
        flex-direction: column;
        gap: 4px;
        
        code {
          font-size: 11px;
          background: var(--td-bg-color-container);
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }
  }
}

.ui-elements-status {
  .elements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    
    .element-status {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: var(--td-bg-color-container);
      border-radius: 6px;
      border: 1px solid var(--td-border-level-1-color);
      
      .element-name {
        font-size: 14px;
        color: var(--td-text-color-primary);
      }
    }
  }
}

.quick-actions {
  .t-button {
    margin-bottom: 8px;
  }
}

.theme-preview {
  .preview-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    
    .preview-section {
      h4 {
        font-size: 16px;
        font-weight: 500;
        color: var(--td-text-color-primary);
        margin: 0 0 16px 0;
      }
    }
  }
}

.color-test {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  
  h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--td-text-color-primary);
    margin: 0 0 16px 0;
  }
  
  .variable-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .variable-item {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .variable-name {
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 12px;
        color: var(--td-text-color-secondary);
        min-width: 200px;
      }
      
      .variable-value {
        width: 40px;
        height: 20px;
        border-radius: 4px;
        border: 1px solid var(--td-border-level-1-color);
      }
    }
  }
  
  .color-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .color-item {
      padding: 8px 12px;
      border-radius: 4px;
      color: white;
      font-size: 12px;
      font-family: 'Monaco', 'Consolas', monospace;
      cursor: pointer;
      text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
      transition: transform 0.2s ease;
      
      &:hover {
        transform: scale(1.02);
      }
    }
  }
}

.settings-data {
  h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--td-text-color-primary);
    margin: 0 0 12px 0;
  }
  
  .settings-json {
    background: var(--td-bg-color-container);
    border: 1px solid var(--td-border-level-1-color);
    border-radius: 6px;
    padding: 16px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 12px;
    line-height: 1.5;
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
  }
}

.import-textarea {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .settings-test {
    padding: 16px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .elements-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-sections {
    grid-template-columns: 1fr;
  }
  
  .color-test {
    grid-template-columns: 1fr;
  }
}
</style>
