<template>
  <div class="settings-panel-test">
    <div class="page-header">
      <h1>设置面板功能测试</h1>
      <p>测试系统设置抽屉组件的各项功能</p>
    </div>

    <!-- 功能测试卡片 -->
    <t-card title="设置面板控制测试" class="test-card">
      <div class="control-tests">
        <div class="test-section">
          <h4>基础控制测试</h4>
          <t-space wrap>
            <t-button 
              theme="primary" 
              @click="openSettingsPanel"
            >
              打开设置面板
            </t-button>
            <t-button 
              @click="closeSettingsPanel"
            >
              关闭设置面板
            </t-button>
            <t-button 
              @click="toggleSettingsPanel"
            >
              切换设置面板
            </t-button>
          </t-space>
        </div>

        <div class="test-section">
          <h4>状态显示</h4>
          <div class="status-display">
            <div class="status-item">
              <span class="status-label">面板状态:</span>
              <t-tag :theme="settingsStore.showSettingsPanel ? 'success' : 'default'">
                {{ settingsStore.showSettingsPanel ? '已打开' : '已关闭' }}
              </t-tag>
            </div>
            <div class="status-item">
              <span class="status-label">当前主题:</span>
              <t-tag :theme="getThemeTagTheme(settingsStore.themeMode)">
                {{ getThemeModeLabel(settingsStore.themeMode) }}
              </t-tag>
            </div>
            <div class="status-item">
              <span class="status-label">主色调:</span>
              <div class="color-display">
                <div 
                  class="color-swatch" 
                  :style="{ backgroundColor: settingsStore.primaryColor }"
                ></div>
                <code>{{ settingsStore.primaryColor }}</code>
              </div>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 交互测试说明 -->
    <t-card title="交互功能测试说明" class="test-card">
      <div class="interaction-tests">
        <div class="test-instruction">
          <h4>🎯 测试目标</h4>
          <ul>
            <li>✅ 验证设置面板可以正常打开和关闭</li>
            <li>✅ 验证ESC键可以关闭设置面板</li>
            <li>✅ 验证点击空白区域可以关闭设置面板</li>
            <li>✅ 验证控制台无Vue Proxy错误</li>
            <li>✅ 验证双向绑定正常工作</li>
          </ul>
        </div>

        <div class="test-steps">
          <h4>🔍 测试步骤</h4>
          <ol>
            <li>
              <strong>基础功能测试：</strong>
              <ul>
                <li>点击"打开设置面板"按钮，验证面板正常打开</li>
                <li>点击"关闭设置面板"按钮，验证面板正常关闭</li>
                <li>点击"切换设置面板"按钮，验证面板状态正确切换</li>
              </ul>
            </li>
            <li>
              <strong>ESC键测试：</strong>
              <ul>
                <li>打开设置面板</li>
                <li>按ESC键，验证面板自动关闭</li>
                <li>检查状态显示是否正确更新</li>
              </ul>
            </li>
            <li>
              <strong>点击空白区域测试：</strong>
              <ul>
                <li>打开设置面板</li>
                <li>点击面板外的空白区域</li>
                <li>验证面板自动关闭</li>
                <li>检查状态显示是否正确更新</li>
              </ul>
            </li>
            <li>
              <strong>控制台检查：</strong>
              <ul>
                <li>打开浏览器开发者工具</li>
                <li>执行上述所有操作</li>
                <li>确认控制台无Vue错误或警告</li>
              </ul>
            </li>
          </ol>
        </div>
      </div>
    </t-card>

    <!-- 技术实现说明 -->
    <t-card title="技术实现说明" class="test-card">
      <div class="technical-details">
        <div class="detail-section">
          <h4>🔧 修复内容</h4>
          <div class="fix-list">
            <div class="fix-item">
              <div class="fix-title">1. Pinia Store双向绑定修复</div>
              <div class="fix-description">
                将 <code>showSettingsPanel</code> 从只读computed属性改为支持getter/setter的computed属性
              </div>
              <div class="fix-code">
                <pre><code>const showSettingsPanel = computed({
  get: () => settings.value.showSettingsPanel,
  set: (value: boolean) => {
    settings.value.showSettingsPanel = value
    saveSettings()
  }
})</code></pre>
              </div>
            </div>

            <div class="fix-item">
              <div class="fix-title">2. TDesign Drawer配置优化</div>
              <div class="fix-description">
                添加 <code>close-on-esc-keydown</code> 和 <code>close-on-overlay-click</code> 属性
              </div>
              <div class="fix-code">
                <pre><code>&lt;t-drawer
  v-model:visible="visible"
  :close-on-esc-keydown="true"
  :close-on-overlay-click="true"
  ...
&gt;</code></pre>
              </div>
            </div>

            <div class="fix-item">
              <div class="fix-title">3. 移除冗余监听</div>
              <div class="fix-description">
                移除不必要的watch监听，因为store已支持双向绑定
              </div>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>🎨 组件架构</h4>
          <div class="architecture-info">
            <div class="arch-item">
              <strong>Settings Store:</strong> 使用Pinia管理全局设置状态
            </div>
            <div class="arch-item">
              <strong>SettingsPanel组件:</strong> 可复用的设置面板组件
            </div>
            <div class="arch-item">
              <strong>双向绑定:</strong> v-model:visible实现状态同步
            </div>
            <div class="arch-item">
              <strong>TDesign集成:</strong> 使用TDesign Drawer组件
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 实时状态监控 -->
    <t-card title="实时状态监控" class="test-card">
      <div class="status-monitor">
        <div class="monitor-grid">
          <div class="monitor-item">
            <div class="monitor-label">设置面板状态</div>
            <div class="monitor-value" :class="{ active: settingsStore.showSettingsPanel }">
              {{ settingsStore.showSettingsPanel ? 'OPEN' : 'CLOSED' }}
            </div>
          </div>
          
          <div class="monitor-item">
            <div class="monitor-label">主题模式</div>
            <div class="monitor-value">
              {{ settingsStore.themeMode.toUpperCase() }}
            </div>
          </div>
          
          <div class="monitor-item">
            <div class="monitor-label">实际主题</div>
            <div class="monitor-value">
              {{ settingsStore.actualThemeMode.toUpperCase() }}
            </div>
          </div>
          
          <div class="monitor-item">
            <div class="monitor-label">布局模式</div>
            <div class="monitor-value">
              {{ settingsStore.layoutMode.toUpperCase() }}
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 设置面板组件 -->
    <SettingsPanel v-model:visible="settingsStore.showSettingsPanel" />
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/modules/settings'
import SettingsPanel from '@/components/common/SettingsPanel/index.vue'
import type { ThemeMode } from '@/types/settings'

// Store
const settingsStore = useSettingsStore()

// 方法
const openSettingsPanel = () => {
  settingsStore.showSettingsPanel = true
}

const closeSettingsPanel = () => {
  settingsStore.showSettingsPanel = false
}

const toggleSettingsPanel = () => {
  settingsStore.toggleSettingsPanel()
}

const getThemeModeLabel = (mode: ThemeMode) => {
  const labels = {
    light: '浅色主题',
    dark: '暗色主题',
    auto: '跟随系统'
  }
  return labels[mode]
}

const getThemeTagTheme = (mode: ThemeMode) => {
  const themes = {
    light: 'primary',
    dark: 'warning',
    auto: 'success'
  }
  return themes[mode]
}
</script>

<style scoped lang="less">
.settings-panel-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--td-text-color-secondary);
    margin: 0;
  }
}

.test-card {
  margin-bottom: 24px;
}

.control-tests {
  .test-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 16px 0;
    }
  }
}

.status-display {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  
  .status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .status-label {
      font-weight: 500;
      color: var(--td-text-color-secondary);
    }
  }
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid var(--td-border-level-1-color);
  }
  
  code {
    font-size: 12px;
    padding: 2px 6px;
    background: var(--td-bg-color-container);
    border-radius: 4px;
    color: var(--td-text-color-primary);
  }
}

.interaction-tests {
  .test-instruction,
  .test-steps {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 12px 0;
    }
    
    ul, ol {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: var(--td-text-color-secondary);
        line-height: 1.5;
        
        strong {
          color: var(--td-text-color-primary);
        }
        
        ul {
          margin-top: 8px;
          
          li {
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}

.technical-details {
  .detail-section {
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 16px 0;
    }
  }
}

.fix-list {
  .fix-item {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--td-bg-color-container);
    border-radius: 8px;
    border: 1px solid var(--td-border-level-1-color);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .fix-title {
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 8px;
    }
    
    .fix-description {
      color: var(--td-text-color-secondary);
      margin-bottom: 12px;
      line-height: 1.5;
      
      code {
        font-size: 12px;
        padding: 2px 6px;
        background: var(--td-bg-color-page);
        border-radius: 4px;
        color: var(--td-brand-color);
      }
    }
    
    .fix-code {
      pre {
        margin: 0;
        padding: 12px;
        background: var(--td-bg-color-page);
        border-radius: 6px;
        overflow-x: auto;
        
        code {
          font-family: 'Monaco', 'Consolas', monospace;
          font-size: 12px;
          line-height: 1.4;
          color: var(--td-text-color-primary);
        }
      }
    }
  }
}

.architecture-info {
  .arch-item {
    margin-bottom: 12px;
    padding: 12px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border-left: 3px solid var(--td-brand-color);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    strong {
      color: var(--td-brand-color);
    }
  }
}

.status-monitor {
  .monitor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    
    .monitor-item {
      text-align: center;
      padding: 16px;
      background: var(--td-bg-color-container);
      border-radius: 8px;
      border: 1px solid var(--td-border-level-1-color);
      
      .monitor-label {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .monitor-value {
        font-size: 18px;
        font-weight: 700;
        color: var(--td-text-color-primary);
        font-family: 'Monaco', 'Consolas', monospace;
        
        &.active {
          color: var(--td-success-color);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings-panel-test {
    padding: 16px;
  }
  
  .status-display {
    flex-direction: column;
    gap: 12px;
  }
  
  .monitor-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
