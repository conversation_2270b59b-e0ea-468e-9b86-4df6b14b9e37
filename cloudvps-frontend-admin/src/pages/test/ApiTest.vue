<template>
  <div class="api-test">
    <div class="page-header">
      <h1>API测试页面</h1>
      <p>测试各个API接口的连接和响应</p>
    </div>

    <t-card title="认证API测试" class="test-card">
      <t-space direction="vertical" size="large">
        <div class="test-section">
          <h3>登录测试</h3>
          <t-form layout="inline" @submit="testLogin">
            <t-form-item label="用户名">
              <t-input v-model="loginForm.username" placeholder="admin" />
            </t-form-item>
            <t-form-item label="密码">
              <t-input v-model="loginForm.password" type="password" placeholder="123456" />
            </t-form-item>
            <t-form-item>
              <t-button theme="primary" type="submit" :loading="loginLoading">
                测试登录
              </t-button>
            </t-form-item>
          </t-form>
          <div v-if="loginResult" class="test-result">
            <h4>登录结果:</h4>
            <pre>{{ JSON.stringify(loginResult, null, 2) }}</pre>
          </div>
        </div>
      </t-space>
    </t-card>

    <t-card title="用户管理API测试" class="test-card">
      <t-space direction="vertical" size="large">
        <div class="test-section">
          <h3>获取用户列表</h3>
          <t-button theme="primary" @click="testGetUsers" :loading="usersLoading">
            获取用户列表
          </t-button>
          <div v-if="usersResult" class="test-result">
            <h4>用户列表结果:</h4>
            <pre>{{ JSON.stringify(usersResult, null, 2) }}</pre>
          </div>
        </div>

        <div class="test-section">
          <h3>获取角色列表</h3>
          <t-button theme="primary" @click="testGetRoles" :loading="rolesLoading">
            获取角色列表
          </t-button>
          <div v-if="rolesResult" class="test-result">
            <h4>角色列表结果:</h4>
            <pre>{{ JSON.stringify(rolesResult, null, 2) }}</pre>
          </div>
        </div>
      </t-space>
    </t-card>

    <t-card title="后端服务连接测试" class="test-card">
      <t-space direction="vertical" size="large">
        <div class="test-section">
          <h3>服务状态检查</h3>
          <t-space>
            <t-button theme="primary" @click="testBackendServices" :loading="servicesLoading">
              检查后端服务
            </t-button>
            <t-button theme="warning" @click="testCorsIssue" :loading="corsLoading">
              测试跨域问题
            </t-button>
            <t-button theme="success" @click="testProxyConfig" :loading="proxyLoading">
              测试代理配置
            </t-button>
          </t-space>
          <div v-if="servicesResult" class="test-result">
            <h4>服务状态:</h4>
            <div class="services-status">
              <div
                v-for="(service, key) in servicesResult"
                :key="key"
                class="service-item"
              >
                <t-tag
                  :theme="service.status === 'online' ? 'success' : 'danger'"
                  variant="light"
                >
                  {{ service.name }} ({{ service.port }})
                </t-tag>
                <span class="service-status">{{ service.message }}</span>
              </div>
            </div>
          </div>
          <div v-if="corsResult" class="test-result">
            <h4>跨域测试结果:</h4>
            <pre>{{ JSON.stringify(corsResult, null, 2) }}</pre>
          </div>
          <div v-if="proxyResult" class="test-result">
            <h4>代理测试结果:</h4>
            <pre>{{ JSON.stringify(proxyResult, null, 2) }}</pre>
          </div>
        </div>
      </t-space>
    </t-card>

    <t-card title="组件功能测试" class="test-card">
      <t-space direction="vertical" size="large">
        <div class="test-section">
          <h3>TDesign组件测试</h3>
          <t-space>
            <t-button theme="primary">主要按钮</t-button>
            <t-button theme="success">成功按钮</t-button>
            <t-button theme="warning">警告按钮</t-button>
            <t-button theme="danger">危险按钮</t-button>
          </t-space>
        </div>

        <div class="test-section">
          <h3>图标测试</h3>
          <t-space>
            <t-icon name="user" size="24px" />
            <t-icon name="setting" size="24px" />
            <t-icon name="server" size="24px" />
            <t-icon name="order" size="24px" />
            <t-icon name="money" size="24px" />
          </t-space>
        </div>

        <div class="test-section">
          <h3>表格测试</h3>
          <t-table
            :data="testTableData"
            :columns="testTableColumns"
            row-key="id"
            size="small"
          />
        </div>
      </t-space>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { authApi, userApi, roleApi } from '@/api'

// 响应式数据
const loginLoading = ref(false)
const usersLoading = ref(false)
const rolesLoading = ref(false)
const servicesLoading = ref(false)
const corsLoading = ref(false)
const proxyLoading = ref(false)

const loginResult = ref(null)
const usersResult = ref(null)
const rolesResult = ref(null)
const servicesResult = ref(null)
const corsResult = ref(null)
const proxyResult = ref(null)

// 登录表单
const loginForm = reactive({
  username: 'admin',
  password: '123456'
})

// 测试表格数据
const testTableData = [
  { id: 1, name: '测试用户1', email: '<EMAIL>', status: '正常' },
  { id: 2, name: '测试用户2', email: '<EMAIL>', status: '正常' },
  { id: 3, name: '测试用户3', email: '<EMAIL>', status: '禁用' }
]

const testTableColumns = [
  { colKey: 'id', title: 'ID', width: 80 },
  { colKey: 'name', title: '姓名', width: 120 },
  { colKey: 'email', title: '邮箱', width: 180 },
  { colKey: 'status', title: '状态', width: 100 }
]

// 测试方法
const testLogin = async () => {
  loginLoading.value = true
  try {
    const response = await authApi.login(loginForm)
    loginResult.value = response
    MessagePlugin.success('登录测试成功')
  } catch (error) {
    console.error('登录测试失败:', error)
    loginResult.value = { error: error.message }
    MessagePlugin.error('登录测试失败')
  } finally {
    loginLoading.value = false
  }
}

const testGetUsers = async () => {
  usersLoading.value = true
  try {
    const response = await userApi.getUsers({ page: 1, size: 10 })
    usersResult.value = response
    MessagePlugin.success('获取用户列表成功')
  } catch (error) {
    console.error('获取用户列表失败:', error)
    usersResult.value = { error: error.message }
    MessagePlugin.error('获取用户列表失败')
  } finally {
    usersLoading.value = false
  }
}

const testGetRoles = async () => {
  rolesLoading.value = true
  try {
    const response = await roleApi.getAllRoles()
    rolesResult.value = response
    MessagePlugin.success('获取角色列表成功')
  } catch (error) {
    console.error('获取角色列表失败:', error)
    rolesResult.value = { error: error.message }
    MessagePlugin.error('获取角色列表失败')
  } finally {
    rolesLoading.value = false
  }
}

const testBackendServices = async () => {
  servicesLoading.value = true
  try {
    const services = {}
    const serviceConfigs = [
      { key: 'gateway', name: 'API网关', port: 8080, path: '/api/health' },
      { key: 'system', name: '系统服务', port: 8081, path: '/system/health' },
      { key: 'virtualization', name: '虚拟化服务', port: 8082, path: '/virtualization/health' },
      { key: 'order', name: '订单服务', port: 8083, path: '/order/health' },
      { key: 'payment', name: '支付服务', port: 8084, path: '/payment/health' }
    ]

    for (const service of serviceConfigs) {
      try {
        const response = await fetch(`http://localhost:${service.port}${service.path}`, {
          method: 'GET',
          mode: 'cors',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        services[service.key] = {
          name: service.name,
          port: service.port,
          status: response.ok ? 'online' : 'error',
          message: response.ok ? '服务正常' : `HTTP ${response.status}`
        }
      } catch (error) {
        services[service.key] = {
          name: service.name,
          port: service.port,
          status: 'offline',
          message: error.message.includes('CORS') ? '跨域错误' : '连接失败'
        }
      }
    }

    servicesResult.value = services
    MessagePlugin.success('后端服务检查完成')
  } catch (error) {
    console.error('检查后端服务失败:', error)
    servicesResult.value = { error: error.message }
    MessagePlugin.error('检查后端服务失败')
  } finally {
    servicesLoading.value = false
  }
}

const testCorsIssue = async () => {
  corsLoading.value = true
  try {
    const results = {}

    // 测试直接访问后端服务（会遇到跨域问题）
    const testUrls = [
      { name: '网关直接访问', url: 'http://localhost:8080/health' },
      { name: '系统服务直接访问', url: 'http://localhost:8081/health' }
    ]

    for (const test of testUrls) {
      try {
        const response = await fetch(test.url, {
          method: 'GET',
          mode: 'cors'
        })
        results[test.name] = {
          status: 'success',
          message: '请求成功',
          statusCode: response.status
        }
      } catch (error) {
        results[test.name] = {
          status: 'error',
          message: error.message,
          type: error.message.includes('CORS') ? 'CORS错误' : '网络错误'
        }
      }
    }

    corsResult.value = results
    MessagePlugin.info('跨域测试完成')
  } catch (error) {
    console.error('跨域测试失败:', error)
    corsResult.value = { error: error.message }
    MessagePlugin.error('跨域测试失败')
  } finally {
    corsLoading.value = false
  }
}

const testProxyConfig = async () => {
  proxyLoading.value = true
  try {
    const results = {}

    // 测试通过Vite代理访问网关路由
    const proxyTests = [
      { name: 'API网关健康检查', path: '/api/health' },
      { name: '系统服务(通过网关)', path: '/api/system/health' },
      { name: '虚拟化服务(通过网关)', path: '/api/virtualization/health' },
      { name: '订单服务(通过网关)', path: '/api/order/health' },
      { name: '支付服务(通过网关)', path: '/api/payment/health' }
    ]

    for (const test of proxyTests) {
      try {
        const response = await fetch(test.path, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        results[test.name] = {
          status: response.ok ? 'success' : 'error',
          message: response.ok ? '代理工作正常' : `HTTP ${response.status}`,
          statusCode: response.status,
          url: test.path
        }
      } catch (error) {
        results[test.name] = {
          status: 'error',
          message: error.message,
          url: test.path
        }
      }
    }

    proxyResult.value = results
    MessagePlugin.success('代理配置测试完成')
  } catch (error) {
    console.error('代理配置测试失败:', error)
    proxyResult.value = { error: error.message }
    MessagePlugin.error('代理配置测试失败')
  } finally {
    proxyLoading.value = false
  }
}
</script>

<style scoped lang="less">
.api-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.test-card {
  margin-bottom: 24px;
}

.test-section {
  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
  }
}

.test-result {
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  
  h4 {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  pre {
    font-size: 12px;
    color: #374151;
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.services-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .service-item {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .service-status {
      font-size: 12px;
      color: #6b7280;
    }
  }
}
</style>
