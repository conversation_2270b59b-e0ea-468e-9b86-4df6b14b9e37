<template>
  <div class="api-refactor-test">
    <div class="page-header">
      <h1>API重构验证测试</h1>
      <p>验证重构后的API接口文件组织结构和类型定义</p>
    </div>

    <!-- API模块结构展示 -->
    <t-card title="API模块结构" class="test-card">
      <div class="api-structure">
        <div class="module-section">
          <h4>🏢 系统服务 (System Service)</h4>
          <div class="api-list">
            <div class="api-item">
              <span class="api-name">authApi</span>
              <span class="api-desc">认证相关API</span>
              <t-button size="small" @click="testSystemAuth">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">userApi</span>
              <span class="api-desc">用户管理API</span>
              <t-button size="small" @click="testSystemUser">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">roleApi</span>
              <span class="api-desc">角色管理API</span>
              <t-button size="small" @click="testSystemRole">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">permissionApi</span>
              <span class="api-desc">权限管理API</span>
              <t-button size="small" @click="testSystemPermission">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">dictTypeApi</span>
              <span class="api-desc">字典类型API</span>
              <t-button size="small" @click="testSystemDict">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">systemConfigApi</span>
              <span class="api-desc">系统配置API</span>
              <t-button size="small" @click="testSystemConfig">测试</t-button>
            </div>
          </div>
        </div>

        <div class="module-section">
          <h4>🖥️ 虚拟化服务 (Virtualization Service)</h4>
          <div class="api-list">
            <div class="api-item">
              <span class="api-name">nodeApi</span>
              <span class="api-desc">节点管理API</span>
              <t-button size="small" @click="testVirtualizationNode">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">vmApi</span>
              <span class="api-desc">虚拟机管理API</span>
              <t-button size="small" @click="testVirtualizationVm">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">vmTemplateApi</span>
              <span class="api-desc">虚拟机模板API</span>
              <t-button size="small" @click="testVirtualizationTemplate">测试</t-button>
            </div>
          </div>
        </div>

        <div class="module-section">
          <h4>📋 订单服务 (Order Service)</h4>
          <div class="api-list">
            <div class="api-item">
              <span class="api-name">orderApi</span>
              <span class="api-desc">订单管理API</span>
              <t-button size="small" @click="testOrderOrder">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">productApi</span>
              <span class="api-desc">产品管理API</span>
              <t-button size="small" @click="testOrderProduct">测试</t-button>
            </div>
          </div>
        </div>

        <div class="module-section">
          <h4>💳 支付服务 (Payment Service)</h4>
          <div class="api-list">
            <div class="api-item">
              <span class="api-name">paymentApi</span>
              <span class="api-desc">支付管理API</span>
              <t-button size="small" @click="testPaymentPayment">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">refundApi</span>
              <span class="api-desc">退款管理API</span>
              <t-button size="small" @click="testPaymentRefund">测试</t-button>
            </div>
            <div class="api-item">
              <span class="api-name">userAccountApi</span>
              <span class="api-desc">用户账户API</span>
              <t-button size="small" @click="testPaymentAccount">测试</t-button>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 类型定义验证 -->
    <t-card title="类型定义验证" class="test-card">
      <div class="type-validation">
        <div class="validation-section">
          <h4>公共类型 (Common Types)</h4>
          <div class="type-list">
            <div class="type-item">
              <span class="type-name">BasePageRequest</span>
              <span class="type-desc">基础分页请求</span>
              <t-tag theme="success">✓ 统一</t-tag>
            </div>
            <div class="type-item">
              <span class="type-name">BaseEntity</span>
              <span class="type-desc">基础实体类型</span>
              <t-tag theme="success">✓ 统一</t-tag>
            </div>
            <div class="type-item">
              <span class="type-name">BatchRequest</span>
              <span class="type-desc">批量操作请求</span>
              <t-tag theme="success">✓ 统一</t-tag>
            </div>
            <div class="type-item">
              <span class="type-name">ApiResponse</span>
              <span class="type-desc">API响应包装</span>
              <t-tag theme="success">✓ 统一</t-tag>
            </div>
            <div class="type-item">
              <span class="type-name">PageResponse</span>
              <span class="type-desc">分页响应</span>
              <t-tag theme="success">✓ 统一</t-tag>
            </div>
          </div>
        </div>

        <div class="validation-section">
          <h4>模块特有类型 (Module-Specific Types)</h4>
          <div class="type-list">
            <div class="type-item">
              <span class="type-name">UserStatus</span>
              <span class="type-desc">用户状态枚举</span>
              <t-tag theme="primary">系统模块</t-tag>
            </div>
            <div class="type-item">
              <span class="type-name">NodeStatus</span>
              <span class="type-desc">节点状态枚举</span>
              <t-tag theme="warning">虚拟化模块</t-tag>
            </div>
            <div class="type-item">
              <span class="type-name">OrderStatus</span>
              <span class="type-desc">订单状态枚举</span>
              <t-tag theme="success">订单模块</t-tag>
            </div>
            <div class="type-item">
              <span class="type-name">PaymentStatus</span>
              <span class="type-desc">支付状态枚举</span>
              <t-tag theme="danger">支付模块</t-tag>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 测试结果 -->
    <t-card title="测试结果" class="test-card">
      <div class="test-results">
        <div v-if="testResults.length === 0" class="no-results">
          <p>点击上方的测试按钮开始验证API接口</p>
        </div>
        <div v-else class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="result.success ? 'success' : 'error'"
          >
            <div class="result-header">
              <span class="result-api">{{ result.api }}</span>
              <span class="result-status">
                <t-tag :theme="result.success ? 'success' : 'danger'">
                  {{ result.success ? '成功' : '失败' }}
                </t-tag>
              </span>
              <span class="result-time">{{ result.timestamp }}</span>
            </div>
            <div class="result-details">
              <div class="result-method">{{ result.method }}</div>
              <div class="result-url">{{ result.url }}</div>
              <div v-if="result.error" class="result-error">{{ result.error }}</div>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 重构总结 -->
    <t-card title="重构总结" class="test-card">
      <div class="refactor-summary">
        <div class="summary-stats">
          <div class="stat-item">
            <div class="stat-number">4</div>
            <div class="stat-label">微服务模块</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">13</div>
            <div class="stat-label">API接口组</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">5</div>
            <div class="stat-label">公共类型</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">20+</div>
            <div class="stat-label">模块特有类型</div>
          </div>
        </div>

        <div class="summary-benefits">
          <h4>重构收益</h4>
          <ul>
            <li>✅ 消除了重复的类型定义</li>
            <li>✅ 建立了清晰的模块边界</li>
            <li>✅ 提高了代码的可维护性</li>
            <li>✅ 统一了API接口的组织方式</li>
            <li>✅ 支持微服务架构的扩展</li>
            <li>✅ 保持了TypeScript类型安全</li>
          </ul>
        </div>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import {
  // 系统服务API
  authApi,
  userApi,
  roleApi,
  permissionApi,
  dictTypeApi,
  systemConfigApi,
  // 虚拟化服务API
  nodeApi,
  vmApi,
  vmTemplateApi,
  // 订单服务API
  orderApi,
  productApi,
  // 支付服务API
  paymentApi,
  refundApi,
  userAccountApi
} from '@/api'

// 测试结果
interface TestResult {
  api: string
  method: string
  url: string
  success: boolean
  error?: string
  timestamp: string
}

const testResults = ref<TestResult[]>([])

// 添加测试结果
const addTestResult = (result: Omit<TestResult, 'timestamp'>) => {
  testResults.value.unshift({
    ...result,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 只保留最近10条结果
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

// 通用测试函数
const testApi = async (apiName: string, apiCall: () => Promise<any>) => {
  try {
    await apiCall()
    addTestResult({
      api: apiName,
      method: 'GET',
      url: '/api/...',
      success: true
    })
    MessagePlugin.success(`${apiName} 测试成功`)
  } catch (error: any) {
    addTestResult({
      api: apiName,
      method: 'GET',
      url: '/api/...',
      success: false,
      error: error.message || '网络错误'
    })
    MessagePlugin.error(`${apiName} 测试失败: ${error.message || '网络错误'}`)
  }
}

// 系统服务测试
const testSystemAuth = () => testApi('authApi.getCurrentUser', () => authApi.getCurrentUser())
const testSystemUser = () => testApi('userApi.getPage', () => userApi.getPage({ current: 1, pageSize: 10 }))
const testSystemRole = () => testApi('roleApi.getAll', () => roleApi.getAll())
const testSystemPermission = () => testApi('permissionApi.getTree', () => permissionApi.getTree())
const testSystemDict = () => testApi('dictTypeApi.getAll', () => dictTypeApi.getAll())
const testSystemConfig = () => testApi('systemConfigApi.getPage', () => systemConfigApi.getPage({ current: 1, pageSize: 10 }))

// 虚拟化服务测试
const testVirtualizationNode = () => testApi('nodeApi.getAll', () => nodeApi.getAll())
const testVirtualizationVm = () => testApi('vmApi.getPage', () => vmApi.getPage({ current: 1, pageSize: 10 }))
const testVirtualizationTemplate = () => testApi('vmTemplateApi.getAll', () => vmTemplateApi.getAll())

// 订单服务测试
const testOrderOrder = () => testApi('orderApi.getPage', () => orderApi.getPage({ current: 1, pageSize: 10 }))
const testOrderProduct = () => testApi('productApi.getAll', () => productApi.getAll())

// 支付服务测试
const testPaymentPayment = () => testApi('paymentApi.getPage', () => paymentApi.getPage({ current: 1, pageSize: 10 }))
const testPaymentRefund = () => testApi('refundApi.getPage', () => refundApi.getPage({ current: 1, pageSize: 10 }))
const testPaymentAccount = () => testApi('userAccountApi.getPage', () => userAccountApi.getPage({ current: 1, pageSize: 10 }))
</script>

<style scoped lang="less">
.api-refactor-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--td-text-color-secondary);
    margin: 0;
  }
}

.test-card {
  margin-bottom: 24px;
}

.api-structure {
  .module-section {
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 16px 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .api-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 12px;
      
      .api-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: var(--td-bg-color-container);
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        
        .api-name {
          font-family: 'Monaco', 'Consolas', monospace;
          font-size: 14px;
          font-weight: 600;
          color: var(--td-brand-color);
        }
        
        .api-desc {
          flex: 1;
          margin: 0 12px;
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }
}

.type-validation {
  .validation-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 16px 0;
    }
    
    .type-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 12px;
      
      .type-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: var(--td-bg-color-container);
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        
        .type-name {
          font-family: 'Monaco', 'Consolas', monospace;
          font-size: 14px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }
        
        .type-desc {
          flex: 1;
          margin: 0 12px;
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }
}

.test-results {
  .no-results {
    text-align: center;
    padding: 40px;
    color: var(--td-text-color-secondary);
  }
  
  .results-list {
    .result-item {
      margin-bottom: 12px;
      padding: 16px;
      border-radius: 6px;
      border: 1px solid var(--td-border-level-1-color);
      
      &.success {
        background: var(--td-success-color-1);
        border-color: var(--td-success-color-3);
      }
      
      &.error {
        background: var(--td-error-color-1);
        border-color: var(--td-error-color-3);
      }
      
      .result-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .result-api {
          font-family: 'Monaco', 'Consolas', monospace;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }
        
        .result-time {
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }
      
      .result-details {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        
        .result-method {
          display: inline-block;
          margin-right: 12px;
          font-weight: 600;
        }
        
        .result-error {
          color: var(--td-error-color);
          margin-top: 4px;
        }
      }
    }
  }
}

.refactor-summary {
  .summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 32px;
        font-weight: 700;
        color: var(--td-brand-color);
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        color: var(--td-text-color-secondary);
      }
    }
  }
  
  .summary-benefits {
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 16px 0;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: var(--td-text-color-secondary);
        line-height: 1.5;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .api-refactor-test {
    padding: 16px;
  }
  
  .api-list {
    grid-template-columns: 1fr;
  }
  
  .type-list {
    grid-template-columns: 1fr;
  }
  
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
