<template>
  <div class="auth-guard-test">
    <div class="page-header">
      <h1>路由守卫测试</h1>
      <p>测试认证路由守卫机制和Token验证策略</p>
    </div>

    <!-- 当前认证状态 -->
    <t-card title="当前认证状态" class="test-card">
      <div class="auth-status">
        <div class="status-item">
          <label>登录状态:</label>
          <t-tag :theme="authStore.isLoggedIn ? 'success' : 'danger'">
            {{ authStore.isLoggedIn ? '已登录' : '未登录' }}
          </t-tag>
        </div>
        
        <div class="status-item">
          <label>Token存在:</label>
          <t-tag :theme="authStore.token ? 'success' : 'default'">
            {{ authStore.token ? '是' : '否' }}
          </t-tag>
        </div>
        
        <div class="status-item">
          <label>用户信息:</label>
          <t-tag :theme="authStore.currentUser ? 'success' : 'default'">
            {{ authStore.currentUser ? authStore.currentUser.username : '无' }}
          </t-tag>
        </div>
        
        <div class="status-item">
          <label>用户类型:</label>
          <t-tag :theme="getTypeTheme(authStore.currentUser?.userType)">
            {{ authStore.currentUser?.userType || '无' }}
          </t-tag>
        </div>
        
        <div class="status-item">
          <label>权限数量:</label>
          <span>{{ authStore.permissions.length }} 个</span>
        </div>
      </div>
      
      <t-divider />
      
      <div class="token-info" v-if="authStore.token">
        <h4>Token信息</h4>
        <div class="token-details">
          <div class="token-item">
            <label>Token长度:</label>
            <span>{{ authStore.token.length }} 字符</span>
          </div>
          <div class="token-item">
            <label>Token前缀:</label>
            <code>{{ authStore.token.substring(0, 20) }}...</code>
          </div>
          <div class="token-item">
            <label>过期时间:</label>
            <span>{{ tokenExpiration || '无法解析' }}</span>
          </div>
          <div class="token-item">
            <label>剩余时间:</label>
            <span :class="{ 'text-danger': timeUntilExpiration < 5 * 60 * 1000 }">
              {{ formatTimeRemaining(timeUntilExpiration) }}
            </span>
          </div>
        </div>
      </div>
    </t-card>

    <!-- Token验证测试 -->
    <t-card title="Token验证测试" class="test-card">
      <div class="test-actions">
        <t-space>
          <t-button theme="primary" @click="testValidateToken" :loading="validating">
            验证Token
          </t-button>
          <t-button theme="warning" @click="testCheckExpiration" :loading="checking">
            检查过期时间
          </t-button>
          <t-button theme="success" @click="testRefreshToken" :loading="refreshing">
            刷新Token
          </t-button>
          <t-button theme="danger" @click="testClearAuth">
            清除认证
          </t-button>
        </t-space>
      </div>
      
      <div v-if="testResult" class="test-result">
        <h4>测试结果:</h4>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </t-card>

    <!-- 路由跳转测试 -->
    <t-card title="路由跳转测试" class="test-card">
      <div class="route-tests">
        <h4>受保护的路由</h4>
        <div class="route-buttons">
          <t-button 
            v-for="route in protectedRoutes" 
            :key="route.path"
            @click="testRouteAccess(route.path)"
            :theme="route.theme"
            variant="outline"
          >
            {{ route.name }}
          </t-button>
        </div>
        
        <h4>公开路由</h4>
        <div class="route-buttons">
          <t-button 
            v-for="route in publicRoutes" 
            :key="route.path"
            @click="testRouteAccess(route.path)"
            theme="default"
            variant="outline"
          >
            {{ route.name }}
          </t-button>
        </div>
      </div>
    </t-card>

    <!-- 权限测试 -->
    <t-card title="权限测试" class="test-card">
      <div class="permission-tests">
        <h4>权限检查</h4>
        <div class="permission-grid">
          <div 
            v-for="permission in testPermissions" 
            :key="permission"
            class="permission-item"
          >
            <span>{{ permission }}</span>
            <t-tag :theme="authStore.hasPermission(permission) ? 'success' : 'danger'">
              {{ authStore.hasPermission(permission) ? '有权限' : '无权限' }}
            </t-tag>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 自动刷新测试 -->
    <t-card title="自动刷新测试" class="test-card">
      <div class="auto-refresh-test">
        <div class="test-controls">
          <t-button 
            :theme="autoRefreshEnabled ? 'danger' : 'success'"
            @click="toggleAutoRefresh"
          >
            {{ autoRefreshEnabled ? '停止自动检查' : '开始自动检查' }}
          </t-button>
          <span v-if="autoRefreshEnabled" class="refresh-status">
            下次检查: {{ nextCheckTime }}秒后
          </span>
        </div>
        
        <div class="refresh-log">
          <h4>检查日志:</h4>
          <div class="log-entries">
            <div 
              v-for="(log, index) in refreshLogs" 
              :key="index"
              class="log-entry"
              :class="log.type"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'
import { MessagePlugin } from 'tdesign-vue-next'
import { getTokenExpiration } from '@/utils/auth'

// 路由和状态
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const validating = ref(false)
const checking = ref(false)
const refreshing = ref(false)
const testResult = ref<any>(null)
const autoRefreshEnabled = ref(false)
const nextCheckTime = ref(30)
const refreshLogs = ref<Array<{ time: string; message: string; type: string }>>([])

// 定时器
let autoRefreshTimer: NodeJS.Timeout | null = null
let countdownTimer: NodeJS.Timeout | null = null

// 计算属性
const tokenExpiration = computed(() => {
  if (!authStore.token) return null
  const expiration = getTokenExpiration(authStore.token)
  return expiration ? expiration.toLocaleString() : null
})

const timeUntilExpiration = computed(() => {
  if (!authStore.token) return 0
  const expiration = getTokenExpiration(authStore.token)
  if (!expiration) return 0
  return expiration.getTime() - Date.now()
})

// 测试路由
const protectedRoutes = [
  { name: '仪表板', path: '/dashboard', theme: 'primary' },
  { name: '用户管理', path: '/system/users', theme: 'success' },
  { name: '角色管理', path: '/system/roles', theme: 'warning' },
  { name: '虚拟化管理', path: '/virtualization', theme: 'danger' }
]

const publicRoutes = [
  { name: '登录页面', path: '/login' },
  { name: '测试页面', path: '/test' },
  { name: '404页面', path: '/404' }
]

// 测试权限
const testPermissions = [
  'USER_VIEW',
  'USER_CREATE',
  'USER_UPDATE',
  'USER_DELETE',
  'ROLE_VIEW',
  'SYSTEM_CONFIG',
  'SUPER_ADMIN'
]

// 方法
const getTypeTheme = (userType?: string) => {
  switch (userType) {
    case 'SUPER_ADMIN': return 'danger'
    case 'ADMIN': return 'warning'
    case 'USER': return 'success'
    default: return 'default'
  }
}

const formatTimeRemaining = (milliseconds: number) => {
  if (milliseconds <= 0) return '已过期'
  
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

const addLog = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
  refreshLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 只保留最近20条日志
  if (refreshLogs.value.length > 20) {
    refreshLogs.value = refreshLogs.value.slice(0, 20)
  }
}

const testValidateToken = async () => {
  validating.value = true
  testResult.value = null
  
  try {
    const startTime = Date.now()
    const isValid = await authStore.validateToken()
    const endTime = Date.now()
    
    testResult.value = {
      action: 'validateToken',
      result: isValid,
      duration: `${endTime - startTime}ms`,
      timestamp: new Date().toISOString()
    }
    
    addLog(`Token验证${isValid ? '成功' : '失败'}`, isValid ? 'success' : 'error')
    MessagePlugin[isValid ? 'success' : 'error'](`Token验证${isValid ? '成功' : '失败'}`)
  } catch (error: any) {
    testResult.value = {
      action: 'validateToken',
      error: error.message,
      timestamp: new Date().toISOString()
    }
    addLog(`Token验证出错: ${error.message}`, 'error')
    MessagePlugin.error('Token验证出错')
  } finally {
    validating.value = false
  }
}

const testCheckExpiration = async () => {
  checking.value = true
  testResult.value = null
  
  try {
    const startTime = Date.now()
    const result = await authStore.checkTokenExpiration()
    const endTime = Date.now()
    
    testResult.value = {
      action: 'checkTokenExpiration',
      result,
      duration: `${endTime - startTime}ms`,
      timestamp: new Date().toISOString()
    }
    
    addLog(`Token过期检查完成`, 'info')
    MessagePlugin.success('Token过期检查完成')
  } catch (error: any) {
    testResult.value = {
      action: 'checkTokenExpiration',
      error: error.message,
      timestamp: new Date().toISOString()
    }
    addLog(`Token过期检查出错: ${error.message}`, 'error')
    MessagePlugin.error('Token过期检查出错')
  } finally {
    checking.value = false
  }
}

const testRefreshToken = async () => {
  refreshing.value = true
  testResult.value = null
  
  try {
    const startTime = Date.now()
    const newToken = await authStore.refreshTokenAction()
    const endTime = Date.now()
    
    testResult.value = {
      action: 'refreshToken',
      result: 'success',
      newTokenLength: newToken.length,
      duration: `${endTime - startTime}ms`,
      timestamp: new Date().toISOString()
    }
    
    addLog('Token刷新成功', 'success')
    MessagePlugin.success('Token刷新成功')
  } catch (error: any) {
    testResult.value = {
      action: 'refreshToken',
      error: error.message,
      timestamp: new Date().toISOString()
    }
    addLog(`Token刷新失败: ${error.message}`, 'error')
    MessagePlugin.error('Token刷新失败')
  } finally {
    refreshing.value = false
  }
}

const testClearAuth = () => {
  authStore.clearAuthData()
  testResult.value = {
    action: 'clearAuth',
    result: 'success',
    timestamp: new Date().toISOString()
  }
  addLog('认证数据已清除', 'warning')
  MessagePlugin.warning('认证数据已清除')
}

const testRouteAccess = (path: string) => {
  addLog(`尝试访问路由: ${path}`, 'info')
  router.push(path).catch(error => {
    addLog(`路由跳转失败: ${error.message}`, 'error')
    MessagePlugin.error('路由跳转失败')
  })
}

const toggleAutoRefresh = () => {
  if (autoRefreshEnabled.value) {
    stopAutoRefresh()
  } else {
    startAutoRefresh()
  }
}

const startAutoRefresh = () => {
  autoRefreshEnabled.value = true
  nextCheckTime.value = 30
  
  addLog('开始自动检查Token状态', 'info')
  
  // 倒计时定时器
  countdownTimer = setInterval(() => {
    nextCheckTime.value--
    if (nextCheckTime.value <= 0) {
      nextCheckTime.value = 30
    }
  }, 1000)
  
  // 检查定时器
  autoRefreshTimer = setInterval(async () => {
    try {
      await authStore.checkTokenExpiration()
      addLog('自动检查Token状态完成', 'success')
    } catch (error: any) {
      addLog(`自动检查Token状态失败: ${error.message}`, 'error')
    }
  }, 30000)
}

const stopAutoRefresh = () => {
  autoRefreshEnabled.value = false
  
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer)
    autoRefreshTimer = null
  }
  
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  
  addLog('停止自动检查Token状态', 'warning')
}

// 生命周期
onMounted(() => {
  addLog('路由守卫测试页面已加载', 'info')
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped lang="less">
.auth-guard-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.test-card {
  margin-bottom: 24px;
}

.auth-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  
  .status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    
    label {
      font-weight: 500;
      color: #374151;
      min-width: 80px;
    }
  }
}

.token-info {
  .token-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
    
    .token-item {
      display: flex;
      align-items: center;
      gap: 12px;
      
      label {
        font-weight: 500;
        color: #374151;
        min-width: 80px;
      }
      
      code {
        background: #f3f4f6;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }
}

.test-actions {
  margin-bottom: 16px;
}

.test-result {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  
  h4 {
    margin: 0 0 12px 0;
    color: #374151;
  }
  
  pre {
    background: #1f2937;
    color: #f9fafb;
    padding: 12px;
    border-radius: 6px;
    font-size: 12px;
    line-height: 1.5;
    overflow-x: auto;
    margin: 0;
  }
}

.route-tests {
  h4 {
    margin: 16px 0 12px 0;
    color: #374151;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  .route-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
  }
}

.permission-tests {
  h4 {
    margin: 0 0 16px 0;
    color: #374151;
  }
  
  .permission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    
    .permission-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8fafc;
      border-radius: 6px;
      border: 1px solid #e5e7eb;
    }
  }
}

.auto-refresh-test {
  .test-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    
    .refresh-status {
      color: #6b7280;
      font-size: 14px;
    }
  }
  
  .refresh-log {
    h4 {
      margin: 0 0 12px 0;
      color: #374151;
    }
    
    .log-entries {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      
      .log-entry {
        display: flex;
        gap: 12px;
        padding: 8px 12px;
        border-bottom: 1px solid #f3f4f6;
        font-size: 12px;
        
        &:last-child {
          border-bottom: none;
        }
        
        .log-time {
          color: #6b7280;
          min-width: 80px;
        }
        
        .log-message {
          flex: 1;
        }
        
        &.info {
          background: #f8fafc;
        }
        
        &.success {
          background: #f0fdf4;
          color: #166534;
        }
        
        &.warning {
          background: #fffbeb;
          color: #92400e;
        }
        
        &.error {
          background: #fef2f2;
          color: #991b1b;
        }
      }
    }
  }
}

.text-danger {
  color: #dc2626;
  font-weight: 500;
}

// 响应式设计
@media (max-width: 768px) {
  .auth-guard-test {
    padding: 16px;
  }
  
  .auth-status {
    grid-template-columns: 1fr;
  }
  
  .token-details {
    grid-template-columns: 1fr;
  }
  
  .permission-grid {
    grid-template-columns: 1fr;
  }
  
  .route-buttons {
    flex-direction: column;
    
    .t-button {
      width: 100%;
    }
  }
}
</style>
