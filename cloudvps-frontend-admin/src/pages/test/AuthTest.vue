<template>
  <div class="auth-test">
    <t-card title="权限系统测试">
      <div class="test-section">
        <h3>当前用户信息</h3>
        <t-descriptions :data="userDescriptions" />
      </div>

      <div class="test-section">
        <h3>权限列表</h3>
        <div class="permissions-grid">
          <t-tag
            v-for="permission in currentPermissions"
            :key="permission"
            theme="primary"
            variant="light"
          >
            {{ permission }}
          </t-tag>
        </div>
      </div>

      <div class="test-section">
        <h3>权限测试</h3>
        <t-space direction="vertical">
          <div>
            <t-button
              :theme="hasUserView ? 'success' : 'danger'"
              :disabled="!hasUserView"
            >
              用户查看权限 (USER_VIEW)
            </t-button>
            <span class="permission-status">
              {{ hasUserView ? '✅ 有权限' : '❌ 无权限' }}
            </span>
          </div>

          <div>
            <t-button
              :theme="hasUserCreate ? 'success' : 'danger'"
              :disabled="!hasUserCreate"
            >
              用户创建权限 (USER_CREATE)
            </t-button>
            <span class="permission-status">
              {{ hasUserCreate ? '✅ 有权限' : '❌ 无权限' }}
            </span>
          </div>

          <div>
            <t-button
              :theme="hasVmView ? 'success' : 'danger'"
              :disabled="!hasVmView"
            >
              虚拟机查看权限 (VM_VIEW)
            </t-button>
            <span class="permission-status">
              {{ hasVmView ? '✅ 有权限' : '❌ 无权限' }}
            </span>
          </div>

          <div>
            <t-button
              :theme="hasOrderView ? 'success' : 'danger'"
              :disabled="!hasOrderView"
            >
              订单查看权限 (ORDER_VIEW)
            </t-button>
            <span class="permission-status">
              {{ hasOrderView ? '✅ 有权限' : '❌ 无权限' }}
            </span>
          </div>
        </t-space>
      </div>

      <div class="test-section">
        <h3>角色测试</h3>
        <t-space>
          <t-tag
            v-for="role in currentRoles"
            :key="role"
            theme="success"
            variant="light"
          >
            {{ role }}
          </t-tag>
        </t-space>
      </div>

      <div class="test-section">
        <h3>操作测试</h3>
        <t-space>
          <t-button @click="testLogin">测试登录</t-button>
          <t-button @click="testLogout">测试登出</t-button>
          <t-button @click="refreshUserInfo">刷新用户信息</t-button>
        </t-space>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { useAuthStore } from '@/stores/modules/auth'

const authStore = useAuthStore()

// 计算属性
const currentPermissions = computed(() => authStore.permissions || [])
const currentRoles = computed(() => authStore.currentUser?.roles || [])

const userDescriptions = computed(() => [
  { label: '用户ID', content: authStore.currentUser?.id || '-' },
  { label: '用户名', content: authStore.currentUser?.username || '-' },
  { label: '真实姓名', content: authStore.currentUser?.realName || '-' },
  { label: '邮箱', content: authStore.currentUser?.email || '-' },
  { label: '手机号', content: authStore.currentUser?.phone || '-' },
  { label: '最后登录', content: authStore.currentUser?.lastLoginTime || '-' },
  { label: '是否超级管理员', content: authStore.isSuperAdmin ? '是' : '否' },
  { label: '登录状态', content: authStore.isLoggedIn ? '已登录' : '未登录' }
])

// 权限检查
const hasUserView = computed(() => authStore.hasPermission('USER_VIEW'))
const hasUserCreate = computed(() => authStore.hasPermission('USER_CREATE'))
const hasVmView = computed(() => authStore.hasPermission('VM_VIEW'))
const hasOrderView = computed(() => authStore.hasPermission('ORDER_VIEW'))

// 方法
const testLogin = async () => {
  try {
    await authStore.login({ username: 'admin', password: 'admin123' })
    MessagePlugin.success('登录成功')
  } catch (error: any) {
    MessagePlugin.error(error.message || '登录失败')
  }
}

const testLogout = async () => {
  try {
    await authStore.logout()
    MessagePlugin.success('登出成功')
  } catch (error: any) {
    MessagePlugin.error(error.message || '登出失败')
  }
}

const refreshUserInfo = async () => {
  try {
    await authStore.getCurrentUser()
    MessagePlugin.success('用户信息刷新成功')
  } catch (error: any) {
    MessagePlugin.error(error.message || '刷新失败')
  }
}
</script>

<style lang="less" scoped>
.auth-test {
  padding: 24px;
}

.test-section {
  margin-bottom: 32px;
  
  h3 {
    margin-bottom: 16px;
    color: var(--td-text-color-primary);
  }
}

.permissions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-status {
  margin-left: 12px;
  font-size: 14px;
}
</style>
