<template>
  <div class="theme-test">
    <div class="page-header">
      <h1>主题系统功能测试</h1>
      <p>测试主题切换、主色调设置、布局切换和界面元素控制功能</p>
    </div>

    <!-- 主题切换测试 -->
    <t-card title="主题切换测试" class="test-card">
      <div class="theme-controls">
        <div class="control-section">
          <h4>主题模式切换</h4>
          <t-space wrap>
            <t-button 
              theme="primary" 
              @click="setTheme('light')"
              :variant="settingsStore.themeMode === 'light' ? 'base' : 'outline'"
            >
              <SunnyIcon />
              浅色主题
            </t-button>
            <t-button 
              theme="primary" 
              @click="setTheme('dark')"
              :variant="settingsStore.themeMode === 'dark' ? 'base' : 'outline'"
            >
              <MoonIcon />
              暗色主题
            </t-button>
            <t-button 
              theme="primary" 
              @click="setTheme('auto')"
              :variant="settingsStore.themeMode === 'auto' ? 'base' : 'outline'"
            >
              <LaptopIcon />
              跟随系统
            </t-button>
          </t-space>
        </div>

        <div class="control-section">
          <h4>当前主题状态</h4>
          <div class="status-display">
            <div class="status-item">
              <span class="status-label">设置的主题模式:</span>
              <t-tag :theme="getThemeTagTheme(settingsStore.themeMode)">
                {{ getThemeModeLabel(settingsStore.themeMode) }}
              </t-tag>
            </div>
            <div class="status-item">
              <span class="status-label">实际主题模式:</span>
              <t-tag :theme="getThemeTagTheme(settingsStore.actualThemeMode)">
                {{ getThemeModeLabel(settingsStore.actualThemeMode) }}
              </t-tag>
            </div>
            <div class="status-item">
              <span class="status-label">HTML类名:</span>
              <code>{{ getCurrentThemeClass() }}</code>
            </div>
            <div class="status-item">
              <span class="status-label">data-theme属性:</span>
              <code>{{ getCurrentDataTheme() }}</code>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 主色调测试 -->
    <t-card title="主色调设置测试" class="test-card">
      <div class="color-controls">
        <div class="control-section">
          <h4>预设颜色</h4>
          <div class="color-presets">
            <div 
              v-for="color in testColors" 
              :key="color.value"
              class="color-preset"
              :class="{ active: settingsStore.primaryColor === color.value }"
              :style="{ backgroundColor: color.value }"
              :title="color.name"
              @click="setPrimaryColor(color.value)"
            >
              <CheckIcon v-if="settingsStore.primaryColor === color.value" class="color-check" />
            </div>
          </div>
        </div>

        <div class="control-section">
          <h4>自定义颜色</h4>
          <t-color-picker 
            v-model="customColor"
            @change="handleColorPickerChange"
            :swatch-colors="[]"
            format="HEX"
          />
        </div>

        <div class="control-section">
          <h4>当前主色调状态</h4>
          <div class="status-display">
            <div class="status-item">
              <span class="status-label">当前主色调:</span>
              <div class="color-display">
                <div 
                  class="color-swatch" 
                  :style="{ backgroundColor: settingsStore.primaryColor }"
                ></div>
                <code>{{ settingsStore.primaryColor }}</code>
              </div>
            </div>
            <div class="status-item">
              <span class="status-label">CSS变量值:</span>
              <code>{{ getCurrentBrandColor() }}</code>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 布局切换测试 -->
    <t-card title="布局切换测试" class="test-card">
      <div class="layout-controls">
        <div class="control-section">
          <h4>布局模式</h4>
          <t-space wrap>
            <t-button 
              v-for="layout in layoutModes"
              :key="layout.mode"
              @click="setLayout(layout.mode)"
              :variant="settingsStore.layoutMode === layout.mode ? 'base' : 'outline'"
              theme="primary"
            >
              {{ layout.name }}
            </t-button>
          </t-space>
        </div>

        <div class="control-section">
          <h4>当前布局状态</h4>
          <div class="status-display">
            <div class="status-item">
              <span class="status-label">当前布局:</span>
              <t-tag theme="primary">{{ getCurrentLayoutName() }}</t-tag>
            </div>
            <div class="status-item">
              <span class="status-label">Body类名:</span>
              <code>{{ getCurrentLayoutClass() }}</code>
            </div>
            <div class="status-item">
              <span class="status-label">data-layout属性:</span>
              <code>{{ getCurrentDataLayout() }}</code>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- UI元素控制测试 -->
    <t-card title="界面元素控制测试" class="test-card">
      <div class="ui-controls">
        <div class="control-section">
          <h4>界面元素开关</h4>
          <div class="ui-switches">
            <div 
              v-for="(value, key) in settingsStore.uiElements"
              :key="key"
              class="ui-switch-item"
            >
              <t-switch 
                v-model="settingsStore.uiElements[key]"
                @change="handleUIElementChange(key, $event)"
                :label="getUIElementName(key)"
              />
              <span class="ui-description">{{ getUIElementDescription(key) }}</span>
            </div>
          </div>
        </div>

        <div class="control-section">
          <h4>CSS变量状态</h4>
          <div class="css-variables">
            <div 
              v-for="(value, key) in settingsStore.uiElements"
              :key="key"
              class="css-var-item"
            >
              <span class="var-name">--ui-{{ key }}:</span>
              <code class="var-value">{{ value ? 'block' : 'none' }}</code>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 实时CSS变量监控 -->
    <t-card title="CSS变量实时监控" class="test-card">
      <div class="css-monitor">
        <div class="monitor-section">
          <h4>主题相关CSS变量</h4>
          <div class="css-vars">
            <div class="css-var">
              <span class="var-name">--td-brand-color:</span>
              <code class="var-value">{{ getCSSVariable('--td-brand-color') }}</code>
            </div>
            <div class="css-var">
              <span class="var-name">--td-bg-color-page:</span>
              <code class="var-value">{{ getCSSVariable('--td-bg-color-page') }}</code>
            </div>
            <div class="css-var">
              <span class="var-name">--td-text-color-primary:</span>
              <code class="var-value">{{ getCSSVariable('--td-text-color-primary') }}</code>
            </div>
          </div>
        </div>

        <div class="monitor-section">
          <h4>DOM属性监控</h4>
          <div class="dom-attrs">
            <div class="dom-attr">
              <span class="attr-name">html.className:</span>
              <code class="attr-value">{{ getHtmlClasses() }}</code>
            </div>
            <div class="dom-attr">
              <span class="attr-name">html[data-theme]:</span>
              <code class="attr-value">{{ getHtmlDataTheme() }}</code>
            </div>
            <div class="dom-attr">
              <span class="attr-name">body[data-layout]:</span>
              <code class="attr-value">{{ getBodyDataLayout() }}</code>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 测试说明 -->
    <t-card title="测试说明" class="test-card">
      <div class="test-instructions">
        <h4>🎯 测试目标</h4>
        <ul>
          <li>✅ 验证主题切换后页面颜色立即改变</li>
          <li>✅ 验证主色调设置后界面主色立即更新</li>
          <li>✅ 验证布局切换后页面结构实际发生变化</li>
          <li>✅ 验证界面元素开关后对应UI立即显示/隐藏</li>
          <li>✅ 验证刷新页面后所有设置保持不变</li>
          <li>✅ 验证CSS变量正确设置</li>
        </ul>

        <h4>🔍 测试步骤</h4>
        <ol>
          <li><strong>主题测试</strong>：点击主题按钮，观察页面背景和文字颜色变化</li>
          <li><strong>颜色测试</strong>：点击预设颜色或使用颜色选择器，观察按钮和链接颜色变化</li>
          <li><strong>布局测试</strong>：切换布局模式，观察页面结构变化</li>
          <li><strong>UI元素测试</strong>：切换界面元素开关，观察对应元素显示/隐藏</li>
          <li><strong>持久化测试</strong>：刷新页面，确认所有设置保持不变</li>
        </ol>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSettingsStore } from '@/stores/modules/settings'
import { MessagePlugin } from 'tdesign-vue-next'
import { SunnyIcon, MoonIcon, LaptopIcon, CheckIcon } from 'tdesign-icons-vue-next'
import type { ThemeMode, LayoutMode } from '@/types/settings'

// Store
const settingsStore = useSettingsStore()

// 响应式数据
const customColor = ref(settingsStore.primaryColor)

// 测试数据
const testColors = [
  { name: '蓝色', value: '#0052d9' },
  { name: '绿色', value: '#00a870' },
  { name: '红色', value: '#e34d59' },
  { name: '橙色', value: '#ed7b2f' },
  { name: '紫色', value: '#8b5cf6' },
  { name: '青色', value: '#06b6d4' },
  { name: '粉色', value: '#ec4899' },
  { name: '黄色', value: '#f59e0b' }
]

const layoutModes = [
  { mode: 'sidebar' as LayoutMode, name: '侧边导航' },
  { mode: 'header' as LayoutMode, name: '顶部导航' },
  { mode: 'mix' as LayoutMode, name: '混合布局' },
  { mode: 'split' as LayoutMode, name: '分割菜单' }
]

// 方法
const setTheme = (mode: ThemeMode) => {
  settingsStore.setThemeMode(mode)
  MessagePlugin.success(`主题已切换到: ${getThemeModeLabel(mode)}`)
}

const setPrimaryColor = (color: string) => {
  settingsStore.setPrimaryColor(color)
  customColor.value = color
  MessagePlugin.success(`主色调已设置为: ${color}`)
}

const handleColorPickerChange = (value: string) => {
  settingsStore.setPrimaryColor(value)
  MessagePlugin.success(`主色调已设置为: ${value}`)
}

const setLayout = (mode: LayoutMode) => {
  settingsStore.setLayoutMode(mode)
  const layout = layoutModes.find(l => l.mode === mode)
  MessagePlugin.success(`布局已切换到: ${layout?.name}`)
}

const handleUIElementChange = (key: string, value: boolean) => {
  settingsStore.setUIElement(key as any, value)
  MessagePlugin.success(`${getUIElementName(key)} ${value ? '已显示' : '已隐藏'}`)
}

const getThemeModeLabel = (mode: ThemeMode) => {
  const labels = {
    light: '浅色主题',
    dark: '暗色主题',
    auto: '跟随系统'
  }
  return labels[mode]
}

const getThemeTagTheme = (mode: ThemeMode) => {
  const themes = {
    light: 'primary',
    dark: 'warning',
    auto: 'success'
  }
  return themes[mode]
}

const getCurrentLayoutName = () => {
  const layout = layoutModes.find(l => l.mode === settingsStore.layoutMode)
  return layout?.name || settingsStore.layoutMode
}

const getUIElementName = (key: string) => {
  const names: Record<string, string> = {
    breadcrumb: '面包屑导航',
    tabs: 'Tab标签页',
    sidebarCollapseButton: '侧边栏折叠按钮',
    footer: '页脚',
    header: '顶部导航',
    sidebar: '侧边栏',
    logo: 'Logo',
    settingsButton: '设置按钮'
  }
  return names[key] || key
}

const getUIElementDescription = (key: string) => {
  const descriptions: Record<string, string> = {
    breadcrumb: '显示当前页面路径',
    tabs: '显示多标签页导航',
    sidebarCollapseButton: '显示侧边栏折叠控制按钮',
    footer: '显示页面底部信息',
    header: '显示顶部导航栏',
    sidebar: '显示侧边导航栏',
    logo: '显示系统Logo',
    settingsButton: '显示设置按钮'
  }
  return descriptions[key] || ''
}

// DOM和CSS监控方法
const getCurrentThemeClass = () => {
  return document.documentElement.className
}

const getCurrentDataTheme = () => {
  return document.documentElement.getAttribute('data-theme') || 'none'
}

const getCurrentLayoutClass = () => {
  return document.body.className
}

const getCurrentDataLayout = () => {
  return document.body.getAttribute('data-layout') || 'none'
}

const getCurrentBrandColor = () => {
  return getComputedStyle(document.documentElement).getPropertyValue('--td-brand-color').trim()
}

const getCSSVariable = (varName: string) => {
  return getComputedStyle(document.documentElement).getPropertyValue(varName).trim() || 'undefined'
}

const getHtmlClasses = () => {
  return document.documentElement.className || 'none'
}

const getHtmlDataTheme = () => {
  return document.documentElement.getAttribute('data-theme') || 'none'
}

const getBodyDataLayout = () => {
  return document.body.getAttribute('data-layout') || 'none'
}

// 定时更新监控数据
let updateInterval: number

onMounted(() => {
  updateInterval = setInterval(() => {
    // 强制更新响应式数据以反映DOM变化
  }, 1000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped lang="less">
.theme-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--td-text-color-secondary);
    margin: 0;
  }
}

.test-card {
  margin-bottom: 24px;
}

.control-section {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin: 0 0 16px 0;
  }
}

.status-display {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .status-label {
      font-weight: 500;
      color: var(--td-text-color-secondary);
      min-width: 120px;
    }
  }
}

.color-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  
  .color-preset {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    
    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &.active {
      border-color: var(--td-text-color-primary);
      box-shadow: 0 0 0 2px var(--td-bg-color-page);
    }
    
    .color-check {
      color: white;
      font-size: 16px;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
    }
  }
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid var(--td-border-level-1-color);
  }
  
  code {
    font-size: 12px;
    padding: 2px 6px;
    background: var(--td-bg-color-container);
    border-radius: 4px;
    color: var(--td-text-color-primary);
  }
}

.ui-switches {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  
  .ui-switch-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 12px;
    background: var(--td-bg-color-container);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);
    
    .ui-description {
      font-size: 12px;
      color: var(--td-text-color-placeholder);
      margin-left: 24px;
    }
  }
}

.css-variables,
.css-vars,
.dom-attrs {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .css-var-item,
  .css-var,
  .dom-attr {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--td-bg-color-container);
    border-radius: 4px;
    border: 1px solid var(--td-border-level-1-color);
    
    .var-name,
    .attr-name {
      font-weight: 500;
      color: var(--td-text-color-secondary);
      min-width: 150px;
    }
    
    .var-value,
    .attr-value {
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 12px;
      padding: 2px 6px;
      background: var(--td-bg-color-page);
      border-radius: 4px;
      color: var(--td-brand-color);
    }
  }
}

.test-instructions {
  h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin: 0 0 12px 0;
  }
  
  ul, ol {
    margin: 0 0 24px 0;
    padding-left: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    li {
      margin-bottom: 8px;
      color: var(--td-text-color-secondary);
      line-height: 1.5;
      
      strong {
        color: var(--td-text-color-primary);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .theme-test {
    padding: 16px;
  }
  
  .status-display {
    .status-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      
      .status-label {
        min-width: auto;
      }
    }
  }
  
  .ui-switches {
    grid-template-columns: 1fr;
  }
}
</style>
