<template>
  <div class="login-comparison">
    <div class="page-header">
      <h1>登录页面对比测试</h1>
      <p>对比TDesign表单组件优化方案和简化版本的效果</p>
    </div>

    <div class="comparison-grid">
      <!-- 方案A：优化的TDesign表单 -->
      <t-card title="方案A：优化的TDesign表单" class="comparison-card">
        <div class="solution-info">
          <h4>解决方案特点：</h4>
          <ul>
            <li>保持TDesign表单组件的完整功能</li>
            <li>通过CSS深度选择器优化布局</li>
            <li>配置表单属性解决对齐问题</li>
            <li>保留表单验证和提交处理</li>
          </ul>
          
          <h4>技术实现：</h4>
          <pre class="code-block">{{ tdesignSolution }}</pre>
          
          <div class="test-actions">
            <t-button theme="primary" @click="openTDesignLogin">
              测试TDesign优化版本
            </t-button>
            <p class="test-note">访问路径：/login-tdesign</p>
          </div>
        </div>
      </t-card>

      <!-- 方案B：简化版本 -->
      <t-card title="方案B：简化版本" class="comparison-card">
        <div class="solution-info">
          <h4>解决方案特点：</h4>
          <ul>
            <li>使用原生HTML表单结构</li>
            <li>单独使用TDesign输入组件</li>
            <li>完全控制布局和样式</li>
            <li>更简洁的代码结构</li>
          </ul>
          
          <h4>技术实现：</h4>
          <pre class="code-block">{{ simpleSolution }}</pre>
          
          <div class="test-actions">
            <t-button theme="success" @click="openSimpleLogin">
              测试简化版本（当前默认）
            </t-button>
            <p class="test-note">访问路径：/login</p>
          </div>
        </div>
      </t-card>
    </div>

    <!-- 对比分析 -->
    <t-card title="方案对比分析" class="analysis-card">
      <div class="analysis-table">
        <table>
          <thead>
            <tr>
              <th>对比项</th>
              <th>TDesign表单优化</th>
              <th>简化版本</th>
              <th>推荐</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>开发复杂度</td>
              <td>中等（需要深度样式调整）</td>
              <td>简单（直接控制）</td>
              <td>简化版本</td>
            </tr>
            <tr>
              <td>维护性</td>
              <td>依赖TDesign版本更新</td>
              <td>独立控制，稳定性好</td>
              <td>简化版本</td>
            </tr>
            <tr>
              <td>功能完整性</td>
              <td>完整的表单功能</td>
              <td>基础功能，可扩展</td>
              <td>TDesign优化</td>
            </tr>
            <tr>
              <td>样式控制</td>
              <td>需要覆盖默认样式</td>
              <td>完全自定义</td>
              <td>简化版本</td>
            </tr>
            <tr>
              <td>响应式设计</td>
              <td>依赖组件库响应式</td>
              <td>自定义响应式规则</td>
              <td>简化版本</td>
            </tr>
            <tr>
              <td>性能</td>
              <td>组件库开销</td>
              <td>轻量级</td>
              <td>简化版本</td>
            </tr>
          </tbody>
        </table>
      </div>
    </t-card>

    <!-- 推荐方案 -->
    <t-card title="推荐方案" class="recommendation-card">
      <div class="recommendation">
        <div class="recommendation-header">
          <t-icon name="check-circle" style="color: #52c41a; font-size: 24px;" />
          <h3>推荐使用简化版本</h3>
        </div>
        
        <div class="recommendation-content">
          <h4>推荐理由：</h4>
          <ol>
            <li><strong>更好的控制性</strong>：完全控制布局和样式，不受组件库限制</li>
            <li><strong>更高的稳定性</strong>：不依赖TDesign表单组件的内部实现</li>
            <li><strong>更简洁的代码</strong>：减少了复杂的样式覆盖和配置</li>
            <li><strong>更好的维护性</strong>：代码结构清晰，易于理解和修改</li>
            <li><strong>更佳的性能</strong>：减少了不必要的组件开销</li>
          </ol>
          
          <h4>实施建议：</h4>
          <ul>
            <li>将简化版本设为默认登录页面</li>
            <li>保留TDesign优化版本作为备选方案</li>
            <li>在其他表单页面中可以继续使用TDesign表单组件</li>
            <li>建立登录页面的设计规范和组件库</li>
          </ul>
        </div>
        
        <div class="recommendation-actions">
          <t-button theme="primary" size="large" @click="applyRecommendation">
            应用推荐方案
          </t-button>
          <t-button variant="outline" @click="openDocumentation">
            查看实施文档
          </t-button>
        </div>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// 计算属性
const tdesignSolution = computed(() => `// TDesign表单优化方案
<t-form
  :label-width="0"
  :colon="false"
  layout="vertical"
>
  <t-form-item>
    <t-input placeholder="用户名">
      <template #prefix-icon>
        <t-icon name="user-circle" />
      </template>
    </t-input>
  </t-form-item>
</t-form>

// CSS深度选择器优化
:deep(.t-form-item) {
  .t-form-item__label {
    display: none;
  }
  .t-form-item__content {
    width: 100%;
  }
}`)

const simpleSolution = computed(() => `// 简化版本方案
<form @submit.prevent="handleSubmit">
  <div class="form-item">
    <t-input
      v-model="formData.username"
      placeholder="用户名"
      size="large"
    >
      <template #prefix-icon>
        <t-icon name="user-circle" />
      </template>
    </t-input>
  </div>
</form>

// 简洁的样式控制
.form-item {
  margin-bottom: 20px;
}`)

// 方法
const openTDesignLogin = () => {
  window.open('/login-tdesign', '_blank')
}

const openSimpleLogin = () => {
  window.open('/login', '_blank')
}

const applyRecommendation = () => {
  MessagePlugin.success('推荐方案应用成功！简化版本已设为默认登录页面')
}

const openDocumentation = () => {
  MessagePlugin.info('实施文档功能开发中...')
}
</script>

<style scoped lang="less">
.login-comparison {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.comparison-card {
  .solution-info {
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #374151;
      margin: 16px 0 12px 0;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    ul, ol {
      margin: 0 0 16px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #4b5563;
        line-height: 1.5;
      }
    }
    
    .code-block {
      background: #1f2937;
      color: #f9fafb;
      padding: 16px;
      border-radius: 8px;
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 12px;
      line-height: 1.5;
      overflow-x: auto;
      margin-bottom: 16px;
    }
    
    .test-actions {
      text-align: center;

      .test-note {
        margin: 8px 0 0 0;
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
}

.analysis-card {
  margin-bottom: 24px;
  
  .analysis-table {
    overflow-x: auto;
    
    table {
      width: 100%;
      border-collapse: collapse;
      
      th, td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
      }
      
      th {
        background: #f9fafb;
        font-weight: 600;
        color: #374151;
      }
      
      td {
        color: #4b5563;
      }
      
      tr:hover {
        background: #f9fafb;
      }
    }
  }
}

.recommendation-card {
  .recommendation {
    .recommendation-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 24px;
      
      h3 {
        margin: 0;
        color: #1f2937;
        font-size: 20px;
        font-weight: 600;
      }
    }
    
    .recommendation-content {
      margin-bottom: 24px;
      
      h4 {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
        margin: 20px 0 12px 0;
        
        &:first-child {
          margin-top: 0;
        }
      }
      
      ol, ul {
        margin: 0 0 16px 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          color: #4b5563;
          line-height: 1.6;
          
          strong {
            color: #1f2937;
          }
        }
      }
    }
    
    .recommendation-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-comparison {
    padding: 16px;
  }
  
  .comparison-grid {
    grid-template-columns: 1fr;
  }
  
  .recommendation-actions {
    flex-direction: column;
    
    .t-button {
      width: 100%;
    }
  }
}
</style>
