<template>
  <div class="api-config-test">
    <div class="page-header">
      <h1>API配置管理测试</h1>
      <p>测试新的API前缀管理架构和微服务路由配置</p>
    </div>

    <!-- API配置信息 -->
    <t-card title="当前API配置" class="config-card">
      <div class="config-info">
        <div class="config-item">
          <label>微服务架构:</label>
          <t-tag :theme="apiConfig.useMicroserviceArchitecture ? 'success' : 'warning'">
            {{ apiConfig.useMicroserviceArchitecture ? '启用' : '禁用' }}
          </t-tag>
        </div>
        
        <div class="config-item">
          <label>基础URL:</label>
          <span>{{ apiConfig.baseURL }}</span>
        </div>
        
        <div class="config-item">
          <label>API前缀:</label>
          <span>{{ apiConfig.apiPrefix }}</span>
        </div>
        
        <div class="config-item">
          <label>自动添加前缀:</label>
          <t-tag :theme="apiConfig.autoAddPrefix ? 'success' : 'default'">
            {{ apiConfig.autoAddPrefix ? '是' : '否' }}
          </t-tag>
        </div>
        
        <div class="config-item">
          <label>请求超时:</label>
          <span>{{ apiConfig.timeout }}ms</span>
        </div>
      </div>
      
      <t-divider />
      
      <h4>微服务前缀配置</h4>
      <div class="service-prefixes">
        <div v-for="(prefix, service) in apiConfig.servicePrefixes" :key="service" class="prefix-item">
          <t-tag theme="primary" variant="light">{{ service }}</t-tag>
          <span>{{ prefix }}</span>
        </div>
      </div>
    </t-card>

    <!-- API路径示例 -->
    <t-card title="API路径构建示例" class="examples-card">
      <div class="examples-grid">
        <div class="example-item">
          <h5>系统服务 - 用户登录</h5>
          <div class="path-info">
            <span class="label">构建路径:</span>
            <code>{{ examples.systemLogin }}</code>
          </div>
        </div>
        
        <div class="example-item">
          <h5>系统服务 - 用户列表</h5>
          <div class="path-info">
            <span class="label">构建路径:</span>
            <code>{{ examples.usersList }}</code>
          </div>
        </div>
        
        <div class="example-item">
          <h5>虚拟化服务 - 节点列表</h5>
          <div class="path-info">
            <span class="label">构建路径:</span>
            <code>{{ examples.nodesList }}</code>
          </div>
        </div>
        
        <div class="example-item">
          <h5>订单服务 - 订单列表</h5>
          <div class="path-info">
            <span class="label">构建路径:</span>
            <code>{{ examples.ordersList }}</code>
          </div>
        </div>
        
        <div class="example-item">
          <h5>支付服务 - 交易记录</h5>
          <div class="path-info">
            <span class="label">构建路径:</span>
            <code>{{ examples.transactionsList }}</code>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 配置验证 -->
    <t-card title="配置验证" class="validation-card">
      <div class="validation-result">
        <div class="validation-status">
          <t-icon :name="validation.valid ? 'check-circle' : 'close-circle'" 
                  :style="{ color: validation.valid ? '#52c41a' : '#ff4d4f' }" />
          <span :class="validation.valid ? 'success' : 'error'">
            {{ validation.valid ? '配置有效' : '配置存在问题' }}
          </span>
        </div>
        
        <div v-if="!validation.valid" class="validation-errors">
          <h5>配置错误:</h5>
          <ul>
            <li v-for="error in validation.errors" :key="error" class="error-item">
              {{ error }}
            </li>
          </ul>
        </div>
      </div>
    </t-card>

    <!-- 实时测试 -->
    <t-card title="API路径实时测试" class="test-card">
      <t-form layout="inline" @submit="handleTest">
        <t-form-item label="服务类型">
          <t-select v-model="testForm.serviceType" placeholder="选择服务类型">
            <t-option value="system" label="系统服务" />
            <t-option value="virtualization" label="虚拟化服务" />
            <t-option value="order" label="订单服务" />
            <t-option value="payment" label="支付服务" />
          </t-select>
        </t-form-item>
        
        <t-form-item label="服务路径">
          <t-input v-model="testForm.servicePath" placeholder="如: /auth/login" />
        </t-form-item>
        
        <t-form-item>
          <t-button theme="primary" type="submit">
            构建路径
          </t-button>
        </t-form-item>
      </t-form>
      
      <div v-if="testResult" class="test-result">
        <h5>构建结果:</h5>
        <div class="result-info">
          <div class="result-item">
            <span class="label">API路径:</span>
            <code>{{ testResult.apiPath }}</code>
          </div>
          <div class="result-item">
            <span class="label">完整URL:</span>
            <code>{{ testResult.fullUrl }}</code>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 环境变量配置 -->
    <t-card title="环境变量配置参考" class="env-card">
      <div class="env-config">
        <h5>开发环境配置 (.env.development)</h5>
        <pre class="env-code">{{ envConfig }}</pre>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { getApiConfig, getApiConfigInfo, validateApiConfig, buildApiPath, buildApiUrl } from '@/utils/api-config'
import type { ServiceType } from '@/utils/api-config'

// 响应式数据
const apiConfig = ref(getApiConfig())
const validation = ref(validateApiConfig())
const testResult = ref<{ apiPath: string; fullUrl: string } | null>(null)

// 测试表单
const testForm = reactive({
  serviceType: 'system' as ServiceType,
  servicePath: '/auth/login'
})

// 计算属性
const examples = computed(() => {
  const configInfo = getApiConfigInfo()
  return configInfo.examples
})

const envConfig = computed(() => {
  return `# 微服务架构配置
VITE_USE_MICROSERVICE_ARCHITECTURE=true
VITE_API_PREFIX=/api
VITE_AUTO_ADD_PREFIX=true

# 网关配置
VITE_GATEWAY_HOST=localhost
VITE_GATEWAY_PORT=8080

# 微服务路由配置
VITE_SYSTEM_SERVICE_PREFIX=/system
VITE_VIRTUALIZATION_SERVICE_PREFIX=/virtualization
VITE_ORDER_SERVICE_PREFIX=/order
VITE_PAYMENT_SERVICE_PREFIX=/payment`
})

// 方法
const handleTest = () => {
  if (!testForm.servicePath) return
  
  const apiPath = buildApiPath(testForm.servicePath, testForm.serviceType)
  const fullUrl = buildApiUrl(testForm.servicePath, testForm.serviceType)
  
  testResult.value = {
    apiPath,
    fullUrl: `${apiConfig.value.baseURL}${fullUrl}`
  }
}

// 生命周期
onMounted(() => {
  // 初始化测试
  handleTest()
})
</script>

<style scoped lang="less">
.api-config-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.config-card,
.examples-card,
.validation-card,
.test-card,
.env-card {
  margin-bottom: 24px;
}

.config-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  
  .config-item {
    display: flex;
    align-items: center;
    gap: 12px;
    
    label {
      font-weight: 500;
      color: #374151;
      min-width: 100px;
    }
    
    span {
      color: #1f2937;
    }
  }
}

.service-prefixes {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  
  .prefix-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
  }
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  
  .example-item {
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    
    h5 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #374151;
    }
    
    .path-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .label {
        font-size: 12px;
        color: #6b7280;
      }
      
      code {
        background: #1f2937;
        color: #f9fafb;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 12px;
      }
    }
  }
}

.validation-result {
  .validation-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    
    .success {
      color: #52c41a;
      font-weight: 500;
    }
    
    .error {
      color: #ff4d4f;
      font-weight: 500;
    }
  }
  
  .validation-errors {
    .error-item {
      color: #ff4d4f;
      margin-bottom: 4px;
    }
  }
}

.test-result {
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  
  h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }
  
  .result-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .result-item {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .label {
        font-size: 12px;
        color: #6b7280;
        min-width: 80px;
      }
      
      code {
        background: #1f2937;
        color: #f9fafb;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 12px;
      }
    }
  }
}

.env-config {
  h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }
  
  .env-code {
    background: #1f2937;
    color: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 12px;
    line-height: 1.5;
    overflow-x: auto;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .api-config-test {
    padding: 16px;
  }
  
  .config-info {
    grid-template-columns: 1fr;
  }
  
  .examples-grid {
    grid-template-columns: 1fr;
  }
}
</style>
