<template>
  <div class="menu-route-test">
    <t-card :bordered="false">
      <template #header>
        <span>菜单路由测试</span>
      </template>
      
      <div class="test-content">
        <t-alert theme="info" message="🔍 菜单路由诊断工具" />
        
        <div class="route-info">
          <h3>当前路由信息</h3>
          <t-descriptions :data="currentRouteInfo" />
        </div>

        <div class="router-info">
          <h3>路由器信息</h3>
          <t-descriptions :data="routerInfo" />
        </div>

        <div class="test-navigation">
          <h3>路由测试</h3>
          <t-space>
            <t-button theme="primary" @click="navigateToMenus">
              跳转到菜单管理 (/system/menus)
            </t-button>
            <t-button theme="primary" @click="navigateToDict">
              跳转到字典管理 (/system/dict)
            </t-button>
            <t-button theme="primary" @click="navigateToUsers">
              跳转到用户管理 (/system/users)
            </t-button>
            <t-button variant="outline" @click="checkRoutes">
              检查所有路由
            </t-button>
          </t-space>
        </div>

        <div class="route-list" v-if="allRoutes.length > 0">
          <h3>所有已注册路由</h3>
          <t-table
            :data="allRoutes"
            :columns="routeColumns"
            :pagination="false"
            size="small"
            stripe
          />
        </div>

        <div class="auto-route-info">
          <h3>自动路由生成信息</h3>
          <t-button @click="checkAutoRoutes">检查自动路由</t-button>
          <pre v-if="autoRouteInfo" class="auto-route-output">{{ autoRouteInfo }}</pre>
        </div>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'

const route = useRoute()
const router = useRouter()

// 响应式数据
const allRoutes = ref<any[]>([])
const autoRouteInfo = ref('')

// 当前路由信息
const currentRouteInfo = computed(() => [
  { label: '路由路径', value: route.path },
  { label: '路由名称', value: route.name },
  { label: '路由参数', value: JSON.stringify(route.params) },
  { label: '查询参数', value: JSON.stringify(route.query) },
  { label: '路由元信息', value: JSON.stringify(route.meta) }
])

// 路由器信息
const routerInfo = computed(() => [
  { label: '路由模式', value: router.options.history.location },
  { label: '总路由数', value: router.getRoutes().length },
  { label: '当前位置', value: router.currentRoute.value.fullPath }
])

// 路由表格列配置
const routeColumns = [
  {
    colKey: 'path',
    title: '路径',
    width: 300
  },
  {
    colKey: 'name',
    title: '名称',
    width: 200
  },
  {
    colKey: 'component',
    title: '组件',
    width: 300,
    cell: (h: any, { row }: any) => {
      return row.component?.toString().substring(0, 50) + '...' || '无'
    }
  },
  {
    colKey: 'meta',
    title: '元信息',
    cell: (h: any, { row }: any) => {
      return JSON.stringify(row.meta || {})
    }
  }
]

// 导航方法
const navigateToMenus = () => {
  console.log('🚀 尝试跳转到菜单管理页面')
  router.push('/system/menus').catch(error => {
    console.error('❌ 跳转失败:', error)
    MessagePlugin.error(`跳转失败: ${error.message}`)
  })
}

const navigateToDict = () => {
  console.log('🚀 尝试跳转到字典管理页面')
  router.push('/system/dict').catch(error => {
    console.error('❌ 跳转失败:', error)
    MessagePlugin.error(`跳转失败: ${error.message}`)
  })
}

const navigateToUsers = () => {
  console.log('🚀 尝试跳转到用户管理页面')
  router.push('/system/users').catch(error => {
    console.error('❌ 跳转失败:', error)
    MessagePlugin.error(`跳转失败: ${error.message}`)
  })
}

// 检查所有路由
const checkRoutes = () => {
  const routes = router.getRoutes()
  allRoutes.value = routes.map(route => ({
    path: route.path,
    name: route.name,
    component: route.components?.default,
    meta: route.meta
  }))
  
  // 检查特定路由是否存在
  const menusRoute = routes.find(r => r.path === '/system/menus')
  const dictRoute = routes.find(r => r.path === '/system/dict')
  const usersRoute = routes.find(r => r.path === '/system/users')
  
  console.log('📊 路由检查结果:')
  console.log('- /system/menus:', menusRoute ? '✅ 存在' : '❌ 不存在')
  console.log('- /system/dict:', dictRoute ? '✅ 存在' : '❌ 不存在')
  console.log('- /system/users:', usersRoute ? '✅ 存在' : '❌ 不存在')
  console.log('- 总路由数:', routes.length)
  
  MessagePlugin.success(`检查完成，总计 ${routes.length} 个路由`)
}

// 检查自动路由
const checkAutoRoutes = async () => {
  try {
    // 动态导入自动路由工具
    const { generateAutoRoutes, printRoutes } = await import('@/router/utils/autoRoutes')
    
    const autoRoutes = generateAutoRoutes()
    
    let output = '🚀 自动生成的路由:\n'
    output += `总计: ${autoRoutes.length} 个路由\n\n`
    
         autoRoutes.forEach(route => {
       output += `📄 ${(route as any).path} -> ${(route as any).name} (${route.meta?.title})\n`
       if (route.meta?.permissions) {
         output += `   权限: ${route.meta.permissions.join(', ')}\n`
       }
     })
    
    autoRouteInfo.value = output
    
    console.log('📋 自动路由详情:')
    printRoutes(autoRoutes)
    
  } catch (error) {
    console.error('❌ 检查自动路由失败:', error)
    MessagePlugin.error('检查自动路由失败')
  }
}

// 初始化
onMounted(() => {
  console.log('📍 菜单路由测试页面已加载')
  checkRoutes()
})
</script>

<style scoped>
.menu-route-test {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: var(--td-comp-margin-l);
}

.route-info,
.router-info,
.test-navigation,
.route-list,
.auto-route-info {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  background-color: var(--td-bg-color-container);
}

.route-info h3,
.router-info h3,
.test-navigation h3,
.route-list h3,
.auto-route-info h3 {
  margin: 0 0 var(--td-comp-margin-l) 0;
  color: var(--td-text-color-primary);
  font-size: var(--td-font-size-title-medium);
  font-weight: var(--td-font-weight-semi-bold);
}

.auto-route-output {
  background: var(--td-bg-color-code);
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-s);
  border-radius: var(--td-radius-small);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--td-font-size-s);
  overflow-x: auto;
  margin-top: var(--td-comp-margin-s);
}
</style> 