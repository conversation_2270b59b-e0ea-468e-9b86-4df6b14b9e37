<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 登录头部 -->
      <div class="login-header">
        <Logo 
          size="large"
          theme="light"
          title="CloudVPS"
          subtitle="管理后台"
        />
        <p class="login-description">云服务平台管理系统</p>
      </div>
      
      <!-- 简化登录表单 -->
      <form class="login-form" @submit.prevent="handleSubmit">
        <!-- 用户名 -->
        <div class="form-item">
          <t-input
            v-model="formData.username"
            placeholder="请输入用户名"
            size="large"
            clearable
            :status="errors.username ? 'error' : 'default'"
          >
            <template #prefix-icon>
              <UserCircleIcon />
            </template>
          </t-input>
          <div v-if="errors.username" class="error-message">{{ errors.username }}</div>
        </div>
        
        <!-- 密码 -->
        <div class="form-item">
          <t-input
            v-model="formData.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            clearable
            :status="errors.password ? 'error' : 'default'"
          >
            <template #prefix-icon>
              <LockOnIcon />
            </template>
          </t-input>
          <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
        </div>
        

        
        <!-- 记住我 -->
        <div class="form-item">
          <t-checkbox v-model="formData.rememberMe">
            记住我
          </t-checkbox>
          <a href="#" class="forgot-password" @click.prevent="handleForgotPassword">
            忘记密码？
          </a>
        </div>
        
        <!-- 登录按钮 -->
        <div class="form-item">
          <t-button
            theme="primary"
            size="large"
            type="submit"
            :loading="loading"
            block
          >
            登录
          </t-button>
        </div>
      </form>
      
      <!-- 底部链接 -->
      <div class="login-footer">
        <span>还没有账号？</span>
        <a href="#" @click.prevent="handleRegister">立即注册</a>
      </div>
    </div>
    
    <!-- 版权信息 -->
    <div class="copyright">
      © 2024 CloudVPS. All rights reserved.
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'
import { authApi } from '@/api/system/auth'
import type { LoginRequest } from '@/api/system/types/auth'
import Logo from '@/components/common/Logo/index.vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { UserCircleIcon, LockOnIcon } from 'tdesign-icons-vue-next'

// 路由和状态
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 错误信息
const errors = reactive({
  username: '',
  password: ''
})

// 表单验证
const validateForm = () => {
  // 重置错误
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })

  let isValid = true

  // 验证用户名
  if (!formData.username) {
    errors.username = '请输入用户名'
    isValid = false
  } else if (formData.username.length < 3) {
    errors.username = '用户名长度不能少于3个字符'
    isValid = false
  } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
    errors.username = '用户名只能包含字母、数字和下划线'
    isValid = false
  }

  // 验证密码
  if (!formData.password) {
    errors.password = '请输入密码'
    isValid = false
  } else if (formData.password.length < 6) {
    errors.password = '密码长度不能少于6个字符'
    isValid = false
  }

  return isValid
}



// 处理登录
const handleSubmit = async () => {
  console.log('🚀 开始登录流程...')

  if (!validateForm()) {
    console.log('❌ 表单验证失败')
    return
  }

  loading.value = true
  try {
    console.log('📝 登录数据:', formData)

    const loginData: LoginRequest = {
      username: formData.username,
      password: formData.password,
      rememberMe: formData.rememberMe
    }

    console.log('🔐 调用登录API...')
    await authStore.login(loginData)

    console.log('✅ 登录成功')
    MessagePlugin.success('登录成功')

    // 登录成功，获取重定向地址
    const redirect = route.query.redirect as string
    const redirectPath = redirect && redirect !== '/login' ? redirect : '/dashboard'

    console.log('🔄 重定向到:', redirectPath)
    router.push(redirectPath)
  } catch (error: any) {
    console.error('❌ 登录失败:', error)

    const errorMessage = error?.response?.data?.message || error?.message || '登录失败'

    if (error?.response?.status === 401) {
      MessagePlugin.error('用户名或密码错误')
    } else if (error?.response?.status === 429) {
      MessagePlugin.error('登录尝试过多，请稍后重试')
    } else {
      MessagePlugin.error(errorMessage)
    }
  } finally {
    loading.value = false
  }
}

// 忘记密码
const handleForgotPassword = () => {
  MessagePlugin.info('忘记密码功能开发中...')
}

// 注册
const handleRegister = () => {
  MessagePlugin.info('注册功能开发中...')
}
</script>

<style scoped lang="less">
.login-container {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
}

.login-wrapper {
  width: 100%;
  max-width: 380px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  box-sizing: border-box;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 32px;
  
  .login-description {
    color: #6b7280;
    margin: 16px 0 0 0;
    font-size: 14px;
  }
}

.login-form {
  .form-item {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .error-message {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
    line-height: 1.4;
  }
  

  
  .forgot-password {
    color: #0052d9;
    text-decoration: none;
    font-size: 14px;
    float: right;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  font-size: 14px;
  color: #6b7280;
  
  a {
    color: #0052d9;
    text-decoration: none;
    margin-left: 4px;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.copyright {
  position: absolute;
  bottom: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  text-align: center;
}

// 响应式设计
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-wrapper {
    padding: 32px 24px;
    max-width: 100%;
  }
}
</style>
