<template>
  <div class="payments-overview">
    <div class="page-header">
      <h1>支付管理</h1>
      <p>管理交易记录、支付渠道和财务统计</p>
    </div>
    
    <t-row :gutter="24">
      <t-col :span="6">
        <t-card title="今日交易" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="swap" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">89</div>
              <div class="card-label">成功交易</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              查看交易
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="支付渠道" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="creditcard" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">6</div>
              <div class="card-label">可用渠道</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              管理渠道
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="今日收款" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="money" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">¥12,456</div>
              <div class="card-label">收款金额</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              查看详情
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="退款处理" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="rollback" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">3</div>
              <div class="card-label">待处理退款</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              处理退款
            </t-button>
          </div>
        </t-card>
      </t-col>
    </t-row>
    
    <t-row :gutter="24" class="mt-6">
      <t-col :span="12">
        <t-card title="最新交易">
          <div class="transaction-list">
            <div class="transaction-item">
              <div class="transaction-info">
                <div class="transaction-id">#TXN-2024-001</div>
                <div class="transaction-method">支付宝</div>
              </div>
              <div class="transaction-status">
                <t-tag theme="success">成功</t-tag>
              </div>
              <div class="transaction-amount">¥299.00</div>
            </div>
            <div class="transaction-item">
              <div class="transaction-info">
                <div class="transaction-id">#TXN-2024-002</div>
                <div class="transaction-method">微信支付</div>
              </div>
              <div class="transaction-status">
                <t-tag theme="success">成功</t-tag>
              </div>
              <div class="transaction-amount">¥599.00</div>
            </div>
            <div class="transaction-item">
              <div class="transaction-info">
                <div class="transaction-id">#TXN-2024-003</div>
                <div class="transaction-method">银行转账</div>
              </div>
              <div class="transaction-status">
                <t-tag theme="warning">处理中</t-tag>
              </div>
              <div class="transaction-amount">¥1,199.00</div>
            </div>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="12">
        <t-card title="财务统计">
          <div class="finance-stats">
            <div class="stat-item">
              <span class="stat-label">本月收入</span>
              <span class="stat-value">¥234,567</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">本月退款</span>
              <span class="stat-value">¥3,456</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">手续费</span>
              <span class="stat-value">¥1,234</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">净收入</span>
              <span class="stat-value">¥229,877</span>
            </div>
          </div>
        </t-card>
      </t-col>
    </t-row>
  </div>
</template>

<script setup lang="ts">
// 支付概览页面逻辑
</script>

<style scoped lang="less">
.payments-overview {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.overview-card {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  
  .card-icon {
    color: #0052d9;
  }
  
  .card-content {
    flex: 1;
    
    .card-number {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 4px;
    }
    
    .card-label {
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.card-actions {
  border-top: 1px solid #f3f4f6;
  padding-top: 16px;
  margin-top: 16px;
}

.transaction-list {
  .transaction-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .transaction-info {
      flex: 1;
      
      .transaction-id {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
      }
      
      .transaction-method {
        font-size: 12px;
        color: #6b7280;
      }
    }
    
    .transaction-amount {
      font-weight: 600;
      color: #00a870;
    }
  }
}

.finance-stats {
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .stat-label {
      color: #6b7280;
    }
    
    .stat-value {
      font-weight: 600;
      color: #1f2937;
    }
  }
}
</style>
