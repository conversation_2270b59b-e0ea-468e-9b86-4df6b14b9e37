<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="角色权限分配"
    width="600px"
    :confirm-btn="null"
    :cancel-btn="null"
    @close="handleClose"
  >
    <div class="role-assign-container">
      <!-- 菜单信息 -->
      <div v-if="menu" class="menu-info-section">
        <t-alert theme="info" :close="false">
          <template #message>
            为菜单 <strong>{{ menu.title }}</strong> 分配角色权限
          </template>
        </t-alert>
      </div>

      <!-- 角色列表 -->
      <div class="role-list-section">
        <h4 class="section-title">选择角色</h4>
        <t-loading :loading="loading">
          <div v-if="roleList.length > 0" class="role-grid">
            <t-checkbox-group v-model="selectedRoles" @change="handleRoleChange">
              <div
                v-for="role in roleList"
                :key="role.id"
                class="role-item"
              >
                <t-checkbox :value="role.id" class="role-checkbox">
                  <div class="role-content">
                    <div class="role-header">
                      <span class="role-name">{{ role.name }}</span>
                      <t-tag
                        :theme="role.enabled ? 'success' : 'warning'"
                        variant="light"
                        size="small"
                      >
                        {{ role.enabled ? '启用' : '禁用' }}
                      </t-tag>
                    </div>
                    <div v-if="role.description" class="role-description">
                      {{ role.description }}
                    </div>
                    <div class="role-stats">
                      已分配菜单: {{ getRoleMenuCount(role.id) }}
                    </div>
                  </div>
                </t-checkbox>
              </div>
            </t-checkbox-group>
          </div>
          
          <t-empty
            v-else-if="!loading"
            description="暂无角色数据"
            size="small"
          />
        </t-loading>
      </div>

      <!-- 分配状态 -->
      <div v-if="selectedRoles.length > 0" class="assign-status-section">
        <h4 class="section-title">分配预览</h4>
        <div class="status-list">
          <div
            v-for="roleId in selectedRoles"
            :key="roleId"
            class="status-item"
          >
            <t-space align="center">
              <span class="role-name">{{ getRoleName(roleId) }}</span>
              <t-tag
                :theme="isRoleHasMenu(roleId) ? 'warning' : 'success'"
                variant="light"
                size="small"
              >
                {{ isRoleHasMenu(roleId) ? '已有权限' : '新增权限' }}
              </t-tag>
            </t-space>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <template #footer>
      <t-space>
        <t-button variant="outline" @click="handleClose">取消</t-button>
        <t-button
          theme="primary"
          :loading="submitting"
          :disabled="selectedRoles.length === 0"
          @click="handleConfirm"
        >
          确定分配
        </t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { menuApi } from '@/api/system/menu'
import type { MenuResponse } from '@/api/system/types/menu'

// 角色类型定义（简化）
interface Role {
  id: number
  name: string
  description?: string
  enabled: boolean
}

// Props
interface Props {
  visible: boolean
  menu?: MenuResponse | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  menu: null
})

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const submitting = ref(false)
const roleList = ref<Role[]>([])
const selectedRoles = ref<number[]>([])
const roleMenuMap = reactive<Record<number, number[]>>({})

// 获取角色列表
const fetchRoleList = async () => {
  try {
    loading.value = true
    // 这里应该调用角色API，暂时使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    roleList.value = [
      {
        id: 1,
        name: '超级管理员',
        description: '拥有系统所有权限',
        enabled: true
      },
      {
        id: 2,
        name: '系统管理员',
        description: '系统管理相关权限',
        enabled: true
      },
      {
        id: 3,
        name: '普通用户',
        description: '基础功能权限',
        enabled: true
      },
      {
        id: 4,
        name: '访客',
        description: '只读权限',
        enabled: false
      }
    ]
  } catch (error) {
    console.error('获取角色列表失败:', error)
    MessagePlugin.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 获取角色已有菜单权限
const fetchRoleMenus = async () => {
  try {
    for (const role of roleList.value) {
      const response = await menuApi.getRoleMenus(role.id)
      if (response.data && response.data.data) {
        roleMenuMap[role.id] = response.data.data
      }
    }
  } catch (error) {
    console.error('获取角色菜单权限失败:', error)
  }
}

// 工具函数
const getRoleName = (roleId: number) => {
  const role = roleList.value.find(r => r.id === roleId)
  return role?.name || `角色${roleId}`
}

const getRoleMenuCount = (roleId: number) => {
  return roleMenuMap[roleId]?.length || 0
}

const isRoleHasMenu = (roleId: number) => {
  if (!props.menu) return false
  return roleMenuMap[roleId]?.includes(props.menu.id) || false
}

// 事件处理
const handleRoleChange = (value: number[]) => {
  selectedRoles.value = value
}

const handleConfirm = async () => {
  if (!props.menu || selectedRoles.value.length === 0) return
  
  try {
    submitting.value = true
    
    // 为每个选中的角色分配菜单权限
    for (const roleId of selectedRoles.value) {
      const currentMenus = roleMenuMap[roleId] || []
      
      // 如果角色还没有这个菜单权限，则添加
      if (!currentMenus.includes(props.menu.id)) {
        const newMenus = [...currentMenus, props.menu.id]
        await menuApi.assignRoleMenus(roleId, newMenus)
        roleMenuMap[roleId] = newMenus
      }
    }
    
    MessagePlugin.success('权限分配成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('分配权限失败:', error)
    MessagePlugin.error('分配权限失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
  selectedRoles.value = []
}

// 监听属性变化
watch(
  () => props.visible,
  (newVisible) => {
    dialogVisible.value = newVisible
    if (newVisible) {
      selectedRoles.value = []
      if (roleList.value.length === 0) {
        fetchRoleList().then(() => {
          fetchRoleMenus()
        })
      } else {
        fetchRoleMenus()
      }
    }
  },
  { immediate: true }
)

watch(
  () => dialogVisible.value,
  (newVisible) => {
    if (!newVisible) {
      emit('update:visible', false)
    }
  }
)
</script>

<style scoped>
.role-assign-container {
  padding: 8px 0;
}

.menu-info-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

/* 角色列表 */
.role-list-section {
  margin-bottom: 24px;
}

.role-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.role-item {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
  background: var(--td-bg-color-container);
}

.role-item:hover {
  border-color: var(--td-brand-color-light);
  background: var(--td-brand-color-1);
}

.role-checkbox {
  width: 100%;
}

.role-checkbox :deep(.t-checkbox__label) {
  width: 100%;
  margin-left: 8px;
}

.role-content {
  width: 100%;
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.role-name {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.role-description {
  font-size: 12px;
  color: var(--td-text-color-secondary);
  margin-bottom: 8px;
  line-height: 1.4;
}

.role-stats {
  font-size: 12px;
  color: var(--td-text-color-placeholder);
}

/* 分配状态 */
.assign-status-section {
  border-top: 1px solid var(--td-border-level-1-color);
  padding-top: 16px;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  padding: 8px 12px;
  background: var(--td-bg-color-container);
  border-radius: 4px;
  border: 1px solid var(--td-border-level-1-color);
}

.status-item .role-name {
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-assign-container {
    padding: 4px 0;
  }
  
  .role-grid {
    max-height: 250px;
  }
  
  .role-item {
    padding: 10px;
  }
  
  .role-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style> 