<template>
  <div class="menu-form-container">
    <t-loading :loading="loading" size="medium">
      <t-form
        ref="formRef"
        :data="formData"
        :rules="formRules"
        @submit="handleSubmit"
        layout="vertical"
        class="menu-form"
      >
        <!-- 基础信息 -->
        <div class="form-section">
          <h4 class="section-title">基础信息</h4>
          
          <t-form-item label="菜单类型" name="type">
            <t-radio-group v-model="formData.type" @change="handleTypeChange">
              <t-radio value="DIRECTORY">目录</t-radio>
              <t-radio value="MENU">菜单</t-radio>
              <t-radio value="BUTTON">按钮</t-radio>
            </t-radio-group>
          </t-form-item>
          
          <t-form-item label="父级菜单" name="parentId">
            <t-tree-select
              v-model="formData.parentId"
              :data="parentMenuOptions"
              :keys="treeSelectKeys"
              placeholder="选择父级菜单（留空为根菜单）"
              clearable
              filterable
            />
          </t-form-item>

          <t-form-item label="菜单名称" name="title">
            <t-input 
              v-model="formData.title" 
              placeholder="请输入菜单显示名称"
              @blur="generateMenuName"
            />
          </t-form-item>
          
          <t-form-item label="菜单编码" name="name">
            <t-input 
              v-model="formData.name" 
              placeholder="请输入菜单唯一编码"
            />
          </t-form-item>

          <t-form-item label="菜单图标" name="icon">
            <t-input 
              v-model="formData.icon" 
              placeholder="请选择或输入图标"
              readonly
              @click="iconSelectVisible = true"
            >
              <template #prefix-icon>
                <component 
                  v-if="formData.icon" 
                  :is="getIconComponent(formData.icon)" 
                />
              </template>
              <template #suffix-icon>
                <browse-icon />
              </template>
            </t-input>
          </t-form-item>
          
          <t-form-item label="排序号" name="sort">
            <t-input-number 
              v-model="formData.sort" 
              :min="0" 
              :max="9999"
              placeholder="数字越小越靠前"
            />
          </t-form-item>

          <t-form-item label="菜单描述" name="description">
            <t-textarea 
              v-model="formData.description" 
              placeholder="请输入菜单描述（可选）"
              :maxlength="200"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </t-form-item>
        </div>

        <!-- 路由信息 -->
        <div v-if="formData.type !== 'BUTTON'" class="form-section">
          <h4 class="section-title">路由信息</h4>
          
          <t-form-item label="路由路径" name="path">
            <t-input 
              v-model="formData.path" 
              placeholder="请输入路由路径，如：/system/user"
            />
          </t-form-item>
          
          <t-form-item v-if="formData.type === 'MENU'" label="组件路径" name="component">
            <t-input 
              v-model="formData.component" 
              placeholder="请输入组件路径，如：system/User"
            />
          </t-form-item>

          <t-form-item label="路由参数" name="params">
            <t-input 
              v-model="formData.params" 
              placeholder="请输入路由参数（可选）"
            />
          </t-form-item>
          
          <t-form-item label="页面设置">
            <t-space>
              <t-checkbox v-model="formData.cache">页面缓存</t-checkbox>
              <t-checkbox v-model="formData.external">外部链接</t-checkbox>
            </t-space>
          </t-form-item>
        </div>

        <!-- 权限信息 -->
        <div class="form-section">
          <h4 class="section-title">权限控制</h4>
          
          <t-form-item label="权限标识" name="permission">
            <t-input 
              v-model="formData.permission" 
              placeholder="请输入权限标识，如：SYSTEM_USER_VIEW"
            />
          </t-form-item>
          
          <t-form-item label="状态设置">
            <t-space>
              <t-checkbox v-model="formData.visible">菜单可见</t-checkbox>
              <t-checkbox v-model="formData.enabled">启用状态</t-checkbox>
            </t-space>
          </t-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <t-space>
            <t-button 
              theme="primary" 
              type="submit"
              :loading="loading"
            >
              {{ mode === 'create' ? '创建菜单' : '保存修改' }}
            </t-button>
            
            <t-button 
              variant="outline" 
              @click="handleCancel"
            >
              取消
            </t-button>
            
            <t-button 
              v-if="mode === 'edit'" 
              variant="outline" 
              @click="handleReset"
            >
              重置
            </t-button>
          </t-space>
        </div>
      </t-form>
    </t-loading>

    <!-- 图标选择对话框 -->
    <!-- <IconSelectDialog
      v-model:visible="iconSelectVisible"
      v-model:selected="formData.icon"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import {
  BrowseIcon,
  ViewModuleIcon,
} from 'tdesign-icons-vue-next'
// import IconSelectDialog from './IconSelectDialog.vue'
import { menuApi } from '@/api/system/menu'
import type { MenuResponse, MenuCreateRequest, MenuUpdateRequest, MenuType, MenuTreeNode } from '@/api/system/types/menu'

// Props
interface Props {
  menu: MenuResponse | null
  mode: 'edit' | 'create'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  menu: null,
  mode: 'create',
  loading: false
})

// Emits
interface Emits {
  (e: 'save'): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref()
const iconSelectVisible = ref(false)
const parentMenuOptions = ref<MenuTreeNode[]>([])

// 表单数据
const formData = reactive<MenuCreateRequest>({
  name: '',
  title: '',
  type: 'MENU',
  sort: 1,
  visible: true,
  enabled: true,
  cache: false,
  external: false
})

// 树选择器配置
const treeSelectKeys = {
  value: 'id',
  label: 'title',
  children: 'children'
}

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '菜单名称不能为空', type: 'error' }
  ],
  name: [
    { required: true, message: '菜单编码不能为空', type: 'error' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_-]*$/, message: '菜单编码格式不正确', type: 'error' }
  ],
  type: [
    { required: true, message: '菜单类型不能为空', type: 'error' }
  ],
  path: [
    { validator: validatePath, type: 'error' }
  ],
  component: [
    { validator: validateComponent, type: 'error' }
  ],
  permission: [
    { pattern: /^[A-Z][A-Z0-9_]*$/, message: '权限标识格式不正确，应为大写字母和下划线', type: 'error' }
  ]
}

const getIconComponent = (iconName: string) => {
  // 这里可以根据图标名称返回对应的图标组件
  // 简化处理，返回默认图标
  return ViewModuleIcon
}

// 表单验证函数
function validatePath(val: string) {
  if (!val && formData.type !== 'BUTTON') {
    return { result: false, message: '路由路径不能为空', type: 'error' }
  }
  if (val && !val.startsWith('/')) {
    return { result: false, message: '路由路径必须以/开头', type: 'error' }
  }
  return { result: true }
}

function validateComponent(val: string) {
  if (!val && formData.type === 'MENU') {
    return { result: false, message: '组件路径不能为空', type: 'error' }
  }
  return { result: true }
}

// 事件处理
const handleTypeChange = () => {
  // 切换类型时清空相关字段
  if (formData.type === 'BUTTON') {
    formData.path = ''
    formData.component = ''
    formData.cache = false
    formData.external = false
  }
}

const generateMenuName = () => {
  if (!formData.name && formData.title) {
    // 根据标题生成编码
    formData.name = formData.title
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-zA-Z0-9_-]/g, '')
  }
}

const handleSubmit = async ({ validateResult }: any) => {
  if (validateResult === true) {
    try {
      if (props.mode === 'create') {
        await menuApi.create(formData)
      } else {
        await menuApi.update(props.menu!.id, formData as MenuUpdateRequest)
      }
      emit('save')
      MessagePlugin.success(props.mode === 'create' ? '创建成功' : '保存成功')
    } catch (error) {
      console.error('保存菜单失败:', error)
      MessagePlugin.error('保存失败')
    }
  }
}

const handleCancel = () => {
  emit('cancel')
}

const handleReset = () => {
  if (props.menu) {
    resetFormData(props.menu)
  }
}

// 重置表单数据
const resetFormData = (menu?: MenuResponse | null) => {
  if (menu) {
    Object.assign(formData, {
      name: menu.name || '',
      title: menu.title || '',
      icon: menu.icon || '',
      path: menu.path || '',
      component: menu.component || '',
      type: menu.type || 'MENU',
      parentId: menu.parentId || undefined,
      sort: menu.sort || 1,
      visible: menu.visible ?? true,
      enabled: menu.enabled ?? true,
      permission: menu.permission || '',
      description: menu.description || '',
      params: menu.params || '',
      cache: menu.cache ?? false,
      external: menu.external ?? false
    })
  } else {
    Object.assign(formData, {
      name: '',
      title: '',
      icon: '',
      path: '',
      component: '',
      type: 'MENU' as MenuType,
      parentId: undefined,
      sort: 1,
      visible: true,
      enabled: true,
      permission: '',
      description: '',
      params: '',
      cache: false,
      external: false
    })
  }
}

// 获取父级菜单选项
const fetchParentMenuOptions = async () => {
  try {
    const response = await menuApi.getTree()
    if (response.data && response.data.data) {
      parentMenuOptions.value = response.data.data.filter((menu: MenuTreeNode) => 
        menu.type !== 'BUTTON'
      )
    }
  } catch (error) {
    console.error('获取父级菜单失败:', error)
  }
}

// 监听菜单数据变化
watch(
  () => props.menu,
  (newMenu) => {
    resetFormData(newMenu)
  },
  { immediate: true }
)

// 初始化
nextTick(() => {
  fetchParentMenuOptions()
  resetFormData(props.menu)
})
</script>

<style scoped>
.menu-form-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.menu-form {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  max-width: 800px;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: 6px;
  border: 1px solid var(--td-border-level-1-color);
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  border-bottom: 1px solid var(--td-border-level-1-color);
  padding-bottom: 8px;
}

.form-actions {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid var(--td-border-level-1-color);
}
</style> 