<template>
  <div class="icon-grid-container">
    <div v-if="icons.length > 0" class="icon-grid">
      <div
        v-for="iconName in icons"
        :key="iconName"
        :class="['icon-item', { 'selected': selected === iconName }]"
        @click="handleSelect(iconName)"
        :title="iconName"
      >
        <div class="icon-display">
          <component 
            :is="getIconComponent(iconName)" 
            class="icon"
          />
        </div>
        <div class="icon-name">{{ iconName }}</div>
      </div>
    </div>
    
    <t-empty 
      v-else
      image="search"
      description="未找到匹配的图标"
      size="small"
    />
  </div>
</template>

<script setup lang="ts">
import {
  DashboardIcon,
  FolderIcon,
  FileIcon,
  UserIcon,
  SettingIcon,
  HomeIcon,
  MenuIcon,
  ListIcon,
  TableIcon,
  ChartIcon,
  CalendarIcon,
  TimeIcon,
  TagIcon,
  DesktopIcon,
  LaptopIcon,
  MobileIcon,
  PrintIcon,
  WifiIcon,
  AddIcon,
  DeleteIcon,
  EditIcon,
  SearchIcon,
  FilterIcon,
  UploadIcon,
  DownloadIcon,
  CopyIcon,
  RefreshIcon,
  CloseIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckIcon,
  ErrorIcon,
  HelpIcon,
} from 'tdesign-icons-vue-next'

// Props
interface Props {
  icons: string[]
  selected?: string
}

const props = withDefaults(defineProps<Props>(), {
  icons: () => [],
  selected: ''
})

// Emits
interface Emits {
  (e: 'select', iconName: string): void
}

const emit = defineEmits<Emits>()

// 图标映射（只使用确实存在的图标）
const iconMap: Record<string, any> = {
  'dashboard': DashboardIcon,
  'folder': FolderIcon,
  'file': FileIcon,
  'user': UserIcon,
  'users': UserIcon, // 使用 UserIcon 代替
  'setting': SettingIcon,
  'home': HomeIcon,
  'menu': MenuIcon,
  'list': ListIcon,
  'table': TableIcon,
  'chart': ChartIcon,
  'pie-chart': ChartIcon, // 使用通用图表图标
  'bar-chart': ChartIcon,
  'line-chart': ChartIcon,
  'calendar': CalendarIcon,
  'clock': TimeIcon,
  'tag': TagIcon,
  'tags': TagIcon,
  'system': SettingIcon,
  'database': DesktopIcon,
  'server': DesktopIcon,
  'desktop': DesktopIcon,
  'laptop': LaptopIcon,
  'mobile': MobileIcon,
  'tablet': MobileIcon,
  'monitor': DesktopIcon,
  'printer': PrintIcon,
  'scanner': PrintIcon,
  'router': DesktopIcon,
  'network': WifiIcon,
  'wifi': WifiIcon,
  'bluetooth': WifiIcon,
  'usb': DesktopIcon,
  'hard-drive': DesktopIcon,
  'shop': HomeIcon,
  'cart': ListIcon,
  'money': TagIcon,
  'credit-card': TagIcon,
  'bank': HomeIcon,
  'wallet': TagIcon,
  'receipt': FileIcon,
  'invoice': FileIcon,
  'order': ListIcon,
  'package': FolderIcon,
  'truck': DesktopIcon,
  'plane': DesktopIcon,
  'ship': DesktopIcon,
  'warehouse': HomeIcon,
  'factory': HomeIcon,
  'building': HomeIcon,
  'add': AddIcon,
  'delete': DeleteIcon,
  'edit': EditIcon,
  'view': SearchIcon,
  'search': SearchIcon,
  'filter': FilterIcon,
  'sort': FilterIcon,
  'upload': UploadIcon,
  'download': DownloadIcon,
  'import': UploadIcon,
  'export': DownloadIcon,
  'print': PrintIcon,
  'copy': CopyIcon,
  'cut': CopyIcon,
  'paste': CopyIcon,
  'undo': RefreshIcon,
  'redo': RefreshIcon,
  'refresh': RefreshIcon,
  'save': CheckIcon,
  'close': CloseIcon,
  'back': ChevronLeftIcon,
  'forward': ChevronRightIcon,
  'up': ChevronUpIcon,
  'down': ChevronDownIcon,
  'left': ChevronLeftIcon,
  'right': ChevronRightIcon,
  'check': CheckIcon,
  'info': HelpIcon,
  'warning': ErrorIcon,
  'error': ErrorIcon,
  'success': CheckIcon,
  'help': HelpIcon,
  'question': HelpIcon,
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  return iconMap[iconName] || SettingIcon
}

// 事件处理
const handleSelect = (iconName: string) => {
  emit('select', iconName)
}
</script>

<style scoped>
.icon-grid-container {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  padding: 8px 0;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--td-bg-color-container);
}

.icon-item:hover {
  border-color: var(--td-brand-color);
  background: var(--td-brand-color-1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.icon-item.selected {
  border-color: var(--td-brand-color);
  background: var(--td-brand-color-1);
  box-shadow: 0 0 0 2px var(--td-brand-color-2);
}

.icon-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
}

.icon {
  font-size: 20px;
  color: var(--td-text-color-primary);
}

.icon-item:hover .icon {
  color: var(--td-brand-color);
}

.icon-item.selected .icon {
  color: var(--td-brand-color);
}

.icon-name {
  font-size: 12px;
  color: var(--td-text-color-secondary);
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
  max-width: 100%;
}

.icon-item:hover .icon-name {
  color: var(--td-brand-color);
}

.icon-item.selected .icon-name {
  color: var(--td-brand-color);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 6px;
  }
  
  .icon-item {
    padding: 8px 4px;
  }
  
  .icon-display {
    width: 24px;
    height: 24px;
    margin-bottom: 6px;
  }
  
  .icon {
    font-size: 16px;
  }
  
  .icon-name {
    font-size: 10px;
  }
}
</style> 