<template>
  <div class="menu-detail-view">
    <div v-if="menu" class="menu-info">
      <!-- 基础信息 -->
      <div class="info-section">
        <h4 class="section-title">基础信息</h4>
        <t-descriptions :column="2" class="info-descriptions">
          <t-descriptions-item label="菜单名称">
            <t-space align="center">
              <component 
                :is="getMenuIcon(menu)" 
                class="menu-icon"
              />
              <span class="menu-title">{{ menu.title }}</span>
              <t-tag 
                :theme="getMenuTypeTheme(menu.type)"
                variant="light"
                size="small"
              >
                {{ getMenuTypeLabel(menu.type) }}
              </t-tag>
            </t-space>
          </t-descriptions-item>
          
          <t-descriptions-item label="菜单编码">
            <t-tag variant="outline" size="small">{{ menu.name }}</t-tag>
          </t-descriptions-item>
          
          <t-descriptions-item label="排序号">{{ menu.sort }}</t-descriptions-item>
          
          <t-descriptions-item label="状态">
            <t-space>
              <t-tag 
                :theme="menu.enabled ? 'success' : 'warning'"
                variant="light"
                size="small"
              >
                {{ menu.enabled ? '启用' : '禁用' }}
              </t-tag>
              <t-tag 
                :theme="menu.visible ? 'success' : 'default'"
                variant="light"
                size="small"
              >
                {{ menu.visible ? '显示' : '隐藏' }}
              </t-tag>
            </t-space>
          </t-descriptions-item>
          
          <t-descriptions-item v-if="menu.description" label="描述" :span="2">
            {{ menu.description }}
          </t-descriptions-item>
        </t-descriptions>
      </div>

      <!-- 路由信息 -->
      <div v-if="menu.type !== 'BUTTON'" class="info-section">
        <h4 class="section-title">路由信息</h4>
        <t-descriptions :column="2" class="info-descriptions">
          <t-descriptions-item v-if="menu.path" label="路由路径">
            <t-tag variant="outline" size="small">{{ menu.path }}</t-tag>
          </t-descriptions-item>
          
          <t-descriptions-item v-if="menu.component" label="组件路径">
            <t-tag variant="outline" size="small">{{ menu.component }}</t-tag>
          </t-descriptions-item>
          
          <t-descriptions-item label="页面缓存">
            <t-tag 
              :theme="menu.cache ? 'success' : 'default'"
              variant="light"
              size="small"
            >
              {{ menu.cache ? '启用' : '禁用' }}
            </t-tag>
          </t-descriptions-item>
          
          <t-descriptions-item label="外部链接">
            <t-tag 
              :theme="menu.external ? 'warning' : 'default'"
              variant="light"
              size="small"
            >
              {{ menu.external ? '是' : '否' }}
            </t-tag>
          </t-descriptions-item>
          
          <t-descriptions-item v-if="menu.params" label="路由参数" :span="2">
            <t-tag variant="outline" size="small">{{ menu.params }}</t-tag>
          </t-descriptions-item>
        </t-descriptions>
      </div>

      <!-- 权限信息 -->
      <div class="info-section">
        <h4 class="section-title">权限信息</h4>
        <t-descriptions :column="2" class="info-descriptions">
          <t-descriptions-item v-if="menu.permission" label="权限标识">
            <t-tag variant="outline" size="small">{{ menu.permission }}</t-tag>
          </t-descriptions-item>
          
          <t-descriptions-item label="父级菜单">
            <span v-if="parentMenuTitle">{{ parentMenuTitle }}</span>
            <span v-else class="text-placeholder">无（根菜单）</span>
          </t-descriptions-item>
        </t-descriptions>
      </div>

      <!-- 系统信息 -->
      <div class="info-section">
        <h4 class="section-title">系统信息</h4>
        <t-descriptions :column="2" class="info-descriptions">
          <t-descriptions-item label="创建时间">
            {{ formatTime(menu.createdTime) }}
          </t-descriptions-item>
          <t-descriptions-item label="更新时间">
            {{ formatTime(menu.updatedTime) }}
          </t-descriptions-item>
          <t-descriptions-item label="创建者">
            -
          </t-descriptions-item>
          <t-descriptions-item label="更新者">
            -
          </t-descriptions-item>
        </t-descriptions>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <t-space>
          <t-button
            v-if="hasPermission('SYSTEM_MENU_UPDATE')"
            theme="primary"
            @click="handleEdit"
          >
            <template #icon>
              <edit-icon />
            </template>
            编辑菜单
          </t-button>
          
          <t-button
            v-if="hasPermission('SYSTEM_MENU_CREATE')"
            variant="outline"
            @click="handleCreateSub"
          >
            <template #icon>
              <add-icon />
            </template>
            新建子菜单
          </t-button>
          
          <t-button
            v-if="hasPermission('SYSTEM_MENU_DELETE')"
            theme="danger"
            variant="outline"
            @click="handleDelete"
          >
            <template #icon>
              <delete-icon />
            </template>
            删除菜单
          </t-button>
        </t-space>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <t-empty
        description="请从左侧选择菜单项查看详情"
      >
        <template #action>
          <t-button
            v-if="hasPermission('SYSTEM_MENU_CREATE')"
            theme="primary"
            @click="handleCreateRoot"
          >
            <template #icon>
              <add-icon />
            </template>
            新建菜单
          </t-button>
        </template>
      </t-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  EditIcon,
  AddIcon,
  DeleteIcon,
  FolderIcon,
  ViewModuleIcon,
  SettingIcon,
} from 'tdesign-icons-vue-next'
import { usePermission } from '@/composables/usePermission'
import { formatDateTime } from '@/utils/date'
import type { MenuResponse, MenuType } from '@/api/system/types/menu'

// Props
interface Props {
  menu: MenuResponse | null
  parentMenuTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  menu: null,
  parentMenuTitle: ''
})

// Emits
interface Emits {
  (e: 'edit', menu: MenuResponse): void
  (e: 'create-sub', menu: MenuResponse): void
  (e: 'create-root'): void
  (e: 'delete', menu: MenuResponse): void
}

const emit = defineEmits<Emits>()

// 权限检查
const { hasPermission } = usePermission()

// 菜单类型相关
const getMenuTypeLabel = (type: MenuType) => {
  const labels = {
    DIRECTORY: '目录',
    MENU: '菜单',
    BUTTON: '按钮'
  }
  return labels[type] || type
}

const getMenuTypeTheme = (type: MenuType) => {
  const themes = {
    DIRECTORY: 'primary',
    MENU: 'success',
    BUTTON: 'warning'
  }
  return themes[type] || 'default'
}

const getMenuIcon = (menu: MenuResponse) => {
  if (menu.icon) {
    return getIconComponent(menu.icon)
  }
  
  switch (menu.type) {
    case 'DIRECTORY':
      return FolderIcon
    case 'MENU':
      return ViewModuleIcon
    case 'BUTTON':
      return SettingIcon
    default:
      return ViewModuleIcon
  }
}

const getIconComponent = (iconName: string) => {
  // 这里可以根据图标名称返回对应的图标组件
  // 简化处理，返回默认图标
  return ViewModuleIcon
}

// 格式化时间
const formatTime = (time?: string) => {
  return time ? formatDateTime(time) : '-'
}

// 事件处理
const handleEdit = () => {
  if (props.menu) {
    emit('edit', props.menu)
  }
}

const handleCreateSub = () => {
  if (props.menu) {
    emit('create-sub', props.menu)
  }
}

const handleCreateRoot = () => {
  emit('create-root')
}

const handleDelete = () => {
  if (props.menu) {
    emit('delete', props.menu)
  }
}
</script>

<style scoped>
.menu-detail-view {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
}

.menu-info {
  max-width: 800px;
}

.info-section {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  border-bottom: 1px solid var(--td-border-level-1-color);
  padding-bottom: 8px;
}

.info-descriptions {
  margin-bottom: 0;
}

.menu-title {
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.menu-icon {
  font-size: 16px;
  color: var(--td-text-color-secondary);
}

.text-placeholder {
  color: var(--td-text-color-placeholder);
}

.action-section {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid var(--td-border-level-1-color);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
}
</style> 