<template>
  <div class="menu-tree-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <t-input
        v-model="searchKeyword"
        placeholder="搜索菜单..."
        size="small"
        clearable
        @enter="handleSearch"
        @clear="handleSearch"
      >
        <template #prefix-icon>
          <search-icon />
        </template>
      </t-input>
    </div>

    <!-- 树形结构 -->
    <div class="tree-section">
      <t-loading :loading="loading" size="medium">
        <t-tree
          v-if="treeData.length > 0"
          ref="treeRef"
          v-model:activated="selectedKeys"
          v-model:expanded="expandedKeys"
          :data="treeData"
          :keys="treeKeys"
          :hover="!disabled"
          expand-on-click-node
          transition
          :activable="!disabled"
          :filter="filterMethod"
          @active="handleNodeSelect"
          @expand="handleNodeExpand"
          :class="['menu-tree', { 'menu-tree--disabled': disabled }]"
        >
          <template #icon="{ node }">
            <component
              :is="getMenuIcon(node.data)"
              class="menu-icon"
            />
          </template>
          
          <template #label="{ node }">
            <div class="tree-node-content">
              <div class="node-main">
                <span class="node-title">{{ node.data.title }}</span>
                <t-space size="4px" class="node-tags">
                  <t-tag
                    :theme="getMenuTypeTheme(node.data.type)"
                    variant="light"
                    size="small"
                  >
                    {{ getMenuTypeLabel(node.data.type) }}
                  </t-tag>
                  <t-tag
                    v-if="!node.data.enabled"
                    theme="warning"
                    variant="light"
                    size="small"
                  >
                    禁用
                  </t-tag>
                  <t-tag
                    v-if="!node.data.visible"
                    theme="default"
                    variant="light"
                    size="small"
                  >
                    隐藏
                  </t-tag>
                </t-space>
              </div>
              
              <div class="node-actions">
                                 <t-dropdown
                   :options="getNodeActionOptions(node.data)"
                   @click="(data: any) => handleNodeAction(data, node.data)"
                   trigger="hover"
                   placement="bottom-right"
                 >
                  <t-button
                    variant="text"
                    size="small"
                    shape="square"
                    class="action-btn"
                  >
                    <template #icon>
                      <more-icon />
                    </template>
                  </t-button>
                </t-dropdown>
              </div>
            </div>
          </template>
        </t-tree>

        <!-- 空状态 -->
        <t-empty
          v-else-if="!loading"
          :description="searchKeyword ? '未找到匹配的菜单' : '暂无菜单数据'"
        >
          <template #action>
            <t-button
              v-if="hasPermission('SYSTEM_MENU_CREATE')"
              theme="primary"
              @click="handleCreateRoot"
            >
              <template #icon>
                <add-icon />
              </template>
              创建根菜单
            </t-button>
          </template>
        </t-empty>
      </t-loading>
    </div>

    <!-- 菜单统计 -->
    <div v-if="menuStats.total > 0" class="stats-section">
      <t-divider />
      <div class="stats-content">
        <t-space size="16px">
          <span class="stats-item">
            <t-tag theme="primary" variant="light" size="small">
              总计: {{ menuStats.total }}
            </t-tag>
          </span>
          <span class="stats-item">
            <t-tag theme="success" variant="light" size="small">
              目录: {{ menuStats.directory }}
            </t-tag>
          </span>
          <span class="stats-item">
            <t-tag theme="warning" variant="light" size="small">
              菜单: {{ menuStats.menu }}
            </t-tag>
          </span>
          <span class="stats-item">
            <t-tag theme="default" variant="light" size="small">
              按钮: {{ menuStats.button }}
            </t-tag>
          </span>
        </t-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import {
  SearchIcon,
  MoreIcon,
  AddIcon,
  EditIcon,
  DeleteIcon,
  FolderIcon,
  ViewModuleIcon,
  SettingIcon,
} from 'tdesign-icons-vue-next'
import { menuApi } from '@/api/system/menu'
import { usePermission } from '@/composables/usePermission'
import type { MenuResponse, MenuTreeNode, MenuType } from '@/api/system/types/menu'

// Props
interface Props {
  selectedId?: number | null
  loading?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selectedId: null,
  loading: false,
  disabled: false
})

// Emits
interface Emits {
  (e: 'select', menu: MenuResponse): void
  (e: 'create', parent: MenuResponse): void
  (e: 'edit', menu: MenuResponse): void
  (e: 'delete', menu: MenuResponse): void
  (e: 'refresh'): void
  (e: 'total-change', total: number): void
  (e: 'update:selectedId', id: number | null): void
}

const emit = defineEmits<Emits>()

// 权限检查
const { hasPermission } = usePermission()

// 响应式数据
const treeRef = ref()
const searchKeyword = ref('')
const treeData = ref<MenuTreeNode[]>([])
const selectedKeys = ref<number[]>([])
const expandedKeys = ref<number[]>([])

// 树配置
const treeKeys = {
  value: 'id',
  label: 'title',
  children: 'children'
}

// 菜单统计
const menuStats = reactive({
  total: 0,
  directory: 0,
  menu: 0,
  button: 0
})

// 计算属性
const filterMethod = computed(() => {
  if (!searchKeyword.value) return undefined
  
  return (node: MenuTreeNode) => {
    const keyword = searchKeyword.value.toLowerCase()
    return node.title.toLowerCase().includes(keyword) ||
           node.name.toLowerCase().includes(keyword) ||
           (node.permission && node.permission.toLowerCase().includes(keyword))
  }
})

// 菜单类型和图标相关
const getMenuTypeLabel = (type: MenuType) => {
  const labels = {
    DIRECTORY: '目录',
    MENU: '菜单',
    BUTTON: '按钮'
  }
  return labels[type] || type
}

const getMenuTypeTheme = (type: MenuType) => {
  const themes = {
    DIRECTORY: 'primary',
    MENU: 'success',
    BUTTON: 'warning'
  }
  return themes[type] || 'default'
}

const getMenuIcon = (menu: MenuResponse) => {
  if (menu.icon) {
    // 这里可以根据图标名称返回对应的图标组件
    // 简化处理，根据类型返回默认图标
  }
  
  switch (menu.type) {
    case 'DIRECTORY':
      return FolderIcon
    case 'MENU':
      return ViewModuleIcon
    case 'BUTTON':
      return SettingIcon
    default:
      return ViewModuleIcon
  }
}

// 获取节点操作选项
const getNodeActionOptions = (menu: MenuResponse) => {
  const options = []
  
  if (hasPermission('SYSTEM_MENU_CREATE')) {
    if (menu.type === 'DIRECTORY' || menu.type === 'MENU') {
      options.push({
        content: '新建子菜单',
        value: 'create-child'
      })
    }
  }
  
  if (hasPermission('SYSTEM_MENU_UPDATE')) {
    options.push({
      content: '编辑',
      value: 'edit'
    })
  }
  
  if (hasPermission('SYSTEM_MENU_DELETE')) {
    options.push({
      content: '删除',
      value: 'delete',
      theme: 'error'
    })
  }
  
  return options
}

// 事件处理
const handleSearch = () => {
  // 搜索时展开所有匹配的节点
  if (searchKeyword.value) {
    expandAllMatched()
  }
}

const handleNodeSelect = (value: number[], context: any) => {
  // 如果树被禁用，不处理选择事件
  if (props.disabled) {
    return
  }
  
  console.log('🌳 Tree activated:', value, context)
  
  if (value.length > 0) {
    const selectedId = value[0]
    emit('update:selectedId', selectedId)
    
    // 查找选中的菜单数据
    const selectedMenu = findMenuById(selectedId)
    if (selectedMenu) {
      console.log('📋 发送选中菜单:', selectedMenu)
      emit('select', selectedMenu)
    }
  } else {
    emit('update:selectedId', null)
  }
}

const handleNodeExpand = (value: number[]) => {
  expandedKeys.value = value
}

const handleNodeAction = (data: any, menu: MenuResponse) => {
  const { value } = data
  
  switch (value) {
    case 'create-child':
      emit('create', menu)
      break
    case 'edit':
      emit('edit', menu)
      break
    case 'delete':
      handleDeleteConfirm(menu)
      break
  }
}

const handleCreateRoot = () => {
  // 创建根菜单，没有父级
  const rootMenu = {
    id: 0,
    name: '',
    title: '',
    type: 'DIRECTORY' as MenuType,
    sort: 1,
    visible: true,
    enabled: true,
    cache: false,
    external: false
  } as MenuResponse
  
  emit('create', rootMenu)
}

const handleDeleteConfirm = (menu: MenuResponse) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除菜单"${menu.title}"吗？${menu.children?.length ? '删除后其子菜单也将被删除，' : ''}此操作不可恢复。`,
    theme: 'danger',
    onConfirm: () => {
      emit('delete', menu)
    }
  })
}

// 工具函数
const findMenuById = (id: number): MenuResponse | null => {
  const findInTree = (nodes: MenuTreeNode[]): MenuResponse | null => {
    for (const node of nodes) {
      if (node.id === id) {
        return node
      }
      if (node.children) {
        const found = findInTree(node.children)
        if (found) return found
      }
    }
    return null
  }
  
  return findInTree(treeData.value)
}

const expandAllMatched = () => {
  const expandedIds: number[] = []
  
  const checkAndExpand = (nodes: MenuTreeNode[]) => {
    nodes.forEach(node => {
      if (filterMethod.value && filterMethod.value(node)) {
        expandedIds.push(node.id)
        // 展开父节点
        let parent = findParentNode(node.id)
        while (parent) {
          expandedIds.push(parent.id)
          parent = findParentNode(parent.id)
        }
      }
      if (node.children) {
        checkAndExpand(node.children)
      }
    })
  }
  
  checkAndExpand(treeData.value)
  expandedKeys.value = [...new Set(expandedIds)]
}

const findParentNode = (childId: number): MenuTreeNode | null => {
  const findParent = (nodes: MenuTreeNode[], targetId: number): MenuTreeNode | null => {
    for (const node of nodes) {
      if (node.children?.some(child => child.id === targetId)) {
        return node
      }
      if (node.children) {
        const found = findParent(node.children, targetId)
        if (found) return found
      }
    }
    return null
  }
  
  return findParent(treeData.value, childId)
}

const calculateStats = (nodes: MenuTreeNode[]) => {
  let total = 0
  let directory = 0
  let menu = 0
  let button = 0
  
  const countNodes = (nodeList: MenuTreeNode[]) => {
    nodeList.forEach(node => {
      total++
      switch (node.type) {
        case 'DIRECTORY':
          directory++
          break
        case 'MENU':
          menu++
          break
        case 'BUTTON':
          button++
          break
      }
      
      if (node.children) {
        countNodes(node.children)
      }
    })
  }
  
  countNodes(nodes)
  
  menuStats.total = total
  menuStats.directory = directory
  menuStats.menu = menu
  menuStats.button = button
  
  emit('total-change', total)
}

// 获取菜单树数据
const fetchMenuTree = async () => {
  try {
    const response = await menuApi.getTree()
    if (response.data && response.data.data) {
      const menuTreeData = response.data.data
      treeData.value = menuTreeData
      calculateStats(menuTreeData)
      
      // 默认展开第一级
      if (menuTreeData.length > 0) {
        expandedKeys.value = menuTreeData.map((node: MenuTreeNode) => node.id)
      }
    }
  } catch (error) {
    console.error('获取菜单树失败:', error)
    MessagePlugin.error('获取菜单树失败')
  }
}

// 监听选中ID变化
watch(
  () => props.selectedId,
  (newId) => {
    if (newId !== null) {
      selectedKeys.value = [newId]
    } else {
      selectedKeys.value = []
    }
  },
  { immediate: true }
)

// 对外暴露刷新方法
const refresh = () => {
  fetchMenuTree()
  emit('refresh')
}

// 初始化
onMounted(() => {
  fetchMenuTree()
})

// 暴露方法给父组件
defineExpose({
  refresh,
  expandAll: () => {
    expandedKeys.value = treeData.value.map(node => node.id)
  },
  collapseAll: () => {
    expandedKeys.value = []
  }
})
</script>

<style scoped>
.menu-tree-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 16px;
}

/* 树形区域 */
.tree-section {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.menu-tree {
  height: 100%;
  overflow-y: auto;
}

/* 树节点内容 */
.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 4px 0;
}

.node-main {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.node-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-text-color-primary);
  word-break: break-word;
}

.node-tags {
  flex-shrink: 0;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.tree-node-content:hover .node-actions {
  opacity: 1;
}

.action-btn {
  width: 24px;
  height: 24px;
}

/* 菜单图标 */
.menu-icon {
  font-size: 16px;
  color: var(--td-text-color-secondary);
  margin-right: 4px;
}

/* 统计区域 */
.stats-section {
  margin-top: 16px;
}

.stats-content {
  display: flex;
  justify-content: center;
  padding: 8px 0;
}

.stats-item {
  display: flex;
  align-items: center;
}

/* 树样式优化 */
.menu-tree :deep(.t-tree-node) {
  border-radius: 6px;
  margin-bottom: 2px;
}

.menu-tree :deep(.t-tree-node:hover) {
  background-color: var(--td-bg-color-container-hover);
}

.menu-tree :deep(.t-tree-node.t-is-active) {
  background-color: var(--td-brand-color-1);
  color: var(--td-brand-color);
}

.menu-tree :deep(.t-tree-node__content) {
  padding: 8px 12px;
}

.menu-tree :deep(.t-tree-node__label) {
  flex: 1;
  min-width: 0;
}

/* 禁用状态 */
.menu-tree--disabled {
  opacity: 0.6;
  pointer-events: none;
}

.menu-tree--disabled :deep(.t-tree-node) {
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .menu-tree-container {
    padding: 12px;
  }
  
  .node-actions {
    opacity: 1;
  }
  
  .stats-content {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 