<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="选择图标"
    width="800px"
    :footer="false"
    @close="handleClose"
  >
    <div class="icon-select-container">
      <!-- 搜索区域 -->
      <div class="search-section">
        <t-input
          v-model="searchKeyword"
          placeholder="搜索图标..."
          clearable
          @input="handleSearch"
        >
          <template #prefix-icon>
            <search-icon />
          </template>
        </t-input>
      </div>

      <!-- 图标分类 -->
      <div class="category-section">
        <t-tabs v-model="activeCategory" theme="card">
          <t-tab-panel value="common" label="常用图标">
            <IconGrid 
              :icons="filteredCommonIcons" 
              :selected="selectedIcon"
              @select="handleIconSelect"
            />
          </t-tab-panel>
          
          <t-tab-panel value="system" label="系统图标">
            <IconGrid 
              :icons="filteredSystemIcons" 
              :selected="selectedIcon"
              @select="handleIconSelect"
            />
          </t-tab-panel>
          
          <t-tab-panel value="business" label="业务图标">
            <IconGrid 
              :icons="filteredBusinessIcons" 
              :selected="selectedIcon"
              @select="handleIconSelect"
            />
          </t-tab-panel>
          
          <t-tab-panel value="action" label="操作图标">
            <IconGrid 
              :icons="filteredActionIcons" 
              :selected="selectedIcon"
              @select="handleIconSelect"
            />
          </t-tab-panel>
        </t-tabs>
      </div>

      <!-- 底部操作 -->
      <div class="footer-actions">
        <t-space>
          <t-button variant="outline" @click="handleClear">清除选择</t-button>
          <t-button variant="outline" @click="handleClose">取消</t-button>
          <t-button theme="primary" @click="handleConfirm">确定</t-button>
        </t-space>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { SearchIcon } from 'tdesign-icons-vue-next'
import IconGrid from './IconGrid.vue'

// Props
interface Props {
  visible: boolean
  selected?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  selected: ''
})

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'update:selected', selected: string): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const searchKeyword = ref('')
const activeCategory = ref('common')
const selectedIcon = ref('')

// 图标库定义
const commonIcons = [
  'dashboard', 'folder', 'file', 'user', 'users', 'setting',
  'home', 'menu', 'list', 'table', 'chart', 'pie-chart',
  'bar-chart', 'line-chart', 'calendar', 'clock', 'tag', 'tags'
]

const systemIcons = [
  'system', 'database', 'server', 'desktop', 'laptop', 'mobile',
  'tablet', 'monitor', 'printer', 'scanner', 'router', 'network',
  'wifi', 'bluetooth', 'usb', 'hard-drive'
]

const businessIcons = [
  'shop', 'cart', 'money', 'credit-card', 'bank', 'wallet',
  'receipt', 'invoice', 'order', 'package', 'truck', 'plane',
  'ship', 'warehouse', 'factory', 'building'
]

const actionIcons = [
  'add', 'delete', 'edit', 'view', 'search', 'filter',
  'sort', 'upload', 'download', 'import', 'export', 'print',
  'copy', 'cut', 'paste', 'undo', 'redo', 'refresh',
  'save', 'close', 'back', 'forward', 'up', 'down',
  'left', 'right', 'check', 'close', 'info', 'warning',
  'error', 'success', 'help', 'question'
]

// 计算属性 - 过滤图标
const filteredCommonIcons = computed(() => {
  return filterIcons(commonIcons)
})

const filteredSystemIcons = computed(() => {
  return filterIcons(systemIcons)
})

const filteredBusinessIcons = computed(() => {
  return filterIcons(businessIcons)
})

const filteredActionIcons = computed(() => {
  return filterIcons(actionIcons)
})

// 过滤函数
const filterIcons = (icons: string[]) => {
  if (!searchKeyword.value) return icons
  return icons.filter(icon => 
    icon.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
}

// 事件处理
const handleSearch = () => {
  // 搜索时自动切换到包含结果最多的分类
  const results = {
    common: filteredCommonIcons.value.length,
    system: filteredSystemIcons.value.length,
    business: filteredBusinessIcons.value.length,
    action: filteredActionIcons.value.length
  }
  
  const maxCategory = Object.keys(results).reduce((a, b) => 
    results[a as keyof typeof results] > results[b as keyof typeof results] ? a : b
  )
  
  if (results[maxCategory as keyof typeof results] > 0) {
    activeCategory.value = maxCategory
  }
}

const handleIconSelect = (iconName: string) => {
  selectedIcon.value = iconName
}

const handleConfirm = () => {
  emit('update:selected', selectedIcon.value)
  handleClose()
}

const handleClear = () => {
  selectedIcon.value = ''
  emit('update:selected', '')
  handleClose()
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听属性变化
watch(
  () => props.visible,
  (newVisible) => {
    dialogVisible.value = newVisible
    if (newVisible) {
      selectedIcon.value = props.selected || ''
      searchKeyword.value = ''
      activeCategory.value = 'common'
    }
  },
  { immediate: true }
)

watch(
  () => dialogVisible.value,
  (newVisible) => {
    if (!newVisible) {
      emit('update:visible', false)
    }
  }
)
</script>

<style scoped>
.icon-select-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.search-section {
  margin-bottom: 16px;
}

.category-section {
  flex: 1;
  overflow: hidden;
}

.footer-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--td-border-level-1-color);
  display: flex;
  justify-content: flex-end;
}

.category-section :deep(.t-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.category-section :deep(.t-tab-panel) {
  height: 100%;
  padding: 16px 0;
}
</style> 