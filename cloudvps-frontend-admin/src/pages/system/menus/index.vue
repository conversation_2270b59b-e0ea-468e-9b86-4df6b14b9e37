<template>
  <div class="menu-management-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧菜单树面板 -->
      <div class="left-panel">
        <t-card 
          class="menu-tree-card" 
          :bordered="false"
        >
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <h3 class="card-title">菜单结构</h3>
                <t-tag 
                  v-if="totalMenus > 0"
                  :count="totalMenus" 
                  size="small"
                  theme="primary"
                  variant="light"
                >
                  {{ totalMenus }}
                </t-tag>
              </div>
              <div class="header-right">
                <t-space>
                  <t-button
                    v-if="hasPermission('SYSTEM_MENU_CREATE')"
                    theme="primary"
                    size="small"
                    @click="handleCreateMenu"
                  >
                    <template #icon>
                      <add-icon />
                    </template>
                    新建菜单
                  </t-button>
                  <t-button
                    variant="outline"
                    size="small"
                    @click="handleRefreshTree"
                    :loading="treeLoading"
                  >
                    <template #icon>
                      <refresh-icon />
                    </template>
                  </t-button>
                </t-space>
              </div>
            </div>
          </template>
          
          <MenuTree
            ref="menuTreeRef"
            v-model:selected-id="selectedMenuId"
            :loading="treeLoading"
            :disabled="currentMode !== 'view'"
            @select="handleMenuSelect"
            @create="handleCreateSubMenu"
            @edit="handleEditMenu"
            @delete="handleDeleteMenu"
            @refresh="handleRefreshTree"
            @total-change="handleTotalMenusChange"
          />
        </t-card>
      </div>

      <!-- 右侧详情面板 -->
      <div class="right-panel">
        <t-card 
          class="menu-detail-card" 
          :bordered="false"
        >
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <h3 class="card-title">
                  <span v-if="selectedMenu">{{ operationTitle }}</span>
                  <span v-else class="empty-title">选择菜单项</span>
                </h3>
                <div v-if="selectedMenu" class="menu-info">
                  <t-tag 
                    :theme="getMenuTypeTheme(selectedMenu.type)"
                    variant="light"
                    size="small"
                  >
                    {{ getMenuTypeLabel(selectedMenu.type) }}
                  </t-tag>
                  <t-tag 
                    :theme="selectedMenu.enabled ? 'success' : 'warning'"
                    variant="light"
                    size="small"
                  >
                    {{ selectedMenu.enabled ? '启用' : '禁用' }}
                  </t-tag>
                </div>
              </div>
              <div class="header-right">
                <t-space>
                  <t-button
                    v-if="selectedMenu && currentMode === 'view' && hasPermission('SYSTEM_MENU_UPDATE')"
                    theme="primary"
                    variant="outline"
                    size="small"
                    @click="handleEditMode"
                  >
                    <template #icon>
                      <edit-icon />
                    </template>
                    编辑
                  </t-button>
                  <t-dropdown
                    v-if="selectedMenu && currentMode === 'view'"
                    :options="getMenuActionOptions(selectedMenu)"
                    @click="handleMenuAction"
                    trigger="click"
                  >
                    <t-button
                      variant="outline"
                      size="small"
                    >
                      更多操作
                      <template #suffix>
                        <chevron-down-icon />
                      </template>
                    </t-button>
                  </t-dropdown>
                </t-space>
              </div>
            </div>
          </template>

          <!-- 菜单详情查看 -->
          <MenuDetailView
            v-if="currentMode === 'view'"
            :menu="selectedMenu"
            @edit="handleEditMenu"
            @create-sub="handleCreateSubMenu"
            @create-root="handleCreateMenu"
            @delete="handleDeleteMenu"
          />
          
          <!-- 菜单表单（创建/编辑） -->
          <MenuForm
            v-else-if="currentMode === 'edit' || currentMode === 'create'"
            :menu="selectedMenu"
            :mode="currentMode"
            :loading="detailLoading"
            @save="handleSaveMenu"
            @cancel="handleCancelEdit"
          />
        </t-card>
      </div>
    </div>

    <!-- 角色菜单分配对话框 -->
    <MenuRoleAssignDialog
      v-model:visible="roleAssignVisible"
      :menu="selectedMenu"
      @success="handleRoleAssignSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import {
  AddIcon,
  RefreshIcon,
  EditIcon,
  ChevronDownIcon,
} from 'tdesign-icons-vue-next'
import MenuTree from './components/MenuTree.vue'
import MenuDetailView from './components/MenuDetailView.vue'
import MenuForm from './components/MenuForm.vue'
import MenuRoleAssignDialog from './components/MenuRoleAssignDialog.vue'
import { menuApi } from '@/api/system/menu'
import { usePermission } from '@/composables/usePermission'
import type { MenuResponse, MenuType } from '@/api/system/types/menu'

// 权限检查
const { hasPermission } = usePermission()

// 页面状态
const menuTreeRef = ref()
const selectedMenuId = ref<number | null>(null)
const selectedMenu = ref<MenuResponse | null>(null)
const currentMode = ref<'view' | 'edit' | 'create'>('view')
const treeLoading = ref(false)
const detailLoading = ref(false)
const totalMenus = ref(0)

// 对话框状态
const roleAssignVisible = ref(false)

// 计算属性
const operationTitle = computed(() => {
  switch (currentMode.value) {
    case 'create':
      return '新建菜单'
    case 'edit':
      return selectedMenu.value ? `编辑 - ${selectedMenu.value.title}` : '编辑菜单'
    default:
      return selectedMenu.value ? selectedMenu.value.title : '选择菜单项'
  }
})

// 菜单类型相关
const getMenuTypeLabel = (type: MenuType) => {
  const labels = {
    DIRECTORY: '目录',
    MENU: '菜单',
    BUTTON: '按钮'
  }
  return labels[type] || type
}

const getMenuTypeTheme = (type: MenuType) => {
  const themes = {
    DIRECTORY: 'primary',
    MENU: 'success',
    BUTTON: 'warning'
  }
  return themes[type] || 'default'
}

// 获取菜单操作选项
const getMenuActionOptions = (menu: MenuResponse) => {
  const options = []
  
  if (hasPermission('SYSTEM_MENU_CREATE')) {
    options.push({
      content: '新建子菜单',
      value: 'create-child',
      prefixIcon: 'add'
    })
  }
  
  if (hasPermission('SYSTEM_MENU_UPDATE')) {
    options.push({
      content: menu.enabled ? '禁用菜单' : '启用菜单',
      value: 'toggle-status',
      theme: menu.enabled ? 'warning' : 'success'
    })
    
    options.push({
      content: menu.visible ? '隐藏菜单' : '显示菜单',
      value: 'toggle-visible'
    })
  }
  
  if (hasPermission('SYSTEM_ROLE_UPDATE')) {
    options.push({
      content: '角色权限',
      value: 'role-assign'
    })
  }
  
  if (hasPermission('SYSTEM_MENU_DELETE')) {
    options.push({
      content: '删除菜单',
      value: 'delete',
      theme: 'error'
    })
  }
  
  return options
}

// 事件处理
const handleMenuSelect = (menu: MenuResponse) => {
  console.log('🏠 主页面接收到菜单选择:', menu)
  selectedMenuId.value = menu.id
  selectedMenu.value = menu
  currentMode.value = 'view'
  console.log('📋 选中菜单:', menu)
  console.log('📊 当前状态:', { selectedMenuId: selectedMenuId.value, currentMode: currentMode.value })
}

const handleCreateMenu = () => {
  selectedMenu.value = null
  currentMode.value = 'create'
}

const handleCreateSubMenu = (parentMenu: MenuResponse) => {
  selectedMenu.value = {
    ...getDefaultMenu(),
    parentId: parentMenu.id
  } as MenuResponse
  currentMode.value = 'create'
}

const handleEditMenu = (menu: MenuResponse) => {
  selectedMenu.value = menu
  currentMode.value = 'edit'
}

const handleEditMode = () => {
  currentMode.value = 'edit'
}

const handleDeleteMenu = async (menu: MenuResponse) => {
  try {
    await menuApi.delete(menu.id)
    MessagePlugin.success('删除成功')
    
    // 如果删除的是当前选中项，清空选择
    if (selectedMenuId.value === menu.id) {
      selectedMenuId.value = null
      selectedMenu.value = null
      currentMode.value = 'view'
    }
    
    handleRefreshTree()
  } catch (error) {
    console.error('删除菜单失败:', error)
  }
}

const handleSaveMenu = () => {
  // 保存成功后刷新树和详情
  handleRefreshTree()
  currentMode.value = 'view'
}

const handleCancelEdit = () => {
  if (currentMode.value === 'create') {
    // 如果是创建模式，取消后清空选择
    selectedMenu.value = null
    selectedMenuId.value = null
  }
  currentMode.value = 'view'
}

const handleRefreshTree = () => {
  if (menuTreeRef.value) {
    menuTreeRef.value.refresh()
  }
}

const handleRefreshDetail = () => {
  if (selectedMenuId.value) {
    fetchMenuDetail(selectedMenuId.value)
  }
}

const handleTotalMenusChange = (total: number) => {
  totalMenus.value = total
}

const handleMenuAction = (data: any) => {
  const { value } = data
  
  switch (value) {
    case 'create-child':
      handleCreateSubMenu(selectedMenu.value!)
      break
    case 'toggle-status':
      handleToggleStatus()
      break
    case 'toggle-visible':
      handleToggleVisible()
      break
    case 'role-assign':
      roleAssignVisible.value = true
      break
    case 'delete':
      handleDeleteMenu(selectedMenu.value!)
      break
  }
}

const handleToggleStatus = async () => {
  if (!selectedMenu.value) return
  
  try {
    const newStatus = !selectedMenu.value.enabled
    await menuApi.toggleStatus(selectedMenu.value.id, newStatus)
    selectedMenu.value.enabled = newStatus
    MessagePlugin.success(`${newStatus ? '启用' : '禁用'}成功`)
    handleRefreshTree()
  } catch (error) {
    console.error('切换状态失败:', error)
  }
}

const handleToggleVisible = async () => {
  if (!selectedMenu.value) return
  
  try {
    const newVisible = !selectedMenu.value.visible
    await menuApi.toggleVisible(selectedMenu.value.id, newVisible)
    selectedMenu.value.visible = newVisible
    MessagePlugin.success(`${newVisible ? '显示' : '隐藏'}成功`)
    handleRefreshTree()
  } catch (error) {
    console.error('切换可见性失败:', error)
  }
}

const handleRoleAssignSuccess = () => {
  MessagePlugin.success('角色权限分配成功')
}

// 获取菜单详情
const fetchMenuDetail = async (id: number) => {
  try {
    detailLoading.value = true
    const response = await menuApi.getById(id)
    if (response.data && response.data.data) {
      selectedMenu.value = response.data.data
    }
  } catch (error) {
    console.error('获取菜单详情失败:', error)
  } finally {
    detailLoading.value = false
  }
}

// 获取默认菜单对象
const getDefaultMenu = (): Partial<MenuResponse> => ({
  name: '',
  title: '',
  type: 'MENU' as MenuType,
  sort: 1,
  visible: true,
  enabled: true,
  cache: false,
  external: false
})

// 初始化
onMounted(() => {
  // 组件挂载后，树组件会自动加载数据
})
</script>

<style scoped>
.menu-management-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  height: calc(100vh - 140px);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  height: 100%;
  max-height: 700px;
}

.left-panel {
  width: 400px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
  min-width: 0;
}

.menu-tree-card,
.menu-detail-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.menu-tree-card :deep(.t-card__body),
.menu-detail-card :deep(.t-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.header-right {
  flex-shrink: 0;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.empty-title {
  color: var(--td-text-color-placeholder);
}

.menu-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: 16px;
    min-height: auto;
  }
  
  .left-panel {
    width: 100%;
  }
  
  .menu-tree-card {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .menu-management-container {
    padding: 12px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-right {
    width: 100%;
  }
}
</style> 