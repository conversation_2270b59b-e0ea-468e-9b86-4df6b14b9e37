<template>
  <t-drawer
    v-model:visible="drawerVisible"
    header="用户详情"
    size="600px"
    :footer="false"
  >
    <div v-if="userData" class="user-detail">
      <!-- 基本信息 -->
      <t-card title="基本信息" :bordered="false" class="detail-card">
        <div class="user-header">
          <t-avatar
            :image="userData.avatar"
            :alt="userData.realName || userData.username"
            size="large"
          >
            {{ (userData.realName || userData.username).charAt(0) }}
          </t-avatar>
          <div class="user-info">
            <h3>{{ userData.realName || userData.username }}</h3>
            <p class="username">@{{ userData.username }}</p>
            <DictTag dict-type="user_status" :value="userData.status" />
          </div>
        </div>

        <t-descriptions :column="2" class="detail-descriptions">
          <t-descriptions-item label="用户ID">
            {{ userData.id }}
          </t-descriptions-item>
          <t-descriptions-item label="用户名">
            {{ userData.username }}
          </t-descriptions-item>
          <t-descriptions-item label="邮箱">
            {{ userData.email }}
          </t-descriptions-item>
          <t-descriptions-item label="手机号">
            {{ userData.phone || '-' }}
          </t-descriptions-item>
          <t-descriptions-item label="真实姓名">
            {{ userData.realName || '-' }}
          </t-descriptions-item>
          <t-descriptions-item label="昵称">
            {{ userData.nickname || '-' }}
          </t-descriptions-item>
          <t-descriptions-item label="性别">
            {{ getGenderText(userData.gender) }}
          </t-descriptions-item>
          <t-descriptions-item label="生日">
            {{ userData.birthday || '-' }}
          </t-descriptions-item>
          <t-descriptions-item label="用户状态">
            <DictTag dict-type="user_status" :value="userData.status" />
          </t-descriptions-item>
          <t-descriptions-item label="是否锁定">
            <t-tag :theme="userData.isLocked ? 'danger' : 'success'" variant="light">
              {{ userData.isLocked ? '已锁定' : '正常' }}
            </t-tag>
          </t-descriptions-item>
        </t-descriptions>

        <t-descriptions v-if="userData.description" :column="1" class="detail-descriptions">
          <t-descriptions-item label="描述">
            {{ userData.description }}
          </t-descriptions-item>
        </t-descriptions>
      </t-card>

      <!-- 角色信息 -->
      <t-card title="角色信息" :bordered="false" class="detail-card">
        <div v-if="userData.roleNames && userData.roleNames.length > 0" class="roles-container">
          <t-space>
            <t-tag
              v-for="roleName in userData.roleNames"
              :key="roleName"
              theme="primary"
              variant="light"
            >
              {{ roleName }}
            </t-tag>
          </t-space>
        </div>
        <t-empty v-else description="暂无角色信息" />
      </t-card>

      <!-- 登录信息 -->
      <t-card title="登录信息" :bordered="false" class="detail-card">
        <t-descriptions :column="2" class="detail-descriptions">
          <t-descriptions-item label="登录次数">
            {{ userData.loginCount || 0 }}
          </t-descriptions-item>
          <t-descriptions-item label="最后登录时间">
            {{ userData.lastLoginTime ? formatDateTime(userData.lastLoginTime) : '从未登录' }}
          </t-descriptions-item>
          <t-descriptions-item label="最后登录IP">
            {{ userData.lastLoginIp || '-' }}
          </t-descriptions-item>
          <t-descriptions-item label="密码过期时间">
            {{ userData.passwordExpireTime ? formatDateTime(userData.passwordExpireTime) : '-' }}
          </t-descriptions-item>
        </t-descriptions>

        <t-descriptions v-if="userData.isLocked" :column="1" class="detail-descriptions">
          <t-descriptions-item label="锁定原因">
            {{ userData.lockReason || '-' }}
          </t-descriptions-item>
          <t-descriptions-item label="锁定时间">
            {{ userData.lockTime ? formatDateTime(userData.lockTime) : '-' }}
          </t-descriptions-item>
        </t-descriptions>
      </t-card>

      <!-- 系统信息 -->
      <t-card title="系统信息" :bordered="false" class="detail-card">
        <t-descriptions :column="2" class="detail-descriptions">
          <t-descriptions-item label="创建时间">
            {{ formatDateTime(userData.createdTime) }}
          </t-descriptions-item>
          <t-descriptions-item label="更新时间">
            {{ formatDateTime(userData.updatedTime) }}
          </t-descriptions-item>
          <t-descriptions-item label="创建者">
            {{ userData.createdBy || '-' }}
          </t-descriptions-item>
          <t-descriptions-item label="更新者">
            {{ userData.updatedBy || '-' }}
          </t-descriptions-item>
        </t-descriptions>
      </t-card>

      <!-- 操作日志 -->
      <t-card title="最近操作日志" :bordered="false" class="detail-card">
        <div v-if="operationLogs.length > 0">
          <t-timeline>
            <t-timeline-item
              v-for="log in operationLogs"
              :key="log.id"
              :label="formatDateTime(log.createdTime)"
            >
              <div class="log-content">
                <div class="log-action">{{ log.action }}</div>
                <div class="log-description">{{ log.description }}</div>
                <div class="log-ip">IP: {{ log.ip }}</div>
              </div>
            </t-timeline-item>
          </t-timeline>
          
          <div class="log-actions">
            <t-button
              variant="outline"
              size="small"
              @click="loadMoreLogs"
              :loading="loadingLogs"
            >
              加载更多
            </t-button>
          </div>
        </div>
        <t-empty v-else description="暂无操作日志" />
      </t-card>
    </div>
  </t-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { DictTag } from '@/components/business'
import { userApi } from '@/api/system/user'
import type { UserResponse } from '@/api/system/types/user'
import { formatDateTime } from '@/utils/date'

interface Props {
  visible: boolean
  data: UserResponse | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: null
})

const emit = defineEmits<Emits>()

// 抽屉可见性
const drawerVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 用户数据
const userData = computed(() => props.data)

// 操作日志
const operationLogs = ref<any[]>([])
const loadingLogs = ref(false)
const logPage = ref(1)

// 获取性别文本
const getGenderText = (gender?: string) => {
  switch (gender) {
    case 'MALE':
      return '男'
    case 'FEMALE':
      return '女'
    case 'UNKNOWN':
      return '未知'
    default:
      return '-'
  }
}

// 加载操作日志
const loadOperationLogs = async (page = 1) => {
  if (!userData.value?.id) return

  try {
    loadingLogs.value = true
    const response = await userApi.getOperationLogs(userData.value.id, {
      current: page,
      pageSize: 10
    })
    
    if (page === 1) {
      operationLogs.value = response.data.list || []
    } else {
      operationLogs.value.push(...(response.data.list || []))
    }
    
    logPage.value = page
  } catch (error) {
    console.error('加载操作日志失败:', error)
  } finally {
    loadingLogs.value = false
  }
}

// 加载更多日志
const loadMoreLogs = () => {
  loadOperationLogs(logPage.value + 1)
}

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (newData) {
      loadOperationLogs()
    } else {
      operationLogs.value = []
      logPage.value = 1
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.user-detail {
  .detail-card {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .user-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    
    .user-info {
      margin-left: 16px;
      
      h3 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 500;
      }
      
      .username {
        margin: 0 0 8px 0;
        color: var(--td-text-color-secondary);
        font-size: 14px;
      }
    }
  }

  .detail-descriptions {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .roles-container {
    padding: 16px 0;
  }

  .log-content {
    .log-action {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .log-description {
      color: var(--td-text-color-secondary);
      font-size: 14px;
      margin-bottom: 4px;
    }
    
    .log-ip {
      color: var(--td-text-color-placeholder);
      font-size: 12px;
    }
  }

  .log-actions {
    text-align: center;
    margin-top: 16px;
  }
}
</style>
