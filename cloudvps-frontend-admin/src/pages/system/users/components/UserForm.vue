<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :header="dialogTitle"
    width="800px"
    :confirm-btn="null"
    :cancel-btn="null"
    @close="handleClose"
  >
    <t-form
      ref="formRef"
      :data="formData"
      :rules="formRules"
      label-width="100px"
      @submit="handleSubmit"
    >
      <t-row :gutter="16">
        <t-col :span="12">
          <t-form-item label="用户名" name="username">
            <t-input
              v-model="formData.username"
              placeholder="请输入用户名"
              :disabled="isUpdateMode"
              clearable
            />
          </t-form-item>
        </t-col>
        <t-col :span="12">
          <t-form-item label="邮箱" name="email">
            <t-input
              v-model="formData.email"
              placeholder="请输入邮箱"
              clearable
            />
          </t-form-item>
        </t-col>
      </t-row>

      <t-row v-if="isCreateMode" :gutter="16">
        <t-col :span="12">
          <t-form-item label="密码" name="password">
            <t-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              clearable
            />
          </t-form-item>
        </t-col>
        <t-col :span="12">
          <t-form-item label="确认密码" name="confirmPassword">
            <t-input
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              clearable
            />
          </t-form-item>
        </t-col>
      </t-row>

      <t-row :gutter="16">
        <t-col :span="12">
          <t-form-item label="真实姓名" name="realName">
            <t-input
              v-model="formData.realName"
              placeholder="请输入真实姓名"
              clearable
            />
          </t-form-item>
        </t-col>
        <t-col :span="12">
          <t-form-item label="昵称" name="nickname">
            <t-input
              v-model="formData.nickname"
              placeholder="请输入昵称"
              clearable
            />
          </t-form-item>
        </t-col>
      </t-row>

      <t-row :gutter="16">
        <t-col :span="12">
          <t-form-item label="手机号" name="phone">
            <t-input
              v-model="formData.phone"
              placeholder="请输入手机号"
              clearable
            />
          </t-form-item>
        </t-col>
        <t-col :span="12">
          <t-form-item label="性别" name="gender">
            <t-select
              v-model="formData.gender"
              placeholder="请选择性别"
              clearable
            >
              <t-option label="男" value="MALE" />
              <t-option label="女" value="FEMALE" />
              <t-option label="未知" value="UNKNOWN" />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row :gutter="16">
        <t-col :span="12">
          <t-form-item label="生日" name="birthday">
            <t-date-picker
              v-model="formData.birthday"
              placeholder="请选择生日"
              clearable
            />
          </t-form-item>
        </t-col>
        <t-col :span="12">
          <t-form-item label="用户状态" name="status">
            <DictSelect
              v-model="formData.status"
              dict-type="user_status"
              placeholder="请选择状态"
            />
          </t-form-item>
        </t-col>
      </t-row>

      <t-row :gutter="16">
        <t-col :span="24">
          <t-form-item label="角色" name="roleIds">
            <t-select
              v-model="formData.roleIds"
              :options="roleOptions"
              placeholder="请选择角色"
              multiple
              clearable
            />
          </t-form-item>
        </t-col>
      </t-row>

      <t-row :gutter="16">
        <t-col :span="24">
          <t-form-item label="头像" name="avatar">
            <div class="avatar-upload">
              <t-upload
                v-model="avatarFileList"
                :action="uploadAction"
                :headers="uploadHeaders"
                :format-response="formatUploadResponse"
                accept="image/*"
                :size-limit="{ size: 2, unit: 'MB' }"
                :multiple="false"
                :auto-upload="true"
                @success="handleAvatarSuccess"
                @fail="handleAvatarFail"
              >
                <template #file-input>
                  <div class="avatar-uploader">
                    <t-avatar
                      v-if="formData.avatar"
                      :image="formData.avatar"
                      size="large"
                    />
                    <div v-else class="avatar-placeholder">
                      <add-icon size="24" />
                      <div class="upload-text">上传头像</div>
                    </div>
                  </div>
                </template>
              </t-upload>
            </div>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row :gutter="16">
        <t-col :span="24">
          <t-form-item label="描述" name="description">
            <t-textarea
              v-model="formData.description"
              placeholder="请输入用户描述"
              :maxlength="500"
              :autosize="{ minRows: 3, maxRows: 6 }"
            />
          </t-form-item>
        </t-col>
      </t-row>
    </t-form>

    <template #footer>
      <t-space>
        <t-button @click="handleClose">取消</t-button>
        <t-button
          theme="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ isCreateMode ? '创建' : '更新' }}
        </t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { AddIcon } from 'tdesign-icons-vue-next'
import { DictSelect } from '@/components/business'
import { userApi } from '@/api/system/user'
import type { UserFormData, RoleOption } from '@/api/system/types/user'
import { useAuthStore } from '@/stores/modules/auth'

interface Props {
  visible: boolean
  mode: 'create' | 'update'
  data?: Partial<UserFormData>
  roleOptions: RoleOption[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'create',
  data: () => ({}),
  roleOptions: () => []
})

const emit = defineEmits<Emits>()

const authStore = useAuthStore()

// 表单引用
const formRef = ref()

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '新增用户' : '编辑用户'
})

// 是否为创建模式
const isCreateMode = computed(() => props.mode === 'create')

// 是否为更新模式
const isUpdateMode = computed(() => props.mode === 'update')

// 提交状态
const submitting = ref(false)

// 表单数据
const formData = reactive<UserFormData>({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  phone: '',
  realName: '',
  nickname: '',
  gender: undefined,
  birthday: '',
  description: '',
  status: 'ACTIVE',
  roleIds: [],
  avatar: ''
})

// 头像上传
const avatarFileList = ref([])
const uploadAction = '/api/system/users/avatar'
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', type: 'error' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', type: 'error' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', type: 'error' }
  ],
  email: [
    { required: true, message: '请输入邮箱', type: 'error' },
    { email: true, message: '请输入正确的邮箱格式', type: 'error' }
  ],
  password: isCreateMode.value ? [
    { required: true, message: '请输入密码', type: 'error' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', type: 'error' }
  ] : [],
  confirmPassword: isCreateMode.value ? [
    { required: true, message: '请确认密码', type: 'error' },
    {
      validator: (val: string) => val === formData.password,
      message: '两次输入的密码不一致',
      type: 'error'
    }
  ] : [],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', type: 'error' }
  ],
  status: [
    { required: true, message: '请选择用户状态', type: 'error' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    realName: '',
    nickname: '',
    gender: undefined,
    birthday: '',
    description: '',
    status: 'ACTIVE',
    roleIds: [],
    avatar: ''
  })
  avatarFileList.value = []
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (newData && Object.keys(newData).length > 0) {
      Object.assign(formData, {
        ...newData,
        password: '',
        confirmPassword: '',
        roleIds: newData.roleIds || []
      })
    } else {
      resetForm()
    }
  },
  { immediate: true, deep: true }
)

// 监听可见性变化
watch(
  () => props.visible,
  (visible) => {
    if (!visible) {
      resetForm()
    }
  }
)

// 处理头像上传成功
const handleAvatarSuccess = (response: any) => {
  if (response.data && response.data.url) {
    formData.avatar = response.data.url
    MessagePlugin.success('头像上传成功')
  }
}

// 处理头像上传失败
const handleAvatarFail = (error: any) => {
  console.error('头像上传失败:', error)
  MessagePlugin.error('头像上传失败')
}

// 格式化上传响应
const formatUploadResponse = (response: any) => {
  return {
    status: response.success ? 'success' : 'fail',
    response
  }
}

// 验证用户名唯一性
const validateUsername = async (username: string) => {
  if (!username || username === props.data?.username) {
    return true
  }

  try {
    const response = await userApi.checkUsername(username)
    return !response.data // 如果不存在则验证通过
  } catch (error) {
    console.error('验证用户名失败:', error)
    return false
  }
}

// 验证邮箱唯一性
const validateEmail = async (email: string) => {
  if (!email || email === props.data?.email) {
    return true
  }

  try {
    const response = await userApi.checkEmail(email)
    return !response.data // 如果不存在则验证通过
  } catch (error) {
    console.error('验证邮箱失败:', error)
    return false
  }
}

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  const isValid = await formRef.value?.validate()
  if (!isValid) {
    return
  }

  // 验证用户名唯一性（仅创建模式）
  if (isCreateMode.value) {
    const isUsernameValid = await validateUsername(formData.username)
    if (!isUsernameValid) {
      MessagePlugin.error('用户名已存在')
      return
    }
  }

  // 验证邮箱唯一性
  const isEmailValid = await validateEmail(formData.email)
  if (!isEmailValid) {
    MessagePlugin.error('邮箱已存在')
    return
  }

  try {
    submitting.value = true

    const submitData = { ...formData }

    // 移除确认密码字段
    delete submitData.confirmPassword

    // 如果是更新模式且没有输入密码，则移除密码字段
    if (isUpdateMode.value && !submitData.password) {
      delete submitData.password
    }

    if (isCreateMode.value) {
      await userApi.create(submitData)
      MessagePlugin.success('用户创建成功')
    } else {
      await userApi.update(submitData.id!, submitData)
      MessagePlugin.success('用户更新成功')
    }

    emit('success')
  } catch (error: any) {
    console.error('提交失败:', error)
    const message = error?.response?.data?.message || '操作失败'
    MessagePlugin.error(message)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="less" scoped>
.avatar-upload {
  .avatar-uploader {
    display: inline-block;
    cursor: pointer;
    
    .avatar-placeholder {
      width: 80px;
      height: 80px;
      border: 2px dashed var(--td-border-level-2-color);
      border-radius: var(--td-radius-default);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      
      &:hover {
        border-color: var(--td-brand-color);
        color: var(--td-brand-color);
      }
      
      .upload-text {
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }
}
</style>
