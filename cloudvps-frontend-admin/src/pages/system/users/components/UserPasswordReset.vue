<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="重置密码"
    width="500px"
    :confirm-btn="null"
    :cancel-btn="null"
    @close="handleClose"
  >
    <div v-if="userData" class="password-reset">
      <!-- 用户信息 -->
      <div class="user-info">
        <t-avatar
          :image="userData.avatar"
          :alt="userData.realName || userData.username"
          size="medium"
        >
          {{ (userData.realName || userData.username).charAt(0) }}
        </t-avatar>
        <div class="info-text">
          <div class="name">{{ userData.realName || userData.username }}</div>
          <div class="username">@{{ userData.username }}</div>
        </div>
      </div>

      <t-divider />

      <!-- 重置选项 -->
      <t-form
        ref="formRef"
        :data="formData"
        :rules="formRules"
        label-width="120px"
      >
        <t-form-item label="重置方式" name="resetType">
          <t-radio-group v-model="formData.resetType">
            <t-radio value="auto">系统生成随机密码</t-radio>
            <t-radio value="manual">手动设置密码</t-radio>
          </t-radio-group>
        </t-form-item>

        <template v-if="formData.resetType === 'manual'">
          <t-form-item label="新密码" name="newPassword">
            <t-input
              v-model="formData.newPassword"
              type="password"
              placeholder="请输入新密码"
              clearable
            />
          </t-form-item>
          
          <t-form-item label="确认密码" name="confirmPassword">
            <t-input
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              clearable
            />
          </t-form-item>
        </template>

        <t-form-item label="发送通知" name="sendNotification">
          <t-checkbox v-model="formData.sendNotification">
            向用户邮箱发送新密码通知
          </t-checkbox>
        </t-form-item>
      </t-form>

      <!-- 提示信息 -->
      <t-alert theme="info" class="reset-tips">
        <template #message>
          <div>
            <p><strong>注意事项：</strong></p>
            <ul>
              <li>重置密码后，用户需要使用新密码重新登录</li>
              <li>如果选择系统生成密码，将生成8位随机密码</li>
              <li>建议用户首次登录后立即修改密码</li>
              <li v-if="formData.sendNotification">新密码将通过邮件发送给用户</li>
            </ul>
          </div>
        </template>
      </t-alert>
    </div>

    <template #footer>
      <t-space>
        <t-button @click="handleClose">取消</t-button>
        <t-button
          theme="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          确定重置
        </t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { userApi } from '@/api/system/user'
import type { UserResponse } from '@/api/system/types/user'

interface Props {
  visible: boolean
  userData: UserResponse | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userData: null
})

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref()

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 提交状态
const submitting = ref(false)

// 表单数据
const formData = reactive({
  resetType: 'auto',
  newPassword: '',
  confirmPassword: '',
  sendNotification: true
})

// 表单验证规则
const formRules = computed(() => ({
  newPassword: formData.resetType === 'manual' ? [
    { required: true, message: '请输入新密码', type: 'error' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', type: 'error' }
  ] : [],
  confirmPassword: formData.resetType === 'manual' ? [
    { required: true, message: '请确认密码', type: 'error' },
    {
      validator: (val: string) => val === formData.newPassword,
      message: '两次输入的密码不一致',
      type: 'error'
    }
  ] : []
}))

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    resetType: 'auto',
    newPassword: '',
    confirmPassword: '',
    sendNotification: true
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 监听可见性变化
watch(
  () => props.visible,
  (visible) => {
    if (!visible) {
      resetForm()
    }
  }
)

// 提交重置
const handleSubmit = async () => {
  if (!props.userData) return

  // 如果是手动设置密码，需要验证表单
  if (formData.resetType === 'manual') {
    const isValid = await formRef.value?.validate()
    if (!isValid) {
      return
    }
  }

  // 确认对话框
  const confirmDialog = DialogPlugin.confirm({
    header: '确认重置密码',
    body: `确定要重置用户 "${props.userData.username}" 的密码吗？`,
    theme: 'warning',
    onConfirm: async () => {
      try {
        submitting.value = true
        
        const password = formData.resetType === 'manual' 
          ? formData.newPassword 
          : undefined

        await userApi.resetPassword(props.userData!.id, password)
        
        MessagePlugin.success('密码重置成功')
        
        // 如果是自动生成密码且需要发送通知
        if (formData.resetType === 'auto' && formData.sendNotification) {
          MessagePlugin.info('新密码已发送到用户邮箱')
        }
        
        emit('success')
        confirmDialog.destroy()
      } catch (error: any) {
        console.error('密码重置失败:', error)
        const message = error?.response?.data?.message || '密码重置失败'
        MessagePlugin.error(message)
      } finally {
        submitting.value = false
      }
    }
  })
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="less" scoped>
.password-reset {
  .user-info {
    display: flex;
    align-items: center;
    padding: 16px 0;
    
    .info-text {
      margin-left: 12px;
      
      .name {
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .username {
        color: var(--td-text-color-secondary);
        font-size: 14px;
      }
    }
  }

  .reset-tips {
    margin-top: 16px;
    
    ul {
      margin: 8px 0 0 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
        font-size: 14px;
      }
    }
  }
}
</style>
