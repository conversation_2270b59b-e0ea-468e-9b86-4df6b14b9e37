<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="分配角色"
    width="600px"
    :confirm-btn="null"
    :cancel-btn="null"
    @close="handleClose"
  >
    <div v-if="userData" class="role-assign">
      <!-- 用户信息 -->
      <div class="user-info">
        <t-avatar
          :image="userData.avatar"
          :alt="userData.realName || userData.username"
          size="medium"
        >
          {{ (userData.realName || userData.username).charAt(0) }}
        </t-avatar>
        <div class="info-text">
          <div class="name">{{ userData.realName || userData.username }}</div>
          <div class="username">@{{ userData.username }}</div>
        </div>
      </div>

      <t-divider />

      <!-- 角色选择 -->
      <div class="role-selection">
        <h4>选择角色</h4>
        <t-checkbox-group v-model="selectedRoleIds" class="role-list">
          <div
            v-for="role in roleOptions"
            :key="role.id"
            class="role-item"
          >
            <t-checkbox :value="role.id">
              <div class="role-content">
                <div class="role-name">{{ role.name }}</div>
                <div class="role-description">{{ role.description || '暂无描述' }}</div>
              </div>
            </t-checkbox>
          </div>
        </t-checkbox-group>
      </div>

      <!-- 当前角色 -->
      <div v-if="currentRoles.length > 0" class="current-roles">
        <h4>当前角色</h4>
        <t-space>
          <t-tag
            v-for="role in currentRoles"
            :key="role.id"
            theme="primary"
            variant="light"
          >
            {{ role.name }}
          </t-tag>
        </t-space>
      </div>
    </div>

    <template #footer>
      <t-space>
        <t-button @click="handleClose">取消</t-button>
        <t-button
          theme="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          确定
        </t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { userApi } from '@/api/system/user'
import type { UserResponse, RoleOption } from '@/api/system/types/user'

interface Props {
  visible: boolean
  userData: UserResponse | null
  roleOptions: RoleOption[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userData: null,
  roleOptions: () => []
})

const emit = defineEmits<Emits>()

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 选中的角色ID
const selectedRoleIds = ref<number[]>([])

// 提交状态
const submitting = ref(false)

// 当前角色
const currentRoles = computed(() => {
  if (!props.userData?.roleIds) return []
  return props.roleOptions.filter(role => 
    props.userData!.roleIds!.includes(role.id)
  )
})

// 监听用户数据变化
watch(
  () => props.userData,
  (userData) => {
    if (userData) {
      selectedRoleIds.value = [...(userData.roleIds || [])]
    } else {
      selectedRoleIds.value = []
    }
  },
  { immediate: true }
)

// 提交角色分配
const handleSubmit = async () => {
  if (!props.userData) return

  try {
    submitting.value = true
    
    await userApi.assignRoles(props.userData.id, {
      roleIds: selectedRoleIds.value
    })
    
    MessagePlugin.success('角色分配成功')
    emit('success')
  } catch (error: any) {
    console.error('角色分配失败:', error)
    const message = error?.response?.data?.message || '角色分配失败'
    MessagePlugin.error(message)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="less" scoped>
.role-assign {
  .user-info {
    display: flex;
    align-items: center;
    padding: 16px 0;
    
    .info-text {
      margin-left: 12px;
      
      .name {
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .username {
        color: var(--td-text-color-secondary);
        font-size: 14px;
      }
    }
  }

  .role-selection {
    margin: 24px 0;
    
    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
    }
    
    .role-list {
      .role-item {
        margin-bottom: 12px;
        padding: 12px;
        border: 1px solid var(--td-border-level-1-color);
        border-radius: var(--td-radius-default);
        transition: all 0.3s;
        
        &:hover {
          border-color: var(--td-brand-color);
          background-color: var(--td-brand-color-light);
        }
        
        .role-content {
          .role-name {
            font-weight: 500;
            margin-bottom: 4px;
          }
          
          .role-description {
            color: var(--td-text-color-secondary);
            font-size: 14px;
          }
        }
      }
    }
  }

  .current-roles {
    margin-top: 24px;
    
    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>
