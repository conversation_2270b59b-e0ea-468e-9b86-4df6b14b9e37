<template>
  <div class="page-container">
    <!-- 搜索表单 -->
    <t-card :bordered="false">
      <template #header>
        <span>用户查询</span>
      </template>
      <t-form
        ref="searchFormRef"
        :data="searchForm"
        layout="inline"
        label-width="80px"
        @submit="handleSearch"
        @reset="handleReset"
      >
        <t-form-item label="关键词" name="keyword">
          <t-input
            v-model="searchForm.keyword"
            placeholder="用户名/邮箱/姓名"
            clearable
          />
        </t-form-item>
        <t-form-item label="用户状态" name="status">
          <DictSelect
            v-model="searchForm.status"
            dict-type="user_status"
            placeholder="请选择状态"
            clearable
          />
        </t-form-item>
        <t-form-item label="角色" name="roleId">
          <t-select
            v-model="searchForm.roleId"
            :options="roleOptions"
            placeholder="请选择角色"
            clearable
          />
        </t-form-item>
        <t-form-item label="创建时间" name="dateRange">
          <t-date-range-picker
            v-model="searchForm.dateRange"
            placeholder="请选择时间范围"
            clearable
          />
        </t-form-item>
        <t-form-item>
          <t-button
            theme="primary"
            :loading="loading.list"
            @click="handleSearch"
          >
            <template #icon>
              <search-icon />
            </template>
            搜索
          </t-button>
          <t-button variant="base" @click="handleReset">
            <template #icon>
              <refresh-icon />
            </template>
            重置
          </t-button>
        </t-form-item>
      </t-form>
    </t-card>

    <!-- 数据表格 -->
    <t-card :bordered="false">
      <t-table
        :data="tableData"
        :columns="columns"
        :loading="loading.list"
        :pagination="paginationConfig"
        :selected-row-keys="selectedRowKeys"
        row-key="id"
        stripe
        hover
        bordered
        size="medium"
        table-layout="fixed"
        vertical-align="middle"
        @select-change="handleSelectionChange"
        @page-change="handlePageChange"
      >
        <!-- 表格顶部操作栏 -->
        <template #topContent>
          <t-row justify="space-between" align="middle" class="table-top-content">
            <!-- 左侧：主要操作按钮 -->
            <t-col flex="auto">
              <t-space>
                <t-button
                  v-if="hasPermission('USER_CREATE')"
                  theme="primary"
                  @click="handleCreate"
                >
                  <template #icon>
                    <add-icon />
                  </template>
                  新增用户
                </t-button>
                <t-button
                  v-if="hasPermission('USER_DELETE') && hasSelection"
                  theme="danger"
                  variant="outline"
                  @click="handleBatchDelete"
                  :disabled="!hasSelection"
                >
                  <template #icon>
                    <delete-icon />
                  </template>
                  批量删除
                </t-button>
                <t-button
                  v-if="hasPermission('USER_UPDATE') && hasSelection"
                  theme="warning"
                  variant="outline"
                  @click="handleBatchEnable"
                  :disabled="!hasSelection"
                >
                  <template #icon>
                    <check-circle-icon />
                  </template>
                  批量启用
                </t-button>
                <t-button
                  v-if="hasPermission('USER_UPDATE') && hasSelection"
                  theme="warning"
                  variant="outline"
                  @click="handleBatchDisable"
                  :disabled="!hasSelection"
                >
                  <template #icon>
                    <close-circle-icon />
                  </template>
                  批量禁用
                </t-button>
              </t-space>
            </t-col>

            <!-- 右侧：辅助功能按钮 -->
            <t-col flex="none">
              <t-space align="center">
                <!-- 选中信息 -->
                <div v-if="hasSelection" class="selection-info">
                  已选中 {{ selectionCount }} 项
                  <t-button
                    theme="primary"
                    variant="text"
                    @click="clearSelection"
                  >
                    清空选择
                  </t-button>
                </div>

                <t-button
                  variant="outline"
                  @click="handleRefresh"
                  :loading="loading.list"
                >
                  <template #icon>
                    <refresh-icon />
                  </template>
                  刷新
                </t-button>
                <t-button
                  v-if="hasPermission('USER_EXPORT')"
                  variant="outline"
                  @click="handleExport"
                >
                  <template #icon>
                    <download-icon />
                  </template>
                  导出
                </t-button>
              </t-space>
            </t-col>
          </t-row>
        </template>
        <!-- 头像列 -->
        <template #avatar="{ row }">
          <t-avatar
            :image="row.avatar"
            :alt="row.realName || row.username"
            size="medium"
          >
            {{ (row.realName || row.username).charAt(0) }}
          </t-avatar>
        </template>

        <!-- 用户信息列 -->
        <template #userInfo="{ row }">
          <div class="user-info">
            <div class="username">{{ row.username }}</div>
            <div class="real-name">{{ row.realName || "-" }}</div>
            <div class="email">{{ row.email }}</div>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <DictTag dict-type="user_status" :value="row.status" theme="primary"/>
        </template>

        <!-- 角色列 -->
        <template #roles="{ row }">
          <t-space v-if="row.roleNames && row.roleNames.length > 0">
            <t-tag
              v-for="roleName in row.roleNames"
              :key="roleName"
              theme="primary"
              variant="light"
              size="small"
            >
              {{ roleName }}
            </t-tag>
          </t-space>
          <span v-else class="text-placeholder">-</span>
        </template>

        <!-- 最后登录列 -->
        <template #lastLogin="{ row }">
          <div v-if="row.lastLoginTime">
            <div>{{ formatDateTime(row.lastLoginTime) }}</div>
            <div class="text-placeholder">{{ row.lastLoginIp || "-" }}</div>
          </div>
          <span v-else class="text-placeholder">从未登录</span>
        </template>

        <!-- 操作列 -->
        <template #actions="{ row }">
          <t-space direction="vertical" size="small">
            <t-button
              v-if="hasPermission('USER_VIEW')"
              theme="primary"
              variant="text"
              size="small"
              @click="handleView(row)"
            >
              <template #icon>
                <browse-icon />
              </template>
              查看
            </t-button>
            <t-button
              v-if="hasPermission('USER_UPDATE')"
              theme="primary"
              variant="text"
              size="small"
              @click="handleUpdate(row)"
            >
              <template #icon>
                <edit-icon />
              </template>
              编辑
            </t-button>
            <t-dropdown
              v-if="
                hasPermission('USER_UPDATE') || hasPermission('USER_DELETE')
              "
              :options="getActionOptions(row)"
              @click="handleActionClick($event, row)"
            >
              <t-button theme="primary" variant="text" size="small">
                <template #icon>
                  <more-icon />
                </template>
                更多
              </t-button>
            </t-dropdown>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 用户表单弹窗 -->
    <UserForm
      v-model:visible="formVisible"
      :mode="formMode"
      :data="formData"
      :role-options="roleOptions"
      @success="handleFormSuccess"
    />

    <!-- 用户详情弹窗 -->
    <UserDetail v-model:visible="detailVisible" :data="detailData" />

    <!-- 角色分配弹窗 -->
    <UserRoleAssign
      v-model:visible="roleAssignVisible"
      :user-data="roleAssignData"
      :role-options="roleOptions"
      @success="handleRoleAssignSuccess"
    />

    <!-- 密码重置弹窗 -->
    <UserPasswordReset
      v-model:visible="passwordResetVisible"
      :user-data="passwordResetData"
      @success="handlePasswordResetSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { MessagePlugin, DialogPlugin } from "tdesign-vue-next";
import {
  SearchIcon,
  RefreshIcon,
  AddIcon,
  DeleteIcon,
  CheckCircleIcon,
  CloseCircleIcon,
  DownloadIcon,
  BrowseIcon,
  EditIcon,
  MoreIcon,
} from "tdesign-icons-vue-next";

// 组件导入
import { DictTag, DictSelect } from "@/components/business";
import UserForm from "./components/UserForm.vue";
import UserDetail from "./components/UserDetail.vue";
import UserRoleAssign from "./components/UserRoleAssign.vue";
import UserPasswordReset from "./components/UserPasswordReset.vue";

// API和类型导入
import { userApi } from "@/api/system/user";
import type {
  UserResponse,
  UserQueryRequest,
  UserFormData,
  RoleOption,
} from "@/api/system/types/user";

// 工具函数导入
import { useTable } from "@/composables/useTable";
import { useForm } from "@/composables/useForm";
import { useAuthStore } from "@/stores/modules/auth";
import { formatDateTime } from "@/utils/date";

// 权限检查
const authStore = useAuthStore();
const hasPermission = (permission: string) =>
  authStore.hasPermission([permission]);

// 搜索表单
const searchFormRef = ref();
const searchForm = reactive<UserQueryRequest>({
  keyword: "",
  status: undefined,
  roleId: undefined,
  dateRange: [],
});



// 角色选项
const roleOptions = ref<RoleOption[]>([]);

// 表格列配置
const columns = [
  {
    colKey: "selection",
    type: "multiple",
    width: 50,
  },
  {
    colKey: "avatar",
    title: "头像",
    width: 80,
    align: "center",
  },
  {
    colKey: "userInfo",
    title: "用户信息",
    width: 200,
  },
  {
    colKey: "status",
    title: "状态",
    width: 100,
    align: "center",
  },
  {
    colKey: "roles",
    title: "角色",
    width: 150,
  },
  {
    colKey: "phone",
    title: "手机号",
    width: 120,
  },
  {
    colKey: "lastLogin",
    title: "最后登录",
    width: 160,
  },
  {
    colKey: "createdTime",
    title: "创建时间",
    width: 160,
    cell: (h: any, { row }: any) => formatDateTime(row.createdTime),
  },
  {
    colKey: "actions",
    title: "操作",
    width: 150,
    align: "center",
    fixed: "right",
  },
];

// 使用表格钩子
const {
  tableData,
  loading,
  selectedRowKeys,
  selectedRows,
  pagination,
  paginationConfig,
  hasSelection,
  selectionCount,
  fetchTableData,
  handleSelectionChange,
  handlePageChange,
  clearSelection,
  handleDelete,
  handleBatchDelete: baseBatchDelete,
} = useTable<UserResponse>({
  api: {
    list: userApi.getPage,
    delete: userApi.delete,
    batchDelete: userApi.batchDelete,
  },
  columns,
  defaultPageSize: 10,
});

// 使用表单钩子
const {
  formVisible,
  formMode,
  formData,
  openCreateForm,
  openUpdateForm,
  closeForm,
} = useForm<UserFormData>({
  api: {
    create: userApi.create,
    update: userApi.update,
  },
  defaultValues: {
    username: "",
    email: "",
    status: "ACTIVE",
    roleIds: [],
  },
});

// 详情弹窗
const detailVisible = ref(false);
const detailData = ref<UserResponse | null>(null);

// 角色分配弹窗
const roleAssignVisible = ref(false);
const roleAssignData = ref<UserResponse | null>(null);

// 密码重置弹窗
const passwordResetVisible = ref(false);
const passwordResetData = ref<UserResponse | null>(null);

// 搜索处理
const handleSearch = () => {
  const params: UserQueryRequest = { ...searchForm };

  // 处理日期范围
  if (params.dateRange && params.dateRange.length === 2) {
    params.startTime = params.dateRange[0];
    params.endTime = params.dateRange[1];
    delete params.dateRange;
  }

  fetchTableData(params);
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: "",
    status: undefined,
    roleId: undefined,
    dateRange: [],
  });
  fetchTableData();
};

// 刷新表格
const handleRefresh = () => {
  fetchTableData();
};

// 创建用户
const handleCreate = () => {
  openCreateForm();
};

// 查看用户
const handleView = (row: UserResponse) => {
  detailData.value = row;
  detailVisible.value = true;
};

// 编辑用户
const handleUpdate = (row: UserResponse) => {
  openUpdateForm(row);
};

// 批量删除
const handleBatchDelete = async () => {
  const dialog = DialogPlugin.confirm({
    header: "确认删除",
    body: `确定要删除选中的 ${selectionCount.value} 个用户吗？此操作不可恢复。`,
    theme: "warning",
    onConfirm: async () => {
      try {
        await baseBatchDelete();
        MessagePlugin.success("批量删除成功");
        dialog.destroy();
      } catch (error) {
        console.error("批量删除失败:", error);
        MessagePlugin.error("批量删除失败");
      }
    },
  });
};

// 批量启用
const handleBatchEnable = async () => {
  try {
    await userApi.batchEnable({ ids: selectedRowKeys.value as number[] });
    MessagePlugin.success("批量启用成功");
    handleRefresh();
    clearSelection();
  } catch (error) {
    console.error("批量启用失败:", error);
    MessagePlugin.error("批量启用失败");
  }
};

// 批量禁用
const handleBatchDisable = async () => {
  try {
    await userApi.batchDisable({ ids: selectedRowKeys.value as number[] });
    MessagePlugin.success("批量禁用成功");
    handleRefresh();
    clearSelection();
  } catch (error) {
    console.error("批量禁用失败:", error);
    MessagePlugin.error("批量禁用失败");
  }
};

// 导出用户
const handleExport = async () => {
  try {
    const params = { ...searchForm };
    if (params.dateRange && params.dateRange.length === 2) {
      params.startTime = params.dateRange[0];
      params.endTime = params.dateRange[1];
      delete params.dateRange;
    }

    const blob = await userApi.export({ query: params, format: "xlsx" });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `用户数据_${new Date().toISOString().slice(0, 10)}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    MessagePlugin.success("导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    MessagePlugin.error("导出失败");
  }
};

// 获取操作选项
const getActionOptions = (row: UserResponse) => {
  const options = [];

  if (hasPermission("USER_UPDATE")) {
    options.push(
      { content: "分配角色", value: "assignRole" },
      { content: "重置密码", value: "resetPassword" },
    );

    if (row.status === "ACTIVE") {
      options.push({ content: "禁用用户", value: "disable" });
    } else {
      options.push({ content: "启用用户", value: "enable" });
    }

    if (row.isLocked) {
      options.push({ content: "解锁用户", value: "unlock" });
    } else {
      options.push({ content: "锁定用户", value: "lock" });
    }
  }

  if (hasPermission("USER_DELETE")) {
    options.push({ content: "删除用户", value: "delete", theme: "error" });
  }

  return options;
};

// 处理操作点击
const handleActionClick = async (action: any, row: UserResponse) => {
  switch (action.value) {
    case "assignRole":
      roleAssignData.value = row;
      roleAssignVisible.value = true;
      break;

    case "resetPassword":
      passwordResetData.value = row;
      passwordResetVisible.value = true;
      break;

    case "enable":
      await handleStatusChange(row.id, "ACTIVE");
      break;

    case "disable":
      await handleStatusChange(row.id, "INACTIVE");
      break;

    case "lock":
      await handleLockUser(row);
      break;

    case "unlock":
      await handleUnlockUser(row);
      break;

    case "delete":
      await handleDeleteUser(row);
      break;
  }
};

// 状态变更
const handleStatusChange = async (id: number, status: string) => {
  try {
    await userApi.updateStatus(id, { status });
    MessagePlugin.success("状态更新成功");
    handleRefresh();
  } catch (error) {
    console.error("状态更新失败:", error);
    MessagePlugin.error("状态更新失败");
  }
};

// 锁定用户
const handleLockUser = async (row: UserResponse) => {
  const dialog = DialogPlugin.confirm({
    header: "确认锁定",
    body: `确定要锁定用户 "${row.username}" 吗？`,
    theme: "warning",
    onConfirm: async () => {
      try {
        await userApi.batchLock({ ids: [row.id] });
        MessagePlugin.success("用户锁定成功");
        handleRefresh();
        dialog.destroy();
      } catch (error) {
        console.error("用户锁定失败:", error);
        MessagePlugin.error("用户锁定失败");
      }
    },
  });
};

// 解锁用户
const handleUnlockUser = async (row: UserResponse) => {
  try {
    await userApi.batchUnlock({ ids: [row.id] });
    MessagePlugin.success("用户解锁成功");
    handleRefresh();
  } catch (error) {
    console.error("用户解锁失败:", error);
    MessagePlugin.error("用户解锁失败");
  }
};

// 删除用户
const handleDeleteUser = async (row: UserResponse) => {
  const dialog = DialogPlugin.confirm({
    header: "确认删除",
    body: `确定要删除用户 "${row.username}" 吗？此操作不可恢复。`,
    theme: "error",
    onConfirm: async () => {
      try {
        await handleDelete(row.id);
        dialog.destroy();
      } catch (error) {
        console.error("删除用户失败:", error);
        MessagePlugin.error("删除用户失败");
      }
    },
  });
};

// 表单成功回调
const handleFormSuccess = () => {
  handleRefresh();
  closeForm();
};

// 角色分配成功回调
const handleRoleAssignSuccess = () => {
  handleRefresh();
  roleAssignVisible.value = false;
};

// 密码重置成功回调
const handlePasswordResetSuccess = () => {
  handleRefresh();
  passwordResetVisible.value = false;
};

// 获取角色选项
const fetchRoleOptions = async () => {
  try {
    const response = await userApi.getRoleOptions();
    roleOptions.value = response.data.map((role) => ({
      label: role.name,
      value: role.id,
      ...role,
    }));
  } catch (error) {
    console.error("获取角色选项失败:", error);
  }
};

// 初始化
onMounted(() => {
  fetchRoleOptions();
});
</script>

<!-- 样式已移至全局样式文件，使用TDesign组件原生样式 -->
