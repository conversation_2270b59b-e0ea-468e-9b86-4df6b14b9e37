<template>
  <div class="role-management-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧角色列表面板 -->
      <div class="left-panel">
        <t-card 
          class="roles-card" 
          :bordered="false"
        >
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <h3 class="card-title">角色列表</h3>
                <t-tag 
                  v-if="totalRoles > 0"
                  :count="totalRoles" 
                  size="small"
                  theme="primary"
                  variant="light"
                >
                  {{ totalRoles }}
                </t-tag>
              </div>
              <div class="header-right">
                <t-space>
                  <t-input
                    v-model="searchKeyword"
                    placeholder="搜索角色名称、编码..."
                    size="small"
                    clearable
                    @enter="handleSearch"
                    @clear="handleSearch"
                  >
                    <template #prefix-icon>
                      <search-icon />
                    </template>
                  </t-input>
                  <t-button
                    v-if="hasPermission('ROLE_CREATE')"
                    theme="primary"
                    size="small"
                    @click="handleCreateRole"
                  >
                    <template #icon>
                      <add-icon />
                    </template>
                    新建角色
                  </t-button>
                  <t-button
                    variant="outline"
                    size="small"
                    @click="handleRefreshRoles"
                    :loading="rolesLoading"
                  >
                    <template #icon>
                      <refresh-icon />
                    </template>
                  </t-button>
                </t-space>
              </div>
            </div>
          </template>
          
          <!-- 筛选栏 -->
          <div class="filter-bar">
            <t-space>
              <t-select
                v-model="filterStatus"
                placeholder="全部状态"
                size="small"
                style="width: 120px"
                clearable
                @change="handleFilterChange"
              >
                <t-option value="ACTIVE" label="启用" />
                <t-option value="INACTIVE" label="禁用" />
              </t-select>
              <t-button
                v-if="selectedRoleIds.length > 0"
                theme="danger"
                variant="outline"
                size="small"
                @click="handleBatchDelete"
              >
                批量删除 ({{ selectedRoleIds.length }})
              </t-button>
            </t-space>
          </div>

          <!-- 角色表格 -->
          <div class="roles-table-container">
            <t-table
              ref="rolesTableRef"
              v-model:selected-row-keys="selectedRoleIds"
              :data="rolesList"
              :columns="rolesColumns"
              :loading="rolesLoading"
              :pagination="rolesPagination"
              row-key="id"
              size="small"
              hover
              stripe
              show-header
              select-on-row-click
              @select-change="handleSelectChange"
              @page-change="handlePageChange"
              @page-size-change="handlePageSizeChange"
              @row-click="handleRowClick"
              class="roles-table"
            >
              <!-- 状态列 -->
              <template #status="{ row }">
                <t-tag 
                  :theme="row.status === 'ACTIVE' ? 'success' : 'warning'"
                  variant="light"
                  size="small"
                >
                  {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
                </t-tag>
              </template>

              <!-- 操作列 -->
              <template #actions="{ row }">
                <t-space size="small">
                  <t-button
                    v-if="hasPermission('ROLE_UPDATE')"
                    theme="primary"
                    variant="text"
                    size="small"
                    @click.stop="handleEditRole(row)"
                  >
                    编辑
                  </t-button>
                  <t-dropdown
                    :options="getRoleActionOptions(row)"
                    @click="(data) => handleRoleAction(data, row)"
                    trigger="click"
                  >
                    <t-button
                      variant="text"
                      size="small"
                      @click.stop
                    >
                      更多
                      <template #suffix>
                        <chevron-down-icon />
                      </template>
                    </t-button>
                  </t-dropdown>
                </t-space>
              </template>
            </t-table>
          </div>
        </t-card>
      </div>

      <!-- 右侧详情面板 -->
      <div class="right-panel">
        <t-card 
          class="role-detail-card" 
          :bordered="false"
        >
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <h3 class="card-title">
                  <span v-if="selectedRole">{{ operationTitle }}</span>
                  <span v-else class="empty-title">选择角色查看详情</span>
                </h3>
                <div v-if="selectedRole" class="role-info">
                  <t-tag 
                    :theme="selectedRole.status === 'ACTIVE' ? 'success' : 'warning'"
                    variant="light"
                    size="small"
                  >
                    {{ selectedRole.status === 'ACTIVE' ? '启用' : '禁用' }}
                  </t-tag>
                  <span class="role-code">{{ selectedRole.code }}</span>
                </div>
              </div>
              <div class="header-right">
                <t-space>
                  <t-button
                    v-if="selectedRole && currentMode === 'view' && hasPermission('ROLE_UPDATE')"
                    theme="primary"
                    variant="outline"
                    size="small"
                    @click="handleEditMode"
                  >
                    <template #icon>
                      <edit-icon />
                    </template>
                    编辑角色
                  </t-button>
                  <t-button
                    v-if="selectedRole && currentMode === 'view' && hasPermission('ROLE_UPDATE')"
                    variant="outline"
                    size="small"
                    @click="handleAssignMenus"
                  >
                    <template #icon>
                      <setting-icon />
                    </template>
                    分配权限
                  </t-button>
                </t-space>
              </div>
            </div>
          </template>

          <!-- 角色详情查看 -->
          <RoleDetailView
            v-if="currentMode === 'view'"
            :role="selectedRole"
            @edit="handleEditRole"
            @assign-menus="handleAssignMenus"
            @delete="handleDeleteRole"
          />
          
          <!-- 角色表单（创建/编辑） -->
          <RoleForm
            v-else-if="currentMode === 'edit' || currentMode === 'create'"
            :role="selectedRole"
            :mode="currentMode"
            :loading="detailLoading"
            @save="handleSaveRole"
            @cancel="handleCancelEdit"
          />
        </t-card>
      </div>
    </div>

    <!-- 菜单权限分配对话框 -->
    <RoleMenuAssignDialog
      v-model:visible="menuAssignVisible"
      :role="selectedRole"
      @success="handleMenuAssignSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import {
  AddIcon,
  RefreshIcon,
  SearchIcon,
  EditIcon,
  ChevronDownIcon,
  SettingIcon,
  DeleteIcon,
  UserIcon
} from 'tdesign-icons-vue-next'
import { roleApi } from '@/api/system/role'
import { usePermission } from '@/composables/usePermission'
import RoleDetailView from './components/RoleDetailView.vue'
import RoleForm from './components/RoleForm.vue'
import RoleMenuAssignDialog from './components/RoleMenuAssignDialog.vue'
import type { RoleResponse, RoleQueryRequest } from '@/api/system/types/role'
import { RoleStatus } from '@/api/system/types/common'

// 权限检查
const { hasPermission } = usePermission()

// 页面状态
const rolesTableRef = ref()
const selectedRoleIds = ref<number[]>([])
const selectedRole = ref<RoleResponse | null>(null)
const currentMode = ref<'view' | 'edit' | 'create'>('view')
const rolesLoading = ref(false)
const detailLoading = ref(false)
const totalRoles = ref(0)

// 搜索和筛选
const searchKeyword = ref('')
const filterStatus = ref<string>('')

// 对话框状态
const menuAssignVisible = ref(false)

// 角色列表
const rolesList = ref<RoleResponse[]>([])

// 分页配置
const rolesPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100]
})

// 表格列配置
const rolesColumns = [
  {
    colKey: 'serial-number',
    title: '序号',
    width: 80,
    align: 'center'
  },
  {
    colKey: 'name',
    title: '角色名称',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'code', 
    title: '角色编码',
    width: 120,
    ellipsis: true
  },
  {
    colKey: 'description',
    title: '描述',
    ellipsis: true
  },
  {
    colKey: 'status',
    title: '状态',
    width: 100,
    align: 'center'
  },
  {
    colKey: 'sortOrder',
    title: '排序',
    width: 80,
    align: 'center'
  },
  {
    colKey: 'createdTime',
    title: '创建时间',
    width: 160,
    ellipsis: true
  },
  {
    colKey: 'actions',
    title: '操作',
    width: 150,
    align: 'center',
    fixed: 'right'
  }
]

// 计算属性
const operationTitle = computed(() => {
  switch (currentMode.value) {
    case 'create':
      return '新建角色'
    case 'edit':
      return selectedRole.value ? `编辑 - ${selectedRole.value.name}` : '编辑角色'
    default:
      return selectedRole.value ? selectedRole.value.name : '选择角色查看详情'
  }
})

// 查询参数
const queryParams = computed((): RoleQueryRequest => {
  const params: RoleQueryRequest = {
    current: rolesPagination.current,
    pageSize: rolesPagination.pageSize,
  }

  if (searchKeyword.value) {
    params.keyword = searchKeyword.value
  }

  if (filterStatus.value) {
    params.status = filterStatus.value as RoleStatus
  }

  return params
})

// 获取角色操作选项
const getRoleActionOptions = (role: RoleResponse) => {
  const options = []
  
  if (hasPermission('ROLE_UPDATE')) {
    options.push({
      content: role.status === 'ACTIVE' ? '禁用角色' : '启用角色',
      value: 'toggle-status',
      theme: role.status === 'ACTIVE' ? 'warning' : 'success'
    })
    
    options.push({
      content: '分配权限',
      value: 'assign-menus',
      prefixIcon: 'setting'
    })
  }
  
  options.push({
    content: '查看用户',
    value: 'view-users',
    prefixIcon: 'user'
  })
  
  if (hasPermission('ROLE_DELETE')) {
    options.push({
      content: '删除角色',
      value: 'delete',
      theme: 'error',
      prefixIcon: 'delete'
    })
  }
  
  return options
}

// 事件处理
const handleSearch = () => {
  rolesPagination.current = 1
  fetchRolesList()
}

const handleFilterChange = () => {
  rolesPagination.current = 1
  fetchRolesList()
}

const handleRefreshRoles = () => {
  fetchRolesList()
}

const handleCreateRole = () => {
  selectedRole.value = null
  currentMode.value = 'create'
}

const handleEditRole = (role: RoleResponse) => {
  selectedRole.value = role
  currentMode.value = 'edit'
}

const handleEditMode = () => {
  currentMode.value = 'edit'
}

const handleDeleteRole = async (role: RoleResponse) => {
  const dialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除角色"${role.name}"吗？删除后所有使用该角色的用户权限将受到影响，此操作不可恢复。`,
    theme: 'danger',
    onConfirm: async () => {
      try {
        await roleApi.delete(role.id)
        MessagePlugin.success('删除成功')
        
        // 如果删除的是当前选中项，清空选择
        if (selectedRole.value?.id === role.id) {
          selectedRole.value = null
          currentMode.value = 'view'
        }
        
        fetchRolesList()
      } catch (error) {
        console.error('删除角色失败:', error)
      }
    }
  })
}

const handleSaveRole = () => {
  // 保存成功后刷新列表和详情
  fetchRolesList()
  currentMode.value = 'view'
}

const handleCancelEdit = () => {
  if (currentMode.value === 'create') {
    selectedRole.value = null
  }
  currentMode.value = 'view'
}

const handleAssignMenus = () => {
  menuAssignVisible.value = true
}

const handleSelectChange = (selectedRowKeys: number[]) => {
  selectedRoleIds.value = selectedRowKeys
}

const handleRowClick = ({ row }: { row: RoleResponse }) => {
  selectedRole.value = row
  currentMode.value = 'view'
}

const handlePageChange = (pageInfo: any) => {
  rolesPagination.current = pageInfo.current
  fetchRolesList()
}

const handlePageSizeChange = (pageInfo: any) => {
  rolesPagination.pageSize = pageInfo.pageSize
  rolesPagination.current = 1
  fetchRolesList()
}

const handleBatchDelete = () => {
  if (selectedRoleIds.value.length === 0) {
    MessagePlugin.warning('请选择要删除的角色')
    return
  }

  const dialog = DialogPlugin.confirm({
    header: '批量删除确认',
    body: `确定要删除选中的 ${selectedRoleIds.value.length} 个角色吗？此操作不可恢复。`,
    theme: 'danger',
    onConfirm: async () => {
      try {
        await roleApi.batchDelete(selectedRoleIds.value)
        MessagePlugin.success('批量删除成功')
        selectedRoleIds.value = []
        fetchRolesList()
      } catch (error) {
        console.error('批量删除失败:', error)
      }
    }
  })
}

const handleRoleAction = (data: { value: string }, role: RoleResponse) => {
  const { value } = data
  
  switch (value) {
    case 'toggle-status':
      handleToggleStatus(role)
      break
    case 'assign-menus':
      selectedRole.value = role
      handleAssignMenus()
      break
    case 'view-users':
      handleViewUsers(role)
      break
    case 'delete':
      handleDeleteRole(role)
      break
  }
}

const handleToggleStatus = async (role: RoleResponse) => {
  const newStatus = role.status === RoleStatus.ACTIVE ? RoleStatus.INACTIVE : RoleStatus.ACTIVE
  const actionText = newStatus === RoleStatus.ACTIVE ? '启用' : '禁用'
  
  try {
    await roleApi.updateStatus(role.id, newStatus)
    MessagePlugin.success(`${actionText}成功`)
    
    // 更新当前选中角色的状态
    if (selectedRole.value?.id === role.id) {
      selectedRole.value.status = newStatus
    }
    
    fetchRolesList()
  } catch (error) {
    console.error(`${actionText}角色失败:`, error)
  }
}

const handleViewUsers = (role: RoleResponse) => {
  // 跳转到用户管理页面并过滤该角色的用户
  console.log('查看角色用户:', role)
  MessagePlugin.info('跳转到用户管理页面功能待实现')
}

const handleMenuAssignSuccess = () => {
  MessagePlugin.success('权限分配成功')
}

// 获取角色列表
const fetchRolesList = async () => {
  try {
    rolesLoading.value = true
    const response = await roleApi.getPage(queryParams.value)
    
    if (response && response.data) {
      const pageData = response.data as any
      rolesList.value = pageData.records || []
      rolesPagination.total = pageData.total || 0
      rolesPagination.current = pageData.current || 1
      totalRoles.value = pageData.total || 0
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    MessagePlugin.error('获取角色列表失败')
  } finally {
    rolesLoading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchRolesList()
})
</script>

<style scoped>
.role-management-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  height: calc(100vh - 140px);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  height: 100%;
  max-height: 700px;
}

.left-panel {
  width: 60%;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
  min-width: 0;
}

.roles-card,
.role-detail-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.roles-card :deep(.t-card__body),
.role-detail-card :deep(.t-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.header-right {
  flex-shrink: 0;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.empty-title {
  color: var(--td-text-color-placeholder);
}

.role-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.role-code {
  font-family: var(--td-font-family-mono);
  font-size: 12px;
  color: var(--td-text-color-secondary);
  background: var(--td-bg-color-component);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 筛选栏 */
.filter-bar {
  padding: 0 16px 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
  margin-bottom: 16px;
}

/* 表格容器 */
.roles-table-container {
  flex: 1;
  padding: 0 16px 16px;
  min-height: 0;
  overflow: hidden;
}

.roles-table {
  height: 100%;
}

.roles-table :deep(.t-table__body) {
  overflow-y: auto;
}

.roles-table :deep(.t-table__row) {
  cursor: pointer;
}

.roles-table :deep(.t-table__row:hover) {
  background-color: var(--td-bg-color-container-hover);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: 16px;
    max-height: none;
  }
  
  .left-panel {
    width: 100%;
    height: 400px;
  }
  
  .right-panel {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .role-management-container {
    padding: 12px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-right {
    width: 100%;
  }
  
  .header-right :deep(.t-space) {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
