<template>
  <div class="role-detail-view">
    <!-- 空状态 -->
    <t-empty
      v-if="!role"
      image="default"
      description="请选择一个角色查看详情"
    >
      <template #action>
        <t-button theme="primary" @click="$emit('create-role')">
          <template #icon>
            <add-icon />
          </template>
          创建角色
        </t-button>
      </template>
    </t-empty>

    <!-- 角色详情 -->
    <div v-else class="role-detail-content">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <t-row :gutter="16">
          <t-col :span="12">
            <div class="detail-item">
              <span class="item-label">角色名称</span>
              <span class="item-value">{{ role.name }}</span>
            </div>
          </t-col>
          <t-col :span="12">
            <div class="detail-item">
              <span class="item-label">角色编码</span>
              <span class="item-value">
                <t-tag variant="outline" size="small">{{ role.code }}</t-tag>
              </span>
            </div>
          </t-col>
          <t-col :span="12">
            <div class="detail-item">
              <span class="item-label">状态</span>
              <span class="item-value">
                <t-tag 
                  :theme="role.status === 'ACTIVE' ? 'success' : 'warning'"
                  variant="light"
                  size="small"
                >
                  {{ role.status === 'ACTIVE' ? '启用' : '禁用' }}
                </t-tag>
              </span>
            </div>
          </t-col>
          <t-col :span="12">
            <div class="detail-item">
              <span class="item-label">排序</span>
              <span class="item-value">{{ role.sortOrder || 0 }}</span>
            </div>
          </t-col>
          <t-col :span="24">
            <div class="detail-item">
              <span class="item-label">描述</span>
              <span class="item-value">{{ role.description || '暂无描述' }}</span>
            </div>
          </t-col>
          <t-col :span="12">
            <div class="detail-item">
              <span class="item-label">创建时间</span>
              <span class="item-value">{{ role.createdTime }}</span>
            </div>
          </t-col>
          <t-col :span="12">
            <div class="detail-item">
              <span class="item-label">更新时间</span>
              <span class="item-value">{{ role.updatedTime }}</span>
            </div>
          </t-col>
        </t-row>
      </div>

      <!-- 权限信息 -->
      <div class="detail-section">
        <div class="section-header">
          <h4 class="section-title">菜单权限</h4>
          <t-button
            v-if="hasPermission('ROLE_UPDATE')"
            theme="primary"
            variant="outline"
            size="small"
            @click="$emit('assign-menus')"
          >
            <template #icon>
              <setting-icon />
            </template>
            分配权限
          </t-button>
        </div>
        
        <div class="permissions-container">
          <t-loading :loading="menusLoading" size="medium">
            <div v-if="roleMenus.length > 0" class="permissions-list">
              <t-tag
                v-for="menu in roleMenus"
                :key="menu.id"
                theme="primary"
                variant="light"
                size="small"
                class="permission-tag"
              >
                {{ menu.title }}
              </t-tag>
            </div>
            <t-empty
              v-else
              image="search"
              description="暂未分配菜单权限"
              size="small"
            >
              <template #action>
                <t-button
                  v-if="hasPermission('ROLE_UPDATE')"
                  theme="primary"
                  size="small"
                  @click="$emit('assign-menus')"
                >
                  立即分配
                </t-button>
              </template>
            </t-empty>
          </t-loading>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="detail-actions">
        <t-space>
          <t-button
            v-if="hasPermission('ROLE_UPDATE')"
            theme="primary"
            @click="$emit('edit', role)"
          >
            <template #icon>
              <edit-icon />
            </template>
            编辑角色
          </t-button>
          <t-button
            v-if="hasPermission('ROLE_UPDATE')"
            variant="outline"
            @click="$emit('assign-menus')"
          >
            <template #icon>
              <setting-icon />
            </template>
            分配权限
          </t-button>
          <t-button
            v-if="hasPermission('ROLE_DELETE')"
            theme="danger"
            variant="outline"
            @click="handleDelete"
          >
            <template #icon>
              <delete-icon />
            </template>
            删除角色
          </t-button>
        </t-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import {
  AddIcon,
  SettingIcon,
  EditIcon,
  DeleteIcon
} from 'tdesign-icons-vue-next'
import { usePermission } from '@/composables/usePermission'
import type { RoleResponse } from '@/api/system/types/role'

// Props
interface Props {
  role?: RoleResponse | null
}

const props = withDefaults(defineProps<Props>(), {
  role: null
})

// Emits
interface Emits {
  (e: 'edit', role: RoleResponse): void
  (e: 'assign-menus'): void
  (e: 'delete', role: RoleResponse): void
  (e: 'create-role'): void
}

const emit = defineEmits<Emits>()

// 权限检查
const { hasPermission } = usePermission()

// 响应式数据
const menusLoading = ref(false)
const roleMenus = ref<any[]>([])

// 处理删除
const handleDelete = () => {
  if (props.role) {
    emit('delete', props.role)
  }
}
</script>

<style scoped>
.role-detail-view {
  height: 100%;
  padding: 16px;
  overflow-y: auto;
}

.role-detail-content {
  max-width: 800px;
}

/* 详情区块 */
.detail-section {
  margin-bottom: 32px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--td-text-color-primary);
}

/* 详情项 */
.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.item-label {
  min-width: 80px;
  font-size: 14px;
  color: var(--td-text-color-secondary);
  margin-right: 16px;
}

.item-value {
  flex: 1;
  font-size: 14px;
  color: var(--td-text-color-primary);
  word-break: break-word;
}

/* 权限列表 */
.permissions-container {
  min-height: 100px;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  margin: 0;
}

/* 操作按钮 */
.detail-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--td-border-level-1-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .item-label {
    margin-bottom: 4px;
    margin-right: 0;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style> 