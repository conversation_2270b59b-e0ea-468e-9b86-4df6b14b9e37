<template>
  <div class="role-form">
    <t-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="100"
      layout="vertical"
      @submit="handleSubmit"
      class="role-form-content"
    >
      <t-row :gutter="16">
        <t-col :span="12">
          <t-form-item label="角色名称" name="name" required>
            <t-input
              v-model="formData.name"
              placeholder="请输入角色名称"
              :disabled="loading"
              clearable
            />
          </t-form-item>
        </t-col>
        <t-col :span="12">
          <t-form-item label="角色编码" name="code" required>
            <t-input
              v-model="formData.code"
              placeholder="请输入角色编码"
              :disabled="loading || mode === 'edit'"
              clearable
            >
              <template #tips>
                {{ mode === 'edit' ? '编辑时不可修改角色编码' : '角色编码全局唯一，建议使用大写字母和下划线' }}
              </template>
            </t-input>
          </t-form-item>
        </t-col>
        <t-col :span="12">
          <t-form-item label="状态" name="status">
            <t-radio-group v-model="formData.status" :disabled="loading">
              <t-radio value="ACTIVE">启用</t-radio>
              <t-radio value="INACTIVE">禁用</t-radio>
            </t-radio-group>
          </t-form-item>
        </t-col>
        <t-col :span="12">
          <t-form-item label="排序" name="sortOrder">
            <t-input-number
              v-model="formData.sortOrder"
              :min="0"
              :max="9999"
              placeholder="排序值"
              :disabled="loading"
            />
          </t-form-item>
        </t-col>
        <t-col :span="24">
          <t-form-item label="描述" name="description">
            <t-textarea
              v-model="formData.description"
              placeholder="请输入角色描述"
              :rows="3"
              :maxlength="500"
              :disabled="loading"
              show-limit
            />
          </t-form-item>
        </t-col>
      </t-row>

      <!-- 表单操作按钮 -->
      <div class="form-actions">
        <t-space>
          <t-button
            theme="primary"
            type="submit"
            :loading="loading"
          >
            <template #icon>
              <save-icon />
            </template>
            {{ mode === 'create' ? '创建角色' : '保存修改' }}
          </t-button>
          <t-button
            variant="outline"
            @click="handleCancel"
            :disabled="loading"
          >
            取消
          </t-button>
          <t-button
            v-if="mode === 'edit'"
            variant="outline"
            @click="handleReset"
            :disabled="loading"
          >
            重置
          </t-button>
        </t-space>
      </div>
    </t-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { SaveIcon } from 'tdesign-icons-vue-next'
import { roleApi } from '@/api/system/role'
import type { RoleResponse, RoleCreateRequest, RoleUpdateRequest } from '@/api/system/types/role'

// Props
interface Props {
  role?: RoleResponse | null
  mode: 'create' | 'edit'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  role: null,
  loading: false
})

// Emits
interface Emits {
  (e: 'save'): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  code: '',
  description: '',
  status: 'ACTIVE' as const,
  sortOrder: 0
})

// 初始表单数据（用于重置）
const initialFormData = reactive({
  name: '',
  code: '',
  description: '',
  status: 'ACTIVE' as const,
  sortOrder: 0
})

// 表单验证规则
const formRules = {
  name: [
    {
      required: true,
      message: '请输入角色名称',
      trigger: 'blur'
    },
    {
      max: 100,
      message: '角色名称长度不能超过100个字符',
      trigger: 'blur'
    }
  ],
  code: [
    {
      required: true,
      message: '请输入角色编码',
      trigger: 'blur'
    },
    {
      pattern: /^[A-Z][A-Z0-9_]*$/,
      message: '角色编码必须以大写字母开头，只能包含大写字母、数字和下划线',
      trigger: 'blur'
    },
    {
      max: 50,
      message: '角色编码长度不能超过50个字符',
      trigger: 'blur'
    },
    {
      validator: async (val: string) => {
        if (!val) return true
        
        try {
          const response = await roleApi.checkCode(val, props.mode === 'edit' ? props.role?.id : undefined)
          if (response?.data?.data === false) {
            return { result: false, message: '角色编码已存在' }
          }
          return true
        } catch (error) {
          console.error('检查角色编码失败:', error)
          return true // 网络错误时不阻止提交
        }
      },
      trigger: 'blur'
    }
  ],
  description: [
    {
      max: 500,
      message: '描述长度不能超过500个字符',
      trigger: 'blur'
    }
  ],
  sortOrder: [
    {
      type: 'number',
      min: 0,
      max: 9999,
      message: '排序值必须在0-9999之间',
      trigger: 'blur'
    }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (props.mode === 'edit' && props.role) {
    // 编辑模式，填充现有数据
    Object.assign(formData, {
      name: props.role.name || '',
      code: props.role.code || '',
      description: props.role.description || '',
      status: props.role.status || 'ACTIVE',
      sortOrder: props.role.sortOrder || 0
    })
  } else {
    // 创建模式，使用默认值
    Object.assign(formData, {
      name: '',
      code: '',
      description: '',
      status: 'ACTIVE',
      sortOrder: 0
    })
  }
  
  // 保存初始数据用于重置
  Object.assign(initialFormData, formData)
}

// 处理提交
const handleSubmit = async ({ validateResult }: any) => {
  if (validateResult !== true) {
    MessagePlugin.error('请检查表单填写是否正确')
    return
  }

  try {
    if (props.mode === 'create') {
      // 创建角色
             const createData: RoleCreateRequest = {
         name: formData.name,
         code: formData.code,
         description: formData.description,
         sortOrder: formData.sortOrder
       }
      
      await roleApi.create(createData)
      MessagePlugin.success('角色创建成功')
    } else {
      // 更新角色
      if (!props.role?.id) {
        MessagePlugin.error('角色ID不存在')
        return
      }
      
             const updateData: RoleUpdateRequest = {
         name: formData.name,
         description: formData.description,
         sortOrder: formData.sortOrder
       }
      
      await roleApi.update(props.role.id, updateData)
      MessagePlugin.success('角色更新成功')
    }
    
    emit('save')
  } catch (error) {
    console.error('保存角色失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 处理重置
const handleReset = () => {
  Object.assign(formData, initialFormData)
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 监听角色变化，重新初始化表单
watch(
  () => [props.role, props.mode],
  () => {
    initFormData()
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  },
  { immediate: true, deep: true }
)
</script>

<style scoped>
.role-form {
  height: 100%;
  padding: 16px;
  overflow-y: auto;
}

.role-form-content {
  max-width: 800px;
}

/* 表单操作按钮 */
.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--td-border-level-1-color);
}

/* 表单项样式调整 */
:deep(.t-form-item__label) {
  font-weight: 500;
}

:deep(.t-input__tips) {
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-form {
    padding: 12px;
  }
  
  .form-actions {
    margin-top: 24px;
    padding-top: 16px;
  }
  
  .form-actions :deep(.t-space) {
    width: 100%;
    justify-content: center;
  }
}
</style> 