<template>
  <t-dialog
    v-model:visible="visible"
    header="分配菜单权限"
    :width="800"
    :draggable="true"
    :footer="false"
    class="role-menu-assign-dialog"
  >
    <div class="dialog-content">
      <!-- 角色信息 -->
      <div v-if="role" class="role-info-bar">
        <t-space align="center">
          <span class="role-label">角色：</span>
          <t-tag theme="primary" variant="light">{{ role.name }}</t-tag>
          <t-tag variant="outline" size="small">{{ role.code }}</t-tag>
        </t-space>
      </div>

      <!-- 菜单权限树 -->
      <div class="menu-tree-container">
        <div class="tree-header">
          <t-space justify="space-between" align="center" class="tree-toolbar">
            <span class="tree-title">菜单权限</span>
            <t-space>
              <t-button
                variant="text"
                size="small"
                @click="handleExpandAll"
              >
                {{ allExpanded ? '收起全部' : '展开全部' }}
              </t-button>
              <t-button
                variant="text"
                size="small"
                @click="handleSelectAll"
              >
                {{ allSelected ? '取消全选' : '全选' }}
              </t-button>
            </t-space>
          </t-space>
        </div>

        <div class="tree-content">
          <t-loading :loading="menuTreeLoading" size="medium">
            <t-tree
              ref="menuTreeRef"
              v-model:value="selectedMenuIds"
              v-model:expanded="expandedMenuIds"
              :data="menuTreeData"
              :keys="treeKeys"
              checkable
              expand-all
              hover
              transition
              :check-strictly="false"
              @change="handleMenuSelectionChange"
              class="menu-tree"
            >
              <template #label="{ node }">
                <div class="menu-node">
                  <t-space align="center" size="small">
                    <!-- 菜单图标 -->
                    <t-icon 
                      v-if="node.data.icon" 
                      :name="node.data.icon" 
                      size="16px"
                      class="menu-icon"
                    />
                    <span class="menu-title">{{ node.data.title }}</span>
                    <t-tag
                      :theme="getMenuTypeTheme(node.data.type)"
                      variant="light"
                      size="small"
                      class="menu-type-tag"
                    >
                      {{ getMenuTypeLabel(node.data.type) }}
                    </t-tag>
                    <t-tag
                      v-if="!node.data.enabled"
                      theme="warning"
                      variant="light"
                      size="small"
                    >
                      已禁用
                    </t-tag>
                  </t-space>
                </div>
              </template>
            </t-tree>
          </t-loading>
        </div>
      </div>

      <!-- 已选权限统计 -->
      <div v-if="selectedMenuIds.length > 0" class="selected-info">
        <t-alert theme="info" :close="false">
          <template #message>
            已选择 <strong>{{ selectedMenuIds.length }}</strong> 个菜单权限
          </template>
        </t-alert>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <t-space>
        <t-button
          variant="outline"
          @click="handleCancel"
          :disabled="saving"
        >
          取消
        </t-button>
        <t-button
          theme="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存分配
        </t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { menuApi } from '@/api/system/menu'
import { roleApi } from '@/api/system/role'
import type { RoleResponse } from '@/api/system/types/role'
import type { MenuTreeNode, MenuType } from '@/api/system/types/menu'

// Props
interface Props {
  visible: boolean
  role?: RoleResponse | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  role: null
})

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const menuTreeRef = ref()
const menuTreeLoading = ref(false)
const saving = ref(false)
const menuTreeData = ref<MenuTreeNode[]>([])
const selectedMenuIds = ref<number[]>([])
const expandedMenuIds = ref<number[]>([])
const allExpanded = ref(true)

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const allSelected = computed(() => {
  const allMenuIds = getAllMenuIds(menuTreeData.value)
  return allMenuIds.length > 0 && selectedMenuIds.value.length === allMenuIds.length
})

// 树组件配置
const treeKeys = {
  value: 'id',
  label: 'title',
  children: 'children'
}

// 获取所有菜单ID
const getAllMenuIds = (nodes: MenuTreeNode[]): number[] => {
  const ids: number[] = []
  
  const traverse = (nodeList: MenuTreeNode[]) => {
    nodeList.forEach(node => {
      ids.push(node.id)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  
  traverse(nodes)
  return ids
}

// 获取所有可展开的节点ID
const getAllExpandableIds = (nodes: MenuTreeNode[]): number[] => {
  const ids: number[] = []
  
  const traverse = (nodeList: MenuTreeNode[]) => {
    nodeList.forEach(node => {
      if (node.children && node.children.length > 0) {
        ids.push(node.id)
        traverse(node.children)
      }
    })
  }
  
  traverse(nodes)
  return ids
}

// 菜单类型相关
const getMenuTypeLabel = (type: MenuType) => {
  const labels = {
    DIRECTORY: '目录',
    MENU: '菜单',
    BUTTON: '按钮'
  }
  return labels[type] || type
}

const getMenuTypeTheme = (type: MenuType) => {
  const themes = {
    DIRECTORY: 'primary',
    MENU: 'success',
    BUTTON: 'warning'
  }
  return themes[type] || 'default'
}

// 获取菜单树数据
const fetchMenuTree = async () => {
  try {
    menuTreeLoading.value = true
    const response = await menuApi.getTree()
    
       if (response?.data?.data) {
       menuTreeData.value = response.data.data
       
       // 自动展开所有节点
       const expandableIds = getAllExpandableIds(response.data.data)
       expandedMenuIds.value = expandableIds
     }
  } catch (error) {
    console.error('获取菜单树失败:', error)
    MessagePlugin.error('获取菜单树失败')
  } finally {
    menuTreeLoading.value = false
  }
}

// 获取角色已有权限
const fetchRoleMenus = async () => {
  if (!props.role?.id) return
  
  try {
    const response = await roleApi.getMenus(props.role.id)
       if (response?.data?.data) {
       selectedMenuIds.value = response.data.data
     }
  } catch (error) {
    console.error('获取角色菜单权限失败:', error)
  }
}

// 处理展开/收起全部
const handleExpandAll = () => {
  if (allExpanded.value) {
    expandedMenuIds.value = []
    allExpanded.value = false
  } else {
    const expandableIds = getAllExpandableIds(menuTreeData.value)
    expandedMenuIds.value = expandableIds
    allExpanded.value = true
  }
}

// 处理全选/取消全选
const handleSelectAll = () => {
  if (allSelected.value) {
    selectedMenuIds.value = []
  } else {
    const allMenuIds = getAllMenuIds(menuTreeData.value)
    selectedMenuIds.value = allMenuIds
  }
}

// 处理菜单选择变化
const handleMenuSelectionChange = (value: number[]) => {
  selectedMenuIds.value = value
}

// 处理保存
const handleSave = async () => {
  if (!props.role?.id) {
    MessagePlugin.error('角色信息不存在')
    return
  }

  try {
    saving.value = true
    await roleApi.assignMenus(props.role.id, selectedMenuIds.value)
    MessagePlugin.success('权限分配成功')
    emit('success')
    visible.value = false
  } catch (error) {
    console.error('分配权限失败:', error)
    MessagePlugin.error('分配权限失败')
  } finally {
    saving.value = false
  }
}

// 处理取消
const handleCancel = () => {
  visible.value = false
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.role) {
      // 重置状态
      selectedMenuIds.value = []
      expandedMenuIds.value = []
      allExpanded.value = true
      
      // 获取数据
      fetchMenuTree()
      
      nextTick(() => {
        fetchRoleMenus()
      })
    }
  }
)

// 监听展开状态变化
watch(
  () => expandedMenuIds.value.length,
  (newLength) => {
    const allExpandableIds = getAllExpandableIds(menuTreeData.value)
    allExpanded.value = newLength === allExpandableIds.length
  }
)
</script>

<style scoped>
.role-menu-assign-dialog :deep(.t-dialog__body) {
  padding: 0;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  max-height: 70vh;
}

/* 角色信息栏 */
.role-info-bar {
  padding: 16px 24px;
  background-color: var(--td-bg-color-container);
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.role-label {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

/* 菜单树容器 */
.menu-tree-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.tree-header {
  padding: 16px 24px 0;
}

.tree-toolbar {
  width: 100%;
}

.tree-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.tree-content {
  flex: 1;
  padding: 16px 24px;
  overflow-y: auto;
  min-height: 300px;
  max-height: 400px;
}

.menu-tree {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  padding: 8px;
}

/* 菜单节点样式 */
.menu-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.menu-icon {
  color: var(--td-text-color-secondary);
}

.menu-title {
  font-size: 14px;
  color: var(--td-text-color-primary);
}

.menu-type-tag {
  margin: 0;
}

/* 已选信息 */
.selected-info {
  padding: 16px 24px;
  border-top: 1px solid var(--td-border-level-1-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-menu-assign-dialog :deep(.t-dialog) {
    width: 95vw !important;
    margin: 16px auto;
  }
  
  .tree-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .tree-content {
    padding: 12px 16px;
  }
  
  .role-info-bar,
  .selected-info {
    padding: 12px 16px;
  }
}
</style> 