<template>
  <div class="dict-management-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <t-card 
          class="types-card" 
          :bordered="false"
        >
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <h3 class="card-title">字典类型</h3>
              </div>
              <div class="header-right">
                <t-input
                  v-model="searchKeyword"
                  placeholder="搜索类型..."
                  size="small"
                  clearable
                  @enter="handleSearch"
                >
                  <template #prefix-icon>
                    <search-icon />
                  </template>
                </t-input>
              </div>
            </div>
          </template>
          
          <DictTypeList
            v-model:search-keyword="searchKeyword"
            :selected-id="selectedDictTypeId"
            @select="handleDictTypeSelect"
            @refresh="handleDictTypeRefresh"
            @total-change="handleTotalTypesChange"
          />
        </t-card>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <t-card 
          class="data-card" 
          :bordered="false"
        >
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <h3 class="card-title">
                  <span v-if="selectedDictType">{{ selectedDictType.dictName }}</span>
                  <span v-else class="empty-title">选择字典类型</span>
                </h3>
                <div v-if="selectedDictType" class="type-info">
                  <t-tag 
                    :theme="selectedDictType.status === 'ACTIVE' ? 'success' : 'warning'"
                    variant="light"
                    size="small"
                  >
                    {{ selectedDictType.status === 'ACTIVE' ? '启用' : '禁用' }}
                  </t-tag>
                  <span class="type-code">{{ selectedDictType.dictCode }}</span>
                </div>
              </div>
              <div class="header-right">
                <t-space>
                  <t-button
                    v-if="selectedDictType && hasPermission('DICT_DATA_CREATE')"
                    theme="primary"
                    size="small"
                    @click="handleCreateData"
                  >
                    <template #icon>
                      <add-icon />
                    </template>
                    新增数据
                  </t-button>
                  <t-button
                    v-if="hasPermission('DICT_TYPE_CREATE')"
                    variant="outline"
                    size="small"
                    @click="handleQuickCreate"
                  >
                    <template #icon>
                      <add-icon />
                    </template>
                    新建类型
                  </t-button>
                </t-space>
              </div>
            </div>
          </template>

          <DictDataList
            :dict-type-id="selectedDictTypeId"
            :dict-type-info="selectedDictType"
            @refresh="handleDictDataRefresh"
          />
        </t-card>
      </div>
    </div>

    <!-- 快速创建对话框 -->
    <t-dialog
      v-model:visible="quickCreateVisible"
      header="快速新建字典"
      :width="600"
      :confirm-btn="null"
      :cancel-btn="null"
    >
      <div class="quick-create-content">
        <t-radio-group v-model="createType" variant="default-filled">
          <t-radio value="type">
            <div class="create-option">
              <data-icon class="option-icon" />
              <div class="option-content">
                <div class="option-title">新建字典类型</div>
                <div class="option-desc">创建一个新的字典分类</div>
              </div>
            </div>
          </t-radio>
          <t-radio value="data" :disabled="!selectedDictType">
            <div class="create-option">
              <list-icon class="option-icon" />
              <div class="option-content">
                <div class="option-title">新建字典数据</div>
                <div class="option-desc">
                  {{ selectedDictType ? `在"${selectedDictType.dictName}"下添加数据` : '请先选择字典类型' }}
                </div>
              </div>
            </div>
          </t-radio>
        </t-radio-group>
      </div>
      
      <template #footer>
        <t-space>
          <t-button variant="outline" @click="quickCreateVisible = false">
            取消
          </t-button>
          <t-button 
            theme="primary" 
            @click="handleQuickCreateConfirm"
            :disabled="!createType"
          >
            继续创建
          </t-button>
        </t-space>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import {
  DataIcon,
  AddIcon,
  SearchIcon,
  ListIcon,
} from 'tdesign-icons-vue-next'
import DictTypeList from './components/DictTypeList.vue'
import DictDataList from './components/DictDataList.vue'
import { usePermission } from '@/composables/usePermission'
import type { DictTypeResponse } from '@/api'

// 权限检查
const { hasPermission } = usePermission()

// 页面状态
const selectedDictTypeId = ref<number | null>(null)
const selectedDictType = ref<DictTypeResponse | null>(null)
const searchKeyword = ref('')
const totalTypes = ref(0)

// 快速创建
const quickCreateVisible = ref(false)
const createType = ref<'type' | 'data' | ''>('')

// 处理字典类型选择
const handleDictTypeSelect = (dictType: DictTypeResponse) => {
  selectedDictTypeId.value = dictType.id
  selectedDictType.value = dictType
  console.log('📋 选中字典类型:', dictType)
}

// 处理字典类型刷新
const handleDictTypeRefresh = () => {
  console.log('🔄 刷新字典类型列表')
}

// 处理字典数据刷新
const handleDictDataRefresh = () => {
  console.log('🔄 刷新字典数据列表')
}

// 处理搜索
const handleSearch = () => {
  console.log('🔍 搜索字典类型:', searchKeyword.value)
}

// 处理类型总数变化
const handleTotalTypesChange = (total: number) => {
  totalTypes.value = total
}

// 处理快速创建
const handleQuickCreate = () => {
  createType.value = ''
  quickCreateVisible.value = true
}

// 处理创建字典数据
const handleCreateData = () => {
  createType.value = 'data'
  handleQuickCreateConfirm()
}

// 处理快速创建确认
const handleQuickCreateConfirm = () => {
  quickCreateVisible.value = false
  
  if (createType.value === 'type') {
    console.log('创建字典类型')
  } else if (createType.value === 'data') {
    console.log('创建字典数据')
  }
}
</script>

<style scoped>
.dict-management-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: calc(100vh - 120px);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  min-height: 600px;
}

.left-panel {
  width: 380px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
  min-width: 0;
}

.types-card,
.data-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.types-card :deep(.t-card__body),
.data-card :deep(.t-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.header-right {
  flex-shrink: 0;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.empty-title {
  color: var(--td-text-color-placeholder);
}

.type-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.type-code {
  font-family: var(--td-font-family-mono);
  font-size: 12px;
  color: var(--td-text-color-secondary);
  background: var(--td-bg-color-component);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 快速创建 */
.quick-create-content {
  padding: 8px 0;
}

.quick-create-content :deep(.t-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quick-create-content :deep(.t-radio) {
  margin: 0;
  padding: 16px;
  border: 2px solid var(--td-border-level-1-color);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.quick-create-content :deep(.t-radio:hover) {
  border-color: var(--td-brand-color);
  background-color: var(--td-brand-color-1);
}

.quick-create-content :deep(.t-radio.t-is-checked) {
  border-color: var(--td-brand-color);
  background-color: var(--td-brand-color-1);
}

.quick-create-content :deep(.t-radio.t-is-disabled) {
  opacity: 0.5;
  cursor: not-allowed;
}

.create-option {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

.option-icon {
  font-size: 24px;
  color: var(--td-brand-color);
  margin-top: 2px;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--td-text-color-primary);
  margin-bottom: 4px;
}

.option-desc {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: 16px;
    min-height: auto;
  }
  
  .left-panel {
    width: 100%;
  }
  
  .types-card {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .dict-management-container {
    padding: 12px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-right {
    width: 100%;
  }
}
</style>
