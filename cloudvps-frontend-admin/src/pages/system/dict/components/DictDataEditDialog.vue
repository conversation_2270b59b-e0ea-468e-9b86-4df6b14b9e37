<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :header="dialogTitle"
    :width="600"
    :confirm-btn="confirmBtnOptions"
    :cancel-btn="cancelBtnOptions"
    :close-on-overlay-click="false"
    :close-on-esc-keydown="false"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <t-form
      ref="formRef"
      :data="formData"
      :rules="formRules"
      :label-width="100"
      :disabled="loading"
      @submit="handleSubmit"
    >
      <t-form-item label="所属字典" name="dictTypeId">
        <t-input
          :value="dictTypeName"
          disabled
          placeholder="请先选择字典类型"
        />
      </t-form-item>
      
      <t-form-item label="数据标签" name="dictLabel">
        <t-input
          v-model="formData.dictLabel"
          placeholder="请输入数据标签"
          maxlength="100"
          show-limit-number
        />
      </t-form-item>
      
      <t-form-item label="数据值" name="dictValue">
        <t-input
          v-model="formData.dictValue"
          placeholder="请输入数据值"
          maxlength="100"
          show-limit-number
        />
      </t-form-item>
      
      <t-form-item label="排序" name="sortOrder">
        <t-input-number
          v-model="formData.sortOrder"
          placeholder="请输入排序号"
          :min="0"
          :max="9999"
          :step="1"
        />
      </t-form-item>
      
      <t-form-item label="状态" name="status">
        <t-radio-group v-model="formData.status">
          <t-radio value="ACTIVE">启用</t-radio>
          <t-radio value="INACTIVE">禁用</t-radio>
        </t-radio-group>
      </t-form-item>
      
      <t-form-item label="是否默认" name="isDefault">
        <t-switch v-model="formData.isDefault" />
      </t-form-item>
      
      <t-form-item label="标签样式" name="cssClass">
        <t-select
          v-model="formData.cssClass"
          placeholder="请选择标签样式"
          clearable
        >
          <t-option value="default" label="默认">
            <t-tag theme="default">默认</t-tag>
          </t-option>
          <t-option value="primary" label="主要">
            <t-tag theme="primary">主要</t-tag>
          </t-option>
          <t-option value="success" label="成功">
            <t-tag theme="success">成功</t-tag>
          </t-option>
          <t-option value="warning" label="警告">
            <t-tag theme="warning">警告</t-tag>
          </t-option>
          <t-option value="danger" label="危险">
            <t-tag theme="danger">危险</t-tag>
          </t-option>
        </t-select>
      </t-form-item>
      
      <t-form-item label="备注" name="remark">
        <t-textarea
          v-model="formData.remark"
          placeholder="请输入备注"
          :maxlength="500"
          show-limit-number
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { dictDataApi } from '@/api/system/dict'
import type { FormRule } from 'tdesign-vue-next'
import type { DictDataCreateRequest, DictDataUpdateRequest, DictDataResponse, DictTypeResponse } from '@/api/system/types/dict'

// Props
interface Props {
  visible: boolean
  editData?: DictDataResponse | null
  dictTypeInfo: DictTypeResponse | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null,
  dictTypeInfo: null
})

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 对话框状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const loading = ref(false)
const formRef = ref()
const isEdit = computed(() => !!props.editData)
const dialogTitle = computed(() => isEdit.value ? '编辑字典数据' : '新增字典数据')
const dictTypeName = computed(() => props.dictTypeInfo ? `${props.dictTypeInfo.dictName} (${props.dictTypeInfo.dictCode})` : '')

// 表单数据
const formData = reactive<DictDataCreateRequest>({
  dictTypeId: 0,
  dictLabel: '',
  dictValue: '',
  sortOrder: 0,
  status: 'ACTIVE',
  isDefault: false,
  cssClass: '',
  listClass: '',
  remark: ''
})

// 表单验证规则
const formRules: Record<string, FormRule[]> = {
  dictTypeId: [
    { required: true, message: '请选择所属字典', trigger: 'change' }
  ],
  dictLabel: [
    { required: true, message: '请输入数据标签', trigger: 'blur' }
  ],
  dictValue: [
    { required: true, message: '请输入数据值', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序号', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 确认按钮配置
const confirmBtnOptions = computed(() => ({
  theme: 'primary',
  loading: loading.value,
  content: isEdit.value ? '保存' : '确定'
}))

// 取消按钮配置
const cancelBtnOptions = computed(() => ({
  theme: 'default',
  disabled: loading.value,
  content: '取消'
}))

// 监听编辑数据变化
watch(
  () => props.editData,
  (newData) => {
    if (newData) {
      // 编辑模式，填充表单数据
      formData.dictTypeId = newData.dictTypeId
      formData.dictLabel = newData.dictLabel
      formData.dictValue = newData.dictValue
      formData.sortOrder = newData.sortOrder
      formData.status = newData.status
      formData.isDefault = newData.isDefault
      formData.cssClass = newData.cssClass || ''
      formData.listClass = newData.listClass || ''
      formData.remark = newData.remark || ''
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  },
  { immediate: true }
)

// 监听字典类型变化
watch(
  () => props.dictTypeInfo,
  (newType) => {
    if (newType && !isEdit.value) {
      formData.dictTypeId = newType.id
    }
  },
  { immediate: true }
)

// 重置表单
const resetForm = () => {
  formData.dictTypeId = props.dictTypeInfo?.id || 0
  formData.dictLabel = ''
  formData.dictValue = ''
  formData.sortOrder = 0
  formData.status = 'ACTIVE'
  formData.isDefault = false
  formData.cssClass = ''
  formData.listClass = ''
  formData.remark = ''
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  const valid = await formRef.value?.validate()
  if (!valid) return

  try {
    loading.value = true
    
    if (isEdit.value) {
      // 编辑模式
      const updateData: DictDataUpdateRequest = {
        dictLabel: formData.dictLabel,
        dictValue: formData.dictValue,
        sortOrder: formData.sortOrder,
        status: formData.status,
        isDefault: formData.isDefault,
        cssClass: formData.cssClass,
        listClass: formData.listClass,
        remark: formData.remark
      }
      
      await dictDataApi.update(props.editData!.id, updateData)
      MessagePlugin.success('更新成功')
    } else {
      // 新增模式
      await dictDataApi.create(formData)
      MessagePlugin.success('创建成功')
    }
    
    // 触发成功事件
    emit('success')
    // 关闭对话框
    dialogVisible.value = false
  } catch (error) {
    console.error('保存字典数据失败:', error)
    // 错误消息由API拦截器统一处理
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
/* 样式可以根据需要添加 */
</style> 