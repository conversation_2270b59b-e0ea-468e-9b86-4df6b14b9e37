<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :header="dialogTitle"
    :width="600"
    :confirm-btn="confirmBtnOptions"
    :cancel-btn="cancelBtnOptions"
    :close-on-overlay-click="false"
    :close-on-esc-keydown="false"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <t-form
      ref="formRef"
      :data="formData"
      :rules="formRules"
      :label-width="100"
      :disabled="loading"
      @submit="handleSubmit"
    >
      <t-form-item label="字典编码" name="dictCode">
        <t-input
          v-model="formData.dictCode"
          placeholder="请输入字典编码"
          :disabled="isEdit"
          maxlength="100"
          show-limit-number
        />
      </t-form-item>
      
      <t-form-item label="字典名称" name="dictName">
        <t-input
          v-model="formData.dictName"
          placeholder="请输入字典名称"
          maxlength="100"
          show-limit-number
        />
      </t-form-item>
      
      <t-form-item label="状态" name="status">
        <t-radio-group v-model="formData.status">
          <t-radio value="ACTIVE">启用</t-radio>
          <t-radio value="INACTIVE">禁用</t-radio>
        </t-radio-group>
      </t-form-item>
      
      <t-form-item label="描述" name="description">
        <t-textarea
          v-model="formData.description"
          placeholder="请输入描述"
          :maxlength="500"
          show-limit-number
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { dictTypeApi } from '@/api/system/dict'
import type { FormRule } from 'tdesign-vue-next'
import type { DictTypeCreateRequest, DictTypeUpdateRequest, DictTypeResponse } from '@/api/system/types/dict'

// Props
interface Props {
  visible: boolean
  editData?: DictTypeResponse | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null
})

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 对话框状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const loading = ref(false)
const formRef = ref()
const isEdit = computed(() => !!props.editData)
const dialogTitle = computed(() => isEdit.value ? '编辑字典类型' : '新增字典类型')

// 表单数据
const formData = reactive<DictTypeCreateRequest>({
  dictCode: '',
  dictName: '',
  description: '',
  status: 'ACTIVE'
})

// 表单验证规则
const formRules: Record<string, FormRule[]> = {
  dictCode: [
    { required: true, message: '请输入字典编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字典编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  dictName: [
    { required: true, message: '请输入字典名称', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 确认按钮配置
const confirmBtnOptions = computed(() => ({
  theme: 'primary',
  loading: loading.value,
  content: isEdit.value ? '保存' : '确定'
}))

// 取消按钮配置
const cancelBtnOptions = computed(() => ({
  theme: 'default',
  disabled: loading.value,
  content: '取消'
}))

// 监听编辑数据变化
watch(
  () => props.editData,
  (newData) => {
    if (newData) {
      // 编辑模式，填充表单数据
      formData.dictCode = newData.dictCode
      formData.dictName = newData.dictName
      formData.description = newData.description || ''
      formData.status = newData.status
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  },
  { immediate: true }
)

// 重置表单
const resetForm = () => {
  formData.dictCode = ''
  formData.dictName = ''
  formData.description = ''
  formData.status = 'ACTIVE'
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  const valid = await formRef.value?.validate()
  if (!valid) return

  try {
    loading.value = true
    
    if (isEdit.value) {
      // 编辑模式
      const updateData: DictTypeUpdateRequest = {
        dictName: formData.dictName,
        description: formData.description,
        status: formData.status
      }
      
      await dictTypeApi.update(props.editData!.id, updateData)
      MessagePlugin.success('更新成功')
    } else {
      // 新增模式
      await dictTypeApi.create(formData)
      MessagePlugin.success('创建成功')
    }
    
    // 触发成功事件
    emit('success')
    // 关闭对话框
    dialogVisible.value = false
  } catch (error) {
    console.error('保存字典类型失败:', error)
    // 错误消息由API拦截器统一处理
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
/* 样式可以根据需要添加 */
</style> 