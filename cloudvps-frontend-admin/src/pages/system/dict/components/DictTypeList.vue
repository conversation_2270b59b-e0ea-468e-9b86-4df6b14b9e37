<template>
  <div class="dict-type-list">
    <!-- 顶部操作栏 -->
    <div class="list-toolbar">
      <t-button
        v-if="hasPermission('DICT_TYPE_CREATE')"
        theme="primary"
        variant="outline"
        size="small"
        @click="handleCreate"
      >
        <template #icon>
          <add-icon />
        </template>
        新建类型
      </t-button>
      <t-button
        variant="text"
        size="small"
        @click="handleRefresh"
        :loading="loading"
      >
        <template #icon>
          <refresh-icon />
        </template>
      </t-button>
    </div>

    <!-- 字典类型列表 -->
    <div class="type-list-container">
      <t-loading :loading="loading" size="medium">
        <t-list 
          v-if="dictTypeList.length > 0"
          :split="false"
          size="medium"
          class="type-list"
        >
          <t-list-item
            v-for="item in dictTypeList"
            :key="item.id"
            :class="[
              'type-item',
              { 'type-item--selected': selectedId === item.id },
              { 'type-item--disabled': item.status !== 'ACTIVE' }
            ]"
            @click="handleSelect(item)"
          >
            <t-list-item-meta>
              <template #title>
                <t-space direction="vertical" size="small" class="item-content">
                  <!-- 第一行：名称 + 编码 + 状态 -->
                  <t-row justify="space-between" align="middle">
                    <t-col flex="1">
                      <t-space size="small" align="center">
                        <span class="item-title">{{ item.dictName }}</span>
                        <t-tag variant="outline" size="small" class="code-tag">
                          {{ item.dictCode }}
                        </t-tag>
                      </t-space>
                    </t-col>
                    <t-col flex="none">
                      <t-tag
                        :theme="item.status === 'ACTIVE' ? 'success' : 'warning'"
                        variant="light"
                        size="small"
                        class="status-tag"
                      >
                        {{ item.status === 'ACTIVE' ? '启用' : '禁用' }}
                      </t-tag>
                    </t-col>
                  </t-row>
                  
                  <!-- 第二行：描述 + 操作按钮 -->
                  <t-row v-if="item.description" justify="space-between" align="middle">
                    <t-col flex="1">
                      <div class="item-description">
                        {{ item.description }}
                      </div>
                    </t-col>
                    <t-col flex="none">
                      <t-dropdown
                        :options="getActionOptions(item)"
                        @click="handleActionClick($event, item)"
                        trigger="click"
                        placement="bottom-right"
                      >
                        <t-button
                          variant="text"
                          size="small"
                          shape="square"
                          @click.stop
                          class="action-btn"
                        >
                          <template #icon>
                            <more-icon />
                          </template>
                        </t-button>
                      </t-dropdown>
                    </t-col>
                  </t-row>
                  
                  <!-- 无描述时的操作按钮行 -->
                  <t-row v-else justify="end" align="middle">
                    <t-col flex="none">
                      <t-dropdown
                        :options="getActionOptions(item)"
                        @click="handleActionClick($event, item)"
                        trigger="click"
                        placement="bottom-right"
                      >
                        <t-button
                          variant="text"
                          size="small"
                          shape="square"
                          @click.stop
                          class="action-btn"
                        >
                          <template #icon>
                            <more-icon />
                          </template>
                        </t-button>
                      </t-dropdown>
                    </t-col>
                  </t-row>
                </t-space>
              </template>
            </t-list-item-meta>
          </t-list-item>
        </t-list>

        <!-- 空状态 -->
        <t-empty
          v-else-if="!loading"
          image="search"
          :description="searchKeyword ? '未找到匹配的字典类型' : '暂无字典类型'"
        >
          <template #action>
            <t-button
              v-if="hasPermission('DICT_TYPE_CREATE')"
              theme="primary"
              @click="handleCreate"
            >
              <template #icon>
                <add-icon />
              </template>
              创建第一个字典类型
            </t-button>
          </template>
        </t-empty>
      </t-loading>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-wrapper">
      <t-pagination
        v-model:current="pagination.current"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-size-options="[10, 20, 30]"
        show-page-size
        show-total
        size="small"
        @change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- 编辑对话框 -->
    <DictTypeEditDialog
      v-model:visible="editDialogVisible"
      :edit-data="editData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import {
  AddIcon,
  RefreshIcon,
  MoreIcon,
  EditIcon,
  DeleteIcon,
} from 'tdesign-icons-vue-next'
import { dictTypeApi } from '@/api/system/dict'
import { usePermission } from '@/composables/usePermission'
import DictTypeEditDialog from './DictTypeEditDialog.vue'
import type { DictTypeResponse, DictTypeQueryRequest } from '@/api'

// Props
interface Props {
  selectedId?: number | null
  searchKeyword?: string
}

const props = withDefaults(defineProps<Props>(), {
  selectedId: null,
  searchKeyword: ''
})

// Emits
interface Emits {
  (e: 'select', dictType: DictTypeResponse): void
  (e: 'refresh'): void
  (e: 'total-change', total: number): void
  (e: 'update:searchKeyword', keyword: string): void
}

const emit = defineEmits<Emits>()

// 权限检查
const { hasPermission } = usePermission()

// 响应式数据
const loading = ref(false)
const dictTypeList = ref<DictTypeResponse[]>([])

// 编辑对话框
const editDialogVisible = ref(false)
const editData = ref<DictTypeResponse | null>(null)

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 查询参数
const queryParams = computed((): DictTypeQueryRequest => {
  const params: DictTypeQueryRequest = {
    current: pagination.current,
    pageSize: pagination.pageSize,
  }

  // 如果有搜索关键词，同时搜索名称和编码
  if (props.searchKeyword) {
    params.dictName = props.searchKeyword
  }

  return params
})

// 获取字典类型列表
const fetchDictTypeList = async () => {
  try {
    loading.value = true
    console.log('📋 获取字典类型列表:', queryParams.value)
    
    const response = await dictTypeApi.getPage(queryParams.value)
    
    if (response && response.data) {
      // 使用类型断言来解决类型问题
      const pageData = response.data as any
      dictTypeList.value = pageData.records || []
      pagination.total = pageData.total || 0
      pagination.current = pageData.current || 1
      pagination.pageSize = pageData.size || 10
      
      // 通知父组件总数变化
      emit('total-change', pagination.total)
      
      console.log('✅ 字典类型列表加载成功:', {
        count: dictTypeList.value.length,
        total: pagination.total
      })
    }
  } catch (error) {
    console.error('❌ 获取字典类型列表失败:', error)
    MessagePlugin.error('获取字典类型列表失败')
  } finally {
    loading.value = false
  }
}

// 处理刷新
const handleRefresh = () => {
  pagination.current = 1
  fetchDictTypeList()
  emit('refresh')
}

// 处理选择
const handleSelect = (item: DictTypeResponse) => {
  if (item.status !== 'ACTIVE') {
    MessagePlugin.warning('该字典类型已禁用，无法查看数据')
    return
  }
  emit('select', item)
}

// 处理创建
const handleCreate = () => {
  console.log('➕ 创建字典类型')
  editData.value = null
  editDialogVisible.value = true
}

// 处理编辑
const handleEdit = (item: DictTypeResponse) => {
  console.log('✏️ 编辑字典类型:', item)
  editData.value = item
  editDialogVisible.value = true
}

// 处理删除
const handleDelete = async (item: DictTypeResponse) => {
  console.log('🗑️ 删除字典类型:', item)
  
  const dialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除字典类型"${item.dictName}"吗？删除后该类型下的所有字典数据也将被删除，此操作不可恢复。`,
    theme: 'danger',
    onConfirm: async () => {
      try {
        await dictTypeApi.delete(item.id)
        MessagePlugin.success('删除成功')
        
        // 如果删除的是当前选中项，清空选择
        if (props.selectedId === item.id) {
          // 这里可以通知父组件清空选择
        }
        
        // 刷新列表
        fetchDictTypeList()
      } catch (error) {
        console.error('删除字典类型失败:', error)
      }
    }
  })
}

// 处理状态切换
const handleToggleStatus = async (item: DictTypeResponse) => {
  const newStatus = item.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
  const actionText = newStatus === 'ACTIVE' ? '启用' : '禁用'
  
  try {
    await dictTypeApi.updateStatus(item.id, newStatus)
    MessagePlugin.success(`${actionText}成功`)
    fetchDictTypeList()
  } catch (error) {
    console.error(`${actionText}字典类型失败:`, error)
  }
}

// 获取操作选项
const getActionOptions = (item: DictTypeResponse) => {
  const options = []

  if (hasPermission('DICT_TYPE_UPDATE')) {
    options.push({
      content: '编辑',
      value: 'edit',
    })
    
    options.push({
      content: item.status === 'ACTIVE' ? '禁用' : '启用',
      value: 'toggle-status',
      theme: item.status === 'ACTIVE' ? 'warning' : 'success'
    })
  }

  if (hasPermission('DICT_TYPE_DELETE')) {
    options.push({
      content: '删除',
      value: 'delete',
      theme: 'error',
    })
  }

  return options
}

// 处理操作点击
const handleActionClick = (data: any, item: DictTypeResponse) => {
  const { value } = data
  
  switch (value) {
    case 'edit':
      handleEdit(item)
      break
    case 'delete':
      handleDelete(item)
      break
    case 'toggle-status':
      handleToggleStatus(item)
      break
  }
}

// 处理分页变化
const handlePageChange = () => {
  fetchDictTypeList()
}

const handlePageSizeChange = () => {
  pagination.current = 1
  fetchDictTypeList()
}

// 处理编辑成功
const handleEditSuccess = () => {
  fetchDictTypeList()
}

// 监听搜索关键词变化
watch(
  () => props.searchKeyword,
  () => {
    pagination.current = 1
    fetchDictTypeList()
  }
)

// 初始化
onMounted(() => {
  fetchDictTypeList()
})
</script>

<style scoped>
.dict-type-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

/* 顶部操作栏 */
.list-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

/* 列表容器 */
.type-list-container {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.type-list {
  height: 100%;
  overflow-y: auto;
}

/* 列表项样式 */
.type-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.type-item:hover {
  background-color: var(--td-bg-color-container-hover);
}

.type-item--selected {
  background-color: var(--td-brand-color-1);
}

.type-item--selected:hover {
  background-color: var(--td-brand-color-2);
}

.type-item--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.type-item--disabled:hover {
  background-color: inherit;
}

/* 内容样式 */
.item-content {
  width: 100%;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-text-color-primary);
  word-break: break-word;
}

.code-tag :deep(.t-tag) {
  border: none !important;
  background: var(--td-bg-color-component) !important;
  color: var(--td-text-color-secondary) !important;
  font-family: var(--td-font-family-mono);
}

.status-tag {
  flex-shrink: 0;
}

.action-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  width: 24px;
  height: 24px;
}

.type-item:hover .action-btn,
.type-item--selected .action-btn {
  opacity: 1;
}

/* 描述样式 */
.item-description {
  font-size: 12px;
  color: var(--td-text-color-placeholder);
  line-height: 1.4;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 移除空状态水印 */
.type-list-container :deep(.t-empty) {
  background: transparent !important;
}

.type-list-container :deep(.t-empty__image) {
  background: transparent !important;
}

.type-list-container :deep(.t-empty__image img) {
  background: transparent !important;
}

.type-list-container :deep(.t-image__error) {
  display: none !important;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--td-border-level-1-color);
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dict-type-list {
    padding: 12px;
  }
  
  .action-btn {
    opacity: 1;
  }
  
  .item-description {
    white-space: normal;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
  }
}
</style>

