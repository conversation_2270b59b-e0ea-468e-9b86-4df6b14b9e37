<template>
  <div class="dict-data-list">
    <!-- 有选中字典类型时显示数据列表 -->
    <div v-if="dictTypeId" class="data-content">
      <!-- 搜索表单 -->
      <div class="search-section">
        <t-form 
          ref="searchFormRef"
          :data="searchForm"
          layout="inline"
          class="search-form"
        >
          <t-form-item label="数据标签" name="dictLabel">
            <t-input
              v-model="searchForm.dictLabel"
              placeholder="请输入数据标签"
              clearable
              style="width: 180px"
            />
          </t-form-item>
          
          <t-form-item label="数据值" name="dictValue">
            <t-input
              v-model="searchForm.dictValue"
              placeholder="请输入数据值"
              clearable
              style="width: 180px"
            />
          </t-form-item>
          
          <t-form-item label="状态" name="status">
            <t-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <t-option value="ACTIVE" label="启用" />
              <t-option value="INACTIVE" label="禁用" />
            </t-select>
          </t-form-item>
          
          <t-form-item>
            <t-space>
              <t-button theme="primary" @click="handleSearch">
                <template #icon>
                  <search-icon />
                </template>
                搜索
              </t-button>
              <t-button variant="outline" @click="handleReset">
                <template #icon>
                  <refresh-icon />
                </template>
                重置
              </t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>

      <!-- 操作栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <t-space>
            <t-button
              v-if="hasPermission('DICT_DATA_CREATE')"
              theme="primary"
              @click="handleCreate"
            >
              <template #icon>
                <add-icon />
              </template>
              新增数据
            </t-button>
            
            <t-button
              v-if="hasPermission('DICT_DATA_DELETE') && selectedRowKeys.length > 0"
              theme="danger"
              variant="outline"
              @click="handleBatchDelete"
            >
              <template #icon>
                <delete-icon />
              </template>
              批量删除 ({{ selectedRowKeys.length }})
            </t-button>
          </t-space>
        </div>
        
        <div class="toolbar-right">
          <t-space>
            <t-button variant="outline" @click="handleRefresh" :loading="loading">
              <template #icon>
                <refresh-icon />
              </template>
              刷新
            </t-button>
            
            <t-dropdown
              :options="[
                { content: '导出数据', value: 'export' },
                { content: '导入数据', value: 'import' },
              ]"
              @click="handleToolAction"
            >
              <t-button variant="outline">
                <template #icon>
                  <setting-icon />
                </template>
                更多
                <template #suffix>
                  <chevron-down-icon />
                </template>
              </t-button>
            </t-dropdown>
          </t-space>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <t-table
          ref="tableRef"
          v-model:selected-row-keys="selectedRowKeys"
          :data="dictDataList"
          :columns="columns"
          :loading="loading"
          :pagination="paginationProps"
          row-key="id"
          :select-on-row-click="true"
          :hover="true"
          :stripe="true"
          size="medium"
          table-layout="auto"
          cell-empty-content="-"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
          @select-change="handleSelectChange"
        >
          <!-- 颜色显示列 -->
          <template #color="{ row }">
            <div v-if="row.color" class="color-display">
              <div 
                class="color-block" 
                :style="{ backgroundColor: row.color }"
              />
              <span class="color-text">{{ row.color }}</span>
            </div>
            <span v-else class="empty-value">-</span>
          </template>

          <!-- 状态列 -->
          <template #status="{ row }">
            <t-tag
              :theme="row.status === 'ACTIVE' ? 'success' : 'warning'"
              variant="light"
            >
              {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
            </t-tag>
          </template>

          <!-- 默认值列 -->
          <template #isDefault="{ row }">
            <t-tag
              v-if="row.isDefault"
              theme="primary"
              variant="light"
              size="small"
            >
              默认
            </t-tag>
            <span v-else class="empty-value">-</span>
          </template>

          <!-- 操作列 -->
          <template #operation="{ row }">
            <t-space size="small">
              <t-button
                v-if="hasPermission('DICT_DATA_UPDATE')"
                theme="primary"
                variant="text"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </t-button>
              
              <t-dropdown
                :options="getMoreActions(row)"
                @click="(data: any) => handleMoreAction(data, row)"
                trigger="click"
              >
                <t-button
                  theme="default"
                  variant="text"
                  size="small"
                >
                  更多
                  <template #suffix>
                    <chevron-down-icon />
                  </template>
                </t-button>
              </t-dropdown>
            </t-space>
          </template>
        </t-table>
      </div>
    </div>

    <!-- 未选中字典类型时的空状态 -->
    <div v-else class="empty-state">
      <t-empty
        image="data"
        description="请从左侧选择一个字典类型来查看和管理字典数据"
      >
        <template #action>
          <div class="empty-actions">
            <p class="empty-tip">💡 提示：字典数据用于存储系统中的枚举值和配置项</p>
          </div>
        </template>
      </t-empty>
    </div>

    <!-- 编辑对话框 -->
    <DictDataEditDialog
      v-model:visible="editDialogVisible"
      :dict-type-id="dictTypeId"
      :dict-type-info="dictTypeInfo"
      :edit-data="editData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import type { PageInfo, TableProps } from 'tdesign-vue-next'
import {
  SearchIcon,
  RefreshIcon,
  AddIcon,
  DeleteIcon,
  SettingIcon,
  ChevronDownIcon,
} from 'tdesign-icons-vue-next'
import { dictDataApi } from '@/api/system/dict'
import { usePermission } from '@/composables/usePermission'
import DictDataEditDialog from './DictDataEditDialog.vue'
import type { DictDataResponse, DictDataQueryRequest, DictTypeResponse } from '@/api'

// Props
interface Props {
  dictTypeId?: number | null
  dictTypeInfo?: DictTypeResponse | null
}

const props = withDefaults(defineProps<Props>(), {
  dictTypeId: null,
  dictTypeInfo: null
})

// Emits
interface Emits {
  (e: 'refresh'): void
}

const emit = defineEmits<Emits>()

// 权限检查
const { hasPermission } = usePermission()

// 响应式数据
const loading = ref(false)
const dictDataList = ref<DictDataResponse[]>([])
const selectedRowKeys = ref<number[]>([])

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  dictLabel: '',
  dictValue: '',
  status: '' as 'ACTIVE' | 'INACTIVE' | ''
})

// 编辑对话框
const editDialogVisible = ref(false)
const editData = ref<DictDataResponse | null>(null)

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 分页属性
const paginationProps = computed<TableProps['pagination']>(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showJumper: true,
  showSizer: true,
  pageSizeOptions: [10, 20, 50, 100],
  showTotal: true,
  size: 'small'
}))

// 表格列配置
const columns = computed(() => [
  {
    colKey: 'serial-number',
    title: '序号',
    width: 80,
    align: 'center'
  },
  {
    colKey: 'dictLabel',
    title: '数据标签',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'dictValue',
    title: '数据值',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'color',
    title: '颜色',
    width: 120,
    align: 'center'
  },
  {
    colKey: 'sortOrder',
    title: '排序',
    width: 80,
    align: 'center'
  },
  {
    colKey: 'status',
    title: '状态',
    width: 80,
    align: 'center'
  },
  {
    colKey: 'isDefault',
    title: '默认值',
    width: 80,
    align: 'center'
  },
  {
    colKey: 'description',
    title: '描述',
    ellipsis: true,
    width: 200
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
])

// 查询参数
const queryParams = computed((): DictDataQueryRequest => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  dictTypeId: props.dictTypeId || 0,
  dictLabel: searchForm.dictLabel || undefined,
  dictValue: searchForm.dictValue || undefined,
  status: searchForm.status || undefined
}))

// 获取字典数据列表
const fetchDictDataList = async () => {
  if (!props.dictTypeId) {
    dictDataList.value = []
    return
  }

  try {
    loading.value = true
    console.log('📊 获取字典数据列表:', queryParams.value)
    
    const response = await dictDataApi.getPage(queryParams.value)
    
    if (response && response.data) {
      const pageData = response.data as any
      dictDataList.value = pageData.records || []
      pagination.total = pageData.total || 0
      pagination.current = pageData.current || 1
      pagination.pageSize = pageData.size || 10
      
      console.log('✅ 字典数据列表加载成功:', {
        count: dictDataList.value.length,
        total: pagination.total
      })
    }
  } catch (error) {
    console.error('❌ 获取字典数据列表失败:', error)
    MessagePlugin.error('获取字典数据列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchDictDataList()
}

// 处理重置
const handleReset = () => {
  searchFormRef.value?.reset()
  pagination.current = 1
  fetchDictDataList()
}

// 处理刷新
const handleRefresh = () => {
  fetchDictDataList()
  emit('refresh')
}

// 处理创建
const handleCreate = () => {
  editData.value = null
  editDialogVisible.value = true
}

// 处理编辑
const handleEdit = (row: DictDataResponse) => {
  editData.value = row
  editDialogVisible.value = true
}

// 处理状态切换
const handleToggleStatus = async (row: DictDataResponse) => {
  const newStatus = row.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
  const actionText = newStatus === 'ACTIVE' ? '启用' : '禁用'
  
  try {
    await dictDataApi.updateStatus(row.id, newStatus)
    MessagePlugin.success(`${actionText}成功`)
    fetchDictDataList()
  } catch (error) {
    console.error(`${actionText}字典数据失败:`, error)
  }
}

// 处理设置默认值
const handleSetDefault = async (row: DictDataResponse) => {
  try {
    // TODO: 需要后端提供设置默认值的API
    MessagePlugin.success('设置默认值成功')
    fetchDictDataList()
  } catch (error) {
    console.error('设置默认值失败:', error)
  }
}

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请选择要删除的数据')
    return
  }

  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除选中的 ${selectedRowKeys.value.length} 条字典数据吗？此操作不可恢复。`,
    theme: 'danger',
    onConfirm: async () => {
      try {
        await dictDataApi.batchDelete(selectedRowKeys.value)
        MessagePlugin.success('批量删除成功')
        selectedRowKeys.value = []
        fetchDictDataList()
      } catch (error) {
        console.error('批量删除失败:', error)
      }
    }
  })
}

// 获取更多操作选项
const getMoreActions = (row: DictDataResponse) => {
  const actions = []
  
  if (hasPermission('DICT_DATA_UPDATE')) {
    // 状态切换
    if (!row.isDefault) {
      actions.push({
        content: row.status === 'ACTIVE' ? '禁用' : '启用',
        value: 'toggle-status',
        theme: row.status === 'ACTIVE' ? 'warning' : 'success'
      })
    }
    
    // 设为默认
    if (!row.isDefault) {
      actions.push({
        content: '设为默认',
        value: 'set-default',
        theme: 'primary'
      })
    }
  }
  
  // 复制
  actions.push({
    content: '复制',
    value: 'copy'
  })
  
  // 删除
  if (hasPermission('DICT_DATA_DELETE') && !row.isDefault) {
    actions.push({
      content: '删除',
      value: 'delete',
      theme: 'error'
    })
  }
  
  return actions
}

// 处理更多操作
const handleMoreAction = async (data: any, row: DictDataResponse) => {
  const { value } = data
  
  switch (value) {
    case 'toggle-status':
      handleToggleStatus(row)
      break
    case 'set-default':
      handleSetDefault(row)
      break
    case 'delete':
      handleDelete(row)
      break
    case 'copy':
      handleCopy(row)
      break
  }
}

// 处理删除
const handleDelete = (row: DictDataResponse) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除字典数据"${row.dictLabel}"吗？此操作不可恢复。`,
    theme: 'danger',
    onConfirm: async () => {
      try {
        await dictDataApi.delete(row.id)
        MessagePlugin.success('删除成功')
        fetchDictDataList()
      } catch (error) {
        console.error('删除字典数据失败:', error)
      }
    }
  })
}

// 处理复制
const handleCopy = (row: DictDataResponse) => {
  editData.value = {
    ...row,
    id: 0, // 设置为0表示新建
    dictLabel: `${row.dictLabel}_副本`,
    dictValue: `${row.dictValue}_copy`,
    isDefault: false // 副本不能是默认值
  }
  editDialogVisible.value = true
}

// 处理工具操作
const handleToolAction = (data: any) => {
  const { value } = data
  
  switch (value) {
    case 'export':
      MessagePlugin.info('导出功能开发中...')
      break
    case 'import':
      MessagePlugin.info('导入功能开发中...')
      break
  }
}

// 处理分页变化
const handlePageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current
  fetchDictDataList()
}

const handlePageSizeChange = (pageInfo: PageInfo) => {
  pagination.pageSize = pageInfo.pageSize
  pagination.current = 1
  fetchDictDataList()
}

// 处理选择变化
const handleSelectChange = (value: number[]) => {
  selectedRowKeys.value = value
}

// 处理编辑成功
const handleEditSuccess = () => {
  fetchDictDataList()
}

// 监听字典类型ID变化
watch(
  () => props.dictTypeId,
  () => {
    selectedRowKeys.value = []
    pagination.current = 1
    fetchDictDataList()
  },
  { immediate: true }
)

// 初始化
onMounted(() => {
  if (props.dictTypeId) {
    fetchDictDataList()
  }
})
</script>

<style scoped>
.dict-data-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 数据内容区域 */
.data-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
}

/* 搜索区域 */
.search-section {
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 8px;
  padding: 16px;
}

.search-form {
  margin: 0;
}

.search-form :deep(.t-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

.search-form :deep(.t-form-item:last-child) {
  margin-right: 0;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 8px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
}

/* 表格区域 */
.table-section {
  flex: 1;
  min-height: 0;
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 8px;
  overflow: hidden;
}

.table-section :deep(.t-table) {
  height: 100%;
}

.table-section :deep(.t-table__content) {
  height: calc(100% - 56px); /* 减去表头高度 */
}

/* 颜色显示 */
.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-block {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid var(--td-border-level-2-color);
  flex-shrink: 0;
}

.color-text {
  font-family: var(--td-font-family-mono);
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

.empty-value {
  color: var(--td-text-color-placeholder);
  font-style: italic;
}

/* 空状态 */
.empty-state {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

/* 移除空状态水印 */
.empty-state :deep(.t-empty) {
  background: transparent !important;
}

.empty-state :deep(.t-empty__image) {
  background: transparent !important;
}

.empty-state :deep(.t-empty__image img) {
  background: transparent !important;
}

.empty-state :deep(.t-image__error) {
  display: none !important;
}

.data-content :deep(.t-empty) {
  background: transparent !important;
}

.data-content :deep(.t-empty__image) {
  background: transparent !important;
}

.data-content :deep(.t-empty__image img) {
  background: transparent !important;
}

.data-content :deep(.t-image__error) {
  display: none !important;
}

.empty-actions {
  margin-top: 16px;
}

.empty-tip {
  margin: 0;
  font-size: 14px;
  color: var(--td-text-color-secondary);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-form {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search-form :deep(.t-form-item) {
    margin-bottom: 12px;
    margin-right: 0;
    width: 100%;
  }
  
  .search-form :deep(.t-form-item:last-child) {
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .data-content {
    padding: 12px;
    gap: 12px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .search-section {
    padding: 12px;
  }
}
</style>
