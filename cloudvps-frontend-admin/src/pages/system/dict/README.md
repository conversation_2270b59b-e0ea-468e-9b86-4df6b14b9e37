# 字典管理页面设计文档

## 页面概述

字典管理页面采用左右分栏的设计布局，提供了高效的字典数据管理功能。该页面基于 TDesign Vue Next 组件库开发，遵循现代化的 UI/UX 设计原则。

## 设计理念

### 1. 布局设计
- **左右分栏布局**：左侧展示字典类型列表，右侧展示对应的字典数据
- **响应式设计**：支持不同屏幕尺寸的自适应显示
- **清晰的视觉层次**：通过合理的间距、颜色和字体大小区分不同层级的信息

### 2. 交互设计
- **即时响应**：点击左侧字典类型后，右侧立即显示对应的数据
- **状态反馈**：选中状态、加载状态、操作反馈等都有明确的视觉提示
- **操作便捷**：常用操作按钮位置固定，减少用户的操作路径

### 3. 功能设计
- **CRUD 操作**：支持字典类型和字典数据的增删改查
- **批量操作**：支持批量删除字典数据
- **搜索过滤**：支持按名称、编码、状态等条件筛选
- **权限控制**：根据用户权限显示相应的操作按钮

## 页面结构

### 左侧：字典类型列表
```
┌─────────────────────────────┐
│ 字典类型          [新增] [刷新] │
├─────────────────────────────┤
│ 🔍 搜索字典类型名称或编码        │
├─────────────────────────────┤
│ ┌─────────────────────────┐ │
│ │ 用户状态               ⋮ │ │
│ │ user_status    [正常]    │ │
│ │ 用户状态字典             │ │
│ ├─────────────────────────┤ │
│ │ 角色状态               ⋮ │ │
│ │ role_status    [正常]    │ │
│ │ 角色状态字典             │ │
│ └─────────────────────────┘ │
│          [分页组件]           │
└─────────────────────────────┘
```

### 右侧：字典数据列表
```
┌──────────────────────────────────────────────────┐
│ 字典数据                          [新增数据] [刷新] │
│ 用户状态 (user_status)                           │
├──────────────────────────────────────────────────┤
│ 数据查询                                         │
│ 数据标签: [_____] 数据值: [_____] 状态: [▼]      │
│                              [搜索] [重置]        │
├──────────────────────────────────────────────────┤
│ □ 数据标签  数据值  排序  状态  默认  备注  操作    │
│ ├──────────────────────────────────────────────┤ │
│ □ 正常     ACTIVE   1   [正常]  ✓   ...  [编辑][删除] │
│ □ 禁用     INACTIVE 2   [正常]  -   ...  [编辑][删除] │
│ □ 锁定     LOCKED   3   [正常]  -   ...  [编辑][删除] │
│ └──────────────────────────────────────────────┘ │
│          [批量删除] 已选中 3 项 [清空选择]          │
│                    [分页组件]                     │
└──────────────────────────────────────────────────┘
```

## 组件使用

### TDesign 组件清单
1. **布局组件**
   - `t-card`: 卡片容器
   - `t-row` / `t-col`: 栅格布局
   - `t-space`: 间距组件

2. **数据展示**
   - `t-list`: 列表组件（字典类型）
   - `t-table`: 表格组件（字典数据）
   - `t-pagination`: 分页组件
   - `t-tag`: 标签组件（状态显示）
   - `t-empty`: 空状态组件

3. **表单组件**
   - `t-form`: 表单容器
   - `t-input`: 输入框
   - `t-input-number`: 数字输入框
   - `t-select`: 下拉选择
   - `t-radio-group`: 单选组
   - `t-switch`: 开关
   - `t-textarea`: 文本域

4. **操作组件**
   - `t-button`: 按钮
   - `t-dropdown`: 下拉菜单
   - `t-dialog`: 对话框
   - `t-loading`: 加载状态

## 交互流程

### 1. 查看字典数据
1. 用户进入页面，左侧显示字典类型列表
2. 点击某个字典类型，该项高亮显示
3. 右侧自动加载并显示对应的字典数据

### 2. 新增字典类型
1. 点击左侧"新增"按钮
2. 弹出新增对话框
3. 填写字典编码、名称、描述等信息
4. 提交后刷新列表

### 3. 编辑字典数据
1. 在右侧表格中点击"编辑"按钮
2. 弹出编辑对话框，显示当前数据
3. 修改需要更新的字段
4. 提交后刷新表格

### 4. 批量删除
1. 勾选需要删除的数据项
2. 点击"批量删除"按钮
3. 确认删除操作
4. 删除成功后刷新列表

## 状态管理

### 页面状态
- `selectedDictTypeId`: 当前选中的字典类型ID
- `selectedDictType`: 当前选中的字典类型详情
- `editDialogVisible`: 编辑对话框显示状态
- `loading`: 加载状态

### 数据流向
```
用户操作 → 组件事件 → API请求 → 状态更新 → 视图更新
```

## 优化建议

1. **性能优化**
   - 使用虚拟滚动优化长列表性能
   - 实现数据缓存，减少重复请求
   - 使用防抖处理搜索输入

2. **用户体验**
   - 添加键盘快捷键支持
   - 支持拖拽排序
   - 提供导入导出功能

3. **可访问性**
   - 添加 ARIA 标签
   - 支持键盘导航
   - 提供高对比度主题

## 技术实现

### 核心技术
- Vue 3 Composition API
- TypeScript
- TDesign Vue Next
- Pinia (状态管理)

### 代码组织
```
dict/
├── index.vue              # 主页面
├── components/           
│   ├── DictTypeList.vue   # 字典类型列表
│   ├── DictDataList.vue   # 字典数据列表
│   ├── DictTypeEditDialog.vue  # 字典类型编辑
│   └── DictDataEditDialog.vue  # 字典数据编辑
└── README.md             # 设计文档
``` 