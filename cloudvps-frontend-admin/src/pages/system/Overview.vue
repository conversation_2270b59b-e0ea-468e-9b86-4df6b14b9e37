<template>
  <div class="system-overview">
    <div class="page-header">
      <h1>系统管理</h1>
      <p>管理用户、角色、权限等系统基础功能</p>
    </div>
    
    <t-row :gutter="24">
      <t-col :span="6">
        <t-card title="用户管理" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="user" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">1,234</div>
              <div class="card-label">总用户数</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              管理用户
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="角色管理" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="user-setting" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">12</div>
              <div class="card-label">角色数量</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              管理角色
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="权限管理" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="lock" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">156</div>
              <div class="card-label">权限数量</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              管理权限
            </t-button>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="6">
        <t-card title="系统配置" hover>
          <div class="overview-card">
            <div class="card-icon">
              <t-icon name="setting" size="32px" />
            </div>
            <div class="card-content">
              <div class="card-number">45</div>
              <div class="card-label">配置项</div>
            </div>
          </div>
          <div class="card-actions">
            <t-button theme="primary" variant="text" block>
              系统设置
            </t-button>
          </div>
        </t-card>
      </t-col>
    </t-row>
    
    <t-row :gutter="24" class="mt-6">
      <t-col :span="12">
        <t-card title="最近活动">
          <div class="activity-list">
            <div class="activity-item">
              <div class="activity-time">2024-01-15 10:30</div>
              <div class="activity-content">管理员创建了新用户 "张三"</div>
            </div>
            <div class="activity-item">
              <div class="activity-time">2024-01-15 09:15</div>
              <div class="activity-content">更新了系统配置 "邮件服务器"</div>
            </div>
            <div class="activity-item">
              <div class="activity-time">2024-01-14 16:45</div>
              <div class="activity-content">新增角色 "客服专员"</div>
            </div>
          </div>
        </t-card>
      </t-col>
      
      <t-col :span="12">
        <t-card title="系统状态">
          <div class="system-status">
            <div class="status-item">
              <span class="status-label">系统版本</span>
              <span class="status-value">v1.0.0</span>
            </div>
            <div class="status-item">
              <span class="status-label">数据库状态</span>
              <t-tag theme="success">正常</t-tag>
            </div>
            <div class="status-item">
              <span class="status-label">缓存状态</span>
              <t-tag theme="success">正常</t-tag>
            </div>
            <div class="status-item">
              <span class="status-label">在线用户</span>
              <span class="status-value">89</span>
            </div>
          </div>
        </t-card>
      </t-col>
    </t-row>
  </div>
</template>

<script setup lang="ts">
// 系统概览页面逻辑
</script>

<style scoped lang="less">
.system-overview {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.overview-card {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  
  .card-icon {
    color: #0052d9;
  }
  
  .card-content {
    flex: 1;
    
    .card-number {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 4px;
    }
    
    .card-label {
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.card-actions {
  border-top: 1px solid #f3f4f6;
  padding-top: 16px;
  margin-top: 16px;
}

.activity-list {
  .activity-item {
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .activity-time {
      font-size: 12px;
      color: #9ca3af;
      margin-bottom: 4px;
    }
    
    .activity-content {
      font-size: 14px;
      color: #1f2937;
    }
  }
}

.system-status {
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .status-label {
      color: #6b7280;
    }
    
    .status-value {
      font-weight: 600;
      color: #1f2937;
    }
  }
}
</style>
