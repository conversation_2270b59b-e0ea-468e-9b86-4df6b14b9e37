/**
 * 应用入口文件
 */

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

// TDesign UI
import TDesign from 'tdesign-vue-next'
import 'tdesign-vue-next/es/style/index.css'

// TDesign 暗色主题
import 'tdesign-vue-next/dist/tdesign.css'

// TDesign Icons
import * as TDesignIcons from 'tdesign-icons-vue-next'

// 全局样式
import '@/assets/styles/index.less'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 注册插件
app.use(pinia)
app.use(router)
app.use(TDesign)

// 注册TDesign图标
Object.keys(TDesignIcons).forEach(key => {
  if (key !== 'default') {
    app.component(key, TDesignIcons[key as keyof typeof TDesignIcons])
  }
})

// 初始化认证状态
async function initAuth() {
  const { useAuthStore } = await import('@/stores/modules/auth')
  const authStore = useAuthStore()

  // 如果有token，验证其有效性
  if (authStore.token) {
    try {
      console.log('检查本地存储的token有效性...')
      const isValid = await authStore.validateToken()
      if (isValid) {
        console.log('Token验证成功，用户已登录')
      } else {
        console.log('Token验证失败，清除本地数据')
      }
    } catch (error) {
      console.error('初始化认证检查失败:', error)
      authStore.clearAuthData()
    }
  }
}

// 初始化设置
async function initSettings() {
  const { useSettingsStore } = await import('@/stores/modules/settings')
  const settingsStore = useSettingsStore()

  try {
    console.log('初始化系统设置...')
    settingsStore.init()
    console.log('系统设置初始化完成')
  } catch (error) {
    console.error('初始化设置失败:', error)
  }
}

// 初始化字典数据
async function initDict() {
  const { useDictStore } = await import('@/stores/modules/dict')
  const dictStore = useDictStore()

  try {
    console.log('预加载常用字典数据...')
    await dictStore.preloadCommonDicts()
    console.log('字典数据预加载完成')
  } catch (error) {
    console.error('字典数据预加载失败:', error)
  }
}

// 挂载应用
app.mount('#app')

// 应用挂载后初始化
Promise.all([
  initSettings(),
  initAuth(),
  initDict()
]).catch(console.error)

// 开发环境下的调试工具
if (import.meta.env.DEV) {
  // 全局错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err)
    console.error('Component:', vm)
    console.error('Info:', info)
  }
  
  // 全局警告处理
  app.config.warnHandler = (msg, vm, trace) => {
    console.warn('Vue Warning:', msg)
    console.warn('Component:', vm)
    console.warn('Trace:', trace)
  }
}
