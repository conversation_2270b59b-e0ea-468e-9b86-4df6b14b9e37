/**
 * 路由守卫
 */

import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'
import { useAppStore } from '@/stores/modules/app'
import { MessagePlugin } from 'tdesign-vue-next'
import NProgress from 'nprogress'
import { isTokenExpired, isValidTokenFormat, clearAuthData } from '@/utils/auth'

// 白名单路由（不需要认证）
const whiteList = [
  '/login',
  '/login-simple',
  '/login-tdesign',
  '/register',
  '/forgot-password',
  '/403',
  '/404',
  '/500',
  '/test' // 测试页面也加入白名单
]

// 设置路由守卫
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    // 开始进度条
    NProgress.start()
    
    const authStore = useAuthStore()
    const appStore = useAppStore()
    
    // 设置页面标题
    const title = to.meta.title as string
    if (title) {
      document.title = `${title} - ${import.meta.env.VITE_APP_TITLE}`
    } else {
      document.title = import.meta.env.VITE_APP_TITLE
    }
    
    // 检查是否在白名单中
    if (whiteList.includes(to.path)) {
      next()
      return
    }
    
    // 检查是否需要认证
    if (to.meta.requiresAuth !== false) {
      // 检查认证状态
      const authResult = await checkAuthenticationStatus(authStore, to)

      if (!authResult.isAuthenticated) {
        // 开发环境下自动登录为管理员
        if (import.meta.env.DEV && !authResult.hasTriedAuth) {
          console.log('开发环境：自动登录为管理员')
          try {
            await authStore.login({ username: 'admin', password: 'admin123' })
            console.log('自动登录成功，继续路由跳转')
            next()
            return
          } catch (error) {
            console.error('自动登录失败:', error)
          }
        }

        console.log('跳转到登录页面')
        MessagePlugin.warning(authResult.message || '请先登录')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }

      // 检查是否已经有用户信息，如果没有则验证token
      if (!authStore.currentUser && !authStore.isValidating) {
        try {
          const isValid = await authStore.validateToken()
          if (!isValid) {
            console.log('Token验证失败，跳转到登录页面')
            MessagePlugin.error('登录已过期，请重新登录')
            next({
              path: '/login',
              query: { redirect: to.fullPath }
            })
            return
          }
        } catch (error) {
          console.error('Token验证过程中出错:', error)
          MessagePlugin.error('认证验证失败，请重新登录')
          authStore.clearAuthData()
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
          return
        }
      }

      // 检查token是否即将过期并自动刷新
      try {
        await authStore.checkTokenExpiration()
      } catch (error) {
        console.warn('检查token过期时间失败:', error)
        // 这里不阻止路由跳转，只是记录警告
      }

      // 检查权限 - 临时禁用权限验证
      const permissions = to.meta.permissions as string[]
      if (permissions && permissions.length > 0) {
        console.log('🔓 权限验证已临时禁用，允许访问:', to.path, '需要权限:', permissions)
        // 临时跳过权限检查，允许访问所有页面
        // const hasPermission = authStore.hasPermission(permissions)
        // if (!hasPermission) {
        //   MessagePlugin.error('没有权限访问该页面')
        //   next('/403')
        //   return
        // }
      }

      // 检查角色权限 - 临时禁用角色验证
      const roles = to.meta.roles as string[]
      if (roles && roles.length > 0) {
        console.log('🔓 角色验证已临时禁用，允许访问:', to.path, '需要角色:', roles)
        // 临时跳过角色检查，允许访问所有页面
        // const userType = authStore.currentUser?.userType
        // if (!userType || !roles.includes(userType)) {
        //   MessagePlugin.error('没有权限访问该页面')
        //   next('/403')
        //   return
        // }
      }
    }
    
    // 移动端自动关闭侧边栏
    if (appStore.isMobile && appStore.sidebarOpened) {
      appStore.closeSidebar(false)
    }
    
    next()
  })
  
  // 全局后置守卫
  router.afterEach((to, from, failure) => {
    // 结束进度条
    NProgress.done()
    
    // 如果路由跳转失败
    if (failure) {
      console.error('路由跳转失败:', failure)
      MessagePlugin.error('页面跳转失败')
    }
  })
  
  // 全局解析守卫
  router.beforeResolve(async (to, from, next) => {
    // 在这里可以进行一些异步操作
    // 比如加载页面数据、检查权限等
    next()
  })
  
  // 路由错误处理
  router.onError((error) => {
    console.error('路由错误:', error)
    MessagePlugin.error('页面加载失败')
    NProgress.done()
  })
}

// 动态添加路由
export function addRoutes(router: Router, routes: any[]) {
  routes.forEach(route => {
    router.addRoute(route)
  })
}

// 重置路由
export function resetRouter(router: Router) {
  const newRouter = router
  ;(router as any).matcher = (newRouter as any).matcher
}

// 检查路由是否存在
export function hasRoute(router: Router, name: string): boolean {
  return router.hasRoute(name)
}

// 获取当前路由的面包屑
export function getBreadcrumb(route: any): Array<{ title: string; path?: string }> {
  const breadcrumb: Array<{ title: string; path?: string }> = []
  
  // 递归获取父级路由
  function getParentRoutes(route: any) {
    if (route.matched && route.matched.length > 0) {
      route.matched.forEach((match: any) => {
        if (match.meta && match.meta.title) {
          breadcrumb.push({
            title: match.meta.title,
            path: match.path
          })
        }
      })
    }
  }
  
  getParentRoutes(route)
  
  return breadcrumb
}

// 获取扁平化的路由列表
export function getFlatRoutes(routes: any[]): any[] {
  const flatRoutes: any[] = []
  
  function flatten(routes: any[]) {
    routes.forEach(route => {
      if (route.children && route.children.length > 0) {
        flatten(route.children)
      } else {
        flatRoutes.push(route)
      }
    })
  }
  
  flatten(routes)
  return flatRoutes
}

// 根据权限过滤路由 - 临时禁用路由过滤
export function filterRoutesByPermission(routes: any[], permissions: string[]): any[] {
  console.log('🔓 路由权限过滤已临时禁用，返回所有路由')
  // 临时返回所有路由，不进行权限过滤
  return routes

  // 原始权限过滤逻辑（已注释）
  // return routes.filter(route => {
  //   if (route.meta && route.meta.permissions) {
  //     const routePermissions = route.meta.permissions as string[]
  //     const hasPermission = routePermissions.some(permission =>
  //       permissions.includes(permission)
  //     )
  //     if (!hasPermission) {
  //       return false
  //     }
  //   }
  //
  //   if (route.children && route.children.length > 0) {
  //     route.children = filterRoutesByPermission(route.children, permissions)
  //   }
  //
  //   return true
  // })
}

/**
 * 检查认证状态
 */
async function checkAuthenticationStatus(authStore: any, to: any): Promise<{
  isAuthenticated: boolean
  message?: string
  hasTriedAuth: boolean
}> {
  // 检查localStorage中的认证信息
  const storedToken = localStorage.getItem('cloudvps_admin_token')
  const storedRefreshToken = localStorage.getItem('cloudvps_admin_refresh_token')
  const storedUser = localStorage.getItem('currentUser')
  const storedPermissions = localStorage.getItem('permissions')

  // 处理字段存在但值为null、''或undefined的情况
  const hasValidToken = storedToken && storedToken !== 'null' && storedToken !== 'undefined' && storedToken.trim() !== ''
  const hasValidRefreshToken = storedRefreshToken && storedRefreshToken !== 'null' && storedRefreshToken !== 'undefined' && storedRefreshToken.trim() !== ''
  const hasValidUser = storedUser && storedUser !== 'null' && storedUser !== 'undefined' && storedUser.trim() !== ''

  // 如果没有任何认证信息
  if (!hasValidToken && !hasValidRefreshToken) {
    clearAuthData()
    return {
      isAuthenticated: false,
      message: '请先登录',
      hasTriedAuth: false
    }
  }

  // 如果有token但格式无效
  if (hasValidToken && !isValidTokenFormat(storedToken)) {
    clearAuthData()
    return {
      isAuthenticated: false,
      message: '认证信息格式错误，请重新登录',
      hasTriedAuth: false
    }
  }

  // 如果token存在且有效
  if (hasValidToken && !isTokenExpired(storedToken)) {
    // 检查是否已经有用户信息
    if (!authStore.currentUser && hasValidUser) {
      try {
        const user = JSON.parse(storedUser)
        authStore.setCurrentUser(user)
      } catch (error) {
        console.error('解析用户信息失败:', error)
      }
    }

    // 如果没有用户信息，验证token
    if (!authStore.currentUser) {
      try {
        const isValid = await authStore.validateToken()
        if (!isValid) {
          return {
            isAuthenticated: false,
            message: 'Token验证失败，请重新登录',
            hasTriedAuth: true
          }
        }
      } catch (error) {
        console.error('Token验证失败:', error)
        return {
          isAuthenticated: false,
          message: '认证验证失败，请重新登录',
          hasTriedAuth: true
        }
      }
    }

    return {
      isAuthenticated: true,
      hasTriedAuth: false
    }
  }

  // 如果token无效但有refresh token，尝试刷新
  if (hasValidRefreshToken) {
    try {
      await authStore.refreshTokenAction()
      return {
        isAuthenticated: true,
        hasTriedAuth: true
      }
    } catch (error) {
      console.error('Token刷新失败:', error)
      clearAuthData()
      return {
        isAuthenticated: false,
        message: '登录已过期，请重新登录',
        hasTriedAuth: true
      }
    }
  }

  // 清除无效的认证信息
  clearAuthData()
  return {
    isAuthenticated: false,
    message: '认证信息已过期，请重新登录',
    hasTriedAuth: true
  }
}
