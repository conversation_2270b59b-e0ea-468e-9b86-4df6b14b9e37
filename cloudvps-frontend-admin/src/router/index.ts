/**
 * 路由配置
 */

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'
import { generateAutoRoutes, buildNestedRoutes, printRoutes } from './utils/autoRoutes'

// 导入手动配置的路由模块（认证和错误页面）
import { authRoutes } from './routes/auth'

// 基础路由
const baseRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: {
      title: '仪表板',
      icon: 'dashboard',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'DashboardIndex',
        component: () => import('@/pages/dashboard/Index.vue'),
        meta: {
          title: '首页'
        }
      }
    ]
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: {
      title: 'API测试',
      icon: 'tools',
      requiresAuth: false,
      hidden: false
    },
    children: [
      {
        path: '',
        name: 'ApiTest',
        component: () => import('@/pages/test/ApiTest.vue'),
        meta: {
          title: 'API测试'
        }
      },
      {
        path: 'config',
        name: 'ApiConfigTest',
        component: () => import('@/pages/test/ApiConfigTest.vue'),
        meta: {
          title: 'API配置测试'
        }
      },
      {
        path: 'login-comparison',
        name: 'LoginComparison',
        component: () => import('@/pages/test/LoginComparison.vue'),
        meta: {
          title: '登录页面对比'
        }
      },
      {
        path: 'icon',
        name: 'IconTest',
        component: () => import('@/pages/test/IconTest.vue'),
        meta: {
          title: 'Icon组件测试'
        }
      },
      {
        path: 'auth-guard',
        name: 'AuthGuardTest',
        component: () => import('@/pages/test/AuthGuardTest.vue'),
        meta: {
          title: '路由守卫测试'
        }
      },
      {
        path: 'settings',
        name: 'SettingsTest',
        component: () => import('@/pages/test/SettingsTest.vue'),
        meta: {
          title: '系统设置测试'
        }
      },
      {
        path: 'api-refactor',
        name: 'ApiRefactorTest',
        component: () => import('@/pages/test/ApiRefactorTest.vue'),
        meta: {
          title: 'API重构验证'
        }
      },
      {
        path: 'settings-panel',
        name: 'SettingsPanelTest',
        component: () => import('@/pages/test/SettingsPanelTest.vue'),
        meta: {
          title: '设置面板测试'
        }
      },
      {
        path: 'theme',
        name: 'ThemeTest',
        component: () => import('@/pages/test/ThemeTest.vue'),
        meta: {
          title: '主题系统测试'
        }
      }
    ]
  }
]

// 错误页面路由
const errorRoutes: RouteRecordRaw[] = [
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/pages/error/403.vue'),
    meta: {
      title: '访问被拒绝',
      hidden: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/pages/error/404.vue'),
    meta: {
      title: '页面不存在',
      hidden: true
    }
  },
  {
    path: '/500',
    name: 'ServerError',
    component: () => import('@/pages/error/500.vue'),
    meta: {
      title: '服务器错误',
      hidden: true
    }
  }
]

// 通配符路由（必须放在最后）
const wildcardRoutes: RouteRecordRaw[] = [
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 生成自动路由
const autoRoutes = generateAutoRoutes()
const nestedAutoRoutes = buildNestedRoutes(autoRoutes)

// 在开发环境下打印路由信息
if (import.meta.env.DEV) {
  console.log('🚀 自动生成的路由:')
  printRoutes(nestedAutoRoutes)
}

// 手动定义的路由（认证、错误页面等）
const manualRoutes: RouteRecordRaw[] = [
  ...baseRoutes,
  ...authRoutes,
  ...errorRoutes
]

// 业务路由：将自动生成的路由包装在默认布局中
const businessRoutes: RouteRecordRaw[] = nestedAutoRoutes.map(route => {
  // 如果是根路径或已经有布局，直接返回
  if (route.path === '/' || route.component?.toString().includes('Layout')) {
    return route
  }

  // 为业务路由添加默认布局
  return {
    path: route.path,
    name: route.name,
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: route.meta,
    children: [
      {
        path: '',
        name: `${route.name}Index`,
        component: route.component,
        meta: route.meta
      }
    ]
  }
})

// 最终路由配置
const routes: RouteRecordRaw[] = [
  ...manualRoutes,
  ...businessRoutes,
  ...wildcardRoutes // 通配符路由必须放在最后
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 设置路由守卫
setupRouterGuards(router)

export default router

// 导出路由类型
export type { RouteRecordRaw }
