/**
 * 基于文件系统的自动路由生成工具
 */

import type { RouteRecordRaw } from 'vue-router'

/**
 * 路由元信息接口
 */
interface RouteMeta {
  title: string
  icon?: string
  requiresAuth?: boolean
  permissions?: string[]
  hidden?: boolean
}

/**
 * 自动生成的路由配置
 */
interface AutoRoute extends RouteRecordRaw {
  meta?: RouteMeta
}

/**
 * 获取所有页面文件
 * 使用 Vite 的 import.meta.glob 功能扫描 pages 目录
 */
const getPageModules = () => {
  // 扫描 src/pages 目录下的所有 index.vue 文件
  // 排除 auth 和 error 目录（这些保持手动配置）
  const modules = import.meta.glob('/src/pages/**/index.vue', {
    eager: false,
    import: 'default'
  })

  return modules
}

/**
 * 将文件路径转换为路由路径
 * @param filePath 文件路径，如：/src/pages/system/users/index.vue
 * @returns 路由路径，如：/system/users
 */
const filePathToRoutePath = (filePath: string): string => {
  // 移除 /src/pages 前缀和 /index.vue 后缀
  let routePath = filePath
    .replace('/src/pages', '')
    .replace('/index.vue', '')

  // 如果是根目录的 index.vue，返回 '/'
  if (routePath === '') {
    return '/'
  }

  // 确保路径以 / 开头
  if (!routePath.startsWith('/')) {
    routePath = '/' + routePath
  }

  return routePath
}

/**
 * 将路径段转换为标题
 * @param segment 路径段，如：users、dict
 * @returns 标题，如：Users、Dict
 */
const segmentToTitle = (segment: string): string => {
  // 特殊映射
  const titleMap: Record<string, string> = {
    'dashboard': 'Dashboard',
    'users': '用户管理',
    'dict': '字典管理',
    'roles': '角色管理',
    'menus': '菜单管理',
    'system': '系统管理',
    'virtualization': '虚拟化管理',
    'order': '订单管理',
    'payment': '支付管理'
  }

  return titleMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)
}

/**
 * 生成路由名称
 * @param routePath 路由路径，如：/system/users
 * @returns 路由名称，如：SystemUsers
 */
const generateRouteName = (routePath: string): string => {
  return routePath
    .split('/')
    .filter(Boolean)
    .map(segment => segment.charAt(0).toUpperCase() + segment.slice(1))
    .join('')
}

/**
 * 根据路由路径推断权限
 * @param routePath 路由路径
 * @returns 权限数组
 */
const inferPermissions = (routePath: string): string[] => {
  const segments = routePath.split('/').filter(Boolean)
  
  if (segments.length === 0) {
    return [] // 首页不需要特殊权限
  }

  // 根据路径推断权限
  const permissionMap: Record<string, string[]> = {
    '/dashboard': [],
    '/system/users': ['USER_VIEW'],
    '/system/dict': ['DICT_VIEW'],
    '/system/roles': ['ROLE_VIEW'],
    '/system/menus': ['SYSTEM_MENU_VIEW'],
    '/virtualization': ['VIRTUALIZATION_VIEW'],
    '/order': ['ORDER_VIEW'],
    '/payment': ['PAYMENT_VIEW']
  }

  return permissionMap[routePath] || []
}

/**
 * 生成自动路由配置
 */
export const generateAutoRoutes = (): AutoRoute[] => {
  const modules = getPageModules()
  const routes: AutoRoute[] = []

  // 排除的路径（保持手动配置）
  const excludePaths = ['/auth', '/error', '/login']

  Object.keys(modules).forEach(filePath => {
    const routePath = filePathToRoutePath(filePath)
    
    // 跳过排除的路径
    if (excludePaths.some(excludePath => routePath.startsWith(excludePath))) {
      return
    }

    const segments = routePath.split('/').filter(Boolean)
    const routeName = generateRouteName(routePath)
    const permissions = inferPermissions(routePath)
    
    // 生成标题
    let title = 'Page'
    if (segments.length > 0) {
      title = segmentToTitle(segments[segments.length - 1])
    }

    // 生成图标
    let icon = 'dashboard'
    if (segments.length > 0) {
      const iconMap: Record<string, string> = {
        'dashboard': 'dashboard',
        'users': 'user',
        'dict': 'books',
        'roles': 'user-setting',
        'menus': 'menu-fold',
        'system': 'setting',
        'virtualization': 'server',
        'order': 'order',
        'payment': 'money'
      }
      icon = iconMap[segments[segments.length - 1]] || 'file'
    }

    const route: AutoRoute = {
      path: routePath,
      name: routeName,
      component: modules[filePath],
      meta: {
        title,
        icon,
        requiresAuth: true,
        permissions: permissions.length > 0 ? permissions : undefined
      }
    }

    routes.push(route)
  })

  // 按路径排序
  routes.sort((a, b) => (a.path || '').localeCompare(b.path || ''))

  return routes
}

/**
 * 获取嵌套路由结构
 * 将平铺的路由转换为嵌套结构
 */
export const buildNestedRoutes = (routes: AutoRoute[]): AutoRoute[] => {
  const routeMap = new Map<string, AutoRoute>()
  const rootRoutes: AutoRoute[] = []

  // 先创建所有路由的映射
  routes.forEach(route => {
    routeMap.set(route.path!, route)
  })

  routes.forEach(route => {
    const path = route.path!
    const segments = path.split('/').filter(Boolean)

    if (segments.length === 1) {
      // 一级路由
      rootRoutes.push(route)
    } else {
      // 多级路由，找到父路由
      const parentPath = '/' + segments.slice(0, -1).join('/')
      const parentRoute = routeMap.get(parentPath)

      if (parentRoute) {
        if (!parentRoute.children) {
          parentRoute.children = []
        }
        
        // 调整子路由的路径为相对路径
        const childRoute = { ...route }
        childRoute.path = segments[segments.length - 1]
        
        parentRoute.children.push(childRoute)
      } else {
        // 如果没有找到父路由，作为根路由
        rootRoutes.push(route)
      }
    }
  })

  return rootRoutes
}

/**
 * 打印路由信息（用于调试）
 */
export const printRoutes = (routes: AutoRoute[], prefix = '') => {
  routes.forEach(route => {
    console.log(`${prefix}📄 ${route.path} -> ${route.name} (${route.meta?.title})`)
    if (route.children && route.children.length > 0) {
      printRoutes(route.children as AutoRoute[], prefix + '  ')
    }
  })
}
