/**
 * 分页相关组合式函数
 */

import { ref, computed, reactive } from 'vue'
import type { PaginationConfig } from '@/types/business'
import type { BasePageRequest } from '@/types/api'

export interface UsePaginationOptions {
  defaultPageSize?: number
  pageSizeOptions?: number[]
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
}

export function usePagination(options: UsePaginationOptions = {}) {
  const {
    defaultPageSize = 10,
    pageSizeOptions = [10, 20, 50, 100],
    showSizeChanger = true,
    showQuickJumper = true,
    showTotal = true
  } = options

  // 分页状态
  const pagination = reactive<PaginationConfig>({
    current: 1,
    pageSize: defaultPageSize,
    total: 0,
    showSizeChanger,
    showQuickJumper,
    showTotal,
    pageSizeOptions
  })

  /**
   * 获取分页参数（用于API请求）
   */
  const getPageParams = computed((): BasePageRequest => ({
    current: pagination.current,
    pageSize: pagination.pageSize
  }))

  /**
   * 更新分页信息
   */
  const updatePagination = (data: { total?: number; current?: number; pageSize?: number }) => {
    if (data.total !== undefined) {
      pagination.total = data.total
    }
    if (data.current !== undefined) {
      pagination.current = data.current
    }
    if (data.pageSize !== undefined) {
      pagination.pageSize = data.pageSize
    }
  }

  /**
   * 重置分页到第一页
   */
  const resetPagination = () => {
    pagination.current = 1
  }

  /**
   * 页码改变处理
   */
  const handlePageChange = (page: number) => {
    pagination.current = page
  }

  /**
   * 页面大小改变处理
   */
  const handlePageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1 // 重置到第一页
  }

  /**
   * 分页配置（用于TDesign组件）
   */
  const paginationConfig = computed(() => ({
    current: pagination.current,
    pageSize: pagination.pageSize,
    total: pagination.total,
    showSizeChanger: pagination.showSizeChanger,
    showQuickJumper: pagination.showQuickJumper,
    showTotal: pagination.showTotal,
    pageSizeOptions: pagination.pageSizeOptions,
    onChange: handlePageChange,
    onShowSizeChange: handlePageSizeChange
  }))

  /**
   * 计算总页数
   */
  const totalPages = computed(() => {
    return Math.ceil(pagination.total / pagination.pageSize)
  })

  /**
   * 计算当前页的数据范围
   */
  const dataRange = computed(() => {
    const start = (pagination.current - 1) * pagination.pageSize + 1
    const end = Math.min(pagination.current * pagination.pageSize, pagination.total)
    return { start, end }
  })

  /**
   * 是否有上一页
   */
  const hasPrevPage = computed(() => {
    return pagination.current > 1
  })

  /**
   * 是否有下一页
   */
  const hasNextPage = computed(() => {
    return pagination.current < totalPages.value
  })

  /**
   * 跳转到指定页
   */
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      pagination.current = page
    }
  }

  /**
   * 上一页
   */
  const prevPage = () => {
    if (hasPrevPage.value) {
      pagination.current--
    }
  }

  /**
   * 下一页
   */
  const nextPage = () => {
    if (hasNextPage.value) {
      pagination.current++
    }
  }

  /**
   * 第一页
   */
  const firstPage = () => {
    pagination.current = 1
  }

  /**
   * 最后一页
   */
  const lastPage = () => {
    pagination.current = totalPages.value
  }

  return {
    // 状态
    pagination,
    
    // 计算属性
    getPageParams,
    paginationConfig,
    totalPages,
    dataRange,
    hasPrevPage,
    hasNextPage,
    
    // 方法
    updatePagination,
    resetPagination,
    handlePageChange,
    handlePageSizeChange,
    goToPage,
    prevPage,
    nextPage,
    firstPage,
    lastPage
  }
}
