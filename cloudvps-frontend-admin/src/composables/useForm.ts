/**
 * 表单相关组合式函数
 */

import { ref, reactive, computed, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import type { FormConfig, FormField, LoadingState } from '@/types/business'

export interface UseFormOptions<T = any> {
  api?: {
    create?: Function
    update?: Function
    getById?: Function
  }
  fields?: FormField[]
  defaultValues?: Partial<T>
  resetAfterSubmit?: boolean
}

export function useForm<T = any>(options: UseFormOptions<T> = {}) {
  const {
    api,
    fields = [],
    defaultValues = {},
    resetAfterSubmit = true
  } = options

  // 表单状态
  const formVisible = ref(false)
  const formMode = ref<'create' | 'update'>('create')
  const formData = reactive<Partial<T>>({ ...defaultValues })
  const formRef = ref()

  // 加载状态
  const loading = reactive<LoadingState>({
    create: false,
    update: false
  })

  // 验证错误
  const errors = ref<Record<string, string>>({})

  /**
   * 表单配置
   */
  const formConfig = computed<FormConfig>(() => ({
    fields,
    layout: 'vertical',
    labelWidth: '120px',
    colon: true,
    requiredMark: true
  }))

  /**
   * 是否为创建模式
   */
  const isCreateMode = computed(() => formMode.value === 'create')

  /**
   * 是否为更新模式
   */
  const isUpdateMode = computed(() => formMode.value === 'update')

  /**
   * 表单标题
   */
  const formTitle = computed(() => {
    return isCreateMode.value ? '新增' : '编辑'
  })

  /**
   * 提交按钮文本
   */
  const submitButtonText = computed(() => {
    return isCreateMode.value ? '创建' : '更新'
  })

  /**
   * 是否正在提交
   */
  const isSubmitting = computed(() => {
    return loading.create || loading.update
  })

  /**
   * 打开表单（创建模式）
   */
  const openCreateForm = (initialData: Partial<T> = {}) => {
    formMode.value = 'create'
    resetForm()
    Object.assign(formData, defaultValues, initialData)
    formVisible.value = true
    
    nextTick(() => {
      clearValidation()
    })
  }

  /**
   * 打开表单（编辑模式）
   */
  const openUpdateForm = async (record: T) => {
    formMode.value = 'update'
    
    // 如果有详情API，先获取详情数据
    if (api?.getById && (record as any).id) {
      try {
        const response = await api.getById((record as any).id)
        Object.assign(formData, response.data)
      } catch (error) {
        console.error('获取详情失败:', error)
        Object.assign(formData, record)
      }
    } else {
      Object.assign(formData, record)
    }
    
    formVisible.value = true
    
    nextTick(() => {
      clearValidation()
    })
  }

  /**
   * 关闭表单
   */
  const closeForm = () => {
    formVisible.value = false
    nextTick(() => {
      resetForm()
    })
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.keys(formData).forEach(key => {
      delete (formData as any)[key]
    })
    Object.assign(formData, defaultValues)
    clearValidation()
  }

  /**
   * 清除验证
   */
  const clearValidation = () => {
    errors.value = {}
    if (formRef.value) {
      formRef.value.clearValidate?.()
    }
  }

  /**
   * 验证表单
   */
  const validateForm = async (): Promise<boolean> => {
    if (!formRef.value) return true
    
    try {
      const result = await formRef.value.validate()
      return result === true
    } catch (error) {
      return false
    }
  }

  /**
   * 提交表单
   */
  const submitForm = async (): Promise<boolean> => {
    // 验证表单
    const isValid = await validateForm()
    if (!isValid) {
      return false
    }

    try {
      if (isCreateMode.value) {
        if (!api?.create) {
          console.warn('未配置创建API')
          return false
        }
        
        loading.create = true
        await api.create(formData)
        MessagePlugin.success('创建成功')
      } else {
        if (!api?.update) {
          console.warn('未配置更新API')
          return false
        }
        
        loading.update = true
        await api.update((formData as any).id, formData)
        MessagePlugin.success('更新成功')
      }

      if (resetAfterSubmit) {
        closeForm()
      }
      
      return true
    } catch (error) {
      console.error('提交失败:', error)
      MessagePlugin.error(isCreateMode.value ? '创建失败' : '更新失败')
      return false
    } finally {
      loading.create = false
      loading.update = false
    }
  }

  /**
   * 设置字段值
   */
  const setFieldValue = (field: string, value: any) => {
    (formData as any)[field] = value
  }

  /**
   * 获取字段值
   */
  const getFieldValue = (field: string) => {
    return (formData as any)[field]
  }

  /**
   * 设置字段错误
   */
  const setFieldError = (field: string, message: string) => {
    errors.value[field] = message
  }

  /**
   * 清除字段错误
   */
  const clearFieldError = (field: string) => {
    delete errors.value[field]
  }

  /**
   * 批量设置表单数据
   */
  const setFormData = (data: Partial<T>) => {
    Object.assign(formData, data)
  }

  /**
   * 获取表单数据副本
   */
  const getFormData = (): Partial<T> => {
    return { ...formData }
  }

  return {
    // 状态
    formVisible,
    formMode,
    formData,
    formRef,
    loading,
    errors,
    
    // 计算属性
    formConfig,
    isCreateMode,
    isUpdateMode,
    formTitle,
    submitButtonText,
    isSubmitting,
    
    // 方法
    openCreateForm,
    openUpdateForm,
    closeForm,
    resetForm,
    clearValidation,
    validateForm,
    submitForm,
    setFieldValue,
    getFieldValue,
    setFieldError,
    clearFieldError,
    setFormData,
    getFormData
  }
}
