/**
 * 表格相关组合式函数
 */

import { ref, computed, reactive, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import type { TableColumn, TableConfig, LoadingState } from '@/types/business'
import type { ApiResponse, PageResponse } from '@/types/api'
import { usePagination } from './usePagination'

export interface UseTableOptions<T = any> {
  rowKey?: string
  api?: {
    list: Function
    delete?: Function
    batchDelete?: Function
  }
  columns?: TableColumn[]
  defaultPageSize?: number
  immediate?: boolean
  isPaginated?: boolean // 新增：是否为分页接口，默认true
}

export function useTable<T = any>(options: UseTableOptions<T> = {}) {
  const {
    rowKey = 'id',
    api,
    columns = [],
    defaultPageSize = 10,
    immediate = true,
    isPaginated = true // 默认为分页接口
  } = options

  // 表格数据
  const tableData = ref<T[]>([])
  const loading = reactive<LoadingState>({
    list: false,
    delete: false
  })

  // 选中行
  const selectedRowKeys = ref<(string | number)[]>([])
  const selectedRows = ref<T[]>([])

  // 分页
  const {
    pagination,
    getPageParams,
    paginationConfig,
    updatePagination,
    resetPagination
  } = usePagination({ defaultPageSize })

  // 查询参数
  const queryParams = ref<Record<string, any>>({})

  /**
   * 表格配置
   */
  const tableConfig = computed<TableConfig>(() => ({
    columns,
    rowKey,
    bordered: true,
    striped: true,
    hover: true,
    size: 'medium',
    loading: loading.list,
    pagination: true,
    selection: true
  }))

  /**
   * 是否有选中行
   */
  const hasSelection = computed(() => {
    return selectedRowKeys.value.length > 0
  })

  /**
   * 选中行数量
   */
  const selectionCount = computed(() => {
    return selectedRowKeys.value.length
  })

  /**
   * 获取表格数据
   */
  const fetchTableData = async (params: Record<string, any> = {}) => {
    if (!api?.list) {
      console.warn('未配置列表API')
      return
    }

    try {
      loading.list = true
      
      const requestParams = {
        ...getPageParams.value,
        ...queryParams.value,
        ...params
      }

      const response = await api.list(requestParams)
      if (response.data) {
        if (isPaginated) {
          // 分页接口：处理 PageResponse<T> 格式
          const pageData = response.data as PageResponse<T>
          if (pageData.records && Array.isArray(pageData.records)) {
            tableData.value = pageData.records

            // 更新分页信息
            updatePagination({
              total: pageData.total || 0,
              current: pageData.current || pagination.current,
              pageSize: pageData.size || pagination.pageSize
            })

            console.log('📊 分页数据加载完成:', {
              dataLength: tableData.value.length,
              total: pageData.total,
              current: pageData.current,
              size: pageData.size,
              pages: pageData.pages
            })
          } else {
            // 兼容旧格式：response.data.list 或 response.data.records 或 response.data
            const responseData = (response.data as any).records || (response.data as any).list || response.data
            tableData.value = Array.isArray(responseData) ? responseData : []

            console.log('📊 兼容格式数据加载完成:', {
              dataLength: tableData.value.length,
              format: 'legacy'
            })
          }
        } else {
          // 非分页接口：处理 T[] 格式
          const listData = response.data as T[]

          if (Array.isArray(listData)) {
            tableData.value = listData
          } else {
            // 兼容包装格式：{data: T[]}
            const wrappedData = (listData as any).data || listData
            tableData.value = Array.isArray(wrappedData) ? wrappedData : []
          }

          console.log('📊 列表数据加载完成:', {
            dataLength: tableData.value.length,
            isPaginated: false
          })
        }
      } else {
        // 如果没有数据，确保设置为空数组
        tableData.value = []
        console.log('📊 无数据返回，设置为空数组')
      }
    } catch (error) {
      console.error('获取表格数据失败:', error)
      MessagePlugin.error('获取数据失败')
      // 确保在错误情况下也设置为空数组
      tableData.value = []
    } finally {
      loading.list = false
    }
  }

  /**
   * 刷新表格数据
   */
  const refreshTable = () => {
    fetchTableData()
  }

  /**
   * 重新加载表格数据（重置到第一页）
   */
  const reloadTable = () => {
    resetPagination()
    clearSelection()
    fetchTableData()
  }

  /**
   * 搜索
   */
  const handleSearch = (params: Record<string, any>) => {
    queryParams.value = { ...params }
    resetPagination()
    clearSelection()
    fetchTableData()
  }

  /**
   * 重置搜索
   */
  const handleReset = () => {
    queryParams.value = {}
    resetPagination()
    clearSelection()
    fetchTableData()
  }

  /**
   * 选择行变化
   */
  const handleSelectionChange = (keys: (string | number)[], rows: T[]) => {
    selectedRowKeys.value = keys
    selectedRows.value = rows
  }

  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedRowKeys.value = []
    selectedRows.value = []
  }

  /**
   * 全选/取消全选
   */
  const toggleSelectAll = () => {
    if (selectedRowKeys.value.length === tableData.value.length) {
      clearSelection()
    } else {
      const keys = tableData.value.map(item => (item as any)[rowKey])
      selectedRowKeys.value = keys
      selectedRows.value = [...tableData.value]
    }
  }

  /**
   * 删除单行
   */
  const handleDelete = async (record: T) => {
    if (!api?.delete) {
      console.warn('未配置删除API')
      return
    }

    try {
      loading.delete = true
      await api.delete((record as any)[rowKey])
      MessagePlugin.success('删除成功')
      
      // 如果当前页只有一条数据且不是第一页，则回到上一页
      if (tableData.value.length === 1 && pagination.current > 1) {
        pagination.current--
      }
      
      await fetchTableData()
    } catch (error) {
      console.error('删除失败:', error)
      MessagePlugin.error('删除失败')
    } finally {
      loading.delete = false
    }
  }

  /**
   * 批量删除
   */
  const handleBatchDelete = async () => {
    if (!api?.batchDelete) {
      console.warn('未配置批量删除API')
      return
    }

    if (selectedRowKeys.value.length === 0) {
      MessagePlugin.warning('请选择要删除的数据')
      return
    }

    try {
      loading.delete = true
      await api.batchDelete({ ids: selectedRowKeys.value })
      MessagePlugin.success('批量删除成功')
      
      clearSelection()
      
      // 如果当前页数据全部删除且不是第一页，则回到上一页
      if (selectedRowKeys.value.length === tableData.value.length && pagination.current > 1) {
        pagination.current--
      }
      
      await fetchTableData()
    } catch (error) {
      console.error('批量删除失败:', error)
      MessagePlugin.error('批量删除失败')
    } finally {
      loading.delete = false
    }
  }

  /**
   * 分页变化处理
   */
  const handlePageChange = (pageInfo: any) => {
    updatePagination(pageInfo)
    fetchTableData()
  }

  // 初始化加载数据
  if (immediate && api?.list) {
    nextTick(() => {
      fetchTableData()
    })
  }

  return {
    // 状态
    tableData,
    loading,
    selectedRowKeys,
    selectedRows,
    queryParams,
    
    // 分页
    pagination,
    paginationConfig,
    
    // 计算属性
    tableConfig,
    hasSelection,
    selectionCount,
    
    // 方法
    fetchTableData,
    refreshTable,
    reloadTable,
    handleSearch,
    handleReset,
    handleSelectionChange,
    clearSelection,
    toggleSelectAll,
    handleDelete,
    handleBatchDelete,
    handlePageChange
  }
}
