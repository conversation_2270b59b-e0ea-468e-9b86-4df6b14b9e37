/**
 * CRUD操作组合式函数
 */

import { computed } from 'vue'
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next'
import type { CrudPageConfig, CrudState } from '@/types/business'
import { useTable } from './useTable'
import { useForm } from './useForm'

export interface UseCrudOptions<T = any> extends CrudPageConfig {
  rowKey?: string
  defaultPageSize?: number
  immediate?: boolean
}

export function useCrud<T = any>(options: UseCrudOptions<T>) {
  const {
    title,
    api,
    table,
    searchForm,
    createForm,
    updateForm,
    actions,
    permissions,
    rowKey = 'id',
    defaultPageSize = 10,
    immediate = true
  } = options

  // 表格相关
  const tableHook = useTable<T>({
    rowKey,
    api: {
      list: api.list,
      delete: api.delete,
      batchDelete: api.batchDelete
    },
    columns: table.columns,
    defaultPageSize,
    immediate
  })

  // 表单相关
  const formHook = useForm<T>({
    api: {
      create: api.create,
      update: api.update
    },
    fields: createForm?.fields || updateForm?.fields || [],
    resetAfterSubmit: true
  })

  /**
   * CRUD状态
   */
  const crudState = computed<CrudState<T>>(() => ({
    // 数据
    list: tableHook.tableData.value,
    current: null,
    
    // 分页
    pagination: tableHook.pagination,
    
    // 加载状态
    loading: {
      ...tableHook.loading,
      ...formHook.loading
    },
    
    // 错误状态
    error: {},
    
    // 选中项
    selectedRowKeys: tableHook.selectedRowKeys.value,
    selectedRows: tableHook.selectedRows.value,
    
    // 表单状态
    formVisible: formHook.formVisible.value,
    formMode: formHook.formMode.value,
    formData: formHook.formData,
    
    // 查询参数
    queryParams: tableHook.queryParams.value
  }))

  /**
   * 创建记录
   */
  const handleCreate = () => {
    formHook.openCreateForm()
  }

  /**
   * 编辑记录
   */
  const handleUpdate = (record: T) => {
    formHook.openUpdateForm(record)
  }

  /**
   * 删除记录
   */
  const handleDelete = (record: T) => {
    const confirmDialog = DialogPlugin.confirm({
      header: '确认删除',
      body: '确定要删除这条记录吗？删除后无法恢复。',
      confirmBtn: '确定删除',
      cancelBtn: '取消',
      theme: 'warning',
      onConfirm: async () => {
        await tableHook.handleDelete(record)
        confirmDialog.destroy()
      },
      onCancel: () => {
        confirmDialog.destroy()
      }
    })
  }

  /**
   * 批量删除
   */
  const handleBatchDelete = () => {
    if (tableHook.selectedRowKeys.value.length === 0) {
      MessagePlugin.warning('请选择要删除的记录')
      return
    }

    const confirmDialog = DialogPlugin.confirm({
      header: '确认批量删除',
      body: `确定要删除选中的 ${tableHook.selectedRowKeys.value.length} 条记录吗？删除后无法恢复。`,
      confirmBtn: '确定删除',
      cancelBtn: '取消',
      theme: 'warning',
      onConfirm: async () => {
        await tableHook.handleBatchDelete()
        confirmDialog.destroy()
      },
      onCancel: () => {
        confirmDialog.destroy()
      }
    })
  }

  /**
   * 提交表单
   */
  const handleSubmit = async () => {
    const success = await formHook.submitForm()
    if (success) {
      // 刷新表格数据
      await tableHook.refreshTable()
    }
  }

  /**
   * 搜索
   */
  const handleSearch = (params: Record<string, any>) => {
    tableHook.handleSearch(params)
  }

  /**
   * 重置搜索
   */
  const handleReset = () => {
    tableHook.handleReset()
  }

  /**
   * 刷新数据
   */
  const handleRefresh = () => {
    tableHook.refreshTable()
  }

  /**
   * 导出数据
   */
  const handleExport = async (config?: any) => {
    try {
      MessagePlugin.loading('正在导出数据...')
      // 这里可以根据需要实现导出逻辑
      // 例如调用导出API或者前端生成文件
      MessagePlugin.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      MessagePlugin.error('导出失败')
    }
  }

  /**
   * 检查权限 - 临时禁用权限验证
   */
  const hasPermission = (action: string): boolean => {
    console.log('🔓 CRUD权限检查已临时禁用，操作:', action, '-> 返回 true')
    // 临时禁用权限验证，始终返回true
    return true

    // 原始权限检查逻辑（已注释）
    // if (!permissions) return true
    //
    // const permissionMap: Record<string, string | undefined> = {
    //   view: permissions.view,
    //   create: permissions.create,
    //   update: permissions.update,
    //   delete: permissions.delete
    // }
    //
    // const requiredPermission = permissionMap[action]
    // if (!requiredPermission) return true
    //
    // // 这里应该集成实际的权限检查逻辑
    // // 例如从用户store中获取权限列表进行检查
    // return true
  }

  /**
   * 获取操作按钮配置
   */
  const getActionButtons = computed(() => {
    const defaultToolbarActions = [
      {
        key: 'create',
        label: '新增',
        type: 'primary' as const,
        icon: 'add',
        permission: permissions?.create,
        onClick: handleCreate
      },
      {
        key: 'batchDelete',
        label: '批量删除',
        type: 'danger' as const,
        icon: 'delete',
        permission: permissions?.delete,
        disabled: !tableHook.hasSelection.value,
        onClick: handleBatchDelete
      },
      {
        key: 'refresh',
        label: '刷新',
        type: 'info' as const,
        icon: 'refresh',
        onClick: handleRefresh
      }
    ]

    const defaultRowActions = [
      {
        key: 'update',
        label: '编辑',
        type: 'primary' as const,
        icon: 'edit',
        permission: permissions?.update,
        onClick: handleUpdate
      },
      {
        key: 'delete',
        label: '删除',
        type: 'danger' as const,
        icon: 'delete',
        permission: permissions?.delete,
        onClick: handleDelete
      }
    ]

    return {
      toolbar: actions?.toolbar || defaultToolbarActions,
      row: actions?.row || defaultRowActions
    }
  })

  return {
    // 状态
    crudState,
    
    // 表格相关
    ...tableHook,
    
    // 表单相关
    ...formHook,
    
    // 操作方法
    handleCreate,
    handleUpdate,
    handleDelete,
    handleBatchDelete,
    handleSubmit,
    handleSearch,
    handleReset,
    handleRefresh,
    handleExport,
    
    // 权限检查
    hasPermission,
    
    // 配置
    getActionButtons,
    
    // 页面配置
    pageConfig: {
      title,
      table,
      searchForm,
      createForm,
      updateForm,
      actions,
      permissions
    }
  }
}
