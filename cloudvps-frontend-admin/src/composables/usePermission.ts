/**
 * 权限管理 Composable
 */

import { computed } from 'vue'
import { useUserStore } from '@/stores/modules/user'

export function usePermission() {
  const userStore = useUserStore()

  /**
   * 检查是否有指定权限
   */
  const hasPermission = (permission: string | string[]): boolean => {
    // 如果用户是超级管理员，拥有所有权限
    if (userStore.isSuperAdmin) {
      return true
    }

    // 获取用户权限列表
    const userPermissions = userStore.permissions || []

    // 如果传入的是字符串，转换为数组
    const permissionsToCheck = Array.isArray(permission) ? permission : [permission]

    // 检查是否拥有所有指定权限
    return permissionsToCheck.every(perm => userPermissions.includes(perm))
  }

  /**
   * 检查是否有任意一个权限
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    // 如果用户是超级管理员，拥有所有权限
    if (userStore.isSuperAdmin) {
      return true
    }

    // 获取用户权限列表
    const userPermissions = userStore.permissions || []

    // 检查是否拥有任意一个权限
    return permissions.some(perm => userPermissions.includes(perm))
  }

  /**
   * 检查是否有指定角色
   */
  const hasRole = (role: string | string[]): boolean => {
    // 获取用户角色列表
    const userRoles = userStore.roles || []

    // 如果传入的是字符串，转换为数组
    const rolesToCheck = Array.isArray(role) ? role : [role]

    // 检查是否拥有所有指定角色
    return rolesToCheck.every(r => userRoles.includes(r))
  }

  /**
   * 检查是否有任意一个角色
   */
  const hasAnyRole = (roles: string[]): boolean => {
    // 获取用户角色列表
    const userRoles = userStore.roles || []

    // 检查是否拥有任意一个角色
    return roles.some(role => userRoles.includes(role))
  }

  /**
   * 检查是否是超级管理员
   */
  const isSuperAdmin = computed(() => userStore.isSuperAdmin)

  /**
   * 获取当前用户权限列表
   */
  const permissions = computed(() => userStore.permissions || [])

  /**
   * 获取当前用户角色列表
   */
  const roles = computed(() => userStore.roles || [])

  return {
    hasPermission,
    hasAnyPermission,
    hasRole,
    hasAnyRole,
    isSuperAdmin,
    permissions,
    roles,
  }
}

/**
 * 权限指令工具函数
 */
export function checkPermission(permission: string | string[]): boolean {
  const { hasPermission } = usePermission()
  return hasPermission(permission)
}

/**
 * 角色指令工具函数
 */
export function checkRole(role: string | string[]): boolean {
  const { hasRole } = usePermission()
  return hasRole(role)
}
