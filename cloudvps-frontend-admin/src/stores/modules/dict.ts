/**
 * 字典数据存储
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { dictDataApi } from '@/api/system/dict'
import { mockGetDictDataByTypeCode } from '@/api/system/mock/dictData'
import type { DictDataResponse } from '@/api/system/types/dict'
import type { DictData, DictItem } from '@/types/business'

export const useDictStore = defineStore('dict', () => {
  // 状态
  const dictData = ref<DictData>({})
  const loading = ref<Record<string, boolean>>({})
  const lastFetchTime = ref<Record<string, number>>({})
  
  // 缓存过期时间（5分钟）
  const CACHE_EXPIRE_TIME = 5 * 60 * 1000

  /**
   * 获取字典数据
   */
  const getDictData = computed(() => {
    return (typeCode: string): DictItem[] => {
      return dictData.value[typeCode] || []
    }
  })

  /**
   * 获取字典标签
   */
  const getDictLabel = computed(() => {
    return (typeCode: string, value: string | number): string => {
      const items = dictData.value[typeCode] || []
      const item = items.find(item => item.value === value)
      return item?.label || String(value)
    }
  })

  /**
   * 获取字典颜色
   */
  const getDictColor = computed(() => {
    return (typeCode: string, value: string | number): string => {
      const items = dictData.value[typeCode] || []
      const item = items.find(item => item.value === value)
      return item?.color || ''
    }
  })

  /**
   * 检查缓存是否过期
   */
  const isCacheExpired = (typeCode: string): boolean => {
    const lastTime = lastFetchTime.value[typeCode]
    if (!lastTime) return true
    return Date.now() - lastTime > CACHE_EXPIRE_TIME
  }

  /**
   * 从API获取字典数据
   */
  const fetchDictData = async (typeCode: string, force = false): Promise<DictItem[]> => {
    // 检查缓存
    if (!force && dictData.value[typeCode] && !isCacheExpired(typeCode)) {
      return dictData.value[typeCode]
    }

    // 避免重复请求
    if (loading.value[typeCode]) {
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!loading.value[typeCode]) {
            resolve(dictData.value[typeCode] || [])
          } else {
            setTimeout(checkLoading, 100)
          }
        }
        checkLoading()
      })
    }

    try {
      loading.value[typeCode] = true
      
      // 使用模拟数据
      const mockData = await mockGetDictDataByTypeCode(typeCode)
      const items: DictItem[] = mockData.map((item: DictDataResponse) => ({
        label: item.label,
        value: item.value,
        color: item.color || getDictItemColor(item.value),
        status: item.status,
        description: item.description
      }))

      dictData.value[typeCode] = items
      lastFetchTime.value[typeCode] = Date.now()
      
      return items
    } catch (error) {
      console.error(`获取字典数据失败: ${typeCode}`, error)
      return []
    } finally {
      loading.value[typeCode] = false
    }
  }

  /**
   * 批量获取字典数据
   */
  const fetchMultipleDictData = async (typeCodes: string[], force = false): Promise<void> => {
    const promises = typeCodes.map(typeCode => fetchDictData(typeCode, force))
    await Promise.all(promises)
  }

  /**
   * 预加载常用字典
   */
  const preloadCommonDicts = async (): Promise<void> => {
    const commonDictTypes = [
      'user_status',
      'dict_status', 
      'config_type',
      'menu_type',
      'resource_type',
      'login_result'
    ]
    
    await fetchMultipleDictData(commonDictTypes)
  }

  /**
   * 刷新字典数据
   */
  const refreshDictData = async (typeCode?: string): Promise<void> => {
    if (typeCode) {
      await fetchDictData(typeCode, true)
    } else {
      // 刷新所有已加载的字典
      const typeCodes = Object.keys(dictData.value)
      await fetchMultipleDictData(typeCodes, true)
    }
  }

  /**
   * 清除字典缓存
   */
  const clearDictCache = (typeCode?: string): void => {
    if (typeCode) {
      delete dictData.value[typeCode]
      delete lastFetchTime.value[typeCode]
    } else {
      dictData.value = {}
      lastFetchTime.value = {}
    }
  }

  /**
   * 获取字典项颜色（根据值自动分配）
   */
  const getDictItemColor = (value: string): string => {
    const colorMap: Record<string, string> = {
      // 状态相关
      'ACTIVE': 'success',
      'INACTIVE': 'warning',
      'LOCKED': 'danger',
      'DELETED': 'default',
      'SUCCESS': 'success',
      'FAILED': 'danger',
      'PENDING': 'warning',
      'PROCESSING': 'primary',
      'COMPLETED': 'success',

      // 类型相关
      'STRING': 'primary',
      'INTEGER': 'success',
      'BOOLEAN': 'warning',
      'JSON': 'primary', // 修改为有效的TDesign主题值

      // 菜单类型
      'DIRECTORY': 'primary',
      'MENU': 'success',
      'BUTTON': 'warning',

      // 资源类型
      'API': 'primary',
      'DATA': 'primary' // 修改为有效的TDesign主题值
    }

    return colorMap[value] || 'default'
  }

  /**
   * 检查字典是否正在加载
   */
  const isDictLoading = computed(() => {
    return (typeCode: string): boolean => {
      return loading.value[typeCode] || false
    }
  })

  /**
   * 获取所有已加载的字典类型
   */
  const getLoadedDictTypes = computed(() => {
    return Object.keys(dictData.value)
  })

  return {
    // 状态
    dictData: computed(() => dictData.value),
    loading: computed(() => loading.value),
    
    // 计算属性
    getDictData,
    getDictLabel,
    getDictColor,
    isDictLoading,
    getLoadedDictTypes,
    
    // 方法
    fetchDictData,
    fetchMultipleDictData,
    preloadCommonDicts,
    refreshDictData,
    clearDictCache
  }
})
