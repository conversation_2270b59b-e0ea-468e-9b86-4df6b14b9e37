/**
 * 应用状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 主题类型
export type ThemeMode = 'light' | 'dark' | 'auto'

// 语言类型
export type Language = 'zh-CN' | 'en-US'

// 布局模式
export type LayoutMode = 'side' | 'top' | 'mix'

// 侧边栏状态
export type SidebarStatus = 'opened' | 'closed'

// 设备类型
export type DeviceType = 'desktop' | 'tablet' | 'mobile'

// 应用配置
export interface AppConfig {
  theme: ThemeMode
  language: Language
  layout: LayoutMode
  sidebarStatus: SidebarStatus
  sidebarWithoutAnimation: boolean
  device: DeviceType
  size: 'small' | 'medium' | 'large'
  fixedHeader: boolean
  showTagsView: boolean
  showSidebarLogo: boolean
  showBreadcrumb: boolean
  showSettings: boolean
}

export const useAppStore = defineStore(
  'app',
  () => {
    // 状态
    const config = ref<AppConfig>({
      theme: 'light',
      language: 'zh-CN',
      layout: 'side',
      sidebarStatus: 'opened',
      sidebarWithoutAnimation: false,
      device: 'desktop',
      size: 'medium',
      fixedHeader: true,
      showTagsView: true,
      showSidebarLogo: true,
      showBreadcrumb: true,
      showSettings: true
    })

    const loading = ref(false)
    const loadingText = ref('')

    // 计算属性
    const isMobile = computed(() => config.value.device === 'mobile')
    const isTablet = computed(() => config.value.device === 'tablet')
    const isDesktop = computed(() => config.value.device === 'desktop')
    const sidebarOpened = computed(() => config.value.sidebarStatus === 'opened')
    const isDarkMode = computed(() => {
      if (config.value.theme === 'auto') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches
      }
      return config.value.theme === 'dark'
    })

    // 方法
    const toggleSidebar = (withoutAnimation = false) => {
      config.value.sidebarStatus = sidebarOpened.value ? 'closed' : 'opened'
      config.value.sidebarWithoutAnimation = withoutAnimation
    }

    const closeSidebar = (withoutAnimation = false) => {
      config.value.sidebarStatus = 'closed'
      config.value.sidebarWithoutAnimation = withoutAnimation
    }

    const openSidebar = (withoutAnimation = false) => {
      config.value.sidebarStatus = 'opened'
      config.value.sidebarWithoutAnimation = withoutAnimation
    }

    const setDevice = (device: DeviceType) => {
      config.value.device = device
      
      // 移动端自动关闭侧边栏
      if (device === 'mobile') {
        closeSidebar(true)
      }
    }

    const setTheme = (theme: ThemeMode) => {
      config.value.theme = theme
      updateThemeClass()
    }

    const setLanguage = (language: Language) => {
      config.value.language = language
    }

    const setLayout = (layout: LayoutMode) => {
      config.value.layout = layout
    }

    const setSize = (size: 'small' | 'medium' | 'large') => {
      config.value.size = size
    }

    const updateConfig = (newConfig: Partial<AppConfig>) => {
      Object.assign(config.value, newConfig)
    }

    const setLoading = (isLoading: boolean, text = '') => {
      loading.value = isLoading
      loadingText.value = text
    }

    const updateThemeClass = () => {
      const html = document.documentElement
      const isDark = isDarkMode.value
      
      if (isDark) {
        html.classList.add('dark')
        html.setAttribute('theme-mode', 'dark')
      } else {
        html.classList.remove('dark')
        html.setAttribute('theme-mode', 'light')
      }
    }

    const initTheme = () => {
      updateThemeClass()
      
      // 监听系统主题变化
      if (config.value.theme === 'auto') {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        mediaQuery.addEventListener('change', updateThemeClass)
      }
    }

    const resetConfig = () => {
      config.value = {
        theme: 'light',
        language: 'zh-CN',
        layout: 'side',
        sidebarStatus: 'opened',
        sidebarWithoutAnimation: false,
        device: 'desktop',
        size: 'medium',
        fixedHeader: true,
        showTagsView: true,
        showSidebarLogo: true,
        showBreadcrumb: true,
        showSettings: true
      }
    }

    return {
      // 状态
      config: readonly(config),
      loading: readonly(loading),
      loadingText: readonly(loadingText),
      
      // 计算属性
      isMobile,
      isTablet,
      isDesktop,
      sidebarOpened,
      isDarkMode,
      
      // 方法
      toggleSidebar,
      closeSidebar,
      openSidebar,
      setDevice,
      setTheme,
      setLanguage,
      setLayout,
      setSize,
      updateConfig,
      setLoading,
      initTheme,
      resetConfig
    }
  },
  {
    persist: {
      key: 'app-store',
      paths: ['config']
    }
  }
)
