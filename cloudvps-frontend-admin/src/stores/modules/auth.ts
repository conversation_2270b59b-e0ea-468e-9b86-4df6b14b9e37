/**
 * 认证状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/system/auth'
import { mockLogin, mockGetCurrentUser, mockLogout, mockRefreshToken } from '@/api/system/mock/authMockApi'
import { removeToken, setToken, getToken } from '@/utils/auth'
import type {
  CurrentUser as User,
  LoginRequest,
  LoginResponse
} from '@/api/system/types/auth'
import { MessagePlugin } from 'tdesign-vue-next'

// 是否使用模拟API（强制使用真实API）
const USE_MOCK_API = false

export const useAuthStore = defineStore(
  'auth',
  () => {
    // 状态
    const token = ref<string>(getToken() || '')
    const refreshToken = ref<string>('')
    const currentUser = ref<User | null>(null)
    const permissions = ref<string[]>([])
    const loading = ref(false)

    // 防重复调用状态
    const isValidating = ref(false)
    const isRefreshing = ref(false)

    // 计算属性
    const isLoggedIn = computed(() => !!token.value && !!currentUser.value)
    const isAdmin = computed(() => {
      return currentUser.value?.userType === 'ADMIN' ||
             currentUser.value?.userType === 'SUPER_ADMIN'
    })
    const isSuperAdmin = computed(() => {
      // return currentUser.value?.userType === 'SUPER_ADMIN'
      return true;
    })

    // 方法
    const setAuthData = (data: LoginResponse) => {
      token.value = data.token
      refreshToken.value = data.refreshToken
      currentUser.value = data.user

      // 临时设置全部权限，绕过权限验证
      const allPermissions = [
        // 系统管理权限
        'SYSTEM_VIEW',
        'USER_VIEW', 'USER_CREATE', 'USER_UPDATE', 'USER_DELETE', 'USER_EXPORT',
        'ROLE_VIEW', 'ROLE_CREATE', 'ROLE_UPDATE', 'ROLE_DELETE',
        'PERMISSION_VIEW', 'PERMISSION_CREATE', 'PERMISSION_UPDATE', 'PERMISSION_DELETE',
        'DICT_VIEW', 'DICT_CREATE', 'DICT_UPDATE', 'DICT_DELETE',
        'CONFIG_VIEW', 'CONFIG_UPDATE',

        // 虚拟化管理权限
        'VIRTUALIZATION_VIEW',
        'VM_VIEW', 'VM_CREATE', 'VM_UPDATE', 'VM_DELETE', 'VM_START', 'VM_STOP', 'VM_RESTART',
        'NODE_VIEW', 'NODE_CREATE', 'NODE_UPDATE', 'NODE_DELETE',
        'TEMPLATE_VIEW', 'TEMPLATE_CREATE', 'TEMPLATE_UPDATE', 'TEMPLATE_DELETE',

        // 订单管理权限
        'ORDER_VIEW', 'ORDER_CREATE', 'ORDER_UPDATE', 'ORDER_DELETE', 'ORDER_CANCEL',

        // 支付管理权限
        'PAYMENT_VIEW', 'PAYMENT_CREATE', 'PAYMENT_UPDATE', 'PAYMENT_DELETE',

        // 仪表板权限
        'DASHBOARD_VIEW'
      ]

      permissions.value = data.user?.permissions || allPermissions
      console.log('🔓 临时设置全部权限，当前权限数量:', permissions.value.length)

      setToken(data.token)

      // 同时保存到localStorage
      localStorage.setItem('cloudvps_admin_refresh_token', data.refreshToken)
      localStorage.setItem('currentUser', JSON.stringify(data.user))
      localStorage.setItem('permissions', JSON.stringify(permissions.value))
    }

    const clearAuthData = () => {
      token.value = ''
      refreshToken.value = ''
      currentUser.value = null
      permissions.value = []

      removeToken()

      // 清除localStorage中的所有认证相关数据
      localStorage.removeItem('cloudvps_admin_refresh_token')
      localStorage.removeItem('currentUser')
      localStorage.removeItem('permissions')
    }

    const login = async (loginData: LoginRequest) => {
      loading.value = true
      try {
        let response
        if (USE_MOCK_API) {
          const data = await mockLogin(loginData)
          response = { data }
        } else {
          response = await authApi.login(loginData)
        }
        setAuthData(response.data)
        MessagePlugin.success('登录成功')
        return response.data
      } catch (error: any) {
        MessagePlugin.error(error.message || '登录失败，请检查用户名和密码')
        throw error
      } finally {
        loading.value = false
      }
    }

    const logout = async () => {
      try {
        if (USE_MOCK_API) {
          await mockLogout()
        } else {
          await authApi.logout()
        }
      } catch (error) {
        console.warn('退出登录请求失败:', error)
      } finally {
        clearAuthData()
        MessagePlugin.success('已退出登录')
      }
    }

    const refreshTokenAction = async () => {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }

      // 防止重复调用
      if (isRefreshing.value) {
        console.log('Token刷新正在进行中，跳过重复调用')
        throw new Error('Token refresh already in progress')
      }

      isRefreshing.value = true

      try {
        let response
        if (USE_MOCK_API) {
          const data = await mockRefreshToken(refreshToken.value)
          response = { data }
        } else {
          response = await authApi.refreshToken(refreshToken.value)
        }
        setAuthData(response.data)
        console.log('Token刷新成功')
        return response.data.accessToken
      } catch (error) {
        console.error('Token刷新失败:', error)
        clearAuthData()
        throw error
      } finally {
        isRefreshing.value = false
      }
    }

    const getCurrentUser = async () => {
      try {
        let response
        if (USE_MOCK_API) {
          const data = await mockGetCurrentUser()
          response = { data }
        } else {
          response = await authApi.getCurrentUser()
        }
        currentUser.value = response.data
        return response.data
      } catch (error) {
        clearAuthData()
        throw error
      }
    }

    // Token验证方法
    const validateToken = async (): Promise<boolean> => {
      if (!token.value) {
        return false
      }

      // 防止重复调用
      if (isValidating.value) {
        console.log('Token验证正在进行中，跳过重复调用')
        return true // 假设正在验证的会成功
      }

      isValidating.value = true

      try {
        // 调用后端API验证token有效性
        const response = await authApi.getCurrentUser()
        currentUser.value = response.data
        return true
      } catch (error: any) {
        console.warn('Token验证失败:', error)

        // 如果是401错误，尝试刷新token
        if (error?.response?.status === 401 && refreshToken.value && !isRefreshing.value) {
          try {
            await refreshTokenAction()
            return true
          } catch (refreshError) {
            console.error('Token刷新失败:', refreshError)
            clearAuthData()
            return false
          }
        }

        // 其他错误直接清除认证数据
        clearAuthData()
        return false
      } finally {
        isValidating.value = false
      }
    }

    // 检查token是否即将过期并自动刷新
    const checkTokenExpiration = async (): Promise<boolean> => {
      if (!token.value) {
        return false
      }

      try {
        // 这里可以解析JWT token的过期时间
        // 如果即将过期（比如5分钟内），则自动刷新
        const tokenPayload = parseJwtToken(token.value)
        if (tokenPayload && tokenPayload.exp) {
          const expirationTime = tokenPayload.exp * 1000 // 转换为毫秒
          const currentTime = Date.now()
          const timeUntilExpiration = expirationTime - currentTime

          // 如果token在5分钟内过期，尝试刷新
          if (timeUntilExpiration < 5 * 60 * 1000 && refreshToken.value) {
            try {
              await refreshTokenAction()
              return true
            } catch (error) {
              console.error('自动刷新token失败:', error)
              clearAuthData()
              return false
            }
          }
        }

        return true
      } catch (error) {
        console.error('检查token过期时间失败:', error)
        return await validateToken()
      }
    }

    // 解析JWT token（简单实现）
    const parseJwtToken = (token: string) => {
      try {
        const base64Url = token.split('.')[1]
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
        const jsonPayload = decodeURIComponent(
          atob(base64)
            .split('')
            .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
            .join('')
        )
        return JSON.parse(jsonPayload)
      } catch (error) {
        console.error('解析JWT token失败:', error)
        return null
      }
    }

    const setCurrentUser = (user: User) => {
      currentUser.value = user
      permissions.value = user.permissions || []

      // 同时保存到localStorage
      localStorage.setItem('currentUser', JSON.stringify(user))
      localStorage.setItem('permissions', JSON.stringify(user.permissions || []))
    }

    const updateUserInfo = (user: Partial<User>) => {
      if (currentUser.value) {
        Object.assign(currentUser.value, user)
        // 更新localStorage
        localStorage.setItem('currentUser', JSON.stringify(currentUser.value))
      }
    }

    const hasPermission = (permission: string | string[]): boolean => {
      // 临时禁用权限验证，始终返回true
      console.log('🔓 权限检查已临时禁用，检查权限:', permission, '-> 返回 true')
      return true

      // 原始权限检查逻辑（已注释）
      // if (isSuperAdmin.value) return true
      //
      // // 确保 permissions 是数组
      // const userPermissions = permissions.value || []
      //
      // if (Array.isArray(permission)) {
      //   return permission.every(p => userPermissions.includes(p))
      // }
      // return userPermissions.includes(permission)
    }

    const hasAnyPermission = (permissionList: string[]): boolean => {
      // 临时禁用权限验证，始终返回true
      console.log('🔓 任意权限检查已临时禁用，检查权限:', permissionList, '-> 返回 true')
      return true

      // 原始权限检查逻辑（已注释）
      // if (isSuperAdmin.value) return true
      //
      // // 确保 permissions 是数组
      // const userPermissions = permissions.value || []
      //
      // return permissionList.some(permission => userPermissions.includes(permission))
    }

    const checkAuth = async () => {
      if (!token.value) return false
      
      try {
        await getCurrentUser()
        return true
      } catch (error) {
        clearAuthData()
        return false
      }
    }

    return {
      // 状态
      token: readonly(token),
      refreshToken: readonly(refreshToken),
      currentUser: readonly(currentUser),
      permissions: readonly(permissions),
      loading: readonly(loading),
      isValidating: readonly(isValidating),
      isRefreshing: readonly(isRefreshing),
      
      // 计算属性
      isLoggedIn,
      isAdmin,
      isSuperAdmin,
      
      // 方法
      login,
      logout,
      refreshTokenAction,
      getCurrentUser,
      validateToken,
      checkTokenExpiration,
      setCurrentUser,
      updateUserInfo,
      hasPermission,
      hasAnyPermission,
      checkAuth,
      setAuthData,
      clearAuthData
    }
  },
  {
    persist: {
      key: 'auth-store',
      paths: ['token', 'refreshToken', 'currentUser', 'permissions']
    }
  }
)
