/**
 * 用户状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserResponse } from '@/api/system/types/user'

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref<UserResponse | null>(null)
  
  // 用户权限列表
  const permissions = ref<string[]>([])
  
  // 用户角色列表
  const roles = ref<string[]>([])

  // 计算属性：是否已登录
  const isLoggedIn = computed(() => !!userInfo.value)

  // 计算属性：是否是超级管理员
  const isSuperAdmin = computed(() => {
    // return roles.value.includes('SUPER_ADMIN') || roles.value.includes('ADMIN')
    return true;
  })

  // 计算属性：用户名
  const username = computed(() => userInfo.value?.username || '')

  // 计算属性：真实姓名
  const realName = computed(() => userInfo.value?.realName || '')

  // 计算属性：邮箱
  const email = computed(() => userInfo.value?.email || '')

  // 计算属性：头像
  const avatar = computed(() => userInfo.value?.avatar || '')

  /**
   * 设置用户信息
   */
  const setUserInfo = (user: UserResponse) => {
    userInfo.value = user
  }

  /**
   * 设置用户权限
   */
  const setPermissions = (perms: string[]) => {
    permissions.value = perms
  }

  /**
   * 设置用户角色
   */
  const setRoles = (userRoles: string[]) => {
    roles.value = userRoles
  }

  /**
   * 清除用户信息
   */
  const clearUserInfo = () => {
    userInfo.value = null
    permissions.value = []
    roles.value = []
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = (updates: Partial<UserResponse>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...updates }
    }
  }

  /**
   * 检查是否有指定权限
   */
  const hasPermission = (permission: string): boolean => {
    if (isSuperAdmin.value) {
      return true
    }
    return permissions.value.includes(permission)
  }

  /**
   * 检查是否有指定角色
   */
  const hasRole = (role: string): boolean => {
    return roles.value.includes(role)
  }

  return {
    // 状态
    userInfo,
    permissions,
    roles,
    
    // 计算属性
    isLoggedIn,
    isSuperAdmin,
    username,
    realName,
    email,
    avatar,
    
    // 方法
    setUserInfo,
    setPermissions,
    setRoles,
    clearUserInfo,
    updateUserInfo,
    hasPermission,
    hasRole,
  }
})
