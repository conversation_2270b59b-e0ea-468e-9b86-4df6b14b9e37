/**
 * 系统设置状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed, watch, nextTick } from 'vue'
import type { 
  SettingsState, 
  ThemeMode, 
  LayoutMode, 
  UIElementsConfig,
  ThemeColor 
} from '@/types/settings'
import { 
  DEFAULT_SETTINGS, 
  SETTINGS_STORAGE_KEY, 
  CSS_VARIABLES,
  THEME_CLASS_MAP,
  LAYOUT_CLASS_MAP,
  PRESET_THEME_COLORS
} from '@/types/settings'

export const useSettingsStore = defineStore('settings', () => {
  // 状态
  const settings = ref<SettingsState>({ ...DEFAULT_SETTINGS })
  
  // 计算属性
  const themeMode = computed(() => settings.value.themeMode)
  const primaryColor = computed(() => settings.value.primaryColor)
  const layoutMode = computed(() => settings.value.layoutMode)
  const uiElements = computed(() => settings.value.uiElements)
  const showSettingsPanel = computed({
    get: () => settings.value.showSettingsPanel,
    set: (value: boolean) => {
      settings.value.showSettingsPanel = value
      saveSettings()
    }
  })
  const customColors = computed(() => settings.value.customColors)
  const recentColors = computed(() => settings.value.recentColors)
  
  // 当前实际主题模式（处理auto模式）
  const actualThemeMode = computed(() => {
    if (settings.value.themeMode === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return settings.value.themeMode
  })
  
  // 是否为暗色主题
  const isDarkTheme = computed(() => actualThemeMode.value === 'dark')
  
  // 当前主题CSS类名
  const themeClassName = computed(() => THEME_CLASS_MAP[actualThemeMode.value])
  
  // 当前布局CSS类名
  const layoutClassName = computed(() => LAYOUT_CLASS_MAP[settings.value.layoutMode])
  
  // 所有可用颜色（预设 + 自定义 + 最近使用）
  const allColors = computed(() => {
    const colors = new Set<string>()
    
    // 添加预设颜色
    PRESET_THEME_COLORS.forEach(color => colors.add(color.value))
    
    // 添加自定义颜色
    settings.value.customColors.forEach(color => colors.add(color))
    
    // 添加最近使用的颜色
    settings.value.recentColors.forEach(color => colors.add(color))
    
    return Array.from(colors)
  })
  
  // 方法
  
  /**
   * 设置主题模式
   */
  const setThemeMode = (mode: ThemeMode) => {
    settings.value.themeMode = mode
    applyTheme()
    saveSettings()
  }
  
  /**
   * 设置主色调
   */
  const setPrimaryColor = (color: string) => {
    if (!isValidColor(color)) {
      console.warn('Invalid color value:', color)
      return
    }
    
    settings.value.primaryColor = color
    addToRecentColors(color)
    applyPrimaryColor(color)
    saveSettings()
  }
  
  /**
   * 设置布局模式
   */
  const setLayoutMode = (mode: LayoutMode) => {
    settings.value.layoutMode = mode
    applyLayout()
    saveSettings()
  }
  
  /**
   * 设置UI元素显示状态
   */
  const setUIElement = (key: keyof UIElementsConfig, value: boolean) => {
    settings.value.uiElements[key] = value
    applyUIElements()
    saveSettings()
  }
  
  /**
   * 批量设置UI元素
   */
  const setUIElements = (elements: Partial<UIElementsConfig>) => {
    Object.assign(settings.value.uiElements, elements)
    applyUIElements()
    saveSettings()
  }
  
  /**
   * 显示/隐藏设置面板
   */
  const toggleSettingsPanel = (show?: boolean) => {
    settings.value.showSettingsPanel = show !== undefined ? show : !settings.value.showSettingsPanel
  }
  
  /**
   * 添加自定义颜色
   */
  const addCustomColor = (color: string) => {
    if (!isValidColor(color)) return
    
    if (!settings.value.customColors.includes(color)) {
      settings.value.customColors.push(color)
      saveSettings()
    }
  }
  
  /**
   * 移除自定义颜色
   */
  const removeCustomColor = (color: string) => {
    const index = settings.value.customColors.indexOf(color)
    if (index > -1) {
      settings.value.customColors.splice(index, 1)
      saveSettings()
    }
  }
  
  /**
   * 添加到最近使用颜色
   */
  const addToRecentColors = (color: string) => {
    if (!isValidColor(color)) return
    
    // 移除已存在的相同颜色
    const index = settings.value.recentColors.indexOf(color)
    if (index > -1) {
      settings.value.recentColors.splice(index, 1)
    }
    
    // 添加到开头
    settings.value.recentColors.unshift(color)
    
    // 限制最近颜色数量
    if (settings.value.recentColors.length > 10) {
      settings.value.recentColors = settings.value.recentColors.slice(0, 10)
    }
  }
  
  /**
   * 重置设置
   */
  const resetSettings = () => {
    settings.value = { ...DEFAULT_SETTINGS }
    applyAllSettings()
    saveSettings()
  }
  
  /**
   * 导出设置
   */
  const exportSettings = () => {
    return JSON.stringify(settings.value, null, 2)
  }
  
  /**
   * 导入设置
   */
  const importSettings = (settingsJson: string) => {
    try {
      const importedSettings = JSON.parse(settingsJson)
      if (validateSettings(importedSettings)) {
        settings.value = { ...DEFAULT_SETTINGS, ...importedSettings }
        applyAllSettings()
        saveSettings()
        return true
      }
    } catch (error) {
      console.error('Failed to import settings:', error)
    }
    return false
  }
  
  /**
   * 应用主题
   */
  const applyTheme = () => {
    const htmlElement = document.documentElement

    // 移除所有主题类
    Object.values(THEME_CLASS_MAP).forEach(className => {
      htmlElement.classList.remove(className)
    })

    // 根据实际主题模式添加对应的主题类
    const actualTheme = actualThemeMode.value
    htmlElement.classList.add(THEME_CLASS_MAP[actualTheme])

    // 同时设置data-theme属性，确保兼容性
    htmlElement.setAttribute('theme-mode', actualTheme)

    // 如果是auto模式，监听系统主题变化
    if (settings.value.themeMode === 'auto') {
      setupAutoThemeListener()
    }

    console.log(`主题已切换到: ${actualTheme}, CSS类: ${THEME_CLASS_MAP[actualTheme]}`)
  }
  
  /**
   * 应用主色调
   */
  const applyPrimaryColor = (color: string) => {
    const root = document.documentElement
    const variants = generateColorVariants(color)

    // 设置TDesign主色调相关的CSS变量
    root.style.setProperty(CSS_VARIABLES.primaryColor, color)
    root.style.setProperty(CSS_VARIABLES.primaryColorHover, variants.hover)
    root.style.setProperty(CSS_VARIABLES.primaryColorFocus, variants.focus)
    root.style.setProperty(CSS_VARIABLES.primaryColorActive, variants.active)
    root.style.setProperty(CSS_VARIABLES.primaryColorDisabled, variants.disabled)

    // 同时设置自定义CSS变量，确保兼容性
    root.style.setProperty('--brand-color', color)
    root.style.setProperty('--brand-color-hover', variants.hover)
    root.style.setProperty('--brand-color-active', variants.active)
    root.style.setProperty('--brand-color-disabled', variants.disabled)

    console.log(`主色调已设置为: ${color}`)
  }
  
  /**
   * 应用布局
   */
  const applyLayout = () => {
    const bodyElement = document.body

    // 移除所有布局类
    Object.values(LAYOUT_CLASS_MAP).forEach(className => {
      bodyElement.classList.remove(className)
    })

    // 添加当前布局类
    bodyElement.classList.add(layoutClassName.value)

    // 设置布局相关的CSS变量
    bodyElement.setAttribute('data-layout', settings.value.layoutMode)

    console.log(`布局已切换到: ${settings.value.layoutMode}, CSS类: ${layoutClassName.value}`)
  }
  
  /**
   * 应用UI元素设置
   */
  const applyUIElements = () => {
    const root = document.documentElement

    // 为每个UI元素设置CSS变量
    Object.entries(settings.value.uiElements).forEach(([key, value]) => {
      root.style.setProperty(`--ui-${key}`, value ? 'block' : 'none')
      root.style.setProperty(`--show-${key}`, value ? '1' : '0')
    })

    // 设置body的data属性，便于CSS选择器使用
    Object.entries(settings.value.uiElements).forEach(([key, value]) => {
      document.body.setAttribute(`data-show-${key}`, value.toString())
    })

    console.log('UI元素设置已应用:', settings.value.uiElements)
  }
  
  /**
   * 应用所有设置
   */
  const applyAllSettings = () => {
    nextTick(() => {
      applyTheme()
      applyPrimaryColor(settings.value.primaryColor)
      applyLayout()
      applyUIElements()
    })
  }
  
  /**
   * 保存设置到localStorage
   */
  const saveSettings = () => {
    try {
      localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(settings.value))
    } catch (error) {
      console.error('Failed to save settings:', error)
    }
  }
  
  /**
   * 从localStorage加载设置
   */
  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem(SETTINGS_STORAGE_KEY)
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings)
        if (validateSettings(parsedSettings)) {
          settings.value = { ...DEFAULT_SETTINGS, ...parsedSettings }
          applyAllSettings()
          return true
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
    return false
  }
  
  /**
   * 设置自动主题监听器
   */
  const setupAutoThemeListener = () => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = () => {
      if (settings.value.themeMode === 'auto') {
        applyTheme()
      }
    }
    
    mediaQuery.addEventListener('change', handleChange)
    
    // 返回清理函数
    return () => mediaQuery.removeEventListener('change', handleChange)
  }
  
  /**
   * 验证颜色值
   */
  const isValidColor = (color: string): boolean => {
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    const rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/
    const rgbaRegex = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/
    
    return hexRegex.test(color) || rgbRegex.test(color) || rgbaRegex.test(color)
  }
  
  /**
   * 生成颜色变体
   */
  const generateColorVariants = (color: string) => {
    // 简单的颜色变体生成，实际项目中可以使用更复杂的算法
    const rgb = hexToRgb(color)
    if (!rgb) return { hover: color, focus: color, active: color, disabled: color }
    
    return {
      hover: adjustBrightness(color, 10),
      focus: adjustBrightness(color, 5),
      active: adjustBrightness(color, -10),
      disabled: adjustOpacity(color, 0.4)
    }
  }
  
  /**
   * 十六进制转RGB
   */
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }
  
  /**
   * 调整亮度
   */
  const adjustBrightness = (color: string, amount: number): string => {
    const rgb = hexToRgb(color)
    if (!rgb) return color
    
    const adjust = (value: number) => Math.max(0, Math.min(255, value + amount))
    
    const r = adjust(rgb.r).toString(16).padStart(2, '0')
    const g = adjust(rgb.g).toString(16).padStart(2, '0')
    const b = adjust(rgb.b).toString(16).padStart(2, '0')
    
    return `#${r}${g}${b}`
  }
  
  /**
   * 调整透明度
   */
  const adjustOpacity = (color: string, opacity: number): string => {
    const rgb = hexToRgb(color)
    if (!rgb) return color
    
    return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})`
  }
  
  /**
   * 验证设置对象
   */
  const validateSettings = (settingsToValidate: any): boolean => {
    if (!settingsToValidate || typeof settingsToValidate !== 'object') {
      return false
    }
    
    // 验证必要字段
    const requiredFields = ['themeMode', 'primaryColor', 'layoutMode', 'uiElements']
    for (const field of requiredFields) {
      if (!(field in settingsToValidate)) {
        return false
      }
    }
    
    return true
  }
  
  // 初始化
  const init = () => {
    loadSettings()
    applyAllSettings()
  }
  
  return {
    // 状态
    settings,
    
    // 计算属性
    themeMode,
    primaryColor,
    layoutMode,
    uiElements,
    showSettingsPanel,
    customColors,
    recentColors,
    actualThemeMode,
    isDarkTheme,
    themeClassName,
    layoutClassName,
    allColors,
    
    // 方法
    setThemeMode,
    setPrimaryColor,
    setLayoutMode,
    setUIElement,
    setUIElements,
    toggleSettingsPanel,
    addCustomColor,
    removeCustomColor,
    addToRecentColors,
    resetSettings,
    exportSettings,
    importSettings,
    applyAllSettings,
    saveSettings,
    loadSettings,
    init
  }
})
