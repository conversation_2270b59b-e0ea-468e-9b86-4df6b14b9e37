/**
 * 订单服务 - 订单管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse, BatchRequest } from '@/types/api'
import type {
  OrderResponse,
  OrderQueryRequest,
  OrderCreateRequest,
  OrderUpdateRequest,
  OrderStatusUpdateRequest,
  OrderDetail
} from './types/order'

/**
 * 订单管理API
 */
export const orderApi = {
  /**
   * 分页查询订单
   */
  getPage: (params: OrderQueryRequest) =>
    request.get<ApiResponse<PageResponse<OrderResponse>>>('/order/orders', { params }),

  /**
   * 根据ID获取订单详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<OrderDetail>>(`/order/orders/${id}`),

  /**
   * 根据订单号获取订单详情
   */
  getByOrderNo: (orderNo: string) =>
    request.get<ApiResponse<OrderDetail>>(`/order/orders/no/${orderNo}`),

  /**
   * 创建订单
   */
  create: (data: OrderCreateRequest) =>
    request.post<ApiResponse<OrderResponse>>('/order/orders', data),

  /**
   * 更新订单
   */
  update: (id: number, data: OrderUpdateRequest) =>
    request.put<ApiResponse<OrderResponse>>(`/order/orders/${id}`, data),

  /**
   * 删除订单
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/order/orders/${id}`),

  /**
   * 批量删除订单
   */
  batchDelete: (data: BatchRequest) =>
    request.delete<ApiResponse<void>>('/order/orders/batch', { data }),

  /**
   * 更新订单状态
   */
  updateStatus: (id: number, data: OrderStatusUpdateRequest) =>
    request.put<ApiResponse<void>>(`/order/orders/${id}/status`, data),

  /**
   * 取消订单
   */
  cancel: (id: number, reason?: string) =>
    request.post<ApiResponse<void>>(`/order/orders/${id}/cancel`, { reason }),

  /**
   * 完成订单
   */
  complete: (id: number) =>
    request.post<ApiResponse<void>>(`/order/orders/${id}/complete`),

  /**
   * 获取订单统计
   */
  getStatistics: (startDate?: string, endDate?: string) =>
    request.get<ApiResponse<any>>('/order/orders/statistics', { 
      params: { startDate, endDate } 
    }),

  /**
   * 导出订单
   */
  export: (params: OrderQueryRequest) =>
    request.get<Blob>('/order/orders/export', { 
      params, 
      responseType: 'blob' 
    })
}
