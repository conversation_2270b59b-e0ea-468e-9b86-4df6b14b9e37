/**
 * 订单服务 - 产品管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse, BatchRequest } from '@/types/api'
import type {
  ProductResponse,
  ProductQueryRequest,
  ProductCreateRequest,
  ProductUpdateRequest
} from './types/product'

/**
 * 产品管理API
 */
export const productApi = {
  /**
   * 分页查询产品
   */
  getPage: (params: ProductQueryRequest) =>
    request.get<ApiResponse<PageResponse<ProductResponse>>>('/order/products', { params }),

  /**
   * 获取所有产品（不分页）
   */
  getAll: () =>
    request.get<ApiResponse<ProductResponse[]>>('/order/products/all'),

  /**
   * 根据ID获取产品详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<ProductResponse>>(`/order/products/${id}`),

  /**
   * 创建产品
   */
  create: (data: ProductCreateRequest) =>
    request.post<ApiResponse<ProductResponse>>('/order/products', data),

  /**
   * 更新产品
   */
  update: (id: number, data: ProductUpdateRequest) =>
    request.put<ApiResponse<ProductResponse>>(`/order/products/${id}`, data),

  /**
   * 删除产品
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/order/products/${id}`),

  /**
   * 批量删除产品
   */
  batchDelete: (data: BatchRequest) =>
    request.delete<ApiResponse<void>>('/order/products/batch', { data }),

  /**
   * 启用/禁用产品
   */
  toggleActive: (id: number, isActive: boolean) =>
    request.put<ApiResponse<void>>(`/order/products/${id}/active`, { isActive }),

  /**
   * 更新产品排序
   */
  updateSort: (id: number, sortOrder: number) =>
    request.put<ApiResponse<void>>(`/order/products/${id}/sort`, { sortOrder }),

  /**
   * 计算产品价格
   */
  calculatePrice: (productId: number, config: any, billingCycle: string, quantity: number) =>
    request.post<ApiResponse<any>>('/order/products/calculate-price', {
      productId,
      config,
      billingCycle,
      quantity
    })
}
