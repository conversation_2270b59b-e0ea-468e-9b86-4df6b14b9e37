/**
 * 订单相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { OrderStatus, ProductType, BillingCycle, PriceInfo } from './common'

// 订单响应
export interface OrderResponse extends BaseEntity {
  orderNo: string
  userId: number
  username: string
  status: OrderStatus
  productType: ProductType
  productName: string
  billingCycle: BillingCycle
  quantity: number
  unitPrice: number
  totalAmount: number
  discountAmount: number
  finalAmount: number
  paidAmount: number
  refundAmount: number
  orderTime: string
  paidTime?: string
  completedTime?: string
  cancelledTime?: string
  expiredTime?: string
  remark?: string
}

// 订单查询请求
export interface OrderQueryRequest extends BasePageRequest {
  userId?: number
  status?: OrderStatus
  productType?: ProductType
  billingCycle?: BillingCycle
  keyword?: string
  orderNo?: string
  username?: string
  orderTimeStart?: string
  orderTimeEnd?: string
}

// 订单创建请求
export interface OrderCreateRequest {
  userId: number
  productType: ProductType
  productName: string
  productConfig: Record<string, any>
  billingCycle: BillingCycle
  quantity: number
  unitPrice: number
  discountAmount?: number
  remark?: string
}

// 订单更新请求
export interface OrderUpdateRequest {
  remark?: string
}

// 订单状态更新请求
export interface OrderStatusUpdateRequest {
  status: OrderStatus
  remark?: string
}

// 订单项
export interface OrderItem {
  productType: ProductType
  productName: string
  productConfig: Record<string, any>
  billingCycle: BillingCycle
  quantity: number
  unitPrice: number
  totalPrice: number
}

// 订单详情
export interface OrderDetail extends OrderResponse {
  items: OrderItem[]
  paymentRecords: PaymentRecord[]
  refundRecords: RefundRecord[]
}

// 支付记录
export interface PaymentRecord {
  id: number
  paymentNo: string
  amount: number
  paymentMethod: string
  paymentTime: string
  status: string
}

// 退款记录
export interface RefundRecord {
  id: number
  refundNo: string
  amount: number
  refundReason: string
  refundTime: string
  status: string
}
