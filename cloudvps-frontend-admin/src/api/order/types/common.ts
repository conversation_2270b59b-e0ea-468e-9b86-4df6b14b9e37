/**
 * 订单服务特有类型定义
 */

// 订单状态枚举
export enum OrderStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

// 产品类型枚举
export enum ProductType {
  VPS = 'VPS',
  DEDICATED_SERVER = 'DEDICATED_SERVER',
  CLOUD_STORAGE = 'CLOUD_STORAGE',
  BANDWIDTH = 'BANDWIDTH'
}

// 计费周期枚举
export enum BillingCycle {
  HOURLY = 'HOURLY',
  DAILY = 'DAILY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  YEARLY = 'YEARLY'
}

// 价格信息
export interface PriceInfo {
  unitPrice: number
  quantity: number
  totalPrice: number
  discountAmount?: number
  finalPrice: number
}
