/**
 * 产品相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { ProductType, BillingCycle } from './common'

// 产品响应
export interface ProductResponse extends BaseEntity {
  name: string
  type: ProductType
  description?: string
  specifications: ProductSpecification[]
  pricing: ProductPricing[]
  isActive: boolean
  sortOrder: number
}

// 产品查询请求
export interface ProductQueryRequest extends BasePageRequest {
  type?: ProductType
  isActive?: boolean
  keyword?: string
  name?: string
}

// 产品创建请求
export interface ProductCreateRequest {
  name: string
  type: ProductType
  description?: string
  specifications: ProductSpecification[]
  pricing: ProductPricing[]
  sortOrder?: number
}

// 产品更新请求
export interface ProductUpdateRequest {
  name?: string
  description?: string
  specifications?: ProductSpecification[]
  pricing?: ProductPricing[]
  sortOrder?: number
}

// 产品规格
export interface ProductSpecification {
  name: string
  value: string
  unit?: string
  description?: string
}

// 产品定价
export interface ProductPricing {
  billingCycle: BillingCycle
  price: number
  originalPrice?: number
  discountRate?: number
}

// 产品配置
export interface ProductConfig {
  cpu: number
  memory: number
  disk: number
  bandwidth: number
  ipCount: number
  osType: string
  location: string
}
