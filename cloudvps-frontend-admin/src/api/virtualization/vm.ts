/**
 * 虚拟化服务 - 虚拟机管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse, BatchRequest } from '@/types/api'
import type {
  VmResponse,
  VmQueryRequest,
  VmCreateRequest,
  VmUpdateRequest,
  VmOperationRequest,
  VmConfig,
  VmSnapshot,
  VmTemplate
} from './types/vm'

/**
 * 虚拟机管理API
 */
export const vmApi = {
  /**
   * 分页查询虚拟机
   */
  getPage: (params: VmQueryRequest) =>
    request.get<ApiResponse<PageResponse<VmResponse>>>('/virtualization/vms', { params }),

  /**
   * 根据ID获取虚拟机详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<VmResponse>>(`/virtualization/vms/${id}`),

  /**
   * 创建虚拟机
   */
  create: (data: VmCreateRequest) =>
    request.post<ApiResponse<VmResponse>>('/virtualization/vms', data),

  /**
   * 更新虚拟机
   */
  update: (id: number, data: VmUpdateRequest) =>
    request.put<ApiResponse<VmResponse>>(`/virtualization/vms/${id}`, data),

  /**
   * 删除虚拟机
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/virtualization/vms/${id}`),

  /**
   * 批量删除虚拟机
   */
  batchDelete: (data: BatchRequest) =>
    request.delete<ApiResponse<void>>('/virtualization/vms/batch', { data }),

  /**
   * 虚拟机操作（启动、停止、重启等）
   */
  operation: (id: number, data: VmOperationRequest) =>
    request.post<ApiResponse<void>>(`/virtualization/vms/${id}/operation`, data),

  /**
   * 获取虚拟机配置
   */
  getConfig: (id: number) =>
    request.get<ApiResponse<VmConfig>>(`/virtualization/vms/${id}/config`),

  /**
   * 更新虚拟机配置
   */
  updateConfig: (id: number, data: VmConfig) =>
    request.put<ApiResponse<void>>(`/virtualization/vms/${id}/config`, data),

  /**
   * 获取虚拟机快照列表
   */
  getSnapshots: (id: number) =>
    request.get<ApiResponse<VmSnapshot[]>>(`/virtualization/vms/${id}/snapshots`),

  /**
   * 创建虚拟机快照
   */
  createSnapshot: (id: number, name: string, description?: string) =>
    request.post<ApiResponse<VmSnapshot>>(`/virtualization/vms/${id}/snapshots`, { 
      name, description 
    }),

  /**
   * 恢复虚拟机快照
   */
  restoreSnapshot: (id: number, snapshotId: string) =>
    request.post<ApiResponse<void>>(`/virtualization/vms/${id}/snapshots/${snapshotId}/restore`),

  /**
   * 删除虚拟机快照
   */
  deleteSnapshot: (id: number, snapshotId: string) =>
    request.delete<ApiResponse<void>>(`/virtualization/vms/${id}/snapshots/${snapshotId}`),

  /**
   * 获取虚拟机监控数据
   */
  getMonitorData: (id: number, timeRange?: string) =>
    request.get<ApiResponse<any>>(`/virtualization/vms/${id}/monitor`, { 
      params: { timeRange } 
    }),

  /**
   * 获取虚拟机控制台URL
   */
  getConsoleUrl: (id: number) =>
    request.get<ApiResponse<string>>(`/virtualization/vms/${id}/console`),

  /**
   * 克隆虚拟机
   */
  clone: (id: number, name: string, description?: string) =>
    request.post<ApiResponse<VmResponse>>(`/virtualization/vms/${id}/clone`, { 
      name, description 
    })
}

/**
 * 虚拟机模板API
 */
export const vmTemplateApi = {
  /**
   * 获取所有模板
   */
  getAll: () =>
    request.get<ApiResponse<VmTemplate[]>>('/virtualization/templates'),

  /**
   * 根据ID获取模板详情
   */
  getById: (id: string) =>
    request.get<ApiResponse<VmTemplate>>(`/virtualization/templates/${id}`),

  /**
   * 从虚拟机创建模板
   */
  createFromVm: (vmId: number, name: string, description?: string) =>
    request.post<ApiResponse<VmTemplate>>('/virtualization/templates', { 
      vmId, name, description 
    }),

  /**
   * 删除模板
   */
  delete: (id: string) =>
    request.delete<ApiResponse<void>>(`/virtualization/templates/${id}`)
}
