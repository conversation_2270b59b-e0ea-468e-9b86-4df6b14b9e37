/**
 * 虚拟化服务 - 节点管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse, BatchRequest } from '@/types/api'
import type {
  NodeResponse,
  NodeQueryRequest,
  NodeCreateRequest,
  NodeUpdateRequest,
  NodeStatusUpdateRequest,
  NodeResourceStats,
  NodeConnectionTestRequest
} from './types/node'

/**
 * 节点管理API
 */
export const nodeApi = {
  /**
   * 分页查询节点
   */
  getPage: (params: NodeQueryRequest) =>
    request.get<ApiResponse<PageResponse<NodeResponse>>>('/virtualization/nodes', { params }),

  /**
   * 获取所有节点（不分页）
   */
  getAll: () =>
    request.get<ApiResponse<NodeResponse[]>>('/virtualization/nodes/all'),

  /**
   * 根据ID获取节点详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<NodeResponse>>(`/virtualization/nodes/${id}`),

  /**
   * 创建节点
   */
  create: (data: NodeCreateRequest) =>
    request.post<ApiResponse<NodeResponse>>('/virtualization/nodes', data),

  /**
   * 更新节点
   */
  update: (id: number, data: NodeUpdateRequest) =>
    request.put<ApiResponse<NodeResponse>>(`/virtualization/nodes/${id}`, data),

  /**
   * 删除节点
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/virtualization/nodes/${id}`),

  /**
   * 批量删除节点
   */
  batchDelete: (data: BatchRequest) =>
    request.delete<ApiResponse<void>>('/virtualization/nodes/batch', { data }),

  /**
   * 更新节点状态
   */
  updateStatus: (id: number, data: NodeStatusUpdateRequest) =>
    request.put<ApiResponse<void>>(`/virtualization/nodes/${id}/status`, data),

  /**
   * 获取节点资源统计
   */
  getResourceStats: (id: number) =>
    request.get<ApiResponse<NodeResourceStats>>(`/virtualization/nodes/${id}/stats`),

  /**
   * 测试节点连接
   */
  testConnection: (data: NodeConnectionTestRequest) =>
    request.post<ApiResponse<boolean>>('/virtualization/nodes/test-connection', data),

  /**
   * 同步节点信息
   */
  sync: (id: number) =>
    request.post<ApiResponse<void>>(`/virtualization/nodes/${id}/sync`),

  /**
   * 获取节点监控数据
   */
  getMonitorData: (id: number, timeRange?: string) =>
    request.get<ApiResponse<any>>(`/virtualization/nodes/${id}/monitor`, { 
      params: { timeRange } 
    })
}
