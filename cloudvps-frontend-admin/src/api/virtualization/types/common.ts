/**
 * 虚拟化服务特有类型定义
 */

// 节点状态枚举
export enum NodeStatus {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
  MAINTENANCE = 'MAINTENANCE',
  ERROR = 'ERROR'
}

// 虚拟机状态枚举
export enum VmStatus {
  RUNNING = 'RUNNING',
  STOPPED = 'STOPPED',
  PAUSED = 'PAUSED',
  SUSPENDED = 'SUSPENDED',
  ERROR = 'ERROR'
}

// 虚拟机操作类型
export enum VmOperation {
  START = 'START',
  STOP = 'STOP',
  RESTART = 'RESTART',
  PAUSE = 'PAUSE',
  RESUME = 'RESUME',
  SUSPEND = 'SUSPEND',
  RESET = 'RESET'
}

// 资源类型
export enum ResourceType {
  CPU = 'CPU',
  MEMORY = 'MEMORY',
  DISK = 'DISK',
  NETWORK = 'NETWORK'
}

// 资源使用情况
export interface ResourceUsage {
  used: number
  total: number
  percentage: number
}
