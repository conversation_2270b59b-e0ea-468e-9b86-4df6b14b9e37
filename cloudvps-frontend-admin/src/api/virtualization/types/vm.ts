/**
 * 虚拟机相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { VmStatus, VmOperation, ResourceUsage } from './common'

// 虚拟机响应
export interface VmResponse extends BaseEntity {
  vmId: string
  name: string
  nodeId: number
  nodeName: string
  status: VmStatus
  cpu: number
  memory: number
  disk: number
  osType: string
  ipAddress?: string
  description?: string
  cpuUsage: ResourceUsage
  memoryUsage: ResourceUsage
  diskUsage: ResourceUsage
  uptime?: number
}

// 虚拟机查询请求
export interface VmQueryRequest extends BasePageRequest {
  nodeId?: number
  status?: VmStatus
  keyword?: string
  name?: string
  vmId?: string
  osType?: string
}

// 虚拟机创建请求
export interface VmCreateRequest {
  name: string
  nodeId: number
  cpu: number
  memory: number
  disk: number
  osType: string
  templateId?: string
  description?: string
}

// 虚拟机更新请求
export interface VmUpdateRequest {
  name?: string
  cpu?: number
  memory?: number
  disk?: number
  description?: string
}

// 虚拟机操作请求
export interface VmOperationRequest {
  operation: VmOperation
  force?: boolean
}

// 虚拟机配置
export interface VmConfig {
  cpu: number
  memory: number
  disk: number
  network: VmNetworkConfig[]
  bootOrder: string[]
}

// 虚拟机网络配置
export interface VmNetworkConfig {
  bridge: string
  model: string
  macAddress?: string
  ipAddress?: string
}

// 虚拟机快照
export interface VmSnapshot {
  id: string
  name: string
  description?: string
  createdTime: string
  size: number
}

// 虚拟机模板
export interface VmTemplate {
  id: string
  name: string
  osType: string
  cpu: number
  memory: number
  disk: number
  description?: string
}
