/**
 * 虚拟化节点相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { NodeStatus, ResourceUsage } from './common'

// 节点响应
export interface NodeResponse extends BaseEntity {
  name: string
  host: string
  port: number
  username: string
  status: NodeStatus
  description?: string
  cpuUsage: ResourceUsage
  memoryUsage: ResourceUsage
  diskUsage: ResourceUsage
  vmCount: number
  lastHeartbeat?: string
}

// 节点查询请求
export interface NodeQueryRequest extends BasePageRequest {
  status?: NodeStatus
  keyword?: string
  name?: string
  host?: string
}

// 节点创建请求
export interface NodeCreateRequest {
  name: string
  host: string
  port: number
  username: string
  password: string
  description?: string
}

// 节点更新请求
export interface NodeUpdateRequest {
  name?: string
  host?: string
  port?: number
  username?: string
  password?: string
  description?: string
}

// 节点状态更新请求
export interface NodeStatusUpdateRequest {
  status: NodeStatus
}

// 节点资源统计
export interface NodeResourceStats {
  totalCpu: number
  usedCpu: number
  totalMemory: number
  usedMemory: number
  totalDisk: number
  usedDisk: number
  vmCount: number
  runningVmCount: number
}

// 节点连接测试请求
export interface NodeConnectionTestRequest {
  host: string
  port: number
  username: string
  password: string
}
