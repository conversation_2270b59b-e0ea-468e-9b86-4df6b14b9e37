/**
 * 支付服务 - 账户管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse } from '@/types/api'
import type {
  UserAccountResponse,
  UserAccountQueryRequest,
  AccountTransactionResponse,
  AccountTransactionQueryRequest,
  AccountRechargeRequest,
  AccountDeductRequest,
  AccountFreezeRequest,
  AccountUnfreezeRequest,
  AccountTransferRequest
} from './types/account'

/**
 * 用户账户API
 */
export const userAccountApi = {
  /**
   * 分页查询用户账户
   */
  getPage: (params: UserAccountQueryRequest) =>
    request.get<ApiResponse<PageResponse<UserAccountResponse>>>('/payment/accounts', { params }),

  /**
   * 根据用户ID获取账户信息
   */
  getByUserId: (userId: number) =>
    request.get<ApiResponse<UserAccountResponse>>(`/payment/accounts/user/${userId}`),

  /**
   * 根据ID获取账户详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<UserAccountResponse>>(`/payment/accounts/${id}`),

  /**
   * 创建用户账户
   */
  create: (userId: number) =>
    request.post<ApiResponse<UserAccountResponse>>('/payment/accounts', { userId }),

  /**
   * 账户充值
   */
  recharge: (data: AccountRechargeRequest) =>
    request.post<ApiResponse<void>>('/payment/accounts/recharge', data),

  /**
   * 账户扣费
   */
  deduct: (data: AccountDeductRequest) =>
    request.post<ApiResponse<void>>('/payment/accounts/deduct', data),

  /**
   * 冻结账户金额
   */
  freeze: (data: AccountFreezeRequest) =>
    request.post<ApiResponse<void>>('/payment/accounts/freeze', data),

  /**
   * 解冻账户金额
   */
  unfreeze: (data: AccountUnfreezeRequest) =>
    request.post<ApiResponse<void>>('/payment/accounts/unfreeze', data),

  /**
   * 账户转账
   */
  transfer: (data: AccountTransferRequest) =>
    request.post<ApiResponse<void>>('/payment/accounts/transfer', data),

  /**
   * 启用/禁用账户
   */
  toggleActive: (id: number, isActive: boolean) =>
    request.put<ApiResponse<void>>(`/payment/accounts/${id}/active`, { isActive }),

  /**
   * 获取账户统计
   */
  getStatistics: () =>
    request.get<ApiResponse<any>>('/payment/accounts/statistics')
}

/**
 * 账户交易记录API
 */
export const accountTransactionApi = {
  /**
   * 分页查询账户交易记录
   */
  getPage: (params: AccountTransactionQueryRequest) =>
    request.get<ApiResponse<PageResponse<AccountTransactionResponse>>>('/payment/transactions', { params }),

  /**
   * 根据ID获取交易详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<AccountTransactionResponse>>(`/payment/transactions/${id}`),

  /**
   * 根据交易号获取交易详情
   */
  getByTransactionNo: (transactionNo: string) =>
    request.get<ApiResponse<AccountTransactionResponse>>(`/payment/transactions/no/${transactionNo}`),

  /**
   * 根据用户ID获取交易记录
   */
  getByUserId: (userId: number, params?: Partial<AccountTransactionQueryRequest>) =>
    request.get<ApiResponse<PageResponse<AccountTransactionResponse>>>(`/payment/transactions/user/${userId}`, { params }),

  /**
   * 导出交易记录
   */
  export: (params: AccountTransactionQueryRequest) =>
    request.get<Blob>('/payment/transactions/export', { 
      params, 
      responseType: 'blob' 
    })
}
