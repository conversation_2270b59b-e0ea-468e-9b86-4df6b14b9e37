/**
 * 账户相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { AccountType, TransactionType } from './common'

// 用户账户响应
export interface UserAccountResponse extends BaseEntity {
  userId: number
  username: string
  balance: number
  credit: number
  frozenAmount: number
  totalRecharge: number
  totalConsumption: number
  isActive: boolean
}

// 用户账户查询请求
export interface UserAccountQueryRequest extends BasePageRequest {
  userId?: number
  username?: string
  isActive?: boolean
  keyword?: string
}

// 账户交易记录响应
export interface AccountTransactionResponse extends BaseEntity {
  transactionNo: string
  userId: number
  username: string
  type: TransactionType
  accountType: AccountType
  amount: number
  balanceBefore: number
  balanceAfter: number
  description: string
  relatedOrderNo?: string
  relatedPaymentNo?: string
}

// 账户交易查询请求
export interface AccountTransactionQueryRequest extends BasePageRequest {
  userId?: number
  type?: TransactionType
  accountType?: AccountType
  keyword?: string
  transactionNo?: string
  username?: string
  transactionTimeStart?: string
  transactionTimeEnd?: string
}

// 账户充值请求
export interface AccountRechargeRequest {
  userId: number
  amount: number
  description?: string
}

// 账户扣费请求
export interface AccountDeductRequest {
  userId: number
  amount: number
  orderNo?: string
  description: string
}

// 账户冻结请求
export interface AccountFreezeRequest {
  userId: number
  amount: number
  description: string
}

// 账户解冻请求
export interface AccountUnfreezeRequest {
  userId: number
  amount: number
  description: string
}

// 账户转账请求
export interface AccountTransferRequest {
  fromUserId: number
  toUserId: number
  amount: number
  description: string
}
