/**
 * 支付服务特有类型定义
 */

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

// 支付方式枚举
export enum PaymentMethod {
  ALIPAY = 'ALIPAY',
  WECHAT = 'WECHAT',
  BANK_CARD = 'BANK_CARD',
  BALANCE = 'BALANCE',
  CREDIT = 'CREDIT'
}

// 退款状态枚举
export enum RefundStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// 账户类型枚举
export enum AccountType {
  BALANCE = 'BALANCE',
  CREDIT = 'CREDIT',
  FROZEN = 'FROZEN'
}

// 交易类型枚举
export enum TransactionType {
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  RECHARGE = 'RECHARGE',
  WITHDRAW = 'WITHDRAW',
  TRANSFER = 'TRANSFER',
  COMMISSION = 'COMMISSION'
}


