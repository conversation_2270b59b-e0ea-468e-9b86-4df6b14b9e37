/**
 * 支付相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { PaymentStatus, PaymentMethod, RefundStatus } from './common'

// 支付记录响应
export interface PaymentResponse extends BaseEntity {
  paymentNo: string
  orderNo: string
  userId: number
  username: string
  amount: number
  actualAmount: number
  paymentMethod: PaymentMethod
  status: PaymentStatus
  paymentTime?: string
  completedTime?: string
  failureReason?: string
  thirdPartyNo?: string
  remark?: string
}

// 支付查询请求
export interface PaymentQueryRequest extends BasePageRequest {
  userId?: number
  status?: PaymentStatus
  paymentMethod?: PaymentMethod
  keyword?: string
  paymentNo?: string
  orderNo?: string
  username?: string
  paymentTimeStart?: string
  paymentTimeEnd?: string
}

// 支付创建请求
export interface PaymentCreateRequest {
  orderNo: string
  amount: number
  paymentMethod: PaymentMethod
  returnUrl?: string
  notifyUrl?: string
  remark?: string
}

// 支付确认请求
export interface PaymentConfirmRequest {
  paymentNo: string
  thirdPartyNo?: string
  actualAmount?: number
}

// 退款记录响应
export interface RefundResponse extends BaseEntity {
  refundNo: string
  paymentNo: string
  orderNo: string
  userId: number
  username: string
  refundAmount: number
  actualRefundAmount: number
  status: RefundStatus
  refundReason: string
  refundTime?: string
  completedTime?: string
  failureReason?: string
  thirdPartyRefundNo?: string
}

// 退款查询请求
export interface RefundQueryRequest extends BasePageRequest {
  userId?: number
  status?: RefundStatus
  keyword?: string
  refundNo?: string
  paymentNo?: string
  orderNo?: string
  username?: string
  refundTimeStart?: string
  refundTimeEnd?: string
}

// 退款创建请求
export interface RefundCreateRequest {
  paymentNo: string
  refundAmount: number
  refundReason: string
}

// 支付回调数据
export interface PaymentCallback {
  paymentNo: string
  thirdPartyNo: string
  status: PaymentStatus
  amount: number
  paymentTime: string
  signature: string
}
