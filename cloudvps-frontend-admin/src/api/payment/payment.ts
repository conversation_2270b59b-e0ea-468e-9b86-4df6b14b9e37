/**
 * 支付服务 - 支付管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse, BatchRequest } from '@/types/api'
import type {
  PaymentResponse,
  PaymentQueryRequest,
  PaymentCreateRequest,
  PaymentConfirmRequest,
  RefundResponse,
  RefundQueryRequest,
  RefundCreateRequest,
  PaymentCallback
} from './types/payment'

/**
 * 支付管理API
 */
export const paymentApi = {
  /**
   * 分页查询支付记录
   */
  getPage: (params: PaymentQueryRequest) =>
    request.get<ApiResponse<PageResponse<PaymentResponse>>>('/payment/payments', { params }),

  /**
   * 根据ID获取支付详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<PaymentResponse>>(`/payment/payments/${id}`),

  /**
   * 根据支付单号获取支付详情
   */
  getByPaymentNo: (paymentNo: string) =>
    request.get<ApiResponse<PaymentResponse>>(`/payment/payments/no/${paymentNo}`),

  /**
   * 创建支付
   */
  create: (data: PaymentCreateRequest) =>
    request.post<ApiResponse<PaymentResponse>>('/payment/payments', data),

  /**
   * 确认支付
   */
  confirm: (data: PaymentConfirmRequest) =>
    request.post<ApiResponse<void>>('/payment/payments/confirm', data),

  /**
   * 取消支付
   */
  cancel: (id: number, reason?: string) =>
    request.post<ApiResponse<void>>(`/payment/payments/${id}/cancel`, { reason }),

  /**
   * 支付回调处理
   */
  callback: (data: PaymentCallback) =>
    request.post<ApiResponse<void>>('/payment/payments/callback', data),

  /**
   * 查询第三方支付状态
   */
  queryThirdPartyStatus: (paymentNo: string) =>
    request.get<ApiResponse<any>>(`/payment/payments/${paymentNo}/third-party-status`),

  /**
   * 获取支付统计
   */
  getStatistics: (startDate?: string, endDate?: string) =>
    request.get<ApiResponse<any>>('/payment/payments/statistics', { 
      params: { startDate, endDate } 
    })
}

/**
 * 退款管理API
 */
export const refundApi = {
  /**
   * 分页查询退款记录
   */
  getPage: (params: RefundQueryRequest) =>
    request.get<ApiResponse<PageResponse<RefundResponse>>>('/payment/refunds', { params }),

  /**
   * 根据ID获取退款详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<RefundResponse>>(`/payment/refunds/${id}`),

  /**
   * 根据退款单号获取退款详情
   */
  getByRefundNo: (refundNo: string) =>
    request.get<ApiResponse<RefundResponse>>(`/payment/refunds/no/${refundNo}`),

  /**
   * 创建退款
   */
  create: (data: RefundCreateRequest) =>
    request.post<ApiResponse<RefundResponse>>('/payment/refunds', data),

  /**
   * 确认退款
   */
  confirm: (id: number) =>
    request.post<ApiResponse<void>>(`/payment/refunds/${id}/confirm`),

  /**
   * 取消退款
   */
  cancel: (id: number, reason?: string) =>
    request.post<ApiResponse<void>>(`/payment/refunds/${id}/cancel`, { reason }),

  /**
   * 查询第三方退款状态
   */
  queryThirdPartyStatus: (refundNo: string) =>
    request.get<ApiResponse<any>>(`/payment/refunds/${refundNo}/third-party-status`)
}
