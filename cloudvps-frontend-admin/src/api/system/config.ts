/**
 * 系统服务 - 系统配置API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse, BatchRequest } from '@/types/api'
import type {
  SystemConfigResponse,
  SystemConfigQueryRequest,
  SystemConfigCreateRequest,
  SystemConfigUpdateRequest
} from './types/config'

/**
 * 系统配置API
 */
export const systemConfigApi = {
  /**
   * 分页查询系统配置
   */
  getPage: (params: SystemConfigQueryRequest) =>
    request.get<ApiResponse<PageResponse<SystemConfigResponse>>>('/system/configs', { params }),

  /**
   * 根据配置键获取配置
   */
  getByKey: (configKey: string) =>
    request.get<ApiResponse<SystemConfigResponse>>(`/system/configs/by-key/${configKey}`),

  /**
   * 根据配置键获取配置值
   */
  getValueByKey: (configKey: string, defaultValue?: string) =>
    request.get<ApiResponse<string>>(`/system/configs/value/${configKey}`, {
      params: { defaultValue }
    }),

  /**
   * 根据ID获取系统配置详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<SystemConfigResponse>>(`/system/configs/${id}`),

  /**
   * 创建系统配置
   */
  create: (data: SystemConfigCreateRequest) =>
    request.post<ApiResponse<SystemConfigResponse>>('/system/configs', data),

  /**
   * 更新系统配置
   */
  update: (id: number, data: SystemConfigUpdateRequest) =>
    request.put<ApiResponse<SystemConfigResponse>>(`/system/configs/${id}`, data),

  /**
   * 根据配置键更新配置值
   */
  updateValueByKey: (configKey: string, configValue: string) =>
    request.put<ApiResponse<void>>(`/system/configs/value/${configKey}`, null, {
      params: { configValue }
    }),

  /**
   * 删除系统配置
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/system/configs/${id}`),

  /**
   * 批量删除系统配置
   */
  batchDelete: (configIds: number[]) =>
    request.post<ApiResponse<void>>('/system/configs/batch-delete', configIds),

  /**
   * 根据类型获取配置列表
   */
  getByType: (configType: 'STRING' | 'INTEGER' | 'BOOLEAN' | 'JSON') =>
    request.get<ApiResponse<SystemConfigResponse[]>>('/system/configs/by-type', {
      params: { configType }
    }),

  /**
   * 获取系统配置列表
   */
  getSystemConfigs: () =>
    request.get<ApiResponse<SystemConfigResponse[]>>('/system/configs/system'),

  /**
   * 获取用户配置列表
   */
  getUserConfigs: () =>
    request.get<ApiResponse<SystemConfigResponse[]>>('/system/configs/user')
}
