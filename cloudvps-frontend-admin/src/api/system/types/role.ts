/**
 * 角色相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { RoleStatus } from './common'

// 角色响应
export interface RoleResponse extends BaseEntity {
  name: string
  code: string
  description?: string
  status: RoleStatus
  sortOrder: number
}

// 角色查询请求
export interface RoleQueryRequest extends BasePageRequest {
  status?: RoleStatus
  keyword?: string
  name?: string
  code?: string
  cleanKeyword?: string
  cleanName?: string
  cleanCode?: string
}

// 角色创建请求
export interface RoleCreateRequest {
  name: string
  code: string
  description?: string
  sortOrder?: number
}

// 角色更新请求
export interface RoleUpdateRequest {
  name?: string
  description?: string
  sortOrder?: number
}

// 角色状态更新请求
export interface RoleStatusUpdateRequest {
  status: RoleStatus
}

// 角色权限分配请求
export interface RolePermissionAssignRequest {
  permissionIds: number[]
}
