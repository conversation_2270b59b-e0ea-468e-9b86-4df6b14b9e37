/**
 * 用户相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { UserStatus } from "@/api"

// 用户响应
export interface UserResponse extends BaseEntity {
  username: string
  email: string
  phone?: string
  realName?: string
  status: UserStatus
  avatar?: string
  lastLoginTime?: string
  // 扩展字段
  nickname?: string
  gender?: 'MALE' | 'FEMALE' | 'UNKNOWN'
  birthday?: string
  description?: string
  // 关联信息
  roleIds?: number[]
  roleNames?: string[]
  departmentId?: number
  departmentName?: string
  // 统计信息
  loginCount?: number
  lastLoginIp?: string
  // 状态信息
  isLocked?: boolean
  lockReason?: string
  lockTime?: string
  passwordExpireTime?: string
}

// 用户查询请求
export interface UserQueryRequest extends BasePageRequest {
  // 基础搜索
  keyword?: string
  username?: string
  email?: string
  phone?: string
  realName?: string
  nickname?: string

  // 状态筛选
  status?: UserStatus
  isLocked?: boolean

  // 关联筛选
  roleId?: number
  departmentId?: number

  // 时间范围筛选
  startTime?: string
  endTime?: string
  lastLoginStartTime?: string
  lastLoginEndTime?: string

  // 排序
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 用户创建请求
export interface UserCreateRequest {
  username: string
  email: string
  password: string
  phone?: string
  realName?: string
  nickname?: string
  gender?: 'MALE' | 'FEMALE' | 'UNKNOWN'
  birthday?: string
  description?: string
  status?: UserStatus
  roleIds?: number[]
  departmentId?: number
  avatar?: string
}

// 用户更新请求
export interface UserUpdateRequest {
  email?: string
  phone?: string
  realName?: string
  nickname?: string
  gender?: 'MALE' | 'FEMALE' | 'UNKNOWN'
  birthday?: string
  description?: string
  status?: UserStatus
  roleIds?: number[]
  departmentId?: number
  avatar?: string
}

// 用户注册请求
export interface UserRegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
  phone?: string
  realName?: string
}

// 修改密码请求
export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
}

// 用户状态更新请求
export interface UserStatusUpdateRequest {
  status: UserStatus
}

// 用户角色分配请求
export interface UserRoleAssignRequest {
  roleIds: number[]
}

// 批量操作请求
export interface UserBatchRequest {
  ids: number[]
}

// 批量状态更新请求
export interface UserBatchStatusRequest extends UserBatchRequest {
  status: UserStatus
}

// 重置密码请求
export interface UserResetPasswordRequest {
  userId: number
  newPassword?: string // 如果不提供，系统生成随机密码
}

// 用户导入请求
export interface UserImportRequest {
  file: File
  updateExisting?: boolean // 是否更新已存在的用户
}

// 用户导出请求
export interface UserExportRequest {
  format?: 'xlsx' | 'csv'
  fields?: string[] // 导出字段
  query?: UserQueryRequest // 导出条件
}

// 用户统计响应
export interface UserStatsResponse {
  totalCount: number
  activeCount: number
  inactiveCount: number
  lockedCount: number
  todayLoginCount: number
  weekLoginCount: number
  monthLoginCount: number
}

// 角色选项
export interface RoleOption {
  id: number
  name: string
  code: string
  description?: string
}

// 部门选项
export interface DepartmentOption {
  id: number
  name: string
  code: string
  parentId?: number
  children?: DepartmentOption[]
}

// 用户表单数据
export interface UserFormData {
  id?: number
  username: string
  email: string
  password?: string
  confirmPassword?: string
  phone?: string
  realName?: string
  nickname?: string
  gender?: 'MALE' | 'FEMALE' | 'UNKNOWN'
  birthday?: string
  description?: string
  status: UserStatus
  roleIds: number[]
  departmentId?: number
  avatar?: string
}
