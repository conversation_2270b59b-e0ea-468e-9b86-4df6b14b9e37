/**
 * 字典相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'

// 字典类型响应
export interface DictTypeResponse extends BaseEntity {
  dictCode: string
  dictName: string
  description: string
  status: 'ACTIVE' | 'INACTIVE'
}

// 字典类型查询请求
export interface DictTypeQueryRequest extends BasePageRequest {
  dictCode?: string
  dictName?: string
  status?: 'ACTIVE' | 'INACTIVE'
}

// 字典类型创建请求
export interface DictTypeCreateRequest {
  dictCode: string
  dictName: string
  description?: string
  status?: 'ACTIVE' | 'INACTIVE'
}

// 字典类型更新请求
export interface DictTypeUpdateRequest {
  dictName?: string
  description?: string
  status?: 'ACTIVE' | 'INACTIVE'
}

// 字典数据响应
export interface DictDataResponse extends BaseEntity {
  dictTypeId: number
  dictLabel: string
  dictValue: string
  sortOrder: number
  status: 'ACTIVE' | 'INACTIVE'
  isDefault: boolean
  cssClass: string
  listClass: string
  remark: string
}

// 字典数据查询请求
export interface DictDataQueryRequest extends BasePageRequest {
  dictTypeId?: number
  dictLabel?: string
  dictValue?: string
  status?: 'ACTIVE' | 'INACTIVE'
  isDefault?: boolean
}

// 字典数据创建请求
export interface DictDataCreateRequest {
  dictTypeId: number
  dictLabel: string
  dictValue: string
  sortOrder?: number
  status?: 'ACTIVE' | 'INACTIVE'
  isDefault?: boolean
  cssClass?: string
  listClass?: string
  remark?: string
}

// 字典数据更新请求
export interface DictDataUpdateRequest {
  dictLabel?: string
  dictValue?: string
  sortOrder?: number
  status?: 'ACTIVE' | 'INACTIVE'
  isDefault?: boolean
  cssClass?: string
  listClass?: string
  remark?: string
}
