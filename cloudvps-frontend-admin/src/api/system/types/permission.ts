/**
 * 权限相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { ResourceType } from './common'

// 权限响应
export interface PermissionResponse extends BaseEntity {
  name: string
  code: string
  description?: string
  resourceType: ResourceType
  resourcePath?: string
  parentId?: number
  sortOrder: number
  children?: PermissionResponse[]
}

// 权限查询请求
export interface PermissionQueryRequest extends BasePageRequest {
  keyword?: string
  name?: string
  code?: string
  resourceType?: ResourceType
  parentId?: number
  cleanKeyword?: string
  cleanName?: string
  cleanCode?: string
}

// 权限创建请求
export interface PermissionCreateRequest {
  name: string
  code: string
  description?: string
  resourceType: ResourceType
  resourcePath?: string
  parentId?: number
  sortOrder?: number
}

// 权限更新请求
export interface PermissionUpdateRequest {
  name?: string
  description?: string
  resourcePath?: string
  sortOrder?: number
}
