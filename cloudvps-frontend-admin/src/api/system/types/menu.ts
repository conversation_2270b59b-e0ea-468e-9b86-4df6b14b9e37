/**
 * 菜单管理相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'

// 菜单类型枚举
export enum MenuType {
  DIRECTORY = 'DIRECTORY',
  MENU = 'MENU',
  BUTTON = 'BUTTON'
}

// 菜单响应
export interface MenuResponse extends BaseEntity {
  /** 菜单名称 */
  name: string
  /** 菜单标题（显示名称） */
  title: string
  /** 菜单图标 */
  icon?: string
  /** 菜单路径 */
  path?: string
  /** 组件路径 */
  component?: string
  /** 菜单类型 */
  type: MenuType
  /** 父菜单ID */
  parentId?: number
  /** 排序号 */
  sort: number
  /** 是否可见 */
  visible: boolean
  /** 是否启用 */
  enabled: boolean
  /** 权限标识 */
  permission?: string
  /** 菜单描述 */
  description?: string
  /** 路由参数 */
  params?: string
  /** 是否缓存 */
  cache: boolean
  /** 是否外链 */
  external: boolean
  /** 子菜单 */
  children?: MenuResponse[]
}

// 菜单查询请求
export interface MenuQueryRequest extends BasePageRequest {
  /** 菜单名称 */
  name?: string
  /** 菜单标题 */
  title?: string
  /** 菜单类型 */
  type?: MenuType
  /** 父菜单ID */
  parentId?: number
  /** 是否可见 */
  visible?: boolean
  /** 是否启用 */
  enabled?: boolean
  /** 权限标识 */
  permission?: string
}

// 菜单创建请求
export interface MenuCreateRequest {
  /** 菜单名称 */
  name: string
  /** 菜单标题（显示名称） */
  title: string
  /** 菜单图标 */
  icon?: string
  /** 菜单路径 */
  path?: string
  /** 组件路径 */
  component?: string
  /** 菜单类型 */
  type?: MenuType
  /** 父菜单ID */
  parentId?: number
  /** 排序号 */
  sort?: number
  /** 是否可见 */
  visible?: boolean
  /** 是否启用 */
  enabled?: boolean
  /** 权限标识 */
  permission?: string
  /** 菜单描述 */
  description?: string
  /** 路由参数 */
  params?: string
  /** 是否缓存 */
  cache?: boolean
  /** 是否外链 */
  external?: boolean
}

// 菜单更新请求
export interface MenuUpdateRequest {
  /** 菜单标题（显示名称） */
  title?: string
  /** 菜单图标 */
  icon?: string
  /** 菜单路径 */
  path?: string
  /** 组件路径 */
  component?: string
  /** 菜单类型 */
  type?: MenuType
  /** 父菜单ID */
  parentId?: number
  /** 排序号 */
  sort?: number
  /** 是否可见 */
  visible?: boolean
  /** 是否启用 */
  enabled?: boolean
  /** 权限标识 */
  permission?: string
  /** 菜单描述 */
  description?: string
  /** 路由参数 */
  params?: string
  /** 是否缓存 */
  cache?: boolean
  /** 是否外链 */
  external?: boolean
}

// 菜单树节点
export interface MenuTreeNode extends MenuResponse {
  /** 子节点 */
  children?: MenuTreeNode[]
  /** 是否展开 */
  expanded?: boolean
  /** 是否选中 */
  checked?: boolean
  /** 是否禁用 */
  disabled?: boolean
}

// 菜单移动请求
export interface MenuMoveRequest {
  /** 目标父菜单ID */
  targetParentId?: number
  /** 目标位置 */
  targetIndex: number
}

// 菜单排序请求
export interface MenuSortRequest {
  /** 排序号 */
  sortOrder: number
}

// 角色菜单分配请求
export interface RoleMenuAssignRequest {
  /** 菜单ID列表 */
  menuIds: number[]
}

// 菜单权限检查响应
export interface MenuPermissionCheckResponse {
  /** 是否有权限 */
  hasPermission: boolean
  /** 权限标识 */
  permission: string
}

// 菜单验证响应
export interface MenuValidationResponse {
  /** 是否存在 */
  exists: boolean
  /** 验证的值 */
  value: string
}
