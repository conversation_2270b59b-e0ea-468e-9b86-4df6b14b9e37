/**
 * 系统配置相关类型定义
 */

import type { BasePageRequest, BaseEntity } from '@/types/api'
import type { ConfigType } from "@/api"

// 系统配置响应
export interface SystemConfigResponse extends BaseEntity {
  configKey: string
  configValue: string
  configType: ConfigType
  description?: string
  isSystem: boolean
}

// 系统配置查询请求
export interface SystemConfigQueryRequest extends BasePageRequest {
  keyword?: string
  configKey?: string
  configType?: ConfigType
  isSystem?: boolean
  cleanKeyword?: string
  cleanConfigKey?: string
}

// 系统配置创建请求
export interface SystemConfigCreateRequest {
  configKey: string
  configValue: string
  configType: ConfigType
  description?: string
}

// 系统配置更新请求
export interface SystemConfigUpdateRequest {
  configValue?: string
  description?: string
}
