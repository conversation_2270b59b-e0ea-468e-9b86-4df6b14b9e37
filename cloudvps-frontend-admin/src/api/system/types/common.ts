/**
 * 系统服务特有类型定义
 */

// 状态枚举
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  LOCKED = 'LOCKED',
  DELETED = 'DELETED'
}

export enum RoleStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE'
}

export enum DictStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE'
}

export enum ConfigType {
  STRING = 'STRING',
  INTEGER = 'INTEGER',
  BOOLEAN = 'BOOLEAN',
  JSON = 'JSON'
}

export enum MenuType {
  DIRECTORY = 'DIRECTORY(目录)',
  MENU = 'MENU(菜单)',
  BUTTON = 'BUTTON(按钮)'
}

export enum ResourceType {
  MENU = 'MENU',
  BUTTON = 'BUTTON',
  API = 'API',
  DATA = 'DATA'
}

export enum LoginResult {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  LOCKED = 'LOCKED'
}


