/**
 * 认证相关类型定义
 */

import type { BaseEntity } from '@/types/api'
import type { LoginResult } from './common'

// 登录请求
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
  captchaKey?: string
  rememberMe?: boolean
}

// 登录响应
export interface LoginResponse {
  token: string
  refreshToken: string
  tokenType?: string
  expiresIn: number
  user: CurrentUser
}

// 当前用户信息
export interface CurrentUser {
  id: number
  username: string
  email: string
  phone: string
  realName: string
  avatar?: string
  roles: string[]
  permissions: string[]
  lastLoginTime?: string
}

// 刷新Token请求
export interface RefreshTokenRequest {
  refreshToken: string
}

// 验证码响应
export interface CaptchaResponse {
  image: string
  key: string
}

// 用户登录日志响应
export interface UserLoginLogResponse extends BaseEntity {
  userId: number
  username: string
  ipAddress: string
  userAgent: string
  loginResult: LoginResult
  failureReason?: string
  loginTime: string
}

// 用户登录日志查询请求
export interface UserLoginLogQueryRequest {
  current?: number
  pageSize?: number
  userId?: number
  username?: string
  ipAddress?: string
  loginResult?: LoginResult
  loginTimeStart?: string
  loginTimeEnd?: string
  cleanUsername?: string
  cleanIpAddress?: string
}
