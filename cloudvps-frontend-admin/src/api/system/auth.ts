/**
 * 系统服务 - 认证相关API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse } from '@/types/api'
import type {
  LoginRequest,
  LoginResponse,
  CurrentUser,
  UserLoginLogResponse,
  UserLoginLogQueryRequest, ChangePasswordRequest
} from "@/api";

/**
 * 认证API
 */
export const authApi = {
  /**
   * 用户登录
   */
  login: (data: LoginRequest) =>
    request.post<ApiResponse<LoginResponse>>('system/auth/login', data),

  /**
   * 用户登出
   */
  logout: () =>
    request.post<ApiResponse<void>>('system/auth/logout'),

  /**
   * 刷新Token
   */
  refreshToken: (refreshToken: string) =>
    request.post<ApiResponse<LoginResponse>>('/system/auth/refresh', null, {
      params: { refreshToken }
    }),

  /**
   * 获取当前用户信息
   */
  getCurrentUser: () =>
    request.get<ApiResponse<CurrentUser>>('system/auth/user'),

  /**
   * 修改密码
   */
  changePassword: (data: ChangePasswordRequest) =>
    request.post<ApiResponse<void>>('/system/users/change-password', data),

  /**
   * 获取个人信息
   */
  getProfile: () =>
    request.get<ApiResponse<CurrentUser>>('/system/users/profile'),

  /**
   * 更新个人信息
   */
  updateProfile: (data: any) =>
    request.put<ApiResponse<CurrentUser>>('/system/users/profile', data),

  /**
   * 获取验证码 (暂未实现)
   */
  // getCaptcha: () =>
  //   Promise.resolve({
  //     success: true,
  //     data: {
  //       image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
  //       key: 'mock-captcha-key'
  //     }
  //   } as ApiResponse<CaptchaResponse>),

  /**
   * 验证Token
   */
  verifyToken: () =>
    request.get<ApiResponse<CurrentUser>>('system/auth/verify')
}

/**
 * 用户登录日志API
 */
export const userLoginLogApi = {
  /**
   * 分页查询用户登录日志
   */
  getPage: (params: UserLoginLogQueryRequest) =>
    request.get<ApiResponse<PageResponse<UserLoginLogResponse>>>('/system/login-logs', { params }),

  /**
   * 根据ID获取用户登录日志详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<UserLoginLogResponse>>(`/system/login-logs/${id}`),

  /**
   * 批量删除用户登录日志
   */
  batchDelete: (ids: number[]) =>
    request.post<ApiResponse<void>>('/system/login-logs/batch-delete', ids),

  /**
   * 删除指定时间之前的日志
   */
  deleteLogsBefore: (beforeTime: string) =>
    request.delete<ApiResponse<void>>(`/system/login-logs/before/${beforeTime}`)
}
