/**
 * 系统服务 - 用户管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse } from '@/types/api'
import type {
  UserResponse,
  UserQueryRequest,
  UserCreateRequest,
  UserUpdateRequest,
  UserRegisterRequest,
  UserBatchRequest,
  RoleOption,
} from "@/api"

/**
 * 用户管理API
 */
export const userApi = {
  /**
   * 分页查询用户
   */
  getPage: (params: UserQueryRequest) =>
    request.get<ApiResponse<PageResponse<UserResponse>>>('/system/users', { params }),

  /**
   * 根据ID获取用户详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<UserResponse>>(`/system/users/${id}`),

  /**
   * 创建用户
   */
  create: (data: UserCreateRequest) =>
    request.post<ApiResponse<UserResponse>>('/system/users', data),

  /**
   * 更新用户
   */
  update: (id: number, data: UserUpdateRequest) =>
    request.put<ApiResponse<UserResponse>>(`/system/users/${id}`, data),

  /**
   * 删除用户
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/system/users/${id}`),

  /**
   * 批量删除用户
   */
  batchDelete: (data: UserBatchRequest) =>
    request.post<ApiResponse<void>>('/system/users/batch-delete', data.ids),

  /**
   * 更新用户状态
   */
  updateStatus: (id: number, status: string) =>
    request.put<ApiResponse<void>>(`/system/users/${id}/status`, null, {
      params: { status }
    }),

  /**
   * 分配用户角色
   */
  assignRoles: (id: number, roleIds: number[]) =>
    request.post<ApiResponse<void>>(`/system/users/${id}/roles`, roleIds),

  /**
   * 移除用户角色
   */
  removeRoles: (id: number, roleIds: number[]) =>
    request.delete<ApiResponse<void>>(`/system/users/${id}/roles`, { data: roleIds }),

  /**
   * 用户注册
   */
  register: (data: UserRegisterRequest) =>
    request.post<ApiResponse<UserResponse>>('/system/users/register', data),

  /**
   * 检查用户名是否存在
   */
  checkUsername: (username: string) =>
    request.get<ApiResponse<boolean>>('/system/users/check-username', { params: { username } }),

  /**
   * 检查邮箱是否存在
   */
  checkEmail: (email: string) =>
    request.get<ApiResponse<boolean>>('/system/users/check-email', { params: { email } }),

  // 注意：以下接口在当前后端版本中不存在，已注释
  // 如需要这些功能，请联系后端开发人员添加相应接口

  // resetPasswordBatch: (data: UserResetPasswordRequest) =>
  //   request.put<ApiResponse<void>>('/system/users/reset-password', data),

  // export: (params: UserExportRequest) =>
  //   request.get<Blob>('/system/users/export', { params, responseType: 'blob' }),

  // import: (data: UserImportRequest) => { ... },

  // getStats: () =>
  //   request.get<ApiResponse<UserStatsResponse>>('/system/users/stats'),

  /**
   * 获取角色选项列表
   */
  getRoleOptions: () =>
    request.get<ApiResponse<RoleOption[]>>('/system/roles', {
      params: {
        current: 1,
        pageSize: 1000,  // 获取所有角色用于选项
        status: 'ACTIVE' // 只获取活跃角色
      }
    }).then(response => {
      // 转换分页响应为选项列表
      if (response.data && response.data.records) {
        const options = response.data.records.map(role => ({
          label: role.roleName,
          value: role.id,
          ...role
        }))
        return { ...response, data: options }
      }
      return { ...response, data: [] }
    }),

  // 注意：以下接口在当前后端版本中不存在，已注释
  // 如需要这些功能，请联系后端开发人员添加相应接口

  // getDepartmentOptions: () =>
  //   request.get<ApiResponse<DepartmentOption[]>>('/system/departments/tree'),

  // uploadAvatar: (file: File) => { ... },

  // getOperationLogs: (userId: number, params?: { current?: number; pageSize?: number }) =>
  //   request.get<ApiResponse<PageResponse<any>>>(`/system/users/${userId}/logs`, { params }),

  // sendResetPasswordEmail: (email: string) =>
  //   request.post<ApiResponse<void>>('/system/users/send-reset-email', { email }),

  // checkPermission: (userId: number, permission: string) =>
  //   request.get<ApiResponse<boolean>>(`/system/users/${userId}/permissions/${permission}`)
}
