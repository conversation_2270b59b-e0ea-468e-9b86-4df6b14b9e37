/**
 * 系统服务 - 角色管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse } from '@/types/api'
import type {
  RoleResponse,
  RoleQueryRequest,
  RoleCreateRequest,
  RoleUpdateRequest,
} from "@/api"

/**
 * 角色管理API
 */
export const roleApi = {
  /**
   * 分页查询角色
   */
  getPage: (params: RoleQueryRequest) =>
    request.get<ApiResponse<PageResponse<RoleResponse>>>('/system/roles', { params }),

  /**
   * 获取所有启用角色
   */
  getActiveRoles: () =>
    request.get<ApiResponse<RoleResponse[]>>('/system/roles/active'),

  /**
   * 根据ID获取角色详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<RoleResponse>>(`/system/roles/${id}`),

  /**
   * 创建角色
   */
  create: (data: RoleCreateRequest) =>
    request.post<ApiResponse<RoleResponse>>('/system/roles', data),

  /**
   * 更新角色
   */
  update: (id: number, data: RoleUpdateRequest) =>
    request.put<ApiResponse<RoleResponse>>(`/system/roles/${id}`, data),

  /**
   * 删除角色
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/system/roles/${id}`),

  /**
   * 批量删除角色
   */
  batchDelete: (roleIds: number[]) =>
    request.post<ApiResponse<void>>('/system/roles/batch-delete', roleIds),

  /**
   * 更新角色状态
   */
  updateStatus: (id: number, status: 'ACTIVE' | 'INACTIVE') =>
    request.put<ApiResponse<RoleResponse>>(`/system/roles/${id}/status`, null, {
      params: { status }
    }),

  /**
   * 获取角色菜单
   */
  getMenus: (id: number) =>
    request.get<ApiResponse<number[]>>(`/system/roles/${id}/menus`),

  /**
   * 分配角色菜单
   */
  assignMenus: (id: number, menuIds: number[]) =>
    request.post<ApiResponse<void>>(`/system/roles/${id}/menus`, menuIds),

  /**
   * 检查角色代码是否存在
   */
  checkCode: (roleCode: string, excludeId?: number) =>
    request.get<ApiResponse<boolean>>('/system/roles/check-code', {
      params: { roleCode, excludeId }
    })
}
