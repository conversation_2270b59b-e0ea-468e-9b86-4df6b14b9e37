/**
 * 系统服务 - 权限管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse } from '@/types/api'
import type {
  PermissionResponse,
  PermissionQueryRequest,
  PermissionCreateRequest,
  PermissionUpdateRequest
} from "@/api"

/**
 * 权限管理API
 */
export const permissionApi = {
  /**
   * 分页查询权限
   */
  getPage: (params: PermissionQueryRequest) =>
    request.get<ApiResponse<PageResponse<PermissionResponse>>>('/system/permissions', { params }),

  /**
   * 获取所有权限列表
   */
  getAll: () =>
    request.get<ApiResponse<PermissionResponse[]>>('/system/permissions/all'),

  /**
   * 根据ID获取权限详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<PermissionResponse>>(`/system/permissions/${id}`),

  /**
   * 创建权限
   */
  create: (data: PermissionCreateRequest) =>
    request.post<ApiResponse<PermissionResponse>>('/system/permissions', data),

  /**
   * 更新权限
   */
  update: (id: number, data: PermissionUpdateRequest) =>
    request.put<ApiResponse<PermissionResponse>>(`/system/permissions/${id}`, data),

  /**
   * 删除权限
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/system/permissions/${id}`),

  /**
   * 批量删除权限
   */
  batchDelete: (permissionIds: number[]) =>
    request.post<ApiResponse<void>>('/system/permissions/batch-delete', permissionIds)
}
