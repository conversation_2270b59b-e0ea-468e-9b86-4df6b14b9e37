/**
 * 认证模拟API
 */

import type { LoginRequest, LoginResponse, CurrentUser } from '../types/auth'

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    password: 'admin123', // 实际项目中不应该明文存储密码
    email: '<EMAIL>',
    phone: '13800138000',
    realName: '系统管理员',
    avatar: 'https://tdesign.gtimg.com/site/avatar.jpg',
    roles: ['SUPER_ADMIN', 'SYSTEM_ADMIN'],
    permissions: [
      // 系统管理权限
      'SYSTEM_VIEW',
      'USER_VIEW', 'USER_CREATE', 'USER_UPDATE', 'USER_DELETE',
      'ROLE_VIEW', 'ROLE_CREATE', 'ROLE_UPDATE', 'ROLE_DELETE',
      'PERMISSION_VIEW', 'PERMISSION_CREATE', 'PERMISSION_UPDATE', 'PERMISSION_DELETE',
      'DICT_VIEW', 'DICT_CREATE', 'DICT_UPDATE', 'DICT_DELETE',
      'CONFIG_VIEW', 'CONFIG_UPDATE',
      
      // 虚拟化管理权限
      'VIRTUALIZATION_VIEW',
      'VM_VIEW', 'VM_CREATE', 'VM_UPDATE', 'VM_DELETE', 'VM_START', 'VM_STOP', 'VM_RESTART',
      'NODE_VIEW', 'NODE_CREATE', 'NODE_UPDATE', 'NODE_DELETE',
      'TEMPLATE_VIEW', 'TEMPLATE_CREATE', 'TEMPLATE_UPDATE', 'TEMPLATE_DELETE',
      
      // 订单管理权限
      'ORDER_VIEW', 'ORDER_CREATE', 'ORDER_UPDATE', 'ORDER_DELETE', 'ORDER_CANCEL',
      
      // 支付管理权限
      'PAYMENT_VIEW', 'PAYMENT_CREATE', 'PAYMENT_UPDATE', 'PAYMENT_DELETE'
    ],
    lastLoginTime: '2024-01-15 09:30:00'
  },
  {
    id: 2,
    username: 'user',
    password: 'user123',
    email: '<EMAIL>',
    phone: '13800138001',
    realName: '普通用户',
    avatar: '',
    roles: ['USER'],
    permissions: [
      'SYSTEM_VIEW',
      'USER_VIEW',
      'VIRTUALIZATION_VIEW',
      'VM_VIEW',
      'ORDER_VIEW'
    ],
    lastLoginTime: '2024-01-14 18:45:00'
  },
  {
    id: 3,
    username: 'demo',
    password: 'demo123',
    email: '<EMAIL>',
    phone: '13800138002',
    realName: '演示用户',
    avatar: '',
    roles: ['DEMO'],
    permissions: [
      'SYSTEM_VIEW',
      'USER_VIEW',
      'ROLE_VIEW',
      'DICT_VIEW',
      'VIRTUALIZATION_VIEW',
      'VM_VIEW',
      'NODE_VIEW',
      'ORDER_VIEW',
      'PAYMENT_VIEW'
    ],
    lastLoginTime: '2024-01-13 15:30:00'
  }
]

/**
 * 模拟延迟
 */
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 模拟登录
 */
export const mockLogin = async (data: LoginRequest): Promise<LoginResponse> => {
  await delay(800) // 模拟网络延迟
  
  const user = mockUsers.find(u => u.username === data.username)
  
  if (!user) {
    throw new Error('用户名不存在')
  }
  
  if (user.password !== data.password) {
    throw new Error('密码错误')
  }
  
  // 生成模拟token
  const accessToken = `mock_access_token_${user.id}_${Date.now()}`
  const refreshToken = `mock_refresh_token_${user.id}_${Date.now()}`
  
  const currentUser: CurrentUser = {
    id: user.id,
    username: user.username,
    email: user.email,
    phone: user.phone,
    realName: user.realName,
    avatar: user.avatar,
    roles: user.roles,
    permissions: user.permissions,
    lastLoginTime: user.lastLoginTime
  }
  
  return {
    accessToken,
    refreshToken,
    tokenType: 'Bearer',
    expiresIn: 7200, // 2小时
    user: currentUser
  }
}

/**
 * 模拟获取当前用户信息
 */
export const mockGetCurrentUser = async (): Promise<CurrentUser> => {
  await delay(300)
  
  // 从localStorage获取token，解析用户ID
  const token = localStorage.getItem('access_token')
  if (!token) {
    throw new Error('未登录')
  }
  
  // 简单的token解析（实际项目中应该验证token）
  const userId = token.includes('_1_') ? 1 : token.includes('_2_') ? 2 : 3
  const user = mockUsers.find(u => u.id === userId)
  
  if (!user) {
    throw new Error('用户不存在')
  }
  
  return {
    id: user.id,
    username: user.username,
    email: user.email,
    phone: user.phone,
    realName: user.realName,
    avatar: user.avatar,
    roles: user.roles,
    permissions: user.permissions,
    lastLoginTime: user.lastLoginTime
  }
}

/**
 * 模拟登出
 */
export const mockLogout = async (): Promise<void> => {
  await delay(200)
  // 清除本地存储的token
  localStorage.removeItem('access_token')
  localStorage.removeItem('refresh_token')
}

/**
 * 模拟刷新token
 */
export const mockRefreshToken = async (refreshToken: string): Promise<LoginResponse> => {
  await delay(300)
  
  // 简单的token解析
  const userId = refreshToken.includes('_1_') ? 1 : refreshToken.includes('_2_') ? 2 : 3
  const user = mockUsers.find(u => u.id === userId)
  
  if (!user) {
    throw new Error('刷新token无效')
  }
  
  // 生成新的token
  const accessToken = `mock_access_token_${user.id}_${Date.now()}`
  const newRefreshToken = `mock_refresh_token_${user.id}_${Date.now()}`
  
  const currentUser: CurrentUser = {
    id: user.id,
    username: user.username,
    email: user.email,
    phone: user.phone,
    realName: user.realName,
    avatar: user.avatar,
    roles: user.roles,
    permissions: user.permissions,
    lastLoginTime: user.lastLoginTime
  }
  
  return {
    accessToken,
    refreshToken: newRefreshToken,
    tokenType: 'Bearer',
    expiresIn: 7200,
    user: currentUser
  }
}

/**
 * 模拟验证token
 */
export const mockVerifyToken = async (): Promise<CurrentUser> => {
  return mockGetCurrentUser()
}

/**
 * 获取验证码
 */
export const mockGetCaptcha = async () => {
  await delay(200)
  
  return {
    image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    key: 'mock_captcha_key_' + Date.now()
  }
}
