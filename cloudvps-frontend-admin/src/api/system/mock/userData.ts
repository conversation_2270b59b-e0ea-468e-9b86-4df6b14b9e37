/**
 * 用户管理模拟数据
 */

import type { UserResponse, RoleOption, DepartmentOption, UserStatsResponse } from '../types/user'

// 模拟用户数据
export const mockUsers: UserResponse[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    phone: '13800138000',
    realName: '系统管理员',
    nickname: 'Admin',
    gender: 'MALE',
    status: 'ACTIVE',
    avatar: 'https://tdesign.gtimg.com/site/avatar.jpg',
    description: '系统超级管理员账户',
    roleIds: [1, 2],
    roleNames: ['超级管理员', '系统管理员'],
    departmentId: 1,
    departmentName: '技术部',
    loginCount: 1250,
    lastLoginTime: '2024-01-15 09:30:00',
    lastLoginIp: '*************',
    isLocked: false,
    createdTime: '2023-01-01 00:00:00',
    updatedTime: '2024-01-15 09:30:00'
  },
  {
    id: 2,
    username: 'z<PERSON><PERSON>',
    email: 'z<PERSON><PERSON>@cloudvps.com',
    phone: '13800138001',
    realName: '张三',
    nickname: '小张',
    gender: 'MALE',
    birthday: '1990-05-15',
    status: 'ACTIVE',
    description: '前端开发工程师',
    roleIds: [3],
    roleNames: ['开发人员'],
    departmentId: 11,
    departmentName: '前端组',
    loginCount: 856,
    lastLoginTime: '2024-01-14 18:45:00',
    lastLoginIp: '*************',
    isLocked: false,
    createdTime: '2023-03-15 10:00:00',
    updatedTime: '2024-01-14 18:45:00'
  },
  {
    id: 3,
    username: 'lisi',
    email: '<EMAIL>',
    phone: '13800138002',
    realName: '李四',
    nickname: '小李',
    gender: 'FEMALE',
    birthday: '1992-08-20',
    status: 'ACTIVE',
    description: '后端开发工程师',
    roleIds: [3],
    roleNames: ['开发人员'],
    departmentId: 12,
    departmentName: '后端组',
    loginCount: 642,
    lastLoginTime: '2024-01-14 17:20:00',
    lastLoginIp: '*************',
    isLocked: false,
    createdTime: '2023-04-20 14:30:00',
    updatedTime: '2024-01-14 17:20:00'
  },
  {
    id: 4,
    username: 'wangwu',
    email: '<EMAIL>',
    phone: '13800138003',
    realName: '王五',
    nickname: '老王',
    gender: 'MALE',
    birthday: '1988-12-10',
    status: 'INACTIVE',
    description: '产品经理',
    roleIds: [4],
    roleNames: ['产品经理'],
    departmentId: 21,
    departmentName: '产品组',
    loginCount: 324,
    lastLoginTime: '2024-01-10 16:00:00',
    lastLoginIp: '*************',
    isLocked: false,
    createdTime: '2023-06-01 09:00:00',
    updatedTime: '2024-01-10 16:00:00'
  },
  {
    id: 5,
    username: 'zhaoliu',
    email: '<EMAIL>',
    phone: '13800138004',
    realName: '赵六',
    nickname: '小赵',
    gender: 'FEMALE',
    birthday: '1995-03-25',
    status: 'ACTIVE',
    description: 'UI设计师',
    roleIds: [5],
    roleNames: ['设计师'],
    departmentId: 22,
    departmentName: '设计组',
    loginCount: 198,
    lastLoginTime: '2024-01-13 15:30:00',
    lastLoginIp: '*************',
    isLocked: false,
    createdTime: '2023-08-15 11:00:00',
    updatedTime: '2024-01-13 15:30:00'
  },
  {
    id: 6,
    username: 'sunqi',
    email: '<EMAIL>',
    phone: '13800138005',
    realName: '孙七',
    nickname: '阿七',
    gender: 'MALE',
    status: 'LOCKED',
    description: '测试工程师',
    roleIds: [6],
    roleNames: ['测试人员'],
    departmentId: 13,
    departmentName: '测试组',
    loginCount: 89,
    lastLoginTime: '2024-01-05 10:15:00',
    lastLoginIp: '*************',
    isLocked: true,
    lockReason: '连续登录失败次数过多',
    lockTime: '2024-01-05 10:20:00',
    createdTime: '2023-10-01 16:00:00',
    updatedTime: '2024-01-05 10:20:00'
  },
  {
    id: 7,
    username: 'zhouba',
    email: '<EMAIL>',
    phone: '13800138006',
    realName: '周八',
    nickname: '小周',
    gender: 'FEMALE',
    birthday: '1993-07-08',
    status: 'ACTIVE',
    description: '运维工程师',
    roleIds: [7],
    roleNames: ['运维人员'],
    departmentId: 3,
    departmentName: '运维部',
    loginCount: 445,
    lastLoginTime: '2024-01-14 20:00:00',
    lastLoginIp: '*************',
    isLocked: false,
    createdTime: '2023-05-10 13:30:00',
    updatedTime: '2024-01-14 20:00:00'
  },
  {
    id: 8,
    username: 'wujiu',
    email: '<EMAIL>',
    phone: '13800138007',
    realName: '吴九',
    nickname: '阿九',
    gender: 'MALE',
    birthday: '1991-11-30',
    status: 'ACTIVE',
    description: '销售经理',
    roleIds: [8],
    roleNames: ['销售人员'],
    departmentId: 4,
    departmentName: '销售部',
    loginCount: 267,
    lastLoginTime: '2024-01-12 14:45:00',
    lastLoginIp: '*************',
    isLocked: false,
    createdTime: '2023-07-20 10:15:00',
    updatedTime: '2024-01-12 14:45:00'
  }
]

// 模拟角色选项
export const mockRoleOptions: RoleOption[] = [
  { id: 1, name: '超级管理员', code: 'SUPER_ADMIN', description: '拥有系统所有权限' },
  { id: 2, name: '系统管理员', code: 'SYSTEM_ADMIN', description: '系统管理相关权限' },
  { id: 3, name: '开发人员', code: 'DEVELOPER', description: '开发相关权限' },
  { id: 4, name: '产品经理', code: 'PRODUCT_MANAGER', description: '产品管理权限' },
  { id: 5, name: '设计师', code: 'DESIGNER', description: '设计相关权限' },
  { id: 6, name: '测试人员', code: 'TESTER', description: '测试相关权限' },
  { id: 7, name: '运维人员', code: 'DEVOPS', description: '运维相关权限' },
  { id: 8, name: '销售人员', code: 'SALES', description: '销售相关权限' },
  { id: 9, name: '普通用户', code: 'USER', description: '基础用户权限' }
]

// 模拟部门选项（树形结构）
export const mockDepartmentOptions: DepartmentOption[] = [
  {
    id: 1,
    name: '技术部',
    code: 'TECH',
    children: [
      { id: 11, name: '前端组', code: 'FRONTEND', parentId: 1 },
      { id: 12, name: '后端组', code: 'BACKEND', parentId: 1 },
      { id: 13, name: '测试组', code: 'QA', parentId: 1 }
    ]
  },
  {
    id: 2,
    name: '产品部',
    code: 'PRODUCT',
    children: [
      { id: 21, name: '产品组', code: 'PRODUCT_TEAM', parentId: 2 },
      { id: 22, name: '设计组', code: 'DESIGN_TEAM', parentId: 2 }
    ]
  },
  {
    id: 3,
    name: '运维部',
    code: 'DEVOPS',
    children: []
  },
  {
    id: 4,
    name: '销售部',
    code: 'SALES',
    children: []
  },
  {
    id: 5,
    name: '人事部',
    code: 'HR',
    children: []
  }
]

// 模拟用户统计数据
export const mockUserStats: UserStatsResponse = {
  totalCount: 156,
  activeCount: 142,
  inactiveCount: 12,
  lockedCount: 2,
  todayLoginCount: 89,
  weekLoginCount: 134,
  monthLoginCount: 156
}

// 生成更多模拟用户数据
export const generateMockUsers = (count: number): UserResponse[] => {
  const users: UserResponse[] = [...mockUsers]
  const names = ['陈', '刘', '杨', '黄', '吴', '徐', '朱', '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于']
  const firstNames = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋']
  
  for (let i = mockUsers.length; i < count; i++) {
    const lastName = names[Math.floor(Math.random() * names.length)]
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
    const realName = lastName + firstName
    const username = `user${i.toString().padStart(3, '0')}`
    
    users.push({
      id: i + 1,
      username,
      email: `${username}@cloudvps.com`,
      phone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      realName,
      nickname: `小${lastName}`,
      gender: Math.random() > 0.5 ? 'MALE' : 'FEMALE',
      status: Math.random() > 0.1 ? 'ACTIVE' : (Math.random() > 0.5 ? 'INACTIVE' : 'LOCKED'),
      description: `${realName}的个人描述`,
      roleIds: [Math.floor(Math.random() * 9) + 1],
      roleNames: [mockRoleOptions[Math.floor(Math.random() * 9)].name],
      departmentId: Math.floor(Math.random() * 5) + 1,
      departmentName: mockDepartmentOptions[Math.floor(Math.random() * 5)].name,
      loginCount: Math.floor(Math.random() * 1000),
      lastLoginTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      lastLoginIp: `192.168.1.${Math.floor(Math.random() * 254) + 1}`,
      isLocked: Math.random() > 0.95,
      createdTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      updatedTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
    })
  }
  
  return users
}
