/**
 * 字典数据模拟
 */

import type { DictDataResponse } from '../types/dict'

// 模拟字典数据
export const mockDictData: Record<string, DictDataResponse[]> = {
  // 用户状态
  user_status: [
    {
      id: 1,
      typeId: 1,
      label: '正常',
      value: 'ACTIVE',
      color: '#52c41a',
      sort: 1,
      status: 'ACTIVE',
      description: '用户状态正常，可以正常使用系统',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 2,
      typeId: 1,
      label: '禁用',
      value: 'INACTIVE',
      color: '#faad14',
      sort: 2,
      status: 'ACTIVE',
      description: '用户被禁用，无法登录系统',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 3,
      typeId: 1,
      label: '锁定',
      value: 'LOCKED',
      color: '#ff4d4f',
      sort: 3,
      status: 'ACTIVE',
      description: '用户被锁定，需要管理员解锁',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    }
  ],

  // 角色状态
  role_status: [
    {
      id: 4,
      typeId: 2,
      label: '正常',
      value: 'ACTIVE',
      color: '#52c41a',
      sort: 1,
      status: 'ACTIVE',
      description: '角色状态正常',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 5,
      typeId: 2,
      label: '禁用',
      value: 'INACTIVE',
      color: '#faad14',
      sort: 2,
      status: 'ACTIVE',
      description: '角色被禁用',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    }
  ],

  // 字典状态
  dict_status: [
    {
      id: 6,
      typeId: 3,
      label: '正常',
      value: 'ACTIVE',
      color: '#52c41a',
      sort: 1,
      status: 'ACTIVE',
      description: '字典状态正常',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 7,
      typeId: 3,
      label: '禁用',
      value: 'INACTIVE',
      color: '#faad14',
      sort: 2,
      status: 'ACTIVE',
      description: '字典被禁用',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    }
  ],

  // 性别
  gender: [
    {
      id: 8,
      typeId: 4,
      label: '男',
      value: 'MALE',
      color: '#1890ff',
      sort: 1,
      status: 'ACTIVE',
      description: '男性',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 9,
      typeId: 4,
      label: '女',
      value: 'FEMALE',
      color: '#eb2f96',
      sort: 2,
      status: 'ACTIVE',
      description: '女性',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 10,
      typeId: 4,
      label: '未知',
      value: 'UNKNOWN',
      color: '#8c8c8c',
      sort: 3,
      status: 'ACTIVE',
      description: '性别未知',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    }
  ],

  // 虚拟机状态
  vm_status: [
    {
      id: 11,
      typeId: 5,
      label: '运行中',
      value: 'RUNNING',
      color: '#52c41a',
      sort: 1,
      status: 'ACTIVE',
      description: '虚拟机正在运行',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 12,
      typeId: 5,
      label: '已停止',
      value: 'STOPPED',
      color: '#8c8c8c',
      sort: 2,
      status: 'ACTIVE',
      description: '虚拟机已停止',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 13,
      typeId: 5,
      label: '暂停',
      value: 'PAUSED',
      color: '#faad14',
      sort: 3,
      status: 'ACTIVE',
      description: '虚拟机已暂停',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 14,
      typeId: 5,
      label: '错误',
      value: 'ERROR',
      color: '#ff4d4f',
      sort: 4,
      status: 'ACTIVE',
      description: '虚拟机出现错误',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    }
  ],

  // 订单状态
  order_status: [
    {
      id: 15,
      typeId: 6,
      label: '待支付',
      value: 'PENDING',
      color: '#faad14',
      sort: 1,
      status: 'ACTIVE',
      description: '订单待支付',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 16,
      typeId: 6,
      label: '已支付',
      value: 'PAID',
      color: '#52c41a',
      sort: 2,
      status: 'ACTIVE',
      description: '订单已支付',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 17,
      typeId: 6,
      label: '已取消',
      value: 'CANCELLED',
      color: '#8c8c8c',
      sort: 3,
      status: 'ACTIVE',
      description: '订单已取消',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 18,
      typeId: 6,
      label: '已退款',
      value: 'REFUNDED',
      color: '#ff4d4f',
      sort: 4,
      status: 'ACTIVE',
      description: '订单已退款',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    }
  ],

  // 支付状态
  payment_status: [
    {
      id: 19,
      typeId: 7,
      label: '待支付',
      value: 'PENDING',
      color: '#faad14',
      sort: 1,
      status: 'ACTIVE',
      description: '支付待处理',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 20,
      typeId: 7,
      label: '支付成功',
      value: 'SUCCESS',
      color: '#52c41a',
      sort: 2,
      status: 'ACTIVE',
      description: '支付成功',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    },
    {
      id: 21,
      typeId: 7,
      label: '支付失败',
      value: 'FAILED',
      color: '#ff4d4f',
      sort: 3,
      status: 'ACTIVE',
      description: '支付失败',
      createdTime: '2024-01-01 00:00:00',
      updatedTime: '2024-01-01 00:00:00'
    }
  ]
}

/**
 * 模拟获取字典数据
 */
export const mockGetDictDataByTypeCode = async (typeCode: string): Promise<DictDataResponse[]> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 200))
  
  const data = mockDictData[typeCode] || []
  return data.filter(item => item.status === 'ACTIVE').sort((a, b) => a.sort - b.sort)
}
