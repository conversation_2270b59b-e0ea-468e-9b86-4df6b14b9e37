/**
 * 用户管理模拟API服务
 */

import type { 
  UserResponse, 
  UserQueryRequest, 
  UserCreateRequest, 
  UserUpdateRequest,
  UserBatchRequest,
  UserBatchStatusRequest,
  UserStatsResponse,
  RoleOption,
  DepartmentOption,
  UserFormData
} from '../types/user'
import type { PageResponse } from '@/types/api'
import { 
  mockUsers, 
  mockRoleOptions, 
  mockDepartmentOptions, 
  mockUserStats,
  generateMockUsers 
} from './userData'

// 生成大量模拟数据
const allMockUsers = generateMockUsers(156)

/**
 * 模拟延迟
 */
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 模拟分页查询用户
 */
export const mockGetUserPage = async (params: UserQueryRequest): Promise<PageResponse<UserResponse>> => {
  await delay(800) // 模拟网络延迟
  
  let filteredUsers = [...allMockUsers]
  
  // 关键词搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredUsers = filteredUsers.filter(user => 
      user.username.toLowerCase().includes(keyword) ||
      user.email.toLowerCase().includes(keyword) ||
      user.realName?.toLowerCase().includes(keyword) ||
      user.phone?.includes(keyword)
    )
  }
  
  // 用户名搜索
  if (params.username) {
    filteredUsers = filteredUsers.filter(user => 
      user.username.toLowerCase().includes(params.username!.toLowerCase())
    )
  }
  
  // 邮箱搜索
  if (params.email) {
    filteredUsers = filteredUsers.filter(user => 
      user.email.toLowerCase().includes(params.email!.toLowerCase())
    )
  }
  
  // 手机号搜索
  if (params.phone) {
    filteredUsers = filteredUsers.filter(user => 
      user.phone?.includes(params.phone!)
    )
  }
  
  // 真实姓名搜索
  if (params.realName) {
    filteredUsers = filteredUsers.filter(user => 
      user.realName?.toLowerCase().includes(params.realName!.toLowerCase())
    )
  }
  
  // 状态筛选
  if (params.status) {
    filteredUsers = filteredUsers.filter(user => user.status === params.status)
  }
  
  // 锁定状态筛选
  if (params.isLocked !== undefined) {
    filteredUsers = filteredUsers.filter(user => user.isLocked === params.isLocked)
  }
  
  // 角色筛选
  if (params.roleId) {
    filteredUsers = filteredUsers.filter(user => 
      user.roleIds?.includes(params.roleId!)
    )
  }
  
  // 部门筛选
  if (params.departmentId) {
    filteredUsers = filteredUsers.filter(user => user.departmentId === params.departmentId)
  }
  
  // 时间范围筛选
  if (params.startTime && params.endTime) {
    filteredUsers = filteredUsers.filter(user => {
      const createdTime = new Date(user.createdTime!)
      const startTime = new Date(params.startTime!)
      const endTime = new Date(params.endTime!)
      return createdTime >= startTime && createdTime <= endTime
    })
  }
  
  // 排序
  if (params.sortField && params.sortOrder) {
    filteredUsers.sort((a, b) => {
      const aValue = (a as any)[params.sortField!]
      const bValue = (b as any)[params.sortField!]
      
      if (params.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }
  
  // 分页
  const current = params.current || 1
  const pageSize = params.pageSize || 10
  const start = (current - 1) * pageSize
  const end = start + pageSize
  
  const list = filteredUsers.slice(start, end)
  const total = filteredUsers.length
  
  return {
    list,
    total,
    current,
    pageSize,
    pages: Math.ceil(total / pageSize)
  }
}

/**
 * 模拟获取用户详情
 */
export const mockGetUserById = async (id: number): Promise<UserResponse> => {
  await delay(300)
  
  const user = allMockUsers.find(u => u.id === id)
  if (!user) {
    throw new Error('用户不存在')
  }
  
  return user
}

/**
 * 模拟创建用户
 */
export const mockCreateUser = async (data: UserCreateRequest): Promise<UserResponse> => {
  await delay(1000)
  
  // 检查用户名是否已存在
  if (allMockUsers.some(u => u.username === data.username)) {
    throw new Error('用户名已存在')
  }
  
  // 检查邮箱是否已存在
  if (allMockUsers.some(u => u.email === data.email)) {
    throw new Error('邮箱已存在')
  }
  
  const newUser: UserResponse = {
    id: Math.max(...allMockUsers.map(u => u.id)) + 1,
    username: data.username,
    email: data.email,
    phone: data.phone,
    realName: data.realName,
    nickname: data.nickname,
    gender: data.gender,
    birthday: data.birthday,
    description: data.description,
    status: data.status || 'ACTIVE',
    roleIds: data.roleIds || [],
    roleNames: data.roleIds?.map(id => mockRoleOptions.find(r => r.id === id)?.name).filter(Boolean) as string[] || [],
    departmentId: data.departmentId,
    departmentName: data.departmentId ? mockDepartmentOptions.find(d => d.id === data.departmentId)?.name : undefined,
    avatar: data.avatar,
    loginCount: 0,
    isLocked: false,
    createdTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    updatedTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
  }
  
  allMockUsers.push(newUser)
  return newUser
}

/**
 * 模拟更新用户
 */
export const mockUpdateUser = async (id: number, data: UserUpdateRequest): Promise<UserResponse> => {
  await delay(800)
  
  const userIndex = allMockUsers.findIndex(u => u.id === id)
  if (userIndex === -1) {
    throw new Error('用户不存在')
  }
  
  // 检查邮箱是否已被其他用户使用
  if (data.email && allMockUsers.some(u => u.id !== id && u.email === data.email)) {
    throw new Error('邮箱已被其他用户使用')
  }
  
  const updatedUser = {
    ...allMockUsers[userIndex],
    ...data,
    roleNames: data.roleIds?.map(id => mockRoleOptions.find(r => r.id === id)?.name).filter(Boolean) as string[] || allMockUsers[userIndex].roleNames,
    departmentName: data.departmentId ? mockDepartmentOptions.find(d => d.id === data.departmentId)?.name : allMockUsers[userIndex].departmentName,
    updatedTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
  }
  
  allMockUsers[userIndex] = updatedUser
  return updatedUser
}

/**
 * 模拟删除用户
 */
export const mockDeleteUser = async (id: number): Promise<void> => {
  await delay(500)
  
  const userIndex = allMockUsers.findIndex(u => u.id === id)
  if (userIndex === -1) {
    throw new Error('用户不存在')
  }
  
  allMockUsers.splice(userIndex, 1)
}

/**
 * 模拟批量删除用户
 */
export const mockBatchDeleteUsers = async (data: UserBatchRequest): Promise<void> => {
  await delay(1000)
  
  data.ids.forEach(id => {
    const userIndex = allMockUsers.findIndex(u => u.id === id)
    if (userIndex !== -1) {
      allMockUsers.splice(userIndex, 1)
    }
  })
}

/**
 * 模拟批量更新用户状态
 */
export const mockBatchUpdateUserStatus = async (data: UserBatchStatusRequest): Promise<void> => {
  await delay(800)
  
  data.ids.forEach(id => {
    const user = allMockUsers.find(u => u.id === id)
    if (user) {
      user.status = data.status
      user.updatedTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
    }
  })
}

/**
 * 模拟获取角色选项
 */
export const mockGetRoleOptions = async (): Promise<RoleOption[]> => {
  await delay(200)
  return mockRoleOptions
}

/**
 * 模拟获取部门选项
 */
export const mockGetDepartmentOptions = async (): Promise<DepartmentOption[]> => {
  await delay(200)
  return mockDepartmentOptions
}

/**
 * 模拟获取用户统计
 */
export const mockGetUserStats = async (): Promise<UserStatsResponse> => {
  await delay(300)
  
  // 基于实际数据计算统计
  const activeCount = allMockUsers.filter(u => u.status === 'ACTIVE').length
  const inactiveCount = allMockUsers.filter(u => u.status === 'INACTIVE').length
  const lockedCount = allMockUsers.filter(u => u.isLocked).length
  
  return {
    ...mockUserStats,
    totalCount: allMockUsers.length,
    activeCount,
    inactiveCount,
    lockedCount
  }
}

/**
 * 模拟重置密码
 */
export const mockResetPassword = async (id: number): Promise<void> => {
  await delay(600)
  
  const user = allMockUsers.find(u => u.id === id)
  if (!user) {
    throw new Error('用户不存在')
  }
  
  // 模拟重置密码操作
  user.updatedTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
}

/**
 * 模拟检查用户名是否存在
 */
export const mockCheckUsername = async (username: string): Promise<boolean> => {
  await delay(300)
  return allMockUsers.some(u => u.username === username)
}

/**
 * 模拟检查邮箱是否存在
 */
export const mockCheckEmail = async (email: string): Promise<boolean> => {
  await delay(300)
  return allMockUsers.some(u => u.email === email)
}
