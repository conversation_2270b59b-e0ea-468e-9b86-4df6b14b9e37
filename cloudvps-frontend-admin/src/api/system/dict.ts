/**
 * 系统服务 - 字典管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse } from '@/types/api'
import type {
  DictTypeResponse,
  DictTypeQueryRequest,
  DictTypeCreateRequest,
  DictTypeUpdateRequest,
  DictDataResponse,
  DictDataQueryRequest,
  DictDataCreateRequest,
  DictDataUpdateRequest
} from "@/api"

/**
 * 字典类型API
 */
export const dictTypeApi = {
  /**
   * 分页查询字典类型
   */
  getPage: (params: DictTypeQueryRequest) =>
    request.get<ApiResponse<PageResponse<DictTypeResponse>>>('/system/dict-types', { params }),

  // TODO: 建议后端添加接口 - /system/dict-types/all: 获取所有字典类型（不分页），用于下拉选择
  // getAll: () =>
  //   request.get<ApiResponse<DictTypeResponse[]>>('/system/dict-types/all'),

  /**
   * 根据ID获取字典类型详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<DictTypeResponse>>(`/system/dict-types/${id}`),

  /**
   * 创建字典类型
   */
  create: (data: DictTypeCreateRequest) =>
    request.post<ApiResponse<DictTypeResponse>>('/system/dict-types', data),

  /**
   * 更新字典类型
   */
  update: (id: number, data: DictTypeUpdateRequest) =>
    request.put<ApiResponse<DictTypeResponse>>(`/system/dict-types/${id}`, data),

  /**
   * 删除字典类型
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/system/dict-types/${id}`),

  /**
   * 批量删除字典类型
   */
  batchDelete: (ids: number[]) =>
    request.post<ApiResponse<void>>('/system/dict-types/batch-delete', ids),

  /**
   * 更新字典类型状态
   */
  updateStatus: (id: number, status: 'ACTIVE' | 'INACTIVE') =>
    request.put<ApiResponse<DictTypeResponse>>(`/system/dict-types/${id}/status`, null, {
      params: { status }
    }),

  // TODO: 建议后端添加接口 - /system/dict-types/{id}/sort: 更新字典类型排序
  // updateSort: (id: number, data: SortUpdateRequest) =>
  //   request.put<ApiResponse<void>>(`/system/dict-types/${id}/sort`, data),

  // TODO: 建议后端添加接口 - /system/dict-types/check-code: 检查字典类型代码是否存在
  // checkCode: (code: string, excludeId?: number) =>
  //   request.get<ApiResponse<boolean>>('/system/dict-types/check-code', {
  //     params: { code, excludeId }
  //   })
}

/**
 * 字典数据API
 */
export const dictDataApi = {
  /**
   * 分页查询字典数据
   */
  getPage: (params: DictDataQueryRequest) =>
    request.get<ApiResponse<PageResponse<DictDataResponse>>>('/system/dict-data', { params }),

  // TODO: 建议后端添加接口 - /system/dict-data/type/{typeCode}: 根据类型代码获取字典数据
  // getByTypeCode: (typeCode: string) =>
  //   request.get<ApiResponse<DictDataResponse[]>>(`/system/dict-data/type/${typeCode}`),

  /**
   * 根据ID获取字典数据详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<DictDataResponse>>(`/system/dict-data/${id}`),

  /**
   * 创建字典数据
   */
  create: (data: DictDataCreateRequest) =>
    request.post<ApiResponse<DictDataResponse>>('/system/dict-data', data),

  /**
   * 更新字典数据
   */
  update: (id: number, data: DictDataUpdateRequest) =>
    request.put<ApiResponse<DictDataResponse>>(`/system/dict-data/${id}`, data),

  /**
   * 删除字典数据
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/system/dict-data/${id}`),

  /**
   * 批量删除字典数据
   */
  batchDelete: (ids: number[]) =>
    request.post<ApiResponse<void>>('/system/dict-data/batch-delete', ids),

  /**
   * 更新字典数据状态
   */
  updateStatus: (id: number, status: 'ACTIVE' | 'INACTIVE') =>
    request.put<ApiResponse<DictDataResponse>>(`/system/dict-data/${id}/status`, null, {
      params: { status }
    }),

  // TODO: 建议后端添加接口 - /system/dict-data/{id}/sort: 更新字典数据排序
  // updateSort: (id: number, data: SortUpdateRequest) =>
  //   request.put<ApiResponse<void>>(`/system/dict-data/${id}/sort`, data),

  // TODO: 建议后端添加接口 - /system/dict-data/check-value: 检查字典数据值是否存在
  // checkValue: (typeId: number, value: string, excludeId?: number) =>
  //   request.get<ApiResponse<boolean>>('/system/dict-data/check-value', {
  //     params: { typeId, value, excludeId }
  //   })
}
