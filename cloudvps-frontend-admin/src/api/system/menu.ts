/**
 * 菜单管理API
 */

import { request } from '@/utils/request'
import type { ApiResponse, PageResponse, BatchRequest } from '@/types/api'
import type {
  MenuResponse,
  MenuQueryRequest,
  MenuCreateRequest,
  MenuUpdateRequest,
  MenuTreeNode,
  MenuType
} from './types/menu'

// 临时模拟数据
const mockMenuTreeData: MenuTreeNode[] = [
  {
    id: 1,
    name: 'system',
    title: '系统管理',
    icon: 'setting',
    type: 'DIRECTORY' as MenuType,
    sort: 1,
    visible: true,
    enabled: true,
    cache: false,
    external: false,
    children: [
      {
        id: 2,
        name: 'system_user',
        title: '用户管理',
        icon: 'user',
        path: '/system/users',
        component: 'system/users/index',
        type: 'MENU' as MenuType,
        parentId: 1,
        sort: 1,
        visible: true,
        enabled: true,
        cache: true,
        external: false,
        permission: 'SYSTEM_USER_VIEW'
      },
      {
        id: 3,
        name: 'system_dict',
        title: '字典管理',
        icon: 'books',
        path: '/system/dict',
        component: 'system/dict/index',
        type: 'MENU' as MenuType,
        parentId: 1,
        sort: 2,
        visible: true,
        enabled: true,
        cache: true,
        external: false,
        permission: 'SYSTEM_DICT_VIEW'
      },
      {
        id: 4,
        name: 'system_menu',
        title: '菜单管理',
        icon: 'menu-fold',
        path: '/system/menus',
        component: 'system/menus/index',
        type: 'MENU' as MenuType,
        parentId: 1,
        sort: 3,
        visible: true,
        enabled: true,
        cache: true,
        external: false,
        permission: 'SYSTEM_MENU_VIEW'
      }
    ]
  },
  {
    id: 10,
    name: 'virtualization',
    title: '虚拟化管理',
    icon: 'server',
    type: 'DIRECTORY' as MenuType,
    sort: 2,
    visible: true,
    enabled: true,
    cache: false,
    external: false,
    children: [
      {
        id: 11,
        name: 'virtualization_nodes',
        title: 'PVE节点',
        icon: 'server',
        path: '/virtualization/nodes',
        component: 'virtualization/nodes/index',
        type: 'MENU' as MenuType,
        parentId: 10,
        sort: 1,
        visible: true,
        enabled: true,
        cache: true,
        external: false,
        permission: 'VIRTUALIZATION_VIEW'
      }
    ]
  }
]

/**
 * 菜单管理API
 */
export const menuApi = {
  /**
   * 分页查询菜单
   */
  getPage: (params: MenuQueryRequest) =>
    request.get<ApiResponse<PageResponse<MenuResponse>>>('/system/menus', { params }),

  /**
   * 获取菜单树形结构
   */
  getTree: () => {
    // 临时返回模拟数据，确保页面有内容显示
    console.log('🌳 返回模拟菜单树数据')
    return Promise.resolve({
      data: {
        data: mockMenuTreeData,
        code: 200,
        message: 'success'
      }
    } as { data: ApiResponse<MenuTreeNode[]> })
    
    // 真实API调用（注释掉避免报错）
    // return request.get<ApiResponse<MenuTreeNode[]>>('/system/menus/tree')
  },

  /**
   * 根据ID获取菜单详情
   */
  getById: (id: number) =>
    request.get<ApiResponse<MenuResponse>>(`/system/menus/${id}`),

  /**
   * 创建菜单
   */
  create: (data: MenuCreateRequest) =>
    request.post<ApiResponse<MenuResponse>>('/system/menus', data),

  /**
   * 更新菜单
   */
  update: (id: number, data: MenuUpdateRequest) =>
    request.put<ApiResponse<MenuResponse>>(`/system/menus/${id}`, data),

  /**
   * 删除菜单
   */
  delete: (id: number) =>
    request.delete<ApiResponse<void>>(`/system/menus/${id}`),

  /**
   * 批量删除菜单
   */
  batchDelete: (data: BatchRequest) =>
    request.post<ApiResponse<void>>('/system/menus/batch-delete', data.ids),

  /**
   * 切换菜单状态
   */
  toggleStatus: (id: number, enabled: boolean) =>
    request.put<ApiResponse<MenuResponse>>(`/system/menus/${id}/enabled`, null, {
      params: { enabled }
    }),

  /**
   * 切换菜单可见性
   */
  toggleVisible: (id: number, visible: boolean) =>
    request.put<ApiResponse<MenuResponse>>(`/system/menus/${id}/visible`, null, {
      params: { visible }
    }),

  // 用户菜单相关
  /**
   * 获取当前用户菜单树
   */
  getUserTree: () =>
    request.get<ApiResponse<MenuTreeNode[]>>('/system/menus/user/tree'),

  /**
   * 获取指定用户菜单树
   */
  getUserTreeById: (userId: number) =>
    request.get<ApiResponse<MenuTreeNode[]>>(`/system/menus/user/${userId}/tree`),

  // 角色菜单管理
  /**
   * 查询角色关联菜单
   */
  getRoleMenus: (roleId: number) =>
    request.get<ApiResponse<number[]>>(`/system/roles/${roleId}/menus`),

  /**
   * 为角色分配菜单权限
   */
  assignRoleMenus: (roleId: number, menuIds: number[]) =>
    request.post<ApiResponse<void>>(`/system/roles/${roleId}/menus`, menuIds),

  // 菜单验证
  /**
   * 检查菜单名称是否存在
   */
  checkName: (name: string, excludeId?: number) =>
    request.get<ApiResponse<boolean>>('/system/menus/check-name', {
      params: { name, excludeId }
    })
}
