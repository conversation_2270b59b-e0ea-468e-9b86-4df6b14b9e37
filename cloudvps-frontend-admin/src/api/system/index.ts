/**
 * 系统服务API统一导出
 */

// API接口导出
export { authApi, userLoginLogApi } from './auth'
export { userApi } from './user'
export { roleApi } from './role'
export { permissionApi } from './permission'
export { dictTypeApi, dictDataApi } from './dict'
export { systemConfigApi } from './config'

// 类型定义导出
export type * from './types/common'
export type * from './types/auth'
export type * from './types/user'
export type * from './types/role'
export type * from './types/permission'
export type * from './types/dict'
export type * from './types/config'
