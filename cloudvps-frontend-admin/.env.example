# 应用配置
VITE_APP_TITLE=CloudVPS管理后台
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:8080
VITE_API_TIMEOUT=10000

# 认证配置
VITE_JWT_TOKEN_KEY=cloudvps_admin_token
VITE_JWT_REFRESH_TOKEN_KEY=cloudvps_admin_refresh_token

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_PWA=false
VITE_ENABLE_DEVTOOLS=true

# 上传配置
VITE_UPLOAD_MAX_SIZE=10485760
VITE_UPLOAD_ALLOWED_TYPES=image/*,application/pdf

# WebSocket配置
VITE_WS_URL=ws://localhost:8080/ws

# 其他配置
VITE_DEFAULT_LANGUAGE=zh-CN
VITE_DEFAULT_THEME=light
