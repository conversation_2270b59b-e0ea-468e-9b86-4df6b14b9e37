# 开发环境配置

# 应用配置
VITE_APP_TITLE=CloudVPS管理后台
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# API配置 - 微服务架构
VITE_API_BASE_URL=http://localhost:8080
VITE_API_TIMEOUT=10000

# 微服务架构配置
VITE_USE_MICROSERVICE_ARCHITECTURE=true
VITE_API_PREFIX=/api
VITE_AUTO_ADD_PREFIX=true

# 网关配置
VITE_GATEWAY_HOST=localhost
VITE_GATEWAY_PORT=8080

# 微服务路由配置
VITE_SYSTEM_SERVICE_PREFIX=/system
VITE_VIRTUALIZATION_SERVICE_PREFIX=/virtualization
VITE_ORDER_SERVICE_PREFIX=/order
VITE_PAYMENT_SERVICE_PREFIX=/payment

# 微服务架构说明
# 当VITE_USE_MICROSERVICE_ARCHITECTURE=true时：
# 所有API请求都通过网关(8080)路由到对应的微服务
# 网关内部路由规则：
# /api/system/** -> system-service:8081
# /api/virtualization/** -> virtualization-service:8082
# /api/order/** -> order-service:8083
# /api/payment/** -> payment-service:8084
#
# 当VITE_USE_MICROSERVICE_ARCHITECTURE=false时：
# 可以直接访问单体应用或其他架构

# 认证配置
VITE_JWT_TOKEN_KEY=cloudvps_admin_token
VITE_JWT_REFRESH_TOKEN_KEY=cloudvps_admin_refresh_token

# 功能开关
VITE_ENABLE_MOCK=true
VITE_ENABLE_PWA=false
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_PROXY_LOGS=true

# 跨域配置
VITE_CORS_ORIGIN=http://localhost:3000
VITE_CORS_CREDENTIALS=true

# 上传配置
VITE_UPLOAD_MAX_SIZE=10485760
VITE_UPLOAD_ALLOWED_TYPES=image/*,application/pdf

# WebSocket配置
VITE_WS_URL=ws://localhost:8080/ws

# 其他配置
VITE_DEFAULT_LANGUAGE=zh-CN
VITE_DEFAULT_THEME=light

# 开发调试
VITE_DEBUG_API=true
VITE_DEBUG_ROUTER=false
