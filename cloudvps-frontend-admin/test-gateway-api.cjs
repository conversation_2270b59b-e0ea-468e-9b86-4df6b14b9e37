#!/usr/bin/env node

/**
 * API网关路由测试脚本
 * 测试前端API调用是否正确通过网关路由到各个微服务
 */

const http = require('http');

// 测试API路由配置
const apiTests = [
  {
    name: '系统服务 - 获取验证码',
    method: 'GET',
    path: '/api/system/auth/captcha',
    expectedStatus: [200, 401, 403, 404],
    description: '测试系统认证服务'
  },
  {
    name: '系统服务 - 用户列表',
    method: 'GET', 
    path: '/api/system/users',
    expectedStatus: [200, 401, 403],
    description: '测试用户管理服务'
  },
  {
    name: '虚拟化服务 - 节点列表',
    method: 'GET',
    path: '/api/virtualization/nodes',
    expectedStatus: [200, 401, 403, 404],
    description: '测试虚拟化管理服务'
  },
  {
    name: '订单服务 - 订单列表',
    method: 'GET',
    path: '/api/order/orders',
    expectedStatus: [200, 401, 403, 404],
    description: '测试订单管理服务'
  },
  {
    name: '支付服务 - 支付记录',
    method: 'GET',
    path: '/api/payment/transactions',
    expectedStatus: [200, 401, 403, 404],
    description: '测试支付管理服务'
  }
];

// 测试单个API
function testApi(apiTest) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 8080,
      path: apiTest.path,
      method: apiTest.method,
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'CloudVPS-Frontend-Gateway-Test',
        'Accept': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const isExpectedStatus = apiTest.expectedStatus.includes(res.statusCode);
        
        resolve({
          name: apiTest.name,
          path: apiTest.path,
          method: apiTest.method,
          status: isExpectedStatus ? 'success' : 'unexpected',
          statusCode: res.statusCode,
          message: getStatusMessage(res.statusCode),
          description: apiTest.description,
          response: data.substring(0, 200), // 只显示前200个字符
          headers: res.headers
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        name: apiTest.name,
        path: apiTest.path,
        method: apiTest.method,
        status: 'error',
        statusCode: null,
        message: error.message,
        description: apiTest.description,
        error: error.code
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        name: apiTest.name,
        path: apiTest.path,
        method: apiTest.method,
        status: 'timeout',
        statusCode: null,
        message: '请求超时',
        description: apiTest.description,
        error: 'TIMEOUT'
      });
    });

    req.end();
  });
}

// 获取状态码说明
function getStatusMessage(statusCode) {
  const messages = {
    200: '成功 - API正常工作',
    401: '未授权 - 需要认证(网关路由成功)',
    403: '禁止访问 - 权限不足(网关路由成功)',
    404: '未找到 - 路径不存在或服务未启动',
    500: '服务器错误 - 内部错误',
    502: '网关错误 - 后端服务不可用',
    503: '服务不可用 - 服务暂时不可用',
    504: '网关超时 - 后端服务响应超时'
  };
  
  return messages[statusCode] || `HTTP ${statusCode}`;
}

// 主测试函数
async function testGatewayRouting() {
  console.log('🚀 开始测试CloudVPS API网关路由...\n');
  console.log('📋 测试目标: 验证前端API请求通过网关正确路由到各微服务\n');
  
  const results = [];
  
  for (const apiTest of apiTests) {
    console.log(`⏳ 测试 ${apiTest.name}...`);
    console.log(`   路径: ${apiTest.method} ${apiTest.path}`);
    
    const result = await testApi(apiTest);
    results.push(result);
    
    const statusIcon = result.status === 'success' ? '✅' : 
                      result.status === 'error' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${result.name}: ${result.message}`);
    
    if (result.statusCode === 401 || result.statusCode === 403) {
      console.log('   🎯 网关路由成功！(认证/权限错误说明请求已到达微服务)');
    } else if (result.statusCode === 404) {
      console.log('   ⚠️  路径不存在或微服务未启动');
    }
    
    console.log('');
  }
  
  console.log('📊 测试结果汇总:');
  console.log('='.repeat(70));
  
  const successfulRoutes = results.filter(r => 
    r.status === 'success' && (r.statusCode === 401 || r.statusCode === 403 || r.statusCode === 200)
  );
  const failedRoutes = results.filter(r => r.status === 'error' || r.statusCode === 404);
  const unexpectedRoutes = results.filter(r => r.status === 'unexpected');
  
  console.log(`✅ 成功路由: ${successfulRoutes.length}/${results.length}`);
  console.log(`❌ 路由失败: ${failedRoutes.length}/${results.length}`);
  console.log(`⚠️  异常状态: ${unexpectedRoutes.length}/${results.length}`);
  
  if (successfulRoutes.length > 0) {
    console.log('\n🟢 成功路由的API:');
    successfulRoutes.forEach(api => {
      console.log(`  - ${api.name}: ${api.message}`);
    });
  }
  
  if (failedRoutes.length > 0) {
    console.log('\n🔴 路由失败的API:');
    failedRoutes.forEach(api => {
      console.log(`  - ${api.name}: ${api.message}`);
    });
  }
  
  console.log('\n💡 结论:');
  if (successfulRoutes.length === results.length) {
    console.log('  🎉 所有API路由都工作正常！');
    console.log('  ✅ 网关正确路由请求到各个微服务');
    console.log('  ✅ 前端可以正常调用后端API');
  } else if (successfulRoutes.length > 0) {
    console.log('  ⚠️  部分API路由正常，部分可能需要检查');
    console.log('  📝 401/403错误是正常的(说明路由成功但需要认证)');
    console.log('  📝 404错误可能表示微服务未启动或路径配置问题');
  } else {
    console.log('  ❌ API路由存在问题，需要检查网关配置');
  }
  
  console.log('\n🌐 下一步测试:');
  console.log('  - 访问前端: http://localhost:3001');
  console.log('  - 测试页面: http://localhost:3001/test');
  console.log('  - 用户管理: http://localhost:3001/system/users');
  
  return results;
}

// 运行测试
if (require.main === module) {
  testGatewayRouting().catch(console.error);
}

module.exports = { testGatewayRouting, testApi };
