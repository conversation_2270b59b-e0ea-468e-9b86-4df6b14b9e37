#!/bin/bash

# JWT认证一致性测试脚本
# 测试直接访问服务和通过网关访问的JWT认证一致性

echo "🔐 CloudVPS JWT认证一致性测试"
echo "=================================="

# 配置
SYSTEM_SERVICE_URL="http://localhost:8081"
GATEWAY_URL="http://localhost:8080"

# 测试用户信息
TEST_USER_ID=1
TEST_USERNAME="testuser"

echo "📋 测试配置:"
echo "  系统服务地址: $SYSTEM_SERVICE_URL"
echo "  网关地址: $GATEWAY_URL"
echo "  测试用户: $TEST_USERNAME (ID: $TEST_USER_ID)"
echo ""

# 步骤1: 生成测试JWT Token
echo "🔑 步骤1: 生成测试JWT Token"
echo "--------------------------------"

# 使用Java生成JWT Token的临时脚本
cat > /tmp/generate_jwt.java << 'EOF'
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;

public class generate_jwt {
    public static void main(String[] args) {
        String secret = "cloudvps-unified-jwt-secret-key-for-all-services-2024";
        String issuer = "cloudvps";
        long expiration = 86400; // 24小时（秒）
        
        Long userId = 1L;
        String username = "testuser";
        
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);
        
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        
        String token = Jwts.builder()
            .issuer(issuer)
            .subject(userId.toString())
            .claim("username", username)
            .issuedAt(now)
            .expiration(expiryDate)
            .signWith(key)
            .compact();
            
        System.out.println(token);
    }
}
EOF

# 编译并运行Java程序生成JWT
echo "正在生成JWT Token..."

# 检查是否有Java和JWT库
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到Java环境"
    exit 1
fi

# 使用系统服务的登录接口生成Token（如果可用）
echo "尝试通过系统服务登录接口获取Token..."

# 创建测试用户登录请求
LOGIN_RESPONSE=$(curl -s -X POST "$SYSTEM_SERVICE_URL/system/auth/login" \
    -H "Content-Type: application/json" \
    -d '{
        "username": "admin",
        "password": "admin123"
    }' 2>/dev/null)

if [ $? -eq 0 ] && echo "$LOGIN_RESPONSE" | grep -q "token"; then
    JWT_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo "✅ 成功获取JWT Token: ${JWT_TOKEN:0:50}..."
else
    echo "⚠️  无法通过登录接口获取Token，使用手动生成的测试Token"
    # 使用预生成的测试Token（仅用于测试）
    JWT_TOKEN="eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJjbG91ZHZwcyIsInN1YiI6IjEiLCJ1c2VybmFtZSI6InRlc3R1c2VyIiwiaWF0IjoxNzM1NTU0MDAwLCJleHAiOjE3MzU2NDA0MDB9.test"
fi

echo ""

# 步骤2: 测试直接访问系统服务
echo "🎯 步骤2: 测试直接访问系统服务"
echo "--------------------------------"

echo "正在测试直接访问系统服务的JWT认证..."

DIRECT_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -H "Content-Type: application/json" \
    "$SYSTEM_SERVICE_URL/system/users/profile" 2>/dev/null)

DIRECT_HTTP_CODE=$(echo "$DIRECT_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
DIRECT_BODY=$(echo "$DIRECT_RESPONSE" | sed '/HTTP_CODE:/d')

echo "直接访问结果:"
echo "  HTTP状态码: $DIRECT_HTTP_CODE"
echo "  响应内容: ${DIRECT_BODY:0:100}..."

if [ "$DIRECT_HTTP_CODE" = "200" ]; then
    echo "✅ 直接访问系统服务JWT认证成功"
    DIRECT_SUCCESS=true
elif [ "$DIRECT_HTTP_CODE" = "401" ]; then
    echo "❌ 直接访问系统服务JWT认证失败 (401 Unauthorized)"
    DIRECT_SUCCESS=false
else
    echo "⚠️  直接访问系统服务返回异常状态码: $DIRECT_HTTP_CODE"
    DIRECT_SUCCESS=false
fi

echo ""

# 步骤3: 测试通过网关访问系统服务
echo "🌐 步骤3: 测试通过网关访问系统服务"
echo "--------------------------------"

echo "正在测试通过网关访问系统服务的JWT认证..."

GATEWAY_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -H "Content-Type: application/json" \
    "$GATEWAY_URL/system/users/profile" 2>/dev/null)

GATEWAY_HTTP_CODE=$(echo "$GATEWAY_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
GATEWAY_BODY=$(echo "$GATEWAY_RESPONSE" | sed '/HTTP_CODE:/d')

echo "网关访问结果:"
echo "  HTTP状态码: $GATEWAY_HTTP_CODE"
echo "  响应内容: ${GATEWAY_BODY:0:100}..."

if [ "$GATEWAY_HTTP_CODE" = "200" ]; then
    echo "✅ 通过网关访问系统服务JWT认证成功"
    GATEWAY_SUCCESS=true
elif [ "$GATEWAY_HTTP_CODE" = "401" ]; then
    echo "❌ 通过网关访问系统服务JWT认证失败 (401 Unauthorized)"
    GATEWAY_SUCCESS=false
else
    echo "⚠️  通过网关访问系统服务返回异常状态码: $GATEWAY_HTTP_CODE"
    GATEWAY_SUCCESS=false
fi

echo ""

# 步骤4: 对比测试结果
echo "📊 步骤4: JWT认证一致性分析"
echo "--------------------------------"

echo "测试结果对比:"
echo "  直接访问系统服务: $([ "$DIRECT_SUCCESS" = true ] && echo "✅ 成功" || echo "❌ 失败") (HTTP $DIRECT_HTTP_CODE)"
echo "  通过网关访问: $([ "$GATEWAY_SUCCESS" = true ] && echo "✅ 成功" || echo "❌ 失败") (HTTP $GATEWAY_HTTP_CODE)"

if [ "$DIRECT_SUCCESS" = "$GATEWAY_SUCCESS" ]; then
    if [ "$DIRECT_SUCCESS" = true ]; then
        echo ""
        echo "🎉 JWT认证一致性测试通过！"
        echo "   ✅ 直接访问和网关访问的JWT认证结果一致"
        echo "   ✅ 两种访问方式都成功通过JWT认证"
        EXIT_CODE=0
    else
        echo ""
        echo "⚠️  JWT认证一致性测试部分通过"
        echo "   ✅ 直接访问和网关访问的JWT认证结果一致"
        echo "   ❌ 但两种访问方式都未能通过JWT认证"
        echo "   💡 可能原因: Token无效、用户不存在或权限不足"
        EXIT_CODE=1
    fi
else
    echo ""
    echo "❌ JWT认证一致性测试失败！"
    echo "   ❌ 直接访问和网关访问的JWT认证结果不一致"
    echo "   🔍 这表明存在JWT配置不一致的问题"
    EXIT_CODE=2
fi

echo ""

# 步骤5: 额外的健康检查测试
echo "🏥 步骤5: 服务健康检查"
echo "--------------------------------"

echo "检查系统服务健康状态..."
SYSTEM_HEALTH=$(curl -s "$SYSTEM_SERVICE_URL/actuator/health" 2>/dev/null)
if echo "$SYSTEM_HEALTH" | grep -q '"status":"UP"'; then
    echo "✅ 系统服务健康状态正常"
else
    echo "❌ 系统服务健康状态异常"
fi

echo "检查网关服务健康状态..."
GATEWAY_HEALTH=$(curl -s "$GATEWAY_URL/actuator/health" 2>/dev/null)
if echo "$GATEWAY_HEALTH" | grep -q '"status":"UP"'; then
    echo "✅ 网关服务健康状态正常"
else
    echo "❌ 网关服务健康状态异常"
fi

echo ""
echo "🏁 测试完成"
echo "=================================="

exit $EXIT_CODE
