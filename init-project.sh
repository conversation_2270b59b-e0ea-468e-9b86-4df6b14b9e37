#!/bin/bash

# =============================================
# CloudVPS 项目初始化脚本
# 用于快速创建项目结构和基础文件
# =============================================

set -e

echo "🚀 开始初始化 CloudVPS 项目..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查必要的工具
check_prerequisites() {
    print_info "检查必要的工具..."
    
    if ! command -v java &> /dev/null; then
        print_error "Java 未安装，请先安装 JDK 21"
        exit 1
    fi
    
    if ! command -v mvn &> /dev/null; then
        print_error "Maven 未安装，请先安装 Maven"
        exit 1
    fi
    
    if ! command -v psql &> /dev/null; then
        print_error "PostgreSQL 客户端未安装，请先安装 PostgreSQL"
        exit 1
    fi
    
    print_success "所有必要工具已安装"
}

# 创建项目目录结构
create_project_structure() {
    print_info "创建项目目录结构..."
    
    # 创建各模块目录
    modules=("cloudvps-parent" "cloudvps-common" "cloudvps-gateway" 
             "cloudvps-service-system" "cloudvps-service-virtualization" 
             "cloudvps-service-order" "cloudvps-service-payment")
    
    for module in "${modules[@]}"; do
        if [ ! -d "$module" ]; then
            mkdir -p "$module"
            print_success "创建目录: $module"
        fi
    done
    
    # 创建各服务模块的标准目录结构
    services=("system" "virtualization" "order" "payment")
    for service in "${services[@]}"; do
        module_dir="cloudvps-service-$service"
        
        # Java 源码目录
        mkdir -p "$module_dir/src/main/java/com/cloudvps/$service"
        mkdir -p "$module_dir/src/main/java/com/cloudvps/$service/controller"
        mkdir -p "$module_dir/src/main/java/com/cloudvps/$service/service"
        mkdir -p "$module_dir/src/main/java/com/cloudvps/$service/repository"
        mkdir -p "$module_dir/src/main/java/com/cloudvps/$service/entity"
        mkdir -p "$module_dir/src/main/java/com/cloudvps/$service/dto/request"
        mkdir -p "$module_dir/src/main/java/com/cloudvps/$service/dto/response"
        mkdir -p "$module_dir/src/main/java/com/cloudvps/$service/config"
        
        # 资源目录
        mkdir -p "$module_dir/src/main/resources/sql"
        mkdir -p "$module_dir/src/main/resources/static"
        mkdir -p "$module_dir/src/main/resources/templates"
        
        # 测试目录
        mkdir -p "$module_dir/src/test/java/com/cloudvps/$service"
        mkdir -p "$module_dir/src/test/resources"
        
        print_success "创建服务模块结构: $module_dir"
    done
    
    # 创建公共模块目录
    mkdir -p "cloudvps-common/src/main/java/com/cloudvps/common"
    mkdir -p "cloudvps-common/src/main/java/com/cloudvps/common/core"
    mkdir -p "cloudvps-common/src/main/java/com/cloudvps/common/security"
    mkdir -p "cloudvps-common/src/main/java/com/cloudvps/common/web"
    mkdir -p "cloudvps-common/src/main/java/com/cloudvps/common/database"
    mkdir -p "cloudvps-common/src/main/resources"
    
    # 创建网关目录
    mkdir -p "cloudvps-gateway/src/main/java/com/cloudvps/gateway"
    mkdir -p "cloudvps-gateway/src/main/resources"
    
    # 创建基础设施目录
    mkdir -p "cloudvps-infrastructure/scripts"
    mkdir -p "cloudvps-infrastructure/config"
    mkdir -p "cloudvps-infrastructure/docs"
    
    print_success "项目目录结构创建完成"
}

# 创建父POM文件
create_parent_pom() {
    print_info "创建父POM文件..."
    
    cat > cloudvps-parent/pom.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cloudvps</groupId>
    <artifactId>cloudvps-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>CloudVPS Parent</name>
    <description>CloudVPS 公有云平台父项目</description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Spring Boot -->
        <spring-boot.version>3.2.1</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        
        <!-- Database -->
        <postgresql.version>42.7.1</postgresql.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        
        <!-- Tools -->
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool.version>5.8.24</hutool.version>
        
        <!-- PVE Client -->
        <pve-java-client.version>1.0.0-SNAPSHOT</pve-java-client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- PVE Java Client -->
            <dependency>
                <groupId>io.github.pve-java-client</groupId>
                <artifactId>pve-java-client</artifactId>
                <version>${pve-java-client.version}</version>
            </dependency>
            
            <!-- Database -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            
            <!-- Tools -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>21</source>
                        <target>21</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
EOF

    print_success "父POM文件创建完成"
}

# 创建根POM文件
create_root_pom() {
    print_info "创建根POM文件..."
    
    cat > pom.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cloudvps</groupId>
    <artifactId>cloudvps-platform</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>CloudVPS Platform</name>
    <description>CloudVPS 公有云售卖平台</description>

    <modules>
        <module>cloudvps-parent</module>
        <module>cloudvps-common</module>
        <module>cloudvps-gateway</module>
        <module>cloudvps-service-system</module>
        <module>cloudvps-service-virtualization</module>
        <module>cloudvps-service-order</module>
        <module>cloudvps-service-payment</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
</project>
EOF

    print_success "根POM文件创建完成"
}

# 检查数据库连接
check_database() {
    print_info "检查数据库连接..."
    
    if psql -U cloudvps -d postgres -c "SELECT 1;" &> /dev/null; then
        print_success "数据库连接正常"
    else
        print_warning "数据库连接失败，请检查PostgreSQL配置"
        print_info "请运行以下命令创建数据库用户："
        echo "sudo -u postgres createuser -s cloudvps"
        echo "sudo -u postgres psql -c \"ALTER USER cloudvps PASSWORD 'cloudvps123';\""
    fi
}

# 初始化数据库
init_databases() {
    print_info "初始化数据库..."
    
    databases=("cloudvps_system" "cloudvps_virtualization" "cloudvps_order" "cloudvps_payment")
    
    for db in "${databases[@]}"; do
        if psql -U cloudvps -lqt | cut -d \| -f 1 | grep -qw "$db"; then
            print_warning "数据库 $db 已存在，跳过创建"
        else
            createdb -U cloudvps "$db"
            print_success "创建数据库: $db"
        fi
        
        # 执行初始化脚本
        module_name=$(echo "$db" | sed 's/cloudvps_/cloudvps-service-/')
        sql_file="$module_name/src/main/resources/sql/init.sql"
        
        if [ -f "$sql_file" ]; then
            psql -U cloudvps -d "$db" -f "$sql_file" > /dev/null 2>&1
            print_success "初始化数据库 $db 完成"
        else
            print_warning "SQL初始化文件不存在: $sql_file"
        fi
    done
}

# 主函数
main() {
    echo "======================================"
    echo "🌟 CloudVPS 项目初始化脚本"
    echo "======================================"
    
    # 检查先决条件
    check_prerequisites
    
    # 创建项目结构
    create_project_structure
    
    # 创建POM文件
    create_parent_pom
    create_root_pom
    
    # 检查数据库
    check_database
    
    # 初始化数据库（如果SQL文件存在）
    if [ -f "cloudvps-service-system/src/main/resources/sql/init.sql" ]; then
        read -p "是否要初始化数据库？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            init_databases
        fi
    fi
    
    echo ""
    echo "======================================"
    print_success "🎉 项目初始化完成！"
    echo "======================================"
    echo ""
    print_info "下一步操作："
    echo "1. 安装 pve-java-client 依赖："
    echo "   cd ../pve-java-client && mvn clean install -DskipTests"
    echo ""
    echo "2. 编译项目："
    echo "   mvn clean compile"
    echo ""
    echo "3. 在 IntelliJ IDEA 中打开项目："
    echo "   File -> Open -> 选择当前目录"
    echo ""
    echo "4. 参考 Quick-Start-Guide.md 继续配置"
    echo ""
    print_success "祝您开发愉快！🚀"
}

# 运行主函数
main "$@"
